#!/bin/bash

# Android 屏幕录制脚本 - 10分钟录制（分段录制）
# 自动录制、拉取、清理

echo "=== Android 10分钟屏幕录制 ==="

# 检查设备连接
echo "检查设备连接..."
DEVICE_COUNT=$(adb devices | grep -v "List of devices" | grep -c "device")

if [ $DEVICE_COUNT -eq 0 ]; then
    echo "❌ 错误: 没有检测到连接的设备"
    echo "请确保设备已连接并开启USB调试"
    exit 1
fi

echo "✓ 设备连接正常"

# 生成唯一文件名
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_DIR="./recordings_${TIMESTAMP}"
FINAL_VIDEO="./screen_record_10min_${TIMESTAMP}.mp4"

# 创建临时目录
mkdir -p "$OUTPUT_DIR"

echo "开始录制10分钟视频（分段录制，每段3分钟）..."
echo "最终文件: $FINAL_VIDEO"
echo "临时目录: $OUTPUT_DIR"
echo ""

# 录制参数 - 使用最大支持的180秒
SEGMENT_TIME=180  # 3分钟 = 180秒（最大值）
TOTAL_SEGMENTS=4  # 录制4段，总共12分钟（稍微超过10分钟）
BITRATE=6000000   # 降低比特率提高兼容性

# 分段录制
for i in $(seq 1 $TOTAL_SEGMENTS); do
    echo "📹 录制第 $i/$TOTAL_SEGMENTS 段 (${SEGMENT_TIME}秒)..."
    echo "   按 Ctrl+C 可提前停止当前段"

    REMOTE_FILE="/sdcard/segment_${i}_${TIMESTAMP}.mp4"
    LOCAL_SEGMENT="$OUTPUT_DIR/segment_${i}.mp4"

    # 使用最基本的录制命令
    echo "   开始录制..."
    adb shell screenrecord --time-limit $SEGMENT_TIME $REMOTE_FILE

    # 检查录制是否成功
    if [ $? -eq 0 ]; then
        echo "  ✓ 第 $i 段录制完成，正在拉取..."

        # 等待一下确保文件写入完成
        sleep 2

        # 拉取文件
        if adb pull $REMOTE_FILE $LOCAL_SEGMENT 2>/dev/null; then
            echo "  ✓ 第 $i 段已保存到本地"
            # 删除设备上的文件
            adb shell rm $REMOTE_FILE 2>/dev/null
        else
            echo "  ❌ 第 $i 段拉取失败，跳过此段"
            continue
        fi
    else
        echo "  ❌ 第 $i 段录制失败"
        continue
    fi

    # 如果不是最后一段，给用户一点准备时间
    if [ $i -lt $TOTAL_SEGMENTS ]; then
        echo "  ⏳ 5秒后开始录制下一段..."
        sleep 5
    fi

    echo ""
done

echo "🔄 所有段录制完成，正在合并视频..."

# 检查是否有 ffmpeg
if command -v ffmpeg &> /dev/null; then
    # 创建文件列表
    LIST_FILE="$OUTPUT_DIR/filelist.txt"
    for i in $(seq 1 $TOTAL_SEGMENTS); do
        echo "file 'segment_${i}.mp4'" >> "$LIST_FILE"
    done
    
    # 合并视频
    if ffmpeg -f concat -safe 0 -i "$LIST_FILE" -c copy "$FINAL_VIDEO" -y 2>/dev/null; then
        echo "✓ 视频合并成功！"
        
        # 清理临时文件
        rm -rf "$OUTPUT_DIR"
        echo "✓ 临时文件已清理"
        
        # 显示最终结果
        echo ""
        echo "🎉 录制完成！"
        echo "📁 最终视频: $FINAL_VIDEO"
        
        if [ -f "$FINAL_VIDEO" ]; then
            FILE_SIZE=$(ls -lh "$FINAL_VIDEO" | awk '{print $5}')
            echo "📊 文件大小: $FILE_SIZE"
        fi
    else
        echo "❌ 视频合并失败"
        echo "📁 分段文件保存在: $OUTPUT_DIR"
    fi
else
    echo "⚠️  未找到 ffmpeg，无法自动合并视频"
    echo "📁 分段文件保存在: $OUTPUT_DIR"
    echo "💡 安装 ffmpeg 后可手动合并："
    echo "   ffmpeg -f concat -safe 0 -i $OUTPUT_DIR/filelist.txt -c copy $FINAL_VIDEO"
fi

echo ""
echo "录制任务完成！"
