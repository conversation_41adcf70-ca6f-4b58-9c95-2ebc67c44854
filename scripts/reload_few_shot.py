import asyncio

from src.action.retrieve import _MaterialRetriever
from src.common.constant import LanguageEnum
from src.settings import agent_setting
from src.common.constant import Area

async def reload_few_shot(env: str, version: str = "draft"):
    global MaterialRetriever

    await _MaterialRetriever(
        version=version, 
        env=env
    ).init_action_and_few_shot_collection()


if __name__ == "__main__":
    env = input("Press Enter to reload ENV (d:dev / t:test / ot:oversea_test / og:oversea_global / ous:oversea_us / otk:oversea_tokyo / r:release): ")
    if env not in ["d", "t", "ot", "r", "og", "ous", "otk", "dev", "test", "oversea_test", "release", "oversea_global", "oversea_us", "oversea_tokyo"]:
        raise ValueError(f"Invalid ENV: {env}")

    if env in ["d", "dev"]:
        env = "dev"
    elif env in ["t", "test"]:
        env = "test"
    elif env in ["ot", "oversea_test"]:
        env = "oversea_test"
    elif env in ["r", "release"]:
        env = "release"
    elif env in ["og", "oversea_global"]:
        env = "oversea_global"
    elif env in ["ous", "oversea_us"]:
        env = "oversea_us"
    elif env in ["otk", "oversea_tokyo"]:
        env = "oversea_tokyo"

    asyncio.run(reload_few_shot(env))
