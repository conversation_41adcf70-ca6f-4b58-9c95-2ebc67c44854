import asyncio
import json
import time
from io import BytesIO
from pathlib import Path

import aiohttp
import numpy as np
import pandas as pd
from loguru import logger
from PIL import Image

from src.settings import agent_setting


async def test_direct_api_response(image_url: str, size_config: dict) -> dict:
    """Test direct API response time with different image sizes"""
    async with aiohttp.ClientSession() as session:
        # Download and process image (same as test_openai_vision_response)
        async with session.get(image_url) as response:
            image_data = await response.read()
            original_size = len(image_data)
            image = Image.open(BytesIO(image_data))

            logger.info("Original image details:")
            logger.info(f"Size: {original_size / 1024:.2f}KB ({original_size:,} bytes)")
            logger.info(f"Dimensions: {image.size}")
            logger.info(f"Format: {image.format}")
            logger.info(f"Mode: {image.mode}")
            logger.info("---")

        # Process image according to size config
        # buffer = BytesIO()
        # if size_config.get("resize"):
        #     image.thumbnail(size_config["max_dimensions"], Image.Resampling.LANCZOS)
        # image.save(
        #     buffer,
        #     format="JPEG",
        #     # quality
        #     # quality=size_config.get("quality", 100),
        #     quality=100,
        #     optimize=size_config.get("optimize", False),
        # )
        # Save processed image to file
        # image.save(
        #     f"processed_image_{size_config.get('name')}.jpg",
        #     format="JPEG",
        #     # quality
        #     # quality=size_config.get("quality", 100),
        #     quality=100,
        #     optimize=size_config.get("optimize", False),
        # )
        # processed_image = buffer.getvalue()
        # processed_size = len(processed_image)

        # Direct API request
        prompt = """先回答有几个人，再分别描述每个人外貌：性别、发型（长度、颜色）、配饰（眼镜、项链、包包等）的颜色和样式、表情、穿着（衣物厚薄、颜色、款式等）、用户特殊行为（如有），要求必须准确、客观、全面、简洁，不超20字。
### 输出格式：
{
    "person_count": 1,
    "description": "完整的特征描述"
}

### 示例输出：
单人示例：
{
    "person_count": 1,
    "description": "黑色长发女生，金框眼镜，白衬衫黑裤，微笑"
}

多人示例：
{
    "person_count": 2,
    "description": "黑短发男，深蓝西装，严肃表情；棕色中发女，银项链，米色针织衫，笑容"
}
直接输出json格式的结果。"""

        url = agent_setting.vision_base_url
        url = f"{url}/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {agent_setting.vision_api_key}",
        }

        # Add image prefix for base64 image
        # base64_image = base64.b64encode(processed_image).decode("utf-8")
        # image_with_prefix = f"data:image/jpeg;base64,{base64_image}"

        payload = {
            "model": agent_setting.vision_model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "image_url": {"url": image_url},
                            "type": "image_url",
                        },
                        {"text": prompt, "type": "text"},
                    ],
                }
            ],
        }

        # Test response time
        start_time = time.time()
        async with session.post(url, headers=headers, json=payload) as response:
            response_data = await response.json()
            end_time = time.time()
            if not response_data.get("choices"):
                result = None
            else:
                try:
                    result = response_data["choices"][0]["message"]["content"]
                except Exception as e:
                    logger.error(f"Failed to get multimodal result: {e}")
                    result = None
            print(result)
            if not result:
                logger.error("Failed to get multimodal result")
                raise Exception(
                    f"Failed to get multimodal result, response_data: {response_data}"
                )
            result = result.strip()

            return {
                "config": size_config,
                "original_size_kb": original_size / 1024,
                "processed_size_kb": original_size / 1024,
                "response_time": end_time - start_time,
                "original_dimensions": image.size,
                "processed_dimensions": image.size,
                "success": response.status == 200,
                "response": result,
            }


async def run_experiments():
    # Test configurations
    test_configs = [
        {"name": "original", "resize": False, "quality": 100, "optimize": False},
        # {
        #     "name": "high_res",
        #     "resize": True,
        #     "max_dimensions": (1024, 1024),  # Original high quality
        #     "quality": 95,
        #     "optimize": True,
        # },
        # {
        #     "name": "medium_res",
        #     "resize": True,
        #     "max_dimensions": (768, 768),  # Balanced resolution
        #     "quality": 90,
        #     "optimize": True,
        # },
        # {
        #     "name": "low_res",
        #     "resize": True,
        #     "max_dimensions": (512, 512),  # OpenAI's low-res specification
        #     "quality": 85,
        #     "optimize": True,
        # },
        # {
        #     "name": "low_res_1",
        #     "resize": True,
        #     "max_dimensions": (256, 256),
        #     "quality": 80,
        #     "optimize": True,
        # },
        # {
        #     "name": "low_res_2",
        #     "resize": True,
        #     "max_dimensions": (128, 128),
        #     "quality": 75,
        #     "optimize": True,
        # },
    ]

    # Test images array
    test_images = [
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1111/c/4/0/9/c409fca43e7e63591f24e8efa80c30e8.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1107/3/7/3/0/37301c448456c70d1a787cc3e2d25a85.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1107/5/f/f/e/5ffe628dde8229812490c0ced0fe5e83.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1113/7/8/1/c/781cdf5cb16ef560f4a19078c24d1463.jpeg",
        # "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1113/6/2/c/6/62c6735c9954f4bf2238bf624607dc84.jpeg",
    ] * 30

    results = []
    # Track aggregated metrics for each configuration
    config_metrics = {
        config["name"]: {
            "total_response_time": 0,
            "total_original_size": 0,
            "total_processed_size": 0,
            "count": 0,
            "results": [],
        }
        for config in test_configs
    }

    count = 0
    for image_url in test_images:
        count += 1
        logger.info(f"Testing image {count}/{len(test_images)}: {image_url}")
        for config in test_configs:
            try:
                logger.info(
                    f"Testing configuration: {config['name']} with image: {image_url}"
                )
                # !!! Change this line to test_openai_vision_response/test_direct_api_response to test multimodal model
                result = await test_direct_api_response(image_url, config)

                # Add to aggregated metrics
                metrics = config_metrics[config["name"]]
                metrics["total_response_time"] += result["response_time"]
                metrics["total_original_size"] += result["original_size_kb"]
                metrics["total_processed_size"] += result["processed_size_kb"]
                metrics["count"] += 1
                metrics["results"].append(result)

                logger.info(f"Results for {config['name']}:")
                logger.info(f"Original size: {result['original_size_kb']:.2f}KB")
                logger.info(f"Processed size: {result['processed_size_kb']:.2f}KB")
                logger.info(f"Response time: {result['response_time']:.2f}s")
                logger.info(f"Dimensions: {result['processed_dimensions']}")
                logger.info("---")

                # Add delay between tests
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(
                    f"Error testing configuration {config['name']} with image {image_url}: {e}"
                )

    # Calculate averages and prepare final results
    final_results = []
    for config_name, metrics in config_metrics.items():
        if metrics["count"] > 0:
            avg_result = {
                "config": next(c for c in test_configs if c["name"] == config_name),
                "avg_response_time": metrics["total_response_time"] / metrics["count"],
                "avg_original_size_kb": metrics["total_original_size"]
                / metrics["count"],
                "avg_processed_size_kb": metrics["total_processed_size"]
                / metrics["count"],
                "sample_count": metrics["count"],
                "individual_results": metrics["results"],
            }
            final_results.append(avg_result)

            # Log averages
            logger.info(f"\nAverages for {config_name}:")
            logger.info(
                f"Average response time: {avg_result['avg_response_time']:.2f}s"
            )
            logger.info(
                f"Average original size: {avg_result['avg_original_size_kb']:.2f}KB"
            )
            logger.info(
                f"Average processed size: {avg_result['avg_processed_size_kb']:.2f}KB"
            )
            logger.info(f"Sample count: {avg_result['sample_count']}")
            logger.info("---")

    # Save results
    output_dir = Path("experiment_results")
    output_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    with open(output_dir / f"image_size_results_{timestamp}.json", "w") as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)


def json_to_csv(json_file_path: str):
    # Read JSON file
    with open(json_file_path, "r") as f:
        data = json.load(f)

    # Prepare data for two different CSV files

    # 1. Summary CSV with averages
    summary_data = []
    for result in data:
        # Calculate p90 response time
        response_times = [r["response_time"] for r in result["individual_results"]]
        p90_response_time = np.percentile(response_times, 90)

        summary_data.append(
            {
                "Configuration": result["config"]["name"],
                "Average Response Time (s)": round(result["avg_response_time"], 3),
                "P90 Response Time (s)": round(p90_response_time, 3),
                "Average Original Size (KB)": round(result["avg_original_size_kb"], 2),
                "Average Processed Size (KB)": round(
                    result["avg_processed_size_kb"], 2
                ),
                "Compression Ratio": round(
                    result["avg_processed_size_kb"] / result["avg_original_size_kb"], 3
                ),
                "Sample Count": result["sample_count"],
                "Quality": result["config"].get("quality", 100),
                "Max Dimensions": str(result["config"].get("max_dimensions", "N/A")),
                "Optimize": result["config"].get("optimize", False),
            }
        )

    # 2. Detailed CSV with responses grouped by image
    # First, organize data by image URL
    image_responses = {}
    for config in data:
        config_name = config["config"]["name"]
        for idx, individual in enumerate(config["individual_results"]):
            image_key = f"image_{idx + 1}"
            if image_key not in image_responses:
                image_responses[image_key] = {
                    "Original Size (KB)": round(individual["original_size_kb"], 2),
                    "Original Dimensions": str(individual["original_dimensions"]),
                }

            # Add configuration-specific data
            image_responses[image_key].update(
                {
                    f"{config_name}_Processed Size (KB)": round(
                        individual["processed_size_kb"], 2
                    ),
                    f"{config_name}_Response Time (s)": round(
                        individual["response_time"], 3
                    ),
                    f"{config_name}_Dimensions": str(
                        individual["processed_dimensions"]
                    ),
                    f"{config_name}_Response": individual.get("response", "N/A"),
                }
            )

    # Convert to DataFrame
    detailed_df = pd.DataFrame.from_dict(image_responses, orient="index")

    # Reorder columns to group metrics together
    column_groups = ["Original Size (KB)", "Original Dimensions"]
    for config_name in [c["config"]["name"] for c in data]:
        column_groups.extend(
            [
                f"{config_name}_Processed Size (KB)",
                f"{config_name}_Response Time (s)",
                f"{config_name}_Dimensions",
                f"{config_name}_Response",
            ]
        )
    detailed_df = detailed_df[column_groups]

    # Create DataFrames
    summary_df = pd.DataFrame(summary_data)

    # Save to CSV files
    output_dir = Path(json_file_path).parent
    base_name = Path(json_file_path).stem

    summary_df.to_csv(output_dir / f"{base_name}_summary.csv", index=False)
    detailed_df.to_csv(output_dir / f"{base_name}_detailed.csv", index=True)

    print("Summary statistics:")
    print(summary_df.to_string())
    print("\nDetailed statistics saved to CSV files:")
    print(f"Summary: {output_dir / f'{base_name}_summary.csv'}")
    print(f"Detailed: {output_dir / f'{base_name}_detailed.csv'}")


def analyze_response_length(csv_file_path: str):
    """
    分析CSV文件中不同分辨率配置的响应文本长度。

    Args:
        csv_file_path (str): 详细结果CSV文件的路径

    Returns:
        dict: 包含每个分辨率配置的统计信息
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file_path)

    # 需要分析的分辨率配置
    resolutions = [
        "original",
        # "high_res",
        # "medium_res",
        # "low_res",
        # "low_res_1",
        # "low_res_2",
    ]

    # 存储统计结果
    stats = {}

    for res in resolutions:
        col_name = f"{res}_Response"
        if col_name in df.columns:
            response_lengths = df[col_name].str.len()
            stats[res] = {
                "avg_length": response_lengths.mean(),
                "min_length": response_lengths.min(),
                "max_length": response_lengths.max(),
                "std_length": response_lengths.std(),
            }

            # 打印统计信息
            print(f"\n{res} 统计:")
            print(f"平均字数: {stats[res]['avg_length']:.1f}")
            print(f"最短字数: {stats[res]['min_length']:.0f}")
            print(f"最长字数: {stats[res]['max_length']:.0f}")
            print(f"标准差: {stats[res]['std_length']:.1f}")

    return stats


if __name__ == "__main__":
    # 运行测试
    asyncio.run(run_experiments())

    # 整理结果
    # json_file = "experiment_results/image_size_results_20250227_123332.json"
    # json_to_csv(json_file)

    # 分析响应长度
    # detailed_csv = "experiment_results/image_size_results_20250227_131933_detailed.csv"
    # length_stats = analyze_response_length(detailed_csv)
