import asyncio
import json
import time
import base64
from io import BytesIO
from pathlib import Path

import aiohttp
import numpy as np
import pandas as pd
from loguru import logger
from PIL import Image

from src.settings import agent_setting


async def test_direct_api_response(
    image_url: str, size_config: dict, use_base64: bool = False
) -> dict:
    """Test direct API response time with different image sizes"""
    download_time = 0
    encode_time = 0
    api_time = 0
    error_msg = None
    image_data = None
    original_size = 0
    image_size = None
    success = False

    try:
        async with aiohttp.ClientSession() as session:
            # Record download start time
            download_start = time.time()

            # Download and process image
            try:
                async with session.get(image_url) as response:
                    image_data = await response.read()
                    original_size = len(image_data)
                    image = Image.open(BytesIO(image_data))
                    image_size = image.size

                # Record download end time
                download_end = time.time()
                download_time = download_end - download_start

                logger.info("Original image details:")
                logger.info(
                    f"Size: {original_size / 1024:.2f}KB ({original_size:,} bytes)"
                )
                logger.info(f"Dimensions: {image_size}")
                logger.info(f"Format: {image.format}")
                logger.info(f"Mode: {image.mode}")
                logger.info(f"Download time: {download_time:.2f}s")
                logger.info("---")
            except Exception as e:
                error_msg = f"Failed to download/process image: {str(e)}"
                logger.error(error_msg)
                return {
                    "config": size_config,
                    "method": "base64" if use_base64 else "url",
                    "original_size_kb": original_size / 1024 if original_size else 0,
                    "download_time": download_time,
                    "encode_time": 0,
                    "api_time": 0,
                    "total_time": download_time,
                    "original_dimensions": image_size,
                    "success": False,
                    "error": error_msg,
                    "error_phase": "download",
                }

            # Direct API request
            prompt = """先回答有几个人，再分别描述每个人外貌：性别、发型（长度、颜色）、配饰（眼镜、项链、包包等）的颜色和样式、表情、穿着（衣物厚薄、颜色、款式等）、用户特殊行为（如有），要求必须准确、客观、全面、简洁，不超20字。
### 输出格式：
{
    "person_count": 1,
    "description": "完整的特征描述"
}"""

            url = agent_setting.vision_base_url
            url = f"{url}/chat/completions"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {agent_setting.vision_api_key}",
            }

            # Prepare image content based on method
            if use_base64:
                try:
                    # Record base64 encoding start time
                    encode_start = time.time()

                    # Convert to base64
                    base64_image = base64.b64encode(image_data).decode("utf-8")
                    image_content = {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                    }

                    # Record base64 encoding end time
                    encode_end = time.time()
                    encode_time = encode_end - encode_start
                    logger.info(f"Base64 encoding time: {encode_time:.2f}s")
                except Exception as e:
                    error_msg = f"Failed to encode image: {str(e)}"
                    logger.error(error_msg)
                    return {
                        "config": size_config,
                        "method": "base64",
                        "original_size_kb": original_size / 1024,
                        "download_time": download_time,
                        "encode_time": encode_time,
                        "api_time": 0,
                        "total_time": download_time + encode_time,
                        "original_dimensions": image_size,
                        "success": False,
                        "error": error_msg,
                        "error_phase": "encode",
                    }
            else:
                image_content = {"type": "image_url", "image_url": {"url": image_url}}

            payload = {
                "model": agent_setting.vision_model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            image_content,
                            {"text": prompt, "type": "text"},
                        ],
                    }
                ],
            }
            logger.info(f"API {url}, {headers}, {payload}")
            # Test API response time
            api_start_time = time.time()
            try:
                async with session.post(url, headers=headers, json=payload) as response:
                    response_data = await response.json()
                    api_end_time = time.time()
                    api_time = api_end_time - api_start_time

                    if not response_data.get("choices"):
                        error_msg = f"No choices in response: {response_data}"
                        raise Exception(error_msg)

                    result = response_data["choices"][0]["message"]["content"].strip()
                    success = True

            except Exception as e:
                error_msg = f"API request failed: {str(e)}"
                logger.error(error_msg)
                if not api_time:  # If api_time wasn't set due to early failure
                    api_time = time.time() - api_start_time
                result = None

            return {
                "config": size_config,
                "method": "base64" if use_base64 else "url",
                "original_size_kb": original_size / 1024,
                "download_time": download_time,
                "encode_time": encode_time,
                "api_time": api_time,
                "total_time": download_time + encode_time + api_time,
                "original_dimensions": image_size,
                "success": success,
                "response": result if success else None,
                "error": error_msg if not success else None,
                "error_phase": "api" if not success else None,
            }

    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return {
            "config": size_config,
            "method": "base64" if use_base64 else "url",
            "original_size_kb": original_size / 1024 if original_size else 0,
            "download_time": download_time,
            "encode_time": encode_time,
            "api_time": api_time,
            "total_time": download_time + encode_time + api_time,
            "original_dimensions": image_size,
            "success": False,
            "error": error_msg,
            "error_phase": "unknown",
        }


async def run_experiments():
    # Test configurations
    test_configs = [
        {
            "name": "url_method",
            "resize": False,
            "quality": 100,
            "optimize": False,
            "use_base64": False,
        },
        {
            "name": "base64_method",
            "resize": False,
            "quality": 100,
            "optimize": False,
            "use_base64": True,
        },
    ]

    # Test image
    test_image = "https://test-global-res.orionstar.com/orics/down/aios001_20250310_502c5723a2197ecb2416968bbb8a942e.jpeg"

    # Number of iterations for each method
    iterations = 10

    results = []
    # Track aggregated metrics for each configuration
    config_metrics = {
        config["name"]: {
            "total_download_time": 0,
            "total_encode_time": 0,
            "total_api_time": 0,
            "total_time": 0,
            "success_count": 0,
            "failed_count": 0,
            "failed_download_time": 0,
            "failed_encode_time": 0,
            "failed_api_time": 0,
            "failed_total_time": 0,
            "results": [],
        }
        for config in test_configs
    }

    for i in range(iterations):
        logger.info(f"Iteration {i + 1}/{iterations}")
        for config in test_configs:
            try:
                logger.info(f"Testing configuration: {config['name']}")
                result = await test_direct_api_response(
                    test_image, config, use_base64=config.get("use_base64", False)
                )

                # Add to aggregated metrics
                metrics = config_metrics[config["name"]]
                if result["success"]:
                    metrics["total_download_time"] += result["download_time"]
                    metrics["total_encode_time"] += result["encode_time"]
                    metrics["total_api_time"] += result["api_time"]
                    metrics["total_time"] += result["total_time"]
                    metrics["success_count"] += 1
                else:
                    metrics["failed_download_time"] += result["download_time"]
                    metrics["failed_encode_time"] += result["encode_time"]
                    metrics["failed_api_time"] += result["api_time"]
                    metrics["failed_total_time"] += result["total_time"]
                    metrics["failed_count"] += 1

                metrics["results"].append(result)

                logger.info(f"Results for {config['name']}:")
                logger.info(f"Download time: {result['download_time']:.2f}s")
                logger.info(f"Encode time: {result['encode_time']:.2f}s")
                logger.info(f"API time: {result['api_time']:.2f}s")
                logger.info(f"Total time: {result['total_time']:.2f}s")
                logger.info(f"Success: {result['success']}")
                if not result["success"]:
                    logger.info(f"Error phase: {result.get('error_phase', 'unknown')}")
                    logger.info(f"Error: {result.get('error', 'unknown error')}")
                logger.info("---")

                # Add delay between tests
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error testing configuration {config['name']}: {e}")

    # Calculate averages and prepare final results
    final_results = []
    for config_name, metrics in config_metrics.items():
        if metrics["success_count"] + metrics["failed_count"] > 0:
            avg_result = {
                "config": next(c for c in test_configs if c["name"] == config_name),
                # Success metrics
                "success_count": metrics["success_count"],
                "avg_success_download_time": metrics["total_download_time"]
                / metrics["success_count"]
                if metrics["success_count"] > 0
                else 0,
                "avg_success_encode_time": metrics["total_encode_time"]
                / metrics["success_count"]
                if metrics["success_count"] > 0
                else 0,
                "avg_success_api_time": metrics["total_api_time"]
                / metrics["success_count"]
                if metrics["success_count"] > 0
                else 0,
                "avg_success_total_time": metrics["total_time"]
                / metrics["success_count"]
                if metrics["success_count"] > 0
                else 0,
                # Failure metrics
                "failed_count": metrics["failed_count"],
                "avg_failed_download_time": metrics["failed_download_time"]
                / metrics["failed_count"]
                if metrics["failed_count"] > 0
                else 0,
                "avg_failed_encode_time": metrics["failed_encode_time"]
                / metrics["failed_count"]
                if metrics["failed_count"] > 0
                else 0,
                "avg_failed_api_time": metrics["failed_api_time"]
                / metrics["failed_count"]
                if metrics["failed_count"] > 0
                else 0,
                "avg_failed_total_time": metrics["failed_total_time"]
                / metrics["failed_count"]
                if metrics["failed_count"] > 0
                else 0,
                "individual_results": metrics["results"],
            }
            final_results.append(avg_result)

            # Log averages
            logger.info(f"\nAverages for {config_name}:")
            logger.info("Successful requests:")
            logger.info(f"Count: {avg_result['success_count']}")
            if avg_result["success_count"] > 0:
                logger.info(
                    f"Average download time: {avg_result['avg_success_download_time']:.2f}s"
                )
                logger.info(
                    f"Average encode time: {avg_result['avg_success_encode_time']:.2f}s"
                )
                logger.info(
                    f"Average API time: {avg_result['avg_success_api_time']:.2f}s"
                )
                logger.info(
                    f"Average total time: {avg_result['avg_success_total_time']:.2f}s"
                )

            logger.info("\nFailed requests:")
            logger.info(f"Count: {avg_result['failed_count']}")
            if avg_result["failed_count"] > 0:
                logger.info(
                    f"Average download time: {avg_result['avg_failed_download_time']:.2f}s"
                )
                logger.info(
                    f"Average encode time: {avg_result['avg_failed_encode_time']:.2f}s"
                )
                logger.info(
                    f"Average API time: {avg_result['avg_failed_api_time']:.2f}s"
                )
                logger.info(
                    f"Average total time: {avg_result['avg_failed_total_time']:.2f}s"
                )
            logger.info("---")

    # Save results
    output_dir = Path("experiment_results")
    output_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    with open(output_dir / f"image_method_comparison_{timestamp}.json", "w") as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)


def json_to_csv(json_file_path: str):
    # Read JSON file
    with open(json_file_path, "r") as f:
        data = json.load(f)

    # Prepare data for summary CSV
    summary_data = []
    for result in data:
        # Calculate p90 times for successful requests
        success_results = [r for r in result["individual_results"] if r["success"]]
        failed_results = [r for r in result["individual_results"] if not r["success"]]

        def calculate_p90(results, key):
            if not results:
                return 0
            values = [r[key] for r in results]
            return np.percentile(values, 90)

        summary_data.append(
            {
                "Method": result["config"]["name"],
                # Success metrics
                "Success Count": result["success_count"],
                "Avg Success Download Time (s)": round(
                    result["avg_success_download_time"], 3
                ),
                "P90 Success Download Time (s)": round(
                    calculate_p90(success_results, "download_time"), 3
                ),
                "Avg Success Encode Time (s)": round(
                    result["avg_success_encode_time"], 3
                ),
                "P90 Success Encode Time (s)": round(
                    calculate_p90(success_results, "encode_time"), 3
                ),
                "Avg Success API Time (s)": round(result["avg_success_api_time"], 3),
                "P90 Success API Time (s)": round(
                    calculate_p90(success_results, "api_time"), 3
                ),
                "Avg Success Total Time (s)": round(
                    result["avg_success_total_time"], 3
                ),
                "P90 Success Total Time (s)": round(
                    calculate_p90(success_results, "total_time"), 3
                ),
                # Failure metrics
                "Failed Count": result["failed_count"],
                "Avg Failed Download Time (s)": round(
                    result["avg_failed_download_time"], 3
                ),
                "P90 Failed Download Time (s)": round(
                    calculate_p90(failed_results, "download_time"), 3
                ),
                "Avg Failed Encode Time (s)": round(
                    result["avg_failed_encode_time"], 3
                ),
                "P90 Failed Encode Time (s)": round(
                    calculate_p90(failed_results, "encode_time"), 3
                ),
                "Avg Failed API Time (s)": round(result["avg_failed_api_time"], 3),
                "P90 Failed API Time (s)": round(
                    calculate_p90(failed_results, "api_time"), 3
                ),
                "Avg Failed Total Time (s)": round(result["avg_failed_total_time"], 3),
                "P90 Failed Total Time (s)": round(
                    calculate_p90(failed_results, "total_time"), 3
                ),
            }
        )

    # Create DataFrame
    summary_df = pd.DataFrame(summary_data)

    # Save to CSV file
    output_dir = Path(json_file_path).parent
    base_name = Path(json_file_path).stem
    summary_df.to_csv(output_dir / f"{base_name}_summary.csv", index=False)

    print("Summary statistics:")
    print(summary_df.to_string())
    print("\nDetailed statistics saved to CSV file:")
    print(f"Summary: {output_dir / f'{base_name}_summary.csv'}")


if __name__ == "__main__":
    # Run experiments
    asyncio.run(run_experiments())

    # Process results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    json_file = f"experiment_results/image_method_comparison_{timestamp}.json"
    json_to_csv(json_file)
