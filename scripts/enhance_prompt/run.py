from typing import Callable, Optional

from openai import Async<PERSON>penA<PERSON>
from pydantic import BaseModel

enhance_prompt_client = AsyncOpenAI(
    api_key="sk-7ctRj4FxlpTnd8rnjt8qSizNKcoSni478eoNSSgUlF1gvK8A",
    base_url="https://api.lkeap.cloud.tencent.com/v1",
)


eval_prompt_client = AsyncOpenAI(
    api_key="***************************************************",
)


class Target(BaseModel):
    class Constraint(BaseModel):
        word_count_limit: Optional[int] = None
        output_language: Optional[str] = "English"
        output_format: Optional[str] = None
        output_rule: Optional[str] = None

    goal: str
    constraints: Optional[Constraint] = None


async def enhance_prompt(original_prompt: str, target: Target) -> str:
    constraints = ""
    if target.constraints:
        constraints_list = []
        if target.constraints.word_count_limit:
            constraints_list.append(
                f"**- Output Word count limit**: {target.constraints.word_count_limit}"
            )
        if target.constraints.output_language:
            constraints_list.append(
                f"**- Output language**: {target.constraints.output_language}"
            )
        if target.constraints.output_format:
            constraints_list.append(
                f"**- Output format**: {target.constraints.output_format}"
            )
        if target.constraints.output_rule:
            constraints_list.append(
                f"**- Output rule**: {target.constraints.output_rule}"
            )

        constraints = (
            "\n## Requirements for the prompt\n" + "\n".join(constraints_list) + "\n"
        )

    meta_prompt = """
Improve the `Original prompt` to {goal}. 
Adhere to prompt engineering best practices.

## Original prompt
```
{simple_prompt}
```
{constraints}
Only return the Optimized prompt.
"""

    meta_prompt = meta_prompt.format(
        simple_prompt=original_prompt,
        constraints=constraints,
        goal=target.goal,
    )
    response = await enhance_prompt_client.chat.completions.create(
        model="deepseek-r1",
        messages=[{"role": "user", "content": meta_prompt}],
        stream=True,
    )

    print(meta_prompt)

    print("===Start===")

    full_output = ""
    async for chunk in response:
        if chunk.choices[0].delta.content:
            print(chunk.choices[0].delta.content, end="", flush=True)
            full_output += chunk.choices[0].delta.content

    print("\n===End===")
    return full_output.replace("```", "").strip()


async def evaluate_prompt(func: Callable, optimized_prompt: str, dataset: list):
    for user_text in dataset:
        original_prompt = optimized_prompt.format(text=user_text)
        print("Input:", original_prompt)
        response = await func(original_prompt)
        print("Response:", response)


async def translate(prompt: str):
    response = await eval_prompt_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
    )
    return response.choices[0].message.content


if __name__ == "__main__":
    import asyncio

    # 你的原始prompt
    original_prompt = """
    任务：完整的翻译用户说的话
    用户的话：`{text}`
    """

    # 1.优化prompt
    target = Target(
        goal="Better meet user requirements",
        constraints=Target.Constraint(
            word_count_limit=100,
            output_language="English",
            output_format="JUST ONLY output the prompt and with ``` ```",
            output_rule="user input must use `{text}`",
        ),
    )
    optimized_prompt = asyncio.run(enhance_prompt(original_prompt, target))

    # 2.评估优化后的prompt
    # TODO: llm judge the optimized prompt

    asyncio.run(
        evaluate_prompt(
            translate,
            optimized_prompt,
            ["我要去长城看看，需要带伞吗？明天吓五去", "长城在哪儿？"],
        )
    )


"""

Improve the `Original prompt` to Better meet user requirements. 
Adhere to prompt engineering best practices.

## Original prompt
```

    任务：完整的翻译用户说的话
    用户的话：`{text}`
    
```

## Requirements for the prompt
**- Output Word count limit**: 100
**- Output language**: English
**- Output format**: JUST ONLY output the prompt and with ``` ```
**- Output rule**: user input must use `{text}`

Only return the Optimized prompt.

===Start===


```
Accurately translate the user's input text into clear, natural English while preserving all contextual nuances. Maintain original formatting and only output the translation without additional content. Input: {text}
```
===End===
Input: Accurately translate the user's input text into clear, natural English while preserving all contextual nuances. Maintain original formatting and only output the translation without additional content. Input: 我要去长城看看，需要带伞吗？明天吓五去
Response: I want to go see the Great Wall. Do I need to bring an umbrella? I'm going tomorrow at five.
Input: Accurately translate the user's input text into clear, natural English while preserving all contextual nuances. Maintain original formatting and only output the translation without additional content. Input: 长城在哪儿？
Response: Where is the Great Wall?
"""