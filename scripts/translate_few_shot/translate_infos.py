import json
import os
from openai import OpenAI
from typing import Dict, Any, List
import time
from src.settings import agent_setting
from src.common.constant import LanguageEnum

OPENAI_API_KEY = "***************************************************"

def translate_text(client: OpenAI, text: str, target_language: str = 'en') -> str:
    """使用OpenAI API翻译文本
    
    Args:
        client: OpenAI客户端
        text: 要翻译的文本
        target_language: 目标语言代码 (例如: 'en' 英语, 'ko' 韩语)
    """
    language_prompts = {
        LanguageEnum.en: "你是一个专业的中英互译助手。请将给定的中文翻译成地道的英文。保持专业性和准确性。",
        LanguageEnum.de: "你是一个专业的中德互译助手。请将给定的中文翻译成地道的德文。保持专业性和准确性。",
        # 可以继续添加其他语言的提示
        'ko': "你是一个专业的中韩互译助手。请将给定的中文翻译成地道的韩文。保持专业性和准确性。",
        'de': "你是一个专业的英德互译助手。请将给定的英文翻译成地道的德文。保持专业性和准确性。",

    }

    # 检查是否支持目标语言
    if target_language not in language_prompts:
        print(f"警告：不支持的目标语言 '{target_language}'，将返回原文")
        return text
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": language_prompts[target_language]},
                {"role": "user", "content": f"请将以下文本翻译成{target_language}:\n{text}"}
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"翻译出错: {e}")
        return text

def translate_params(client: OpenAI, params: Dict[str, Any], target_language: str = 'en') -> Dict[str, Any]:
    """递归翻译params字典中的中文值"""
    translated_params = {}
    for key, value in params.items():
        if isinstance(value, str) and any('\u4e00' <= char <= '\u9fff' for char in value):
            # 如果是包含中文的字符串，进行翻译
            translated_params[key] = translate_text(client, value, target_language)
        elif isinstance(value, dict):
            # 如果是字典，递归翻译
            translated_params[key] = translate_params(client, value, target_language)
        elif isinstance(value, list):
            # 如果是列表，检查并翻译列表中的元素
            translated_params[key] = [
                translate_text(client, item, target_language) if isinstance(item, str) and any('\u4e00' <= char <= '\u9fff' for char in item)
                else item for item in value
            ]
        else:
            translated_params[key] = value
    return translated_params

def translate_few_shot(input_file: str, output_file: str, target_language: str = LanguageEnum.en):
    """翻译few_shot文件"""
    client = OpenAI(api_key=OPENAI_API_KEY)
    
    # 读取原始JSON文件
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    translated_data = []
    
    # 处理每个样例
    for idx, item in enumerate(data):
        print(f"处理第 {idx + 1}/{len(data)} 个样例...")
        translated_item = item.copy()
        
        # 翻译Input
        if 'Input' in item:
            translated_item['Input'] = translate_text(client, item['Input'], target_language)
            time.sleep(0.5)  # 添加延迟以避免API限制
        
        # 翻译Output中的params
        if 'Output' in item and 'params' in item['Output']:
            translated_item['Output']['params'] = translate_params(
                client, item['Output']['params'], target_language
            )
            time.sleep(0.5)
        
        translated_data.append(translated_item)
    
    # 保存翻译后的JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(translated_data, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    language = 'de'
    
    # 从 LanguageEnum 中获取所有支持的语言，排除中文
    # supported_languages = [getattr(LanguageEnum, attr) for attr in dir(LanguageEnum)
    #                      if not attr.startswith('_') and getattr(LanguageEnum, attr) != LanguageEnum.zh]
    # if language not in supported_languages:
    #     print(f"错误：不支持的目标语言 '{language}'")
    #     print(f"当前支持的语言: {', '.join(supported_languages)}")
    #     exit(1)
    
    input_file = "src/action/few_shot_data/_few_shot_en_US.json"
    output_file = f"src/action/few_shot_data/tmp_few_shot_{language}.json"
    
    translate_few_shot(input_file, output_file, target_language=language)
