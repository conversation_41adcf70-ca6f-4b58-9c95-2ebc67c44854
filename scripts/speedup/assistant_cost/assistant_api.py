import random

from typing_extensions import override
import time
from typing import List
import uuid

import aiohttp
from openai import AssistantEventHandler
from openai.types.beta.threads.runs import ToolCall, RunStep
from openai.types.beta.threads import MessageD<PERSON><PERSON>, Message
from loguru import logger
from openai import OpenAI

import pandas as pd
import numpy as np


global_elapsed_stats = []
global_vector_stats = []


OVERSEA_ONLINE_API_KEY = "********************************************************************************************************************************************************************"
TEST_API_KEY = "***************************************************"


class EventHandler(AssistantEventHandler):
    """
    流式获取 assistant 答案
    """

    def __init__(self, query):
        super().__init__()
        self.start_at = time.time()
        self.id = str(uuid.uuid4())
        self.query = query
        self.stats = {}
        self.first_chunk = False

    @override
    def on_text_created(self, text) -> None:
        print("\nassistant > ", end="", flush=True)

    @override
    def on_tool_call_created(self, tool_call):
        print(f"\nassistant > {tool_call.type}\n", flush=True)
        self.stats["tool_call_created_elapsed"] = time.time() - self.start_at

    @override
    def on_tool_call_done(self, tool_call: ToolCall) -> None:
        print(f"tool call {tool_call}")
        self.stats["tool_call_done_elapsed"] = time.time() - self.start_at

    @override
    def on_run_step_done(self, run_step: RunStep) -> None:
        """Callback that is fired when a run step is completed"""
        print(f"Finished run step {run_step} elapsed {time.time() - self.start_at}")
        self.stats["run_file_search_done_elapsed"] = time.time() - self.start_at

    @override
    def on_message_delta(self, delta: MessageDelta, snapshot: Message) -> None:
        if not self.first_chunk:
            self.stats["first_chunk_elapsed"] = time.time() - self.start_at
            self.stats["first_chunk_content"] = delta.content[0].text
            self.first_chunk = True
        print(f"delta: {delta} elapsed {time.time() - self.start_at}")

    @override
    def on_message_done(self, message) -> None:
        # print a citation to the file searched

        message_content = message.content[0].text
        print(f"message content {message_content}")
        self.stats["message_done_elapsed"] = time.time() - self.start_at
        self.stats["answer_content"] = message_content
        global_elapsed_stats.append(self.stats)
        print(f"elapsed: {time.time() - self.start_at}")
        annotations = message_content.annotations
        citations = []
        print("Get annotation content")
        print(annotations)
        for index, annotation in enumerate(annotations):
            message_content.value = message_content.value.replace(
                annotation.text, f"[{index}]"
            )
            # if file_citation := getattr(annotation, "file_citation", None):
            #     cited_file = client.files.retrieve(file_citation.file_id)
            #     citations.append(f"[{index}] {cited_file.filename}")

        print(message_content.value)
        print("\n".join(citations))


def prepare_test_assistant_data():
    "asst_EjODc5XS6J6j7zDEjzTizK2G"
    client = OpenAI(api_key=TEST_API_KEY)
    assistant = client.beta.assistants.create(
        name="AgentOS Test Assistant",
        instructions="You are an expert agentos expertise. Use you knowledge base to answer questions about AgentOS",
        model="gpt-4o",
        tools=[{"type": "file_search"}],
    )
    print(f"Assistant ID: {assistant.id}")

    file_paths = ["Ravin group Stall activity and Company brief.docx"]
    file_streams = [open(path, "rb") for path in file_paths]
    vector_store = client.beta.vector_stores.create(name="Ravin")
    file_batch = client.beta.vector_stores.file_batches.upload_and_poll(
        vector_store_id=vector_store.id, files=file_streams
    )
    print(file_batch.status)
    print(file_batch.file_counts)

    assistant = client.beta.assistants.update(
        assistant_id=assistant.id,
        tool_resources={"file_search": {"vector_store_ids": [vector_store.id]}},
    )


def measure_assistant_speed(api_key: str, assistant_id: str, queries: List[str]):
    # "asst_8cW64u21U1Vq0iFMRYeAeJzB"
    client = OpenAI(api_key=api_key)
    assistant = client.beta.assistants.retrieve(assistant_id=assistant_id)

    for query in queries:
        thread = client.beta.threads.create(
            messages=[
                {
                    "role": "user",
                    "content": f"{random.randint(0, 1000)}You are a very smart robot",
                },
                {
                    "role": "user",
                    "content": f"{query}",
                    # Attach the new file to the message.
                },
            ]
        )

        with client.beta.threads.runs.stream(
            thread_id=thread.id,
            assistant_id=assistant.id,
            instructions="",
            event_handler=EventHandler(query),
            include=["step_details.tool_calls[*].file_search.results[*].content"],
            tools=[
                {
                    "type": "file_search",
                    "file_search": {
                        "max_num_results": 3,
                        "ranking_options": {"score_threshold": 0.9},
                    },
                }  # 减少result是否能加速？无用
            ],
            # tool_choice=AssistantToolChoiceParam(type="file_search")  # 指定选择file_search是否能加速？ 实际测试没有用
        ) as stream:
            stream.until_done()


def measure_test_assistant_speed():
    assistant_id = "asst_EjODc5XS6J6j7zDEjzTizK2G"
    ravin_questions = [
        "How long has the Ravin Group been in operation, and where are its manufacturing facilities located?",
        "What are the four key business verticals of the Ravin Group?",
        "What types of power cables does Ravin manufacture, and for which applications?",
        "What services does Ravin provide in the High Voltage (HV) and Extra High Voltage (EHV) EPC domain?",
        "What is the main function of Power Equipment TransX, and how does it benefit power transformers?",
        "What types of solar tracking systems does Ravin offer, and by how much can they increase energy generation?",
        "To how many countries does Ravin export its products?",
        "Which major domestic Indian infrastructure projects have used Ravin’s specialized cables?",
        "What are some of the notable international projects that Ravin has contributed to?",
        "What are IGNAMO Fire Survival Cables, and under what conditions do they maintain circuit integrity?",
        "How many kilometers of EHV cables has Ravin installed, and how many joints and terminations has it completed?",
        "What system-integrated solutions does Ravin provide for power transmission and distribution?",
        "What is the 'Gyaan & Vigyaan Knowledge Quiz,' and what is its purpose?",
        "What is the 'Predict the Future Contest,' and what does it aim to inspire?",
        "How can visitors participate in the 'Selfie with the Gyaan Vigyaan and Dhanwaan' social media contest?",
        "Who or what are 'Gyaan Vigyaan and Dhanwaan' in the context of the stall activities?",
        "What advantages does the online moisture removal technology of TransX have over traditional methods?",
        "What technology is used in Ravin’s solar tracking systems to enhance power generation?",
        "How does Ravin ensure safety and reliability in its high-voltage cable systems?",
        "How does Ravin implement customization and system integration in its cable solutions?",
    ]
    measure_assistant_speed(TEST_API_KEY, assistant_id, ravin_questions)
    # call pandas to save excel
    df = pd.DataFrame(global_elapsed_stats)
    df.to_csv("test_assistant_speed_2025070301.csv")


def get_vector_id(assistant_id: str):
    client = OpenAI(api_key=OVERSEA_ONLINE_API_KEY)
    assistant = client.beta.assistants.retrieve(assistant_id=assistant_id)
    vector_ids = assistant.tool_resources.file_search.vector_store_ids
    assert len(vector_ids) == 1

    vector_id = vector_ids[0]
    print(f"vector_id: {vector_id}")
    return vector_id


async def search_vector_store(
    vector_store_id: str, query: str, filters: dict = None
) -> dict:
    """
    搜索Vector Store

    Args:
        vector_store_id: Vector Store ID
        query: 查询文本
        filters: 过滤条件

    Returns:
        dict: 搜索结果
    """
    try:
        search_url = f"https://api.openai.com/v1/vector_stores/{vector_store_id}/search"

        headers = {
            "Authorization": f"Bearer {OVERSEA_ONLINE_API_KEY}",
            "Content-Type": "application/json",
        }

        payload = {
            "query": query,
        }

        if filters:
            payload["filters"] = filters

        async with aiohttp.ClientSession() as session:
            async with session.post(
                search_url, headers=headers, json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"搜索成功，返回{len(result.get('data', []))}个结果, {result}")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"搜索失败: HTTP {response.status}, {error_text}")
                    return {"error": f"HTTP {response.status}: {error_text}"}

    except Exception as e:
        logger.error(f"搜索Vector Store失败: {str(e)}")
        return {"error": str(e)}


async def vector_search_stats(vector_id: str):
    base_questions = [
        "What does the term 'clinker-built' mean in the context of traditional Norwegian boats?",
        "Why were cotton, hemp, or animal hair soaked in tar used between hull planks?",
        "How did people traditionally measure and classify boat size along the Norwegian coast?",
        "Why were oars considered an important safety feature in traditional Norwegian boats?",
        "What was the typical sail setup on traditional Norwegian boats before the 20th century?",
        "How was boat-building knowledge traditionally passed down through generations?",
        "How did regional environmental conditions influence boat design in different parts of Norway?",
        "What types of wood were commonly used in Norwegian boat building, and why?",
        "Why was timber typically felled during the cold season for boat construction?",
        "How did landmark navigation play a role in traditional coastal fishing?",
        "What were some traditional methods of preserving fish along the Norwegian coast?",
        "In what ways were traditional boats used beyond fishing for coastal communities?",
        "What role did women play in fish markets during the 19th century?",
        "What types of boats were popular for leisure and racing in the 19th and early 20th centuries?",
        "What was the role of boathouses and quayside warehouses in traditional fishing villages?",
        "How was the herring fishery organized, and what made it economically important?",
        "What dangers did fishermen face at sea along the Norwegian coast?",
        "What were common superstitions or taboos observed by Norwegian fishermen?",
        "Who was Sea-Sara, and why is she remembered in coastal folklore?",
        "What kinds of clothing and gear were essential for fishermen during winter fisheries?",
        "How did young boys (skårunge) get introduced to life at sea?",
        "What was the function of boat rugs and how were they made?",
        "What types of boats were used in the Lofoten fisheries?",
        "What did a typical fisherman’s provision list look like in the 1860s?",
        "How were fishing profits divided among the boat crew?",
        "What was life like in fishermen’s shacks during the Lofoten fishery?",
        "How did the introduction of motorised vessels change fishing culture?",
        "What were church boats used for in Norwegian coastal communities?",
        "What daily diet did coastal Norwegians typically have in the 19th century?",
        "How did people reuse and repurpose old materials and gear?",
        "What does the term 'snekke' refer to and how was it used?",
        "What distinguishes the design of the geitbåt?",
        "Why were some mittens made with two thumbs?",
        "What were the roles and responsibilities of a høvedsmann on a fishing boat?",
        "How did the fisherman’s superstition relate to the halibut?",
        "What kinds of dangers and shipwrecks occurred off the Norwegian coast?",
        "How did fish markets function in cities like Oslo and Bergen?",
        "How did the seine fishing technique work?",
        "Why were traditional church boat journeys important culturally?",
        "How did children participate in fishing culture and chores?",
        "How did seasonal work patterns affect the daily routines of coastal families?",
        "What was the significance of tar production for coastal communities?",
        "How did peat serve as a vital resource in some coastal areas?",
        "What were the differences between western and eastern Norwegian boats?",
        "How did cod get processed into stockfish and clipfish?",
        "What was the role of women in fish processing industries like clipfish?",
        "What were the consequences of timber shortages on boat building?",
        "How did the transition to steamships cause conflicts with traditional fishermen?",
        "What did landmark-based navigation involve?",
        "Why was oak both valued and challenging as a boatbuilding material?",
        "How were church events like funerals and weddings tied to maritime transport?",
        "How was community life structured around coastal fishing and farming?",
        "What does the phrase 'sailing for fun' imply in the document?",
        "What were the impacts of fish exports on urban growth?",
        "How did families cope with frequent deaths at sea?",
        "What did fishermen believe about supernatural sea creatures like Draugen?",
        "Why were women traditionally seen as bad luck aboard fishing vessels?",
        "How were schoolchildren's lives affected by geography and sea crossings?",
        "What does the phrase 'the forest and the boat' symbolize in Norwegian culture?",
        "How did wool, leather and linen contribute to seafaring gear?",
        "What materials were used to make rivets and sail ropes?",
        "How did local resources influence the economy of coastal settlements?",
        "What were the special features of snidbetning boats?",
        "What is the significance of the Lofoten fishery in Norwegian history?",
        "How were nets deployed during a successful seine operation?",
        "What was the role of navigation landmarks in line fishing?",
        "How did the ‘lottsystem’ divide profits and responsibilities?",
        "Why was felted wool preferred for sea mittens?",
        "How were mackerel caught traditionally in southern and eastern Norway?",
        "How were the different types of rowing boats adapted to their environments?",
        "What were the uses of the pram boat in coastal transport?",
        "How were regional boat ‘dialects’ preserved and passed down?",
        "How did religious life influence social gatherings by boat?",
        "What was the experience of single mothers in tight-knit coastal communities?",
        "What traditional fishing rituals included gender taboos?",
        "What were the signs and rituals surrounding storm omens?",
        "How did boats support trade and market travel in rural areas?",
        "What were the defining characteristics of a søgnebåt?",
        "How were hard weather conditions handled with traditional boat design?",
        "How did clipfish production support regional employment?",
        "How did the gaff tool become a taboo item at sea?",
        "What practices ensured safety when navigating fjords and open waters?",
        "What was the process of producing stockfish for export?",
        "How did women contribute to the tar and wool industries?",
        "What were the duties of a skårunge on a fishing boat?",
        "How was kelp gathered and used in agriculture?",
        "What role did small trading posts play in coastal livelihoods?",
        "How did church boats vary across different Norwegian regions?",
        "What was the cultural role of singing and hymns on church days?",
        "What legal changes affected village owners' fishing rights?",
        "What cultural customs surrounded boat burials?",
        "Why was human or horse hair used in mittens?",
        "How did fishing impact gender roles and family dynamics?",
        "What did early boat racing traditions look like in Norway?",
        "How did the fish trade affect towns like Stavanger and Fredrikstad?",
        "Why did motorised vessels eventually replace traditional ones?",
        "How did women’s labor shape coastal economic activities?",
        "What challenges are associated with modern fish farming?",
        "How did coastal communities balance fishing with agriculture?",
        "What educational challenges did children in remote coastal areas face?",
        "How did men’s seasonal migration for fishing shape family life?",
        "How was seaweed preserved or dried for later use?",
        "What beliefs surrounded dreams and premonitions before sea journeys?",
        "How did the introduction of radio and weather forecasting change fishing safety?",
        "What features distinguished the notbåt from other vessel types?",
        "How did coastal people adapt to limited access to material goods?",
        "How did Norway become a global leader in cod and herring export?",
        "How has the cultural image of the snekke evolved post-WWII?",
    ]

    for question in base_questions:
        start_at = time.time()
        search_result = await search_vector_store(vector_id, question)
        global_vector_stats.append(
            {
                "question": question,
                "vector_id": vector_id,
                "elapsed": time.time() - start_at,
                "answers": search_result,
            }
        )

    pd.DataFrame(global_vector_stats).to_csv("vector_search_stats.csv")


def analysis_vector_search_stats():
    # calculate stats
    df = pd.read_csv("vector_search_stats.csv")

    # Calculate statistics for elapsed time
    elapsed_times = df["elapsed"].values

    # Basic statistics
    avg_time = elapsed_times.mean()
    min_time = elapsed_times.min()
    max_time = elapsed_times.max()
    median_time = np.median(elapsed_times)
    p90_time = np.percentile(elapsed_times, 90)
    p95_time = np.percentile(elapsed_times, 95)
    p99_time = np.percentile(elapsed_times, 99)

    # Print statistics
    logger.info("Vector Search Statistics:")
    logger.info(f"Average time: {avg_time:.4f}s")
    logger.info(f"Median time: {median_time:.4f}s")
    logger.info(f"Min time: {min_time:.4f}s")
    logger.info(f"Max time: {max_time:.4f}s")
    logger.info(f"P90 time: {p90_time:.4f}s")
    logger.info(f"P95 time: {p95_time:.4f}s")
    logger.info(f"P99 time: {p99_time:.4f}s")

    # Return statistics as a dictionary
    return {
        "avg": avg_time,
        "median": median_time,
        "min": min_time,
        "max": max_time,
        "p90": p90_time,
        "p95": p95_time,
        "p99": p99_time,
    }


if __name__ == "__main__":
    import asyncio
    # asst_YhEa5j4kqVt7JsEZjZ0msYvZ
    # Norsk Maritimt Museum
    # get_vector_id("asst_YhEa5j4kqVt7JsEZjZ0msYvZ")
    asyncio.run(vector_search_stats("vs_6863a65c7dc88191ab50dc363f5c7a92"))
    # analysis_vector_search_stats()
