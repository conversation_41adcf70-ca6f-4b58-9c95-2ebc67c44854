#!/usr/bin/env python3
"""
LLM Streaming Performance 测试
专门测算streaming接口中tool首token、content首token、reasoning首token的耗时
"""

import requests
import json
import time
import csv
import uuid
from datetime import datetime
from copy import deepcopy
import numpy as np
from collections import defaultdict
from loguru import logger

# 测试消息模板
base_messages = [
    {
        "role": "user",
        "content": """You MUST choose the most appropriate action based on the latest `Chat Conversation`、`Screen Information` and `Similar Answers`. The output format must be JSON and The output text language is zh_CN.

# Robot's Information
{"基本能力": "具备出色的任务规划与执行能力、丰富的语言表达能力以及强大的记忆力", "当前音量": 10, "电池电量": 77, "当前时间": "2025年02月05日 19点20分31秒", "当前位置经纬度": "39.911431,116.566432", "所在国家": "中国", "所在省份": "北京市", "所在城市": "北京市", "所在地区": "朝阳区", "所在街道": "建国路", "所在地点": "猎豹移动", "当前移动速度": 1.0, "目标最大平稳速度": 1.0}

# Robot's Actions
"SAY:说。(text[True,string]:Speak in the first person)",
"NAVIGATE_START:室内导航，带用户去下面提供的位置(destination[False,enum['接待点', '南极洲会议室', '厕所']]:The destination)",
"KNOWLEDGE_QA:知识问答，详细介绍自己、公司、产品、业务、领导等(question[True,string]:The question to ask)",

# Chat Conversation
<Robot> SAY '你好，有什么可以帮助你的么？'
<User> SAY '带我去厕所'
""",
    }
]


class StreamingTimer:
    """streaming响应计时器"""

    def __init__(self):
        self.start_time = None
        self.first_token_time = None
        self.tool_first_token_time = None
        self.content_first_token_time = None
        self.reasoning_first_token_time = None
        self.total_response = ""
        self.current_stage = None

    def start(self):
        """开始计时"""
        self.start_time = time.time()
        self.first_token_time = None
        self.tool_first_token_time = None
        self.content_first_token_time = None
        self.reasoning_first_token_time = None
        self.total_response = ""
        self.current_stage = None

    def process_delta(self, delta, debug=False):
        """处理streaming delta"""
        logger.critical(delta)
        current_time = time.time()
        elapsed_from_start = current_time - self.start_time if self.start_time else 0

        # 记录首个token时间
        is_first_token = self.first_token_time is None
        if is_first_token:
            self.first_token_time = current_time

        # 检测各种类型的首token
        is_reasoning_first = False
        is_tool_first = False
        is_content_first = False

        # 检测reasoning首token
        if self.reasoning_first_token_time is None and "reasoning_content" in delta:
            reasoning_content = delta["reasoning_content"]
            if reasoning_content:
                self.reasoning_first_token_time = current_time
                self.current_stage = "reasoning"
                is_reasoning_first = True
                self.total_response += f"[REASONING]{reasoning_content}"

        # 检测tool首token (function call)
        if self.tool_first_token_time is None and "tool_calls" in delta:
            tool_calls = delta["tool_calls"]
            if tool_calls:
                self.tool_first_token_time = current_time
                self.current_stage = "tool"
                is_tool_first = True
                self.total_response += f"[TOOL_CALLS]{tool_calls}"

        # 检测content首token
        if self.content_first_token_time is None and "content" in delta:
            content = delta["content"]
            if content:
                self.content_first_token_time = current_time
                self.current_stage = "content"
                is_content_first = True
                self.total_response += content

        # 打印每个delta的详细信息
        if debug:
            delta_info = []

            if "reasoning_content" in delta and delta["reasoning_content"]:
                reasoning_preview = delta["reasoning_content"][:30]
                if len(delta["reasoning_content"]) > 30:
                    reasoning_preview += "..."
                delta_info.append(f"reasoning='{reasoning_preview}'")

            if "tool_calls" in delta and delta["tool_calls"]:
                delta_info.append(f"tool_calls={len(delta['tool_calls'])} items")

            if "content" in delta and delta["content"]:
                content_preview = delta["content"][:30]
                if len(delta["content"]) > 30:
                    content_preview += "..."
                delta_info.append(f"content='{content_preview}'")

            markers = []
            if is_first_token:
                markers.append("🥇首token")
            if is_reasoning_first:
                markers.append("🧠reasoning首token")
            if is_tool_first:
                markers.append("🔧tool首token")
            if is_content_first:
                markers.append("📝content首token")

            marker_str = " ".join(markers) if markers else ""
            delta_str = " | ".join(delta_info) if delta_info else "empty"
            print(f"    [{elapsed_from_start:.3f}s] {marker_str} {delta_str}")

    def get_metrics(self):
        """获取计时指标"""
        if self.start_time is None:
            return {}

        metrics = {
            "total_latency": time.time() - self.start_time if self.start_time else 0,
            "first_token_latency": self.first_token_time - self.start_time
            if self.first_token_time
            else None,
            "tool_first_token_latency": self.tool_first_token_time - self.start_time
            if self.tool_first_token_time
            else None,
            "content_first_token_latency": self.content_first_token_time
            - self.start_time
            if self.content_first_token_time
            else None,
            "reasoning_first_token_latency": self.reasoning_first_token_time
            - self.start_time
            if self.reasoning_first_token_time
            else None,
            "response_length": len(self.total_response),
        }

        return metrics


def call_streaming_api(messages, model_name, api_url, api_key, debug=False):
    """调用streaming API"""
    timer = StreamingTimer()
    timer.start()

    try:
        response = requests.post(
            api_url,
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            },
            json={
                "messages": messages,
                "model": model_name,
                "temperature": 0.7,
                "max_tokens": 1000,
                "stream": True,
                "chat_template_kwargs": {"enable_thinking": False},
            },
            stream=True,
        )

        response.raise_for_status()

        # 处理streaming响应
        for line in response.iter_lines():
            if line:
                line_text = line.decode("utf-8")

                # 跳过非数据行
                if not line_text.startswith("data: "):
                    continue

                # 处理结束标记
                if line_text.strip() == "data: [DONE]":
                    break

                try:
                    # 解析JSON数据
                    json_str = line_text[6:]  # 移除 'data: ' 前缀
                    data = json.loads(json_str)

                    # 处理streaming数据
                    if "choices" in data and len(data["choices"]) > 0:
                        choice = data["choices"][0]
                        if "delta" in choice:
                            delta = choice["delta"]
                            timer.process_delta(delta, debug=debug)

                except json.JSONDecodeError:
                    # 忽略无法解析的行
                    continue

    except requests.RequestException as e:
        print(f"API调用失败: {e}")
        return None

    return timer.get_metrics()


def run_streaming_performance_test():
    """运行streaming性能测试"""

    # API配置 - 使用qwen-max (从settings.py中的plan_model配置)
    OPENAI_BASE_URL = (
        "https://prem.dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    )
    OPENAI_KEY = (
        "sk-2f99e60920ee4e22bde8fda877567b99"  # 从settings.py中的plan_model_api_key
    )
    MODEL_NAME = "qwen-max"

    # 创建CSV文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"llm_streaming_performance_{timestamp}.csv"

    # CSV列标题
    headers = [
        "iteration",
        "total_latency",
        "first_token_latency",
        "tool_first_token_latency",
        "content_first_token_latency",
        "reasoning_first_token_latency",
        "response_length",
    ]

    # 收集所有指标用于统计
    all_metrics = defaultdict(list)

    print("=" * 80)
    print("🚀 LLM Streaming Performance 测试开始")
    print("=" * 80)
    print(f"模型: {MODEL_NAME}")
    print("API: prem.dashscope")
    print("测试轮数: 50")
    print(f"结果文件: {csv_filename}")
    print()

    with open(csv_filename, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers)
        writer.writeheader()

        for i in range(50):
            print(f"🔄 执行第 {i + 1} 轮测试...")

            # 准备消息 (添加多种随机字符串避免缓存)
            messages = deepcopy(base_messages)
            random_uuid = str(uuid.uuid4())
            random_timestamp = str(int(time.time() * 1000000))  # 微秒时间戳
            random_number = str(np.random.randint(100000, 999999))

            # 在消息开头和结尾都加上随机串
            original_content = messages[0]["content"]
            messages[0]["content"] = f"""
随机标识: {random_uuid}
时间戳: {random_timestamp}
随机数: {random_number}

{original_content}

测试轮次: {i + 1}
随机后缀: {random_uuid[:8]}
"""

            # 调用streaming API (启用debug模式以显示每个chunk)
            metrics = call_streaming_api(
                messages, MODEL_NAME, OPENAI_BASE_URL, OPENAI_KEY, debug=True
            )

            if metrics is None:
                print(f"❌ 第 {i + 1} 轮测试失败")
                continue

            # 添加iteration信息
            metrics["iteration"] = i + 1

            # 写入CSV
            writer.writerow(metrics)

            # 收集统计数据
            for key, value in metrics.items():
                if key != "iteration" and value is not None:
                    all_metrics[key].append(value)

            # 打印当前轮次结果
            print(f"   总延迟: {metrics.get('total_latency', 0):.3f}s")
            print(f"   首token: {metrics.get('first_token_latency', 'N/A')}")
            print(f"   tool首token: {metrics.get('tool_first_token_latency', 'N/A')}")
            print(
                f"   content首token: {metrics.get('content_first_token_latency', 'N/A')}"
            )
            print(
                f"   reasoning首token: {metrics.get('reasoning_first_token_latency', 'N/A')}"
            )
            print()

            # 避免请求过于频繁
            time.sleep(0.5)

    # 打印统计分析
    print_performance_analysis(all_metrics, csv_filename)


def print_performance_analysis(all_metrics, csv_filename):
    """打印性能分析结果"""

    print("=" * 80)
    print("📊 Streaming Performance 分析结果")
    print("=" * 80)

    for metric_name, values in all_metrics.items():
        if not values:
            continue

        # 过滤None值
        valid_values = [v for v in values if v is not None]
        if not valid_values:
            continue

        avg = np.mean(valid_values)
        p50 = np.percentile(valid_values, 50)
        p90 = np.percentile(valid_values, 90)
        p99 = np.percentile(valid_values, 99)
        min_val = np.min(valid_values)
        max_val = np.max(valid_values)

        print(f"\n🎯 {metric_name}:")
        print(f"   样本数: {len(valid_values)}")
        if metric_name.endswith("_latency"):
            print(f"   平均值: {avg:.3f}s")
            print(f"   P50: {p50:.3f}s")
            print(f"   P90: {p90:.3f}s")
            print(f"   P99: {p99:.3f}s")
            print(f"   最小值: {min_val:.3f}s")
            print(f"   最大值: {max_val:.3f}s")
        else:
            print(f"   平均值: {avg:.1f}")
            print(f"   最小值: {min_val}")
            print(f"   最大值: {max_val}")

    print()
    print("💡 关键洞察:")

    # 分析首token延迟
    if "first_token_latency" in all_metrics:
        ftl_values = [v for v in all_metrics["first_token_latency"] if v is not None]
        if ftl_values:
            avg_ftl = np.mean(ftl_values)
            print(f"   首token平均延迟: {avg_ftl:.3f}s")

    # 分析tool vs content延迟差异
    tool_latencies = [
        v for v in all_metrics.get("tool_first_token_latency", []) if v is not None
    ]
    content_latencies = [
        v for v in all_metrics.get("content_first_token_latency", []) if v is not None
    ]

    if tool_latencies and content_latencies:
        avg_tool = np.mean(tool_latencies)
        avg_content = np.mean(content_latencies)
        print(f"   tool首token平均: {avg_tool:.3f}s")
        print(f"   content首token平均: {avg_content:.3f}s")

        if avg_tool < avg_content:
            print(f"   ✅ tool响应比content快 {(avg_content - avg_tool) * 1000:.0f}ms")
        else:
            print(f"   ⚠️  content响应比tool快 {(avg_tool - avg_content) * 1000:.0f}ms")

    print(f"\n📁 详细数据已保存到: {csv_filename}")
    print("=" * 80)


if __name__ == "__main__":
    run_streaming_performance_test()
