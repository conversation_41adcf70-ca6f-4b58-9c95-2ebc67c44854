"""
分析时间开销
"""

import pandas as pd


def analysis_time_cost(file_path: str):
    # read excel
    df = pd.read_excel(file_path)

    # 需要转换为毫秒的列
    ms_convert_cols = [
        "asr_cost_time",
        "wakeup_cost_time",
        "agent_core_total_time",
        "plan_total_cost_time",
        "agent_core_load_context_time",
        "agent_core_load_action_time",
        "agent_core_embedded_time",
        "agent_core_call_summary_llm_time",
        "agent_core_select_few_shot_time",
        "agent_core_call_select_action_llm_time",
        "agent_core_load_user_profile_time",
    ]

    # 将这些列的值乘以1000（转换为毫秒）
    for col in ms_convert_cols:
        df[col] = df[col] * 1000

    # 过滤掉重试的记录
    df = df[df["agent_core_llm_retry_count"] == 0]
    sample_count = len(df)

    # 准备结果数据
    results = []

    # 核心处理时间分析
    core_time_cols = [
        "T2T1时间差(毫秒)",
        "T3T2时间差(豪秒)",
        "plan_end_timestamp-vad_end_timestamp（毫秒）",
        "asr_cost_time",
        "wakeup_cost_time",
        "agent_core_total_time",
        "plan_total_cost_time",
    ]

    for col in core_time_cols:
        # 过滤0值
        valid_data = df[df[col] > 0][col]
        valid_count = len(valid_data)

        results.append(
            {
                "指标": col,
                "类别": "核心耗时",
                "样本数": valid_count,
                "平均时间(ms)": valid_data.mean(),
                "最大时间(ms)": valid_data.max(),
                "最小时间(ms)": valid_data.min(),
                "P70时间(ms)": valid_data.quantile(0.7),
                "P90时间(ms)": valid_data.quantile(0.9),
                "P95时间(ms)": valid_data.quantile(0.95),
                "标准差(ms)": valid_data.std(),
            }
        )

    # Agent Core 细分时间分析
    agent_detail_cols = [
        "agent_core_load_context_time",
        "agent_core_load_action_time",
        "agent_core_embedded_time",
        "agent_core_call_summary_llm_time",
        "agent_core_select_few_shot_time",
        "agent_core_call_select_action_llm_time",
        "agent_core_load_user_profile_time",
    ]

    for col in agent_detail_cols:
        # 过滤0值
        valid_data = df[df[col] > 0][col]
        valid_count = len(valid_data)

        results.append(
            {
                "指标": col,
                "类别": "Agent Core细分",
                "样本数": valid_count,
                "平均时间(ms)": valid_data.mean(),
                "最大时间(ms)": valid_data.max(),
                "最小时间(ms)": valid_data.min(),
                "P70时间(ms)": valid_data.quantile(0.7),
                "P90时间(ms)": valid_data.quantile(0.9),
                "P95时间(ms)": valid_data.quantile(0.95),
                "标准差(ms)": valid_data.std(),
            }
        )

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # 任务类型分组分析
    task_group = (
        df.groupby("基础/高级任务规划")["plan_total_cost_time"]
        .agg(["mean", "count"])
        .reset_index()
    )
    task_group.columns = ["任务类型", "平均耗时(ms)", "样本数"]

    # 保存到Excel
    output_file = file_path.replace(".xlsx", "_analysis.xlsx")
    with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
        # 写入基本信息
        pd.DataFrame([{"总样本数": sample_count}]).to_excel(
            writer, sheet_name="基本信息", index=False
        )

        # 写入时间分析结果
        results_df.to_excel(writer, sheet_name="时间分析", index=False)

        # 写入任务类型分析
        task_group.to_excel(writer, sheet_name="任务类型分析", index=False)

        # 写入LLM重试统计
        pd.DataFrame(
            [
                {
                    "平均重试次数": df["agent_core_llm_retry_count"].mean(),
                    "最大重试次数": df["agent_core_llm_retry_count"].max(),
                }
            ]
        ).to_excel(writer, sheet_name="LLM重试统计", index=False)

    print(f"分析结果已保存到: {output_file}")


if __name__ == "__main__":
    analysis_time_cost("test_results/Agent测试集性能摸底测试.xlsx")
