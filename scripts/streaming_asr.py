import json
import logging
import os
import time
import uuid
import argparse
from pathlib import Path
import websocket  # 需要安装 websocket-client 包

# 常量定义
CHUNK_SIZE = 3200  # 每个音频块的大小（字节）
SEND_INTERVAL = 0.1  # 100毫秒
REQUEST_BOUNDARY_START = "\r\n--DD**ASR**LIB\r\n"

class UserSemanticsInfo:
    def __init__(self, conn_id):
        self.client_id = "orion.ovs.client.1514259512471"
        self.enterprise_id = "orion.ovs.entprise.1429922673-"
        self.group_id = "ovs.group.158623137963780"
        self.version = "1.0"
        self.model = ""
        self.sn = conn_id
        self.devid = "M03SCN1A14024530EC45"

    def to_dict(self):
        return {
            "client_id": self.client_id,
            "enterprise_id": self.enterprise_id,
            "group_id": self.group_id,
            "version": self.version,
            "model": self.model,
            "sn": self.sn,
            "devid": self.devid
        }

class WSClient:
    def __init__(self, config):
        self.config = config
        self.conn_id = str(uuid.uuid4())
        self.sequence = 0
        self.ws = None
        self.is_connected = False
        logging.info(f"连接ConnID: {self.conn_id}")

    def create_params(self, index):
        semantics_info = UserSemanticsInfo(self.conn_id)
        
        return {
            "sid": self.conn_id,
            "idx": str(index),
            "ws_version": 1,
            "sdk_version": 1,
            "audio_type": 0,  # K16_PCM_HEAD
            "lang": 1,  # 默认中文
            "protocol": "0",
            "devid": "M03SCN1A14024530EC45",
            "ver": "1.0.0",
            "pfm": "linux",
            "pid": 100010,
            "user_semantics": json.dumps(semantics_info.to_dict())
        }

    def on_message(self, ws, message):
        logging.info(f"收到响应: {message}")

    def on_error(self, ws, error):
        logging.error(f"错误: {error}")

    def on_open(self, ws):
        logging.info("WebSocket连接已建立")
        self.is_connected = True
        self.send_audio()

    def on_close(self, ws, close_status_code, close_msg):
        self.is_connected = False
        if self.config["debug"]:
            logging.info("连接已关闭")

    def connect(self):
        base_url = self.config["base_url"] or "ws://speech-test.ainirobot.com"
        ws_url = f"{base_url}/ws/streaming-asr"
        
        headers = {"X-Real-IP": "127.0.0.1"}
        if self.config["token"]:
            headers["Authorization"] = f"Bearer {self.config['token']}"

        if self.config["debug"]:
            logging.info(f"连接URL: {ws_url}")
            logging.info(f"请求头: {headers}")

        self.ws = websocket.WebSocketApp(
            ws_url,
            header=headers,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        self.ws.run_forever()

    def send_audio(self):
        if not self.is_connected:
            raise Exception("WebSocket未连接")

        # 发送初始参数
        params = self.create_params(1)
        self.ws.send(json.dumps(params))

        with open(self.config["audio_path"], "rb") as file:
            total_bytes = 0
            self.sequence = 0

            while True:
                chunk = file.read(CHUNK_SIZE)
                if not chunk:
                    # 发送结束信号
                    end_params = self.create_params(-(self.sequence + 1))
                    self.ws.send(json.dumps(end_params))
                    break

                self.sequence += 1
                
                # 处理音频数据
                if self.sequence == 1:
                    # 第一个包添加PCM头
                    audio_data = bytearray([0x05, 0x00, 0x00, 0x00]) + chunk
                else:
                    audio_data = chunk

                # 添加边界标识并发送
                packet = REQUEST_BOUNDARY_START.encode() + audio_data
                self.ws.send(packet, websocket.ABNF.OPCODE_BINARY)  # 使用send方法并指定BINARY类型

                total_bytes += len(chunk)
                if self.config["debug"]:
                    logging.info(f"已发送 {total_bytes} 字节, 序号: {self.sequence}")

                time.sleep(SEND_INTERVAL)

        if self.config["debug"]:
            logging.info(f"音频发送完成，总计 {total_bytes} 字节")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--audio", default="./9b6805fb-0788-4c36-9489-d7a772459d0c_audio.wav",
                      help="音频文件路径 (必需)")
    parser.add_argument("--url", default="ws://speech-test.ainirobot.com",
                      help="WebSocket服务器地址")
    parser.add_argument("--debug", action="store_true", default=True,
                      help="是否启用调试模式")

    args = parser.parse_args()

    if not args.audio:
        logging.fatal("请指定音频文件路径，使用 --audio 参数")
        return

    config = {
        "audio_path": str(Path(args.audio).absolute()),
        "is_svad": False,
        "token": "",
        "base_url": args.url,
        "debug": args.debug
    }

    logging.basicConfig(
        level=logging.DEBUG if args.debug else logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    client = WSClient(config)
    
    try:
        logging.info("开始发送音频数据...")
        client.connect()
        logging.info("处理完成")
    except Exception as e:
        logging.fatal(f"错误: {e}")
    finally:
        if client.ws:
            client.ws.close()

if __name__ == "__main__":
    main()