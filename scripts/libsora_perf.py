import time
import io

import librosa
import wave
import audioread

start = time.time()
backends = audioread.available_backends()
print(f"backends {backends}")
print(f"Backends elapsed {time.time() - start}")

start = time.time()
io_buffer = io.BytesIO()
with open("/Users/<USER>/Codes/cos/test.wav", "rb") as f:

    with wave.open(io_buffer, "wb") as wav:
        wav.setnchannels(1)
        wav.setsampwidth(2)  # 16-bit
        wav.setframerate(16000)
        wav.writeframes(f.read())

print(f"Orion STT wav file elapsed {time.time() - start}")
io_result = io_buffer.getvalue()
print(f"Orion STT io buffer elapsed {time.time() - start}")

io_bytes = io.BytesIO(io_result)
print(f"Orion STT io bytes elapsed {time.time() - start}")

y, sr = librosa.load(io_bytes, start_at=start)
print(f"Orion STT librosa load downsample elapsed {time.time() - start}")


start = time.time()
io_buffer = io.BytesIO()
with open("/Users/<USER>/Codes/cos/附近有什么好吃的.wav", "rb") as f:

    with wave.open(io_buffer, "wb") as wav:
        wav.setnchannels(1)
        wav.setsampwidth(2)  # 16-bit
        wav.setframerate(16000)
        wav.writeframes(f.read())

print(f"Orion STT wav file elapsed {time.time() - start}")
io_result = io_buffer.getvalue()
print(f"Orion STT io buffer elapsed {time.time() - start}")

io_bytes = io.BytesIO(io_result)
print(f"Orion STT io bytes elapsed {time.time() - start}")

y, sr = librosa.load(io_bytes)
print(f"Orion STT librosa load downsample elapsed {time.time() - start}")

start = time.time()
y, sr = librosa.load("/Users/<USER>/Codes/cos/test.wav", sr=16000)
print(f"2 Orion STT librosa load downsample elapsed {time.time() - start}")
