import re
import json
import requests
from src.settings import agent_setting


def correct_asr(text):
    """
    Correct common ASR errors in Chinese text by calling LLM API
    """
    messages = [
        {
            "role": "system",
            "content": "你是小豹 我是猎户星空创造的接待机器人 猎户星空(ORIONSTAR)创立于2016年9月,是由猎豹移动投资的智能服务机器人公司对话感，自然，亲切.",
        },
        {
            "role": "user",
            "content": "# ROBOT BASIC INFORMATION\n{'基本能力': '具备出色的任务规划与执行能力、丰富的语言表达能力以及强大的记忆力', '当前位置经纬度': '39.911373,116.566506', '所在国家': '中国', '所在省份': '北京市', '所在城市': '北京市', '所在地区': '朝阳区', '所在街道': '建国路', '所在地点': '万东科技文化创意产业园'}\n\n# ROBOT'S ACTIONS \nFormat: action:usage_conditions(param1[is_required,type[enum]]:desc[**usage_restrictions**],...)\n\nNAVIGATE_START:室内导航，带用户去下面提供的位置，范围200米内，强调「仅仅去某地，不涉及其他操作」，例如去会议室，前台等等(destination[False,enum['南极洲会议室', '晓曦工位', '拉斯维加斯会议室', '老板办公区', '彦礼工位', '接待点', '电梯1', '测试部', '休息区', '会议室', '厕所', '测试点', '回充点']]:The destination to navigate to)\nSET_VOLUME:调整音量。调整的幅度是10或30，根据用户语气选择(volume_level[True,int]:The volume level to be set **(取值范围: 0 - 100)**)\nSAY:说话，与用户的基础交流。(text[True,string]:Speak in the first person, words limit 30)\nCANCEL:取消当前动作()\nEXIT:退出当前应用()\nBACK:返回上一级()\nNEXT:下一步()\nCONFIRM:用户确认操作()\nCOMMON_REPLAY:Triggered when there is a paused video on the screen information()\nMULTIMEDIA_PLAY:Triggered when there is a video to be played on the screen information()\nCOMMON_PAUSE:Triggered when there is a video playing on the screen information()\nADJUST_SPEED:调整最新的「当前移动速度」(adjusted_speed[True,float]:新的移动速度 **(取值范围: 0.1 - 1.2)**)\nKNOWLEDGE_QA:查询资料回答用户的问题。(question[True,string]:The question to ask)\n\nTAKE_PHOTO:开始拍照()\nVERIFICATION_CODE_INPUT:填写验证码，必须是四位有效数字，数字范围0到9或者零到九(verification_code[True,Integer array]:The captcha to be filled in)\nLAST_4_DIGITS_INPUT:填写手机号后四位，必须是四位有效数字，数字范围0到9或者零到九(last_4_digits[True,Integer array]:The last four digits of the phone number, Must be four valid digits, Must be converted to Arabic numerals, Numeric range 0 to 9)\n",
        },
        {
            "role": "user",
            "content": f"# ROBOT REAL-TIME INFORMATION\n{{'当前音量': 10, '电池电量': 2, '当前时间': '2025年03月11日 18点49分14秒', '当前移动速度': 0.29774445}}\n\n# OUTPUT FORMAT\n```json\n{{\"<action>\":{{\"<param1>\":<value1>,...}}}}\n```\n\n# EXAMPLES\nScore: 0.697, Input: <Robot> SAY '你确认要xxx吗？' <User> SAY '确定'\n{{\"CONFIRM\": {{}}}}\nScore: 0.691, Input: <User> SAY '帮我打把伞'。<Robot> SAY '好的'。<User> SAY '我刚才说了什么？'\n{{\"SAY\": {{\"text\": \"你上次说 \"帮我带把伞\"\"}}}}\n\n# CHAT CONVERSATION\n<Robot> SAY '小希,晚上好你来了'\n<User> SAY '{text}'\n\n Speech-to-text content from 「<User> SAY」 contain homophonic errors, near-sound errors and misrecognitions. You MUST correct the key words first. First. Traverse the usage conditions of each action. If you are still unsure of the answer, you can also refer to the provided **EXAMPLES**. Finally, ONLY OUTPUT THE MOST SUITABLE SINGLE ACTION, If the Action's parameters have enumerated values, the parameters must be chosen from the given enumerated values. with the default robot language being Chinese.",
        },
    ]

    data = {"messages": messages, "model": agent_setting.plan_model, "temperature": 0.0}

    # 这里需要替换为实际的API endpoint和headers
    url = agent_setting.plan_model_base_url + "/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": agent_setting.plan_model_api_key,
    }

    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {e}")
        return None


if __name__ == "__main__":
    # Test sentence
    text_sentences = [
        "top out如何防止模型过年",
        "任务调度中一步和同步有什么区别",
        "分部时系统中的异步通信和刘诗处理如何协同工作",
        "rest net的残差结构有什么劣势",
        "你给我解释下什么叫一步任务什么叫流失任务",
        "我是管理员如果你的电话低于十你就去充电",
        # 复杂的技术术语混淆
        "把transformer里面的attention喊成注意力机制合适吗",  # 中英混搭
        "把批处理大小从巴驰改成一百二十八",  # batch 的音译错误
        "把学习率从零点零零三调成零点零零三儿",  # 数字读音混淆
        # 复杂的同音字/近音字组合
        "把握度调到最大声",  # 音量->握度
        "帮我到老板办公区周围转一圈而已",  # 导航+限定词
        "这个摄像头是坏的麻烦帮我拍张招片",  # 照片->招片
        "带我去南及洲会议室开个小型的会议",  # 南极洲->南及洲
        # 长句中的多重错误
        "你现在的移动速度太快了我都看不清楚了麻烦帮我把速度调整到零点一五",  # 多个要素：速度调整+数值
        "刚才那个会议室的验证吗是多少我记得好像是零八九六要不就是零六八九",  # 验证码+数字混淆
        "请问一下你知道测试部的李工在不在如果在的话麻烦帮我带过去一下",  # 导航+条件判断
        # 方言/口音影响
        "把声音调到老高老高的",  # 方言表达
        "我想去测试部门儿看一眼儿",  # 儿化音
        "这个机器人咋调整速度的嘞",  # 方言语气词
        # 语音识别常见错误模式
        "把learning rate从零点零零三调成零点零零散",  # 中英混搭+数字识别错误
        "帮我导航到拉斯维加死会议室",  # 专有名词识别错误
        "你看看前面有没有什么障碍物儿挡着咱们",  # 口语化表达
        # 特殊场景
        "电池电量还剩百分之多少麻烦告诉我一下",  # 系统状态查询
        "刚才那个验证吗是柒玖叁壹对吧",  # 中文数字混用
        "你带我去测试部的时候能不能走快一点儿啊太慢了",  # 导航+速度调整
        "这个声音太小了调到最大声音都听不清你在说什么",  # 音量+抱怨
        # 极端情况
        "唉那个什么我刚才是不是让你去南极洲会议室来着你怎么带我来这儿了",  # 长句+多重确认
        "哎呀声音太大了太大了赶紧调小点儿调小点儿受不了了",  # 重复+紧急程度
        "你这个导航怎么老是出问题啊能不能重新带我去一下测试部门",  # 投诉+重新执行
    ]
    for text in text_sentences:
        result = correct_asr(text)
        print(f"Original: {text}")
        print(f"API Response: {result}")
