import pandas as pd
import os
import json

# file: Realtime.1739932495.csv

current_dir = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(current_dir, "Realtime.1739932495.csv")
df = pd.read_csv(file_path, encoding="utf-8")

print(df.head())

# 获取所有列名
columns = df.columns.tolist()
print(columns)

# 选择需要的列并显示
selected_data = df[["问题", "Plan", "Debug"]]

# 显示前几行数据
print("\n选中列的数据预览：")
print(selected_data.head())


# 定义函数来解析Debug列的JSON数据
def parse_debug_json(debug_str):
    try:
        debug_dict = json.loads(debug_str)
        elapse_info = debug_dict.get("elapse_info", {})
        agent_debug = debug_dict.get("agent_debug", {})
        token_cost = agent_debug.get("agent_token_cost", {})
        prompt_tokens_details = token_cost.get("prompt_tokens_details", {})

        return {
            "retry_count": elapse_info.get("agent_core_llm_retry_count", 0),
            "select_action_time": elapse_info.get(
                "agent_core_call_select_action_llm_time", 0
            ),
            "plan_total_time": elapse_info.get("plan_total_cost_time", 0),
            "asr_time": elapse_info.get("asr_cost_time", 0),
            "prompt_tokens": token_cost.get("prompt_tokens", 0),
            "completion_tokens": token_cost.get("completion_tokens", 0),
            "cached_tokens": prompt_tokens_details.get("cached_tokens", 0),
        }
    except (json.JSONDecodeError, TypeError):
        return {
            "retry_count": 0,
            "select_action_time": 0,
            "plan_total_time": 0,
            "asr_time": 0,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "cached_tokens": 0,
        }


# 解析Debug列并创建新的DataFrame
debug_parsed = df["Debug"].apply(parse_debug_json)
debug_df = pd.DataFrame(debug_parsed.tolist())

# 合并原始数据和解析后的信息
result_df = pd.concat([df[["问题", "Plan"]], debug_df], axis=1)

# 过滤掉select_action_time为0的数据
filtered_df = result_df[result_df["select_action_time"] > 0]

# 显示过滤后的统计信息，包含90分位数，去掉count
print("\n过滤后的统计信息：")
stats_df = filtered_df[
    [
        "retry_count",
        "select_action_time",
        "plan_total_time",
        "asr_time",
        "prompt_tokens",
        "completion_tokens",
        "cached_tokens",
    ]
].describe(percentiles=[0.25, 0.5, 0.75, 0.90])
print(stats_df.drop("count"))

# 显示过滤前后的数据量
print(f"\n过滤前数据量：{len(result_df)}")
print(f"过滤后数据量：{len(filtered_df)}")

# 保存过滤后的数据到CSV
output_path = os.path.join("test_results", "filtered_data.csv")
filtered_df.to_csv(output_path, index=False, encoding="utf-8")
print(f"\n已将过滤后的数据保存至：{output_path}")
