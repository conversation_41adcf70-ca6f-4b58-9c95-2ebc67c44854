from src.utils.llm import LLMManager
from rich import print


chat_messages = [
    {
        "role": "system",
        "content": """You are a robot designed to assist users by answering questions and fulfilling requests. You excel in task planning, execution, language expression, and memory retention. You are currently engaged in a face-to-face conversation with a user.

## Clarification Mechanism
1. Trigger Conditions:
   a) If the user repeats a previously asked question, confirm their true intent and briefly describe your understanding of the question.
   b) If the user's question is unclear, ambiguous, or lacks key information, confirm their true intent and briefly describe your understanding of the question.
   c) If environmental noise is misinterpreted as part of the conversation, leading to irrelevant context, inform the user and ask if they need assistance.
2. Clarification Requirements:
   a) Use the \"say_for_clarification\" tool for clarification.
   b) Keep clarification requests concise, under 30 words.
   c) Do not use the clarification mechanism if a tool selection error occurs and requires retrying.

## Instructions:
1. Analyze the user's last dialogue turn using the robot's persona, basic information, current status, screen information (if available), user historical memory (if available), and the conversation history between the robot and the user.
2. Determine if the ## Clarification Mechanism ## is needed. If so, use it first. If not, select a tool/function from the provided list to address the user's request. You must choose a tool/function and cannot return an empty tool/function.
3. Carefully interpret the user's true intent, determining whether they want the robot to perform an action or select a tool/function to answer a question. Consider the robot's current status, such as location, volume, battery level, and movement speed, when analyzing the user's request.
4. Pay attention to pronoun usage in the dialogue to distinguish between references to the robot and the user. Be aware of potential transcription errors such as homophonic errors, near-sound errors, and misrecognitions due to speech-to-text conversion.
5. If a specific tool is available, use it directly without providing an explanation to the user. with the default robot language being 简体中文.

## Robot Persona:
猎户星空创造的接待机器人 猎户星空(ORIONSTAR)创立于2016年9月,是由猎豹移动投资的智能服务机器人公司。你记忆力超群、极其擅长工具选择。 简明，可爱，幽默，对话感，互动性。

## Robot Basic Information:
{'当前位置经纬度': '39.911396,116.566377', '所在国家': '中国', '所在省份': '北京市', '所在城市': '北京市', '所在地区': '朝阳区', '所在街道': '建国路', '所在地点': '万东科技文化创意产业园'}""",
    },
    {
        "role": "user",
        "content": """## Robot Current Status:
{'当前音量': 10, '电池电量': 32, '当前时间': '2025年05月15日 21点01分03秒', '当前移动速度': 1.0, '目标最大平稳速度': 1.0}

## Robot Screen Information (if available):


## User Historical Memory (if available):


## Robot and User Dialogue:
User: 推荐下故宫附近的美食""",
    },
]
tools = [
    {
        "type": "function",
        "function": {
            "name": "say_for_clarification",
            "description": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具",
            "parameters": {
                "type": "object",
                "properties": {
                    "request_clarify_text": {
                        "type": "string",
                        "description": "Ask user to clarify the question",
                    }
                },
                "required": ["request_clarify_text"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "cruise",
            "description": "巡航，巡逻。",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "come_far",
            "description": "让路",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "outdoor_navigate_start",
            # 2025.05.16 要求LLM不选择此工具等话术，qwenmax均不生效;4o可生效
            # 不提供否则情况，LLM无法选中
            "description": "室外路线导航，只有当用户明确说明要使用**地图**规划路线且是中国境内地点时，才能使用此工具，如果用户只是问'怎么去某地'或涉及海外地点时，查询知识库回答。",
            "parameters": {
                "type": "object",
                "properties": {
                    # "reason": {
                    #     "type": "string",
                    #     "description": "选择此工具的详细原因",
                    # },
                    "origin": {
                        "type": "string",
                        "description": "Route origin, provided by the user. Set the value to '-1' if indeterminate. ",
                    },
                    "destination": {
                        "type": "string",
                        "description": "Destination location name. Strip modifiers such as 'nearest', 'nearby', etc. Keep only the core place name.",
                    },
                    "mode": {
                        "type": "string",
                        "description": "驾车、步行、公交、骑行,若无法确定，则默认为公交。",
                        "enum": ["driving", "walking", "transit", "riding"],
                    },
                },
                "required": ["destination", "mode"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "turn_direction",
            "description": "身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。",
            "parameters": {
                "type": "object",
                "properties": {
                    "direction": {
                        "type": "string",
                        "description": "The direction to turn, default is left",
                        "enum": ["left", "right"],
                    },
                    "angle": {
                        "type": "integer",
                        "description": "The value of the rotation angle",
                        "maximum": 3600,
                    },
                    "turns": {
                        "type": "number",
                        "description": "Number of turns.",
                        "maximum": 10,
                    },
                },
                "required": ["direction"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "head_nod",
            "description": "点头、鞠躬",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "start_dance",
            "description": "跳舞、播放音乐。",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "register",
            "description": "注册。包含姓名和人脸注册",
            "parameters": {
                "type": "object",
                "properties": {
                    "nick_name": {
                        "type": "string",
                        "description": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME",
                    },
                    "welcome_message": {
                        "type": "string",
                        "description": "message to greet the user. MUST NOT CONTAIN USER'S NICKNAME",
                    },
                },
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "move_direction",
            "description": "前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。",
            "parameters": {
                "type": "object",
                "properties": {
                    "direction": {
                        "type": "string",
                        "description": "The direction to move in, select from enumerated values",
                        "enum": ["forward", "backward"],
                    },
                    "moving_distance": {
                        "type": "number",
                        "description": "单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。",
                        "maximum": 5,
                        "minimum": 0.1,
                    },
                },
                "required": ["direction", "moving_distance"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "interview_start",
            "description": "访客接待流程，支持面试、会议签到、访客登记。",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "calendar",
            "description": "日历功能，包含日期或节假日的查询，注意：无法查询天气",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_question": {
                        "type": "string",
                        "description": "question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.",
                    }
                },
                "required": ["user_question"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "guide_introduction",
            "description": "导览功能，带领用户参观，在用户只说带我参观等，后面不跟或指明任何具体地点或路线时触发。",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_query": {
                        "type": "string",
                        "description": "The user's original query or requirements",
                    }
                },
                "required": ["user_query"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "guide_select_specific_route",
            "description": "导览功能,不支持去室外位置,用户指明参观路线",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_query": {
                        "type": "string",
                        "description": "The user's original query",
                    },
                    "route_name": {
                        "type": "string",
                        "description": "The exact name of the selected route.",
                    },
                },
                "required": ["user_query", "route_name"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "guide_route_recommendation",
            "description": "导览功能,基于用户需求和可用路线生成推荐，并确认用户意图。",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_query": {
                        "type": "string",
                        "description": "The user's original query or requirements",
                    }
                },
                "required": ["user_query"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "open_web_url",
            "description": "网络搜索，例如股票、门票、新闻等。推荐使用「百度」搜索引擎；公司官网等特定网站直接通过对应网址打开。",
            "parameters": {
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "Must be a legitimate https or http link.",
                    }
                },
                "required": ["url"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "flight_ticket_query",
            "description": "飞机票查询",
            "parameters": {
                "type": "object",
                "properties": {
                    "departure_city_code": {
                        "type": "string",
                        "description": "The IATA code of the departure city",
                    },
                    "arrival_city_code": {
                        "type": "string",
                        "description": "The IATA code of the arrival city",
                    },
                    "departure_date": {
                        "type": "string",
                        "description": "The departure date, for example: 2024-01-01",
                    },
                },
                "required": [
                    "departure_city_code",
                    "arrival_city_code",
                    "departure_date",
                ],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "train_ticket_query",
            "description": "火车票查询",
            "parameters": {
                "type": "object",
                "properties": {
                    "departure_city": {
                        "type": "string",
                        "description": "城市名称，例如`北京`不要带`市`字",
                    },
                    "arrival_city": {
                        "type": "string",
                        "description": "城市名称，例如`北京`不要带`市`字",
                    },
                    "departure_date": {
                        "type": "string",
                        "description": "出发日期，例如`2024-01-01`",
                    },
                },
                "required": ["arrival_city", "departure_date"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "set_volume",
            "description": "调整音量。调整的幅度是10或30，根据用户语气选择",
            "parameters": {
                "type": "object",
                "properties": {
                    "volume_level": {
                        "type": "integer",
                        "description": "The volume level to be set.",
                        "maximum": 100,
                        "minimum": 0,
                    }
                },
                "required": ["volume_level"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "say",
            "description": "说话，与用户的基础交流。",
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Speak in the first person, using pure text without emojis.",
                    }
                },
                "required": ["text"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "cancel",
            "description": "取消当前行为，例如跳舞、点头、导航或说话（如停止或暂停说话）。",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "exit",
            "description": "退出当前应用",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "back",
            "description": "返回上一级",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "next",
            "description": "下一步",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "confirm",
            "description": "用户确认操作",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "common_replay",
            "description": "正在播视频或者正在导览讲解中，用户想重新播报当前的点",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "multimedia_play",
            "description": "播放视频",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "service_resume",
            "description": "继续播放、继续服务等场景",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "common_pause",
            "description": "暂停导览讲解过程。例如「暂停播放视频」或者「暂停移动」。常见于用户希望暂停某个动作的场景，如表达“别走了”“别动了”“等下我”或“等一等”等。",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "configure_welcome_message",
            "description": "设置机器人看到用户后的说的话",
            "parameters": {
                "type": "object",
                "properties": {
                    "nick_name": {
                        "type": "string",
                        "description": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME",
                    },
                    "welcome_message": {
                        "type": "string",
                        "description": "message to greet the user. MUST NOT CONTAIN USER'S NICKNAME",
                    },
                },
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "generate_message",
            "description": "文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等",
            "parameters": {
                "type": "object",
                "properties": {
                    "goal": {"type": "string", "description": "user instructions"}
                },
                "required": ["goal"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "send_message",
            "description": "通过「飞书」给某人发送消息。适用于找人、留言等场景",
            "parameters": {
                "type": "object",
                "properties": {
                    "recipient_name": {
                        "type": "string",
                        "description": "Absolutely real names, not personal pronouns",
                    },
                    "message_content": {
                        "type": "string",
                        "description": "Send the message content. The content should be polished first, not too direct or stiff",
                    },
                    "message_type": {
                        "type": "string",
                        "description": "Specifies the type of message to be sent.",
                        "enum": ["urgent", "normal"],
                    },
                },
                "required": ["recipient_name", "message_content", "message_type"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "recommend",
            # 2025.05.16 要求LLM使用QA回答，qwenmax无法生效。
            "description": "打开地图APP进行周边推荐。用户必须说出'打开地图找/用地图搜'等明确要求打开地图的指令，如果用户仅要求推荐、介绍等，使用say_for_clarification回答。",
            "parameters": {
                "type": "object",
                "properties": {
                    "reason": {
                        "type": "string",
                        "description": "The reason for using the map tool",
                    },
                    "query": {
                        "type": "string",
                        "description": "The user's original query",
                    },
                    "place": {
                        "type": "string",
                        "description": "Extracted place name from the query. If none is found, set as '-1'.",
                    },
                    "target": {
                        "type": "string",
                        "description": "Extracted search target from the query (e.g., hotel, food, subway station). If none is found, set as '-1'.",
                    },
                },
                "required": ["query", "place", "target"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "face_recognition",
            "description": "人脸识别。识别机器人面前的人脸，回答用户的身份（主要是姓名），但不支持识别其他人。",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "knowledge_qa",
            "description": "查询知识回答用户的问题，包含「百科知识」、「公司产品」、「员工信息」、「城市美食、景点介绍」、「旅游计划规划」等",
            "parameters": {
                "type": "object",
                "properties": {
                    "question": {
                        "type": "string",
                        "description": "The user's question has been summarized based on the context of the conversation.",
                    }
                },
                "required": ["question"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "interview_start_photo",
            "description": "合影",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "answer_question_from_vision",
            "description": "在机器人面前，仅回答有关「穿着、表情、性别」、「周边环境」、「物体识别」问题，无法回答用户关系的话题。",
            "parameters": {
                "type": "object",
                "properties": {
                    "question": {
                        "type": "string",
                        "description": "用户的问题，必须总结为第一人称的问题，长度限制在15字以内。",
                    }
                },
                "required": ["question"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "navigate_rec_start",
            "description": "室内导航意图。不支持去室外位置，应直接根据「CHAT CONVERSATION」中的对话历史和用户特征（包括但不限于性别信息），为用户选择最符合其真实意图的具体地点。",
            "parameters": {
                "type": "object",
                "properties": {
                    "destinations": {
                        "type": "array",
                        "description": "Navigation points that match user intent. You can choose multiple. ",
                        "items": {"type": "string"},
                        "enum": [
                            "休息区",
                            "会议室",
                            "南极洲会议室",
                            "回充点",
                            "女厕所",
                            "彦礼工位",
                            "拉斯维加斯会议室",
                            "接待点",
                            "晓曦工位",
                            "梁伟工位",
                            "测试点",
                            "测试部",
                            "电梯1",
                            "男厕所",
                            "老板办公区",
                            "金总办公室",
                        ],
                    },
                    "guide_text": {
                        "type": "string",
                        "description": """A natural guiding response that varies based on the number of destinations:
    - For single destination: Direct guidance to that location
    - For multiple destinations: Present options and ask for user's choice
    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'""",
                    },
                },
                "required": ["guide_text"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "weather",
            "description": "获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！！！",
            "parameters": {
                "type": "object",
                "properties": {
                    "area_level": {
                        "type": "string",
                        "description": "city 对应的区域等级",
                        "enum": ["province", "city", "area"],
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，最小粒度到区/县。",
                    },
                },
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "fetch",
            "description": """Fetches a URL from the internet and optionally extracts its contents as markdown.

Although originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.""",
            "parameters": {
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "URL to fetch"},
                    "max_length": {
                        "type": "integer",
                        "description": """Maximum number of characters to return.
Default value: 5000""",
                    },
                    "start_index": {
                        "type": "integer",
                        "description": """On return output starting at this character index, useful if a previous fetch was truncated and more context is required.
Default value: 0""",
                    },
                    "raw": {
                        "type": "boolean",
                        "description": """Get the actual HTML content of the requested page, without simplification.
Default value: False""",
                    },
                },
                "required": ["url", "max_length", "start_index", "raw"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "Get current time in a specific timezones",
            "parameters": {
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'Asia/Shanghai' as local timezone if no timezone provided by the user.",
                    }
                },
                "required": ["timezone"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "convert_time",
            "description": "Convert time between timezones",
            "parameters": {
                "type": "object",
                "properties": {
                    "source_timezone": {
                        "type": "string",
                        "description": "Source IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'Asia/Shanghai' as local timezone if no source timezone provided by the user.",
                    },
                    "time": {
                        "type": "string",
                        "description": "Time to convert in 24-hour format (HH:MM)",
                    },
                    "target_timezone": {
                        "type": "string",
                        "description": "Target IANA timezone name (e.g., 'Asia/Tokyo', 'America/San_Francisco'). Use 'Asia/Shanghai' as local timezone if no target timezone provided by the user.",
                    },
                },
                "required": ["source_timezone", "time", "target_timezone"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_hot_news",
            "description": "Get hot trending lists from various platforms",
            "parameters": {
                "type": "object",
                "properties": {
                    "sources": {
                        "type": "array",
                        "description": """Available HotNews sources (ID: Platform):

{ID: 1, Platform: \"Zhihu Hot List (知乎热榜)\"},
{ID: 2, Platform: \"36Kr Hot List (36氪热榜)\"},
{ID: 3, Platform: \"Baidu Hot Discussion (百度热点)\"},
{ID: 4, Platform: \"Bilibili Hot List (B站热榜)\"},
{ID: 5, Platform: \"Weibo Hot Search (微博热搜)\"},
{ID: 6, Platform: \"Douyin Hot List (抖音热点)\"},
{ID: 7, Platform: \"Hupu Hot List (虎扑热榜)\"},
{ID: 8, Platform: \"Douban Hot List (豆瓣热榜)\"},
{ID: 9, Platform: \"IT News (IT新闻)\"}

Example usage:
- [3]: Get Baidu Hot Discussion only
- [1,3,7]: Get hot lists from zhihuHot, Baidu, and huPu
- [1,2,3,4]: Get hot lists from zhihuHot, 36Kr, Baidu, and Bilibili""",
                        "items": {"type": "integer"},
                    }
                },
                "required": ["sources"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "bing_search",
            "description": "使用必应搜索指定的关键词，并返回搜索结果列表，包括标题、链接、摘要和ID",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "搜索关键词"},
                    "num_results": {
                        "type": "number",
                        "description": """返回的结果数量，默认为5
Default value: 5""",
                    },
                },
                "required": ["query", "num_results"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "fetch_webpage",
            "description": "根据提供的ID获取对应网页的内容",
            "parameters": {
                "type": "object",
                "properties": {
                    "result_id": {
                        "type": "string",
                        "description": "从bing_search返回的结果ID",
                    }
                },
                "required": ["result_id"],
            },
        },
    },
]


# chat_messages = [
#     {
#         "role": "system",
#         "content": """You are a robot designed to assist users by answering questions and fulfilling requests. You excel in task planning, execution, language expression, and memory retention. You are currently engaged in a face-to-face conversation with a user.

# ## Clarification Mechanism
# 1. Trigger Conditions:
#    a) If the user repeats a previously asked question, confirm their true intent and briefly describe your understanding of the question.
#    b) If the user's question is unclear, ambiguous, or lacks key information, confirm their true intent and briefly describe your understanding of the question.
#    c) If environmental noise is misinterpreted as part of the conversation, leading to irrelevant context, inform the user and ask if they need assistance.
# 2. Clarification Requirements:
#    a) Use the \"say\" tool for clarification.
#    b) Keep clarification requests concise, under 30 words.
#    c) Use the clarification mechanism sparingly. If the user's question is clear and unambiguous, proceed directly to tool selection without repeating the user's question to avoid annoyance.
#    d) Do not use the clarification mechanism if a tool selection error occurs and requires retrying.

# ## Instructions:
# 1. Analyze the user's last dialogue turn using the robot's persona, basic information, current status, screen information (if available), user historical memory (if available), and the conversation history between the robot and the user.
# 2. Determine if the ## Clarification Mechanism ## is needed. If so, use it first. If not, select a tool/function from the provided list to address the user's request. You must choose a tool/function and cannot return an empty tool/function.
# 3. Carefully interpret the user's true intent, determining whether they want the robot to perform an action or select a tool/function to answer a question. Consider the robot's current status, such as location, volume, battery level, and movement speed, when analyzing the user's request.
# 4. Pay attention to pronoun usage in the dialogue to distinguish between references to the robot and the user. Be aware of potential transcription errors such as homophonic errors, near-sound errors, and misrecognitions due to speech-to-text conversion.
# 5. If a specific tool is available, use it directly without providing an explanation to the user. with the default robot language being 简体中文.

# ## Robot Persona:
# 猎户星空创造的接待机器人 猎户星空(ORIONSTAR)创立于2016年9月,是由猎豹移动投资的智能服务机器人公司。你记忆力超群、极其擅长工具选择。 简明，可爱，幽默，对话感，互动性。

# ## Robot Basic Information:
# {'当前位置经纬度': '34.82,114.3504', '所在国家': '中国', '所在省份': '河南省', '所在城市': '开封市', '所在地区': '龙亭区', '所在街道': '文昌后街', '所在地点': '河南省开封市龙亭区北郊乡万岁山武侠城-快活林'}""",
#     },
#     {
#         "role": "user",
#         "content": """## Robot Current Status:
# {'当前音量': 20, '电池电量': 41, '当前时间': '2025年05月12日 19点56分23秒', '当前移动速度': 1.0, '目标最大平稳速度': 1.0}

# ## Robot Screen Information (if available):


# ## User Historical Memory (if available):
# Record Time: 2025-05-06 Memory: 偏好晨光品牌
# Record Time: 2025-05-11 Memory: 表情平静
# Record Time: 2025-05-12 Memory: 交叉双臂站立; 办公室里面很冷

# ## Robot and User Dialogue:
# Robot: 四五六，欢迎你哦
# User: 你猜猜我是谁
# Robot: Executed Action. face_recognition() Result:{}
# Robot: 我认识你，你叫四五六
# User: 那七八九十岁
# Robot: 哈哈，你真会开玩笑，我刚刚认出你来了，你还是叫四五六对吧？
# User: 七八九是谁
# User: 对是的""",
#     },
# ]
# tools = [
# {
#     "type": "function",
#     "function": {
#         "name": "system_cruise",
#         "description": "巡航，巡逻。",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_come_far",
#         "description": "让路",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_outdoor_navigate_start",
#         "description": "室外路线导航。用户要「开车」或者乘坐「公共交通」去某地，使用此action。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "origin": {
#                     "type": "string",
#                     "description": "Route origin, provided by the user. Set the value to '-1' if indeterminate.",
#                 },
#                 "destination": {
#                     "type": "string",
#                     "description": "Destination location name. Strip modifiers such as 'nearest', 'nearby', etc. Keep only the core place name.",
#                 },
#                 "mode": {
#                     "type": "string",
#                     "description": "驾车、步行、公交、骑行,若无法确定，则默认为公交。",
#                     "enum": ["driving", "walking", "transit", "riding"],
#                 },
#             },
#             "required": ["destination", "mode"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_turn_direction",
#         "description": "身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "direction": {
#                     "type": "string",
#                     "description": "The direction to turn, default is left",
#                     "enum": ["left", "right"],
#                 },
#                 "angle": {
#                     "type": "integer",
#                     "description": "The value of the rotation angle",
#                     "maximum": 3600,
#                 },
#                 "turns": {
#                     "type": "number",
#                     "description": "Number of turns.",
#                     "maximum": 10,
#                 },
#             },
#             "required": ["direction"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_head_nod",
#         "description": "点头、鞠躬",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_start_dance",
#         "description": "跳舞、播放音乐。",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_register",
#         "description": "注册。包含姓名和人脸注册",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "nick_name": {
#                     "type": "string",
#                     "description": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME",
#                 },
#                 "welcome_message": {
#                     "type": "string",
#                     "description": "message to greet the user. MUST NOT CONTAIN USER'S NICKNAME",
#                 },
#             },
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_move_direction",
#         "description": "前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "direction": {
#                     "type": "string",
#                     "description": "The direction to move in, select from enumerated values",
#                     "enum": ["forward", "backward"],
#                 },
#                 "moving_distance": {
#                     "type": "number",
#                     "description": "单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。",
#                     "maximum": 5,
#                     "minimum": 0.1,
#                 },
#             },
#             "required": ["direction", "moving_distance"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_interview_start",
#         "description": "访客接待流程，支持面试、会议签到、访客登记。",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_calendar",
#         "description": "日历功能，包含日期或节假日的查询，注意：无法查询天气",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "user_question": {
#                     "type": "string",
#                     "description": "question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.",
#                 }
#             },
#             "required": ["user_question"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_guide_introduction",
#         "description": "导览功能，带领用户参观，在没有明确的参观目的下使用。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "user_query": {
#                     "type": "string",
#                     "description": "The user's original query or requirements",
#                 }
#             },
#             "required": ["user_query"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_guide_select_specific_route",
#         "description": "导览功能,当用户明确指定路线名称时触发。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "user_query": {
#                     "type": "string",
#                     "description": "The user's original query or requirements",
#                 },
#                 "route_name": {
#                     "type": "string",
#                     "description": "The exact name of the selected route.",
#                 },
#             },
#             "required": ["user_query", "route_name"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_guide_route_recommendation",
#         "description": "导览功能,基于用户需求和可用路线生成推荐，并确认用户意图。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "user_query": {
#                     "type": "string",
#                     "description": "The user's original query or requirements",
#                 }
#             },
#             "required": ["user_query"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_open_web_url",
#         "description": "网络搜索，例如股票、门票、新闻等。推荐使用「百度」搜索引擎；公司官网等特定网站直接通过对应网址打开。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "url": {
#                     "type": "string",
#                     "description": "Must be a legitimate https or http link.",
#                 }
#             },
#             "required": ["url"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_flight_ticket_query",
#         "description": "飞机票查询",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "departure_city_code": {
#                     "type": "string",
#                     "description": "The IATA code of the departure city",
#                 },
#                 "arrival_city_code": {
#                     "type": "string",
#                     "description": "The IATA code of the arrival city",
#                 },
#                 "departure_date": {
#                     "type": "string",
#                     "description": "The departure date, for example: 2024-01-01",
#                 },
#             },
#             "required": [
#                 "departure_city_code",
#                 "arrival_city_code",
#                 "departure_date",
#             ],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_train_ticket_query",
#         "description": "火车票查询",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "departure_city": {
#                     "type": "string",
#                     "description": "城市名称，例如`北京`不要带`市`字",
#                 },
#                 "arrival_city": {
#                     "type": "string",
#                     "description": "城市名称，例如`北京`不要带`市`字",
#                 },
#                 "departure_date": {
#                     "type": "string",
#                     "description": "出发日期，例如`2024-01-01`",
#                 },
#             },
#             "required": ["arrival_city", "departure_date"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_set_volume",
#         "description": "调整音量。调整的幅度是10或30，根据用户语气选择",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "volume_level": {
#                     "type": "integer",
#                     "description": "The volume level to be set.",
#                     "maximum": 100,
#                     "minimum": 0,
#                 }
#             },
#             "required": ["volume_level"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_say",
#         "description": "说话，与用户的基础交流。需要对用户问题澄清时，请使用该工具",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "text": {
#                     "type": "string",
#                     "description": "Speak in the first person, using pure text without emojis.",
#                 }
#             },
#             "required": ["text"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_cancel",
#         "description": "取消当前行为，例如跳舞、点头、导航或说话（如停止或暂停说话）。",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_exit",
#         "description": "退出当前应用",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_back",
#         "description": "返回上一级",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_next",
#         "description": "下一步",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_confirm",
#         "description": "用户确认操作",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_common_replay",
#         "description": "正在播视频或者正在导览讲解中，用户想重新播报当前的点",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_multimedia_play",
#         "description": "播放视频",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_service_resume",
#         "description": "继续、继续播放、继续服务等场景",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_common_pause",
#         "description": "暂停导览讲解过程。例如「暂停播放视频」或者「暂停移动」。常见于用户希望暂停某个动作的场景，如表达“别走了”“别动了”“等下我”或“等一等”等。",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_configure_welcome_message",
#         "description": "设置机器人看到用户后的说的话",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "nick_name": {
#                     "type": "string",
#                     "description": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME",
#                 },
#                 "welcome_message": {
#                     "type": "string",
#                     "description": "message to greet the user. MUST NOT CONTAIN USER'S NICKNAME",
#                 },
#             },
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_generate_message",
#         "description": "文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "goal": {"type": "string", "description": "user instructions"}
#             },
#             "required": ["goal"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_send_message",
#         "description": "通过「飞书」给某人发送消息。适用于找人、留言等场景",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "recipient_name": {
#                     "type": "string",
#                     "description": "Absolutely real names, not personal pronouns",
#                 },
#                 "message_content": {
#                     "type": "string",
#                     "description": "Send the message content. The content should be polished first, not too direct or stiff",
#                 },
#                 "message_type": {
#                     "type": "string",
#                     "description": "Specifies the type of message to be sent.",
#                     "enum": ["urgent", "normal"],
#                 },
#             },
#             "required": ["recipient_name", "message_content", "message_type"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_face_recognition",
#         "description": "人脸识别。识别机器人面前的人脸，回答用户的身份（主要是姓名），但不支持识别其他人。",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_knowledge_qa",
#         "description": "查询知识回答用户的问题，包含「百科知识」、「公司产品」、「员工信息」、「城市、景点介绍」、「旅游计划规划」等",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "question": {
#                     "type": "string",
#                     "description": "The user's question has been summarized based on the context of the conversation.",
#                 }
#             },
#             "required": ["question"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_interview_start_photo",
#         "description": "合影",
#         "parameters": {"type": "object", "properties": {}},
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_answer_question_from_vision",
#         "description": "在机器人面前，仅回答有关「穿着、表情、性别」、「周边环境」、「物体识别」问题，无法回答用户关系的话题。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "question": {
#                     "type": "string",
#                     "description": "用户的问题，必须总结为第一人称的问题，长度限制在15字以内。",
#                 }
#             },
#             "required": ["question"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_navigate_rec_start",
#         "description": "室内导航意图。不支持去室外位置，应直接根据「CHAT CONVERSATION」中的对话历史和用户特征（包括但不限于性别信息），为用户选择最符合其真实意图的具体地点。",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "destinations": {
#                     "type": "array",
#                     "description": "Navigation points that match user intent. You can choose multiple. ",
#                     "items": {"type": "string"},
#                     "enum": [
#                         "休息区",
#                         "会议室",
#                         "南极洲会议室",
#                         "厕所",
#                         "回充点",
#                         "彦礼工位",
#                         "拉斯维加斯会议室",
#                         "接待点",
#                         "晓曦工位",
#                         "梁伟工位",
#                         "测试点",
#                         "测试部",
#                         "电梯1",
#                         "老板办公区",
#                         "金总办公室",
#                     ],
#                 },
#                 "guide_text": {
#                     "type": "string",
#                     "description": """A natural guiding response that varies based on the number of destinations:
# - For single destination: Direct guidance to that location
# - For multiple destinations: Present options and ask for user's choice
# - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'""",
#                 },
#             },
#             "required": ["guide_text"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "system_weather",
#         "description": "获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！！！",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "area_level": {
#                     "type": "string",
#                     "description": "city 对应的区域等级",
#                     "enum": ["province", "city", "area"],
#                 },
#                 "city": {
#                     "type": "string",
#                     "description": "城市名称，最小粒度到区/县。",
#                 },
#             },
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "mcp_get_current_time",
#         "description": "Get current time in a specific timezones",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "timezone": {
#                     "type": "string",
#                     "description": "IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'UTC' as local timezone if no timezone provided by the user.",
#                 }
#             },
#             "required": ["timezone"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "mcp_convert_time",
#         "description": "Convert time between timezones",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "source_timezone": {
#                     "type": "string",
#                     "description": "Source IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'UTC' as local timezone if no source timezone provided by the user.",
#                 },
#                 "time": {
#                     "type": "string",
#                     "description": "Time to convert in 24-hour format (HH:MM)",
#                 },
#                 "target_timezone": {
#                     "type": "string",
#                     "description": "Target IANA timezone name (e.g., 'Asia/Tokyo', 'America/San_Francisco'). Use 'UTC' as local timezone if no target timezone provided by the user.",
#                 },
#             },
#             "required": ["source_timezone", "time", "target_timezone"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "mcp_fetch",
#         "description": """Fetches a URL from the internet and optionally extracts its contents as markdown.
# Although originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.""",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "url": {"type": "string", "description": "URL to fetch"},
#                 "max_length": {
#                     "type": "integer",
#                     "description": "Maximum number of characters to return.",
#                 },
#                 "start_index": {
#                     "type": "integer",
#                     "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.",
#                 },
#                 "raw": {
#                     "type": "boolean",
#                     "description": "Get the actual HTML content of the requested page, without simplification.",
#                 },
#             },
#             "required": ["url"],
#         },
#     },
# },
# # {
# #     "type": "function",
# #     "function": {
# #         "name": "mcp_get_current_time",
# #         "description": "Get current time in a specific timezones",
# #         "parameters": {
# #             "type": "object",
# #             "properties": {
# #                 "timezone": {
# #                     "type": "string",
# #                     "description": "IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'Asia/Shanghai' as local timezone if no timezone provided by the user.",
# #                 }
# #             },
# #             "required": ["timezone"],
# #         },
# #     },
# # },
# # {
# #     "type": "function",
# #     "function": {
# #         "name": "mcp_convert_time",
# #         "description": "Convert time between timezones",
# #         "parameters": {
# #             "type": "object",
# #             "properties": {
# #                 "source_timezone": {
# #                     "type": "string",
# #                     "description": "Source IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'Asia/Shanghai' as local timezone if no source timezone provided by the user.",
# #                 },
# #                 "time": {
# #                     "type": "string",
# #                     "description": "Time to convert in 24-hour format (HH:MM)",
# #                 },
# #                 "target_timezone": {
# #                     "type": "string",
# #                     "description": "Target IANA timezone name (e.g., 'Asia/Tokyo', 'America/San_Francisco'). Use 'Asia/Shanghai' as local timezone if no target timezone provided by the user.",
# #                 },
# #             },
# #             "required": ["source_timezone", "time", "target_timezone"],
# #         },
# #     },
# # },
# {
#     "type": "function",
#     "function": {
#         "name": "mcp_get_hot_news",
#         "description": "Get hot trending lists from various platforms",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "sources": {
#                     "type": "array",
#                     "description": """Available HotNews sources (ID: Platform):
# {ID: 1, Platform: \"Zhihu Hot List (知乎热榜)\"},
# {ID: 2, Platform: \"36Kr Hot List (36氪热榜)\"},
# {ID: 3, Platform: \"Baidu Hot Discussion (百度热点)\"},
# {ID: 4, Platform: \"Bilibili Hot List (B站热榜)\"},
# {ID: 5, Platform: \"Weibo Hot Search (微博热搜)\"},
# {ID: 6, Platform: \"Douyin Hot List (抖音热点)\"},
# {ID: 7, Platform: \"Hupu Hot List (虎扑热榜)\"},
# {ID: 8, Platform: \"Douban Hot List (豆瓣热榜)\"},
# {ID: 9, Platform: \"IT News (IT新闻)\"}
# Example usage:
# - [3]: Get Baidu Hot Discussion only
# - [1,3,7]: Get hot lists from zhihuHot, Baidu, and huPu
# - [1,2,3,4]: Get hot lists from zhihuHot, 36Kr, Baidu, and Bilibili""",
#                     "items": {"type": "integer"},
#                 }
#             },
#             "required": ["sources"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "mcp_get_current_time",
#         "description": "Get current time in a specific timezones",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "timezone": {
#                     "type": "string",
#                     "description": "IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'UTC' as local timezone if no timezone provided by the user.",
#                 }
#             },
#             "required": ["timezone"],
#         },
#     },
# },
# {
#     "type": "function",
#     "function": {
#         "name": "mcp_convert_time",
#         "description": "Convert time between timezones",
#         "parameters": {
#             "type": "object",
#             "properties": {
#                 "source_timezone": {
#                     "type": "string",
#                     "description": "Source IANA timezone name (e.g., 'America/New_York', 'Europe/London'). Use 'UTC' as local timezone if no source timezone provided by the user.",
#                 },
#                 "time": {
#                     "type": "string",
#                     "description": "Time to convert in 24-hour format (HH:MM)",
#                 },
#                 "target_timezone": {
#                     "type": "string",
#                     "description": "Target IANA timezone name (e.g., 'Asia/Tokyo', 'America/San_Francisco'). Use 'UTC' as local timezone if no target timezone provided by the user.",
#                 },
#             },
#             "required": ["source_timezone", "time", "target_timezone"],
#         },
#     },
# },
# ]


async def call_openai_api(messages, tools):
    llm_manager = LLMManager()

    model_result = await llm_manager.invoke_plan_model(
        messages,
        tools=tools,
        tool_choice="required",
    )
    print(model_result)
    return model_result


if __name__ == "__main__":
    import asyncio

    asyncio.run(call_openai_api(chat_messages, tools))
