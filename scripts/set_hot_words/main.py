import io

import dashscope
import msoffcrypto
import pandas as pd
from dashscope.audio.asr import VocabularyService

dashscope.api_key = "sk-0b724831ddc5450a9a262541df4baeb7"

target_model = "paraformer-v2"


class HotWordsService:
    @classmethod
    def read_hot_words_from_file(cls) -> list[dict]:
        decrypted_workbook = io.BytesIO()
        with open(
            "/Users/<USER>/workspace/easyNLP/scripts/set_hot_words/员工名单.xlsx", "rb"
        ) as file:
            office_file = msoffcrypto.OfficeFile(file)
            office_file.load_key(password="Mx_cmcm")
            office_file.decrypt(decrypted_workbook)

        df = pd.read_excel(decrypted_workbook)
        vocabulary = []
        duplicate = set()
        for record in df.to_records():
            if record[1] in duplicate:
                print(f"Duplicate: {record[1]}")
                continue

            duplicate.add(record[1])
            vocabulary.append(
                {
                    "text": record[1],
                    "weight": 1,
                    "lang": "zh",
                }
            )
        locations = [
            "充电桩",
            "智慧餐厅",
            "接待点",
            "回充点",
            "休息区",
            "企业荣誉",
            "咖啡厅",
            "智慧展厅",
            "专利证书",
            "服务台",
            "合作伙伴",
            "工位",
        ]
        for idx, location in enumerate(locations):
            vocabulary.append(
                {
                    "text": location,
                    "weight": 1,
                    "lang": "zh",
                }
            )
        print("inert words: ", len(vocabulary))

        return vocabulary

    @classmethod
    def update_hot_words(cls, vocabulary_id: str, vocabulary: list[dict]):
        if not vocabulary:
            return "No hot words to update"

        return VocabularyService().update_vocabulary(
            vocabulary_id=vocabulary_id,
            vocabulary=vocabulary,
        )

    @classmethod
    def load_hot_words(cls, vocabulary_id: str):
        vocabulary = VocabularyService().query_vocabulary(vocabulary_id=vocabulary_id)
        for word in vocabulary["vocabulary"]:
            print(word)
        print("gmt_modified: ", vocabulary["gmt_modified"])
        print("Total words: ", len(vocabulary["vocabulary"]))

    @classmethod
    def delete(cls, vocabulary_id: str):
        return VocabularyService().delete_vocabulary(vocabulary_id=vocabulary_id)


if __name__ == "__main__":
    HotWordsService().update_hot_words(
        vocabulary_id="vocab-username1-8ed184cf36b34ce7ad277c901f4e06e0",
        vocabulary=HotWordsService().read_hot_words_from_file(),
    )

    HotWordsService().load_hot_words(
        vocabulary_id="vocab-username1-8ed184cf36b34ce7ad277c901f4e06e0"
    )
