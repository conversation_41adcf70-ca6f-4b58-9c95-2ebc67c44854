# 分析 agent_benchmark_result.csv 存在多少 select_action_cost_time，select_action_cost_time_v2都慢的情况；存在多少select_action_cost_time_v2, select_action_cost_time一个比较慢的
import os
import pandas as pd
import numpy as np

current_dir = os.path.dirname(os.path.abspath(__file__))
agent_benchmark_result_path = os.path.join(current_dir, "agent_benchmark_result.csv")

# 读取CSV文件
df = pd.read_csv(agent_benchmark_result_path)
print(df.columns)

# 计算时间差异
df["time_diff"] = df["select_action_cost_time_v2"] - df["select_action_cost_time"]

# 设定阈值（例如：0.5秒）来定义"慢"
threshold = 0.5

# 分析结果
both_slow = df[
    (df["select_action_cost_time"] > df["select_action_cost_time"].mean() + threshold)
    & (
        df["select_action_cost_time_v2"]
        > df["select_action_cost_time_v2"].mean() + threshold
    )
]

v1_slow = df[
    (df["select_action_cost_time"] > df["select_action_cost_time"].mean() + threshold)
    & (
        df["select_action_cost_time_v2"]
        <= df["select_action_cost_time_v2"].mean() + threshold
    )
]

v2_slow = df[
    (df["select_action_cost_time"] <= df["select_action_cost_time"].mean() + threshold)
    & (
        df["select_action_cost_time_v2"]
        > df["select_action_cost_time_v2"].mean() + threshold
    )
]

# 打印分析结果
print(f"总样本数: {len(df)}")
print(f"两次都慢的案例数（比均值慢0.5s以上）: {len(both_slow)}")
print(f"两次相差0.5s以上的案例数: {len(v1_slow)}")

# 计算平均时间
print(f"\n平均耗时:")
print(f"select_action_cost_time 平均: {df['select_action_cost_time'].mean():.3f}秒")
print(
    f"select_action_cost_time_v2 平均: {df['select_action_cost_time_v2'].mean():.3f}秒"
)

# 保存详细结果到新的CSV文件
result_file = os.path.join(current_dir, "analysis_result.csv")
