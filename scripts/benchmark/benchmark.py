import csv
import os
import time
import traceback
from datetime import datetime
from typing import Literal, Optional

import pandas as pd
from livekit.agents.utils import http_context
from loguru import logger
from pydantic import BaseModel
from redis import Redis
from rich import print

from src.action.action_version.version_manager import ActionVersionManager
from src.agent_core.models.model import AgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.settings import agent_setting

os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key


class InputCase(BaseModel):
    input: str
    expects: list[str]
    parameter: str
    multiple_rounds: Literal["多轮", "单轮"]
    advanced_mode: Literal["基础任务", "高级任务"]


class OutputCase(BaseModel):
    input: str
    return_type: Optional[str] = ""
    output: str
    expects: list[str]
    success: Optional[bool]
    correct: Optional[bool]
    error_msg: Optional[str] = ""
    total_cost_time: Optional[float] = 0
    summary_cost_time: Optional[float] = 0
    load_action_cost_time: Optional[float] = 0
    load_context_cost_time: Optional[float] = 0
    embedded_cost_time: Optional[float] = 0
    retrieval_cost_time: Optional[float] = 0
    strategy_check_cost_time: Optional[float] = 0
    agent_llm_token: int = 0
    summary_llm_token: int = 0
    retry: int = 0
    model_name: str = ""
    input_tokens: int = 0
    cached_tokens: int = 0
    output_tokens: int = 0
    select_action_cost_time: float = 0
    action_flag: str = ""


class AgentBenchMark:
    def __init__(
        self,
        current_dir: str,
        case_file_path: str,
        output_file_path: str,
        limit: Optional[int] = None,
        model: str = agent_setting.plan_model,
    ):
        self.candidate_actions = ActionVersionManager().fetch_actions_by_version(
            "draft"
        )
        self.current_dir = current_dir
        self.run_id = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

        makedir = os.path.join(self.current_dir, self.run_id)
        if not os.path.exists(makedir):
            os.makedirs(makedir)
        else:
            for file in os.listdir(makedir):
                os.remove(os.path.join(makedir, file))

        self.case_file_path = case_file_path
        self.output_file_path = output_file_path

        self.limit = limit

        if model:
            agent_setting.plan_model = model

    async def run(self):
        http_context._new_session_ctx()()

        input_cases = self.load_input_case()
        print(f"Total {len(input_cases)} cases to run")
        for case in input_cases:
            print(case)
        input_cases = input_cases
        # 不支持多任务规划和多轮对话
        benchmark_result = [
            await self.run_case(case)
            for case in input_cases
            if case.multiple_rounds == "单轮" and case.advanced_mode == "基础任务"
        ]

        stats = await self.stats(benchmark_result)

        self.save_result_to_local(stats, benchmark_result)

        await self.plot_time_distribution(
            [
                result.select_action_cost_time
                for result in benchmark_result
                if result.action_flag == "llm"
            ],
            "Agent select action cost time distribution",
        )
        await self.plot_time_distribution(
            [
                result.total_cost_time
                for result in benchmark_result
                if result.action_flag == "llm"
            ],
            "Agent total cost time distribution",
        )

    async def plot_time_distribution(
        self, select_action_cost_time_list: list[float], title: str
    ):
        import matplotlib.pyplot as plt

        median = select_action_cost_time_list[len(select_action_cost_time_list) // 2]
        avg = sum(select_action_cost_time_list) / len(select_action_cost_time_list)
        max_time = max(select_action_cost_time_list)
        min_time = min(select_action_cost_time_list)
        sorted_times = sorted(select_action_cost_time_list)
        p60 = sorted_times[int(len(sorted_times) * 0.6)]
        p90 = sorted_times[int(len(sorted_times) * 0.9)]
        p70 = sorted_times[int(len(sorted_times) * 0.7)]

        print(
            f"Median: {median:.2f}, Avg: {avg:.2f}, Max: {max_time:.2f}, Min: {min_time:.2f}, P90: {p90:.2f}, P70: {p70:.2f}"
        )
        # 绘制直方图并标记统计值
        plt.hist(select_action_cost_time_list, bins=40, alpha=0.7, color="blue")
        plt.axvline(
            median,
            color="r",
            linestyle="dashed",
            linewidth=1,
            label=f"Median: {median:.2f}",
        )
        plt.axvline(
            avg, color="g", linestyle="dashed", linewidth=1, label=f"Average: {avg:.2f}"
        )
        plt.axvline(
            max_time,
            color="y",
            linestyle="dashed",
            linewidth=1,
            label=f"Max: {max_time:.2f}",
        )
        plt.axvline(
            min_time,
            color="c",
            linestyle="dashed",
            linewidth=1,
            label=f"Min: {min_time:.2f}",
        )
        plt.axvline(
            p90, color="m", linestyle="dashed", linewidth=1, label=f"P90: {p90:.2f}"
        )
        plt.axvline(
            p70, color="b", linestyle="dashed", linewidth=1, label=f"P70: {p70:.2f}"
        )
        plt.axvline(
            p60, color="y", linestyle="dashed", linewidth=1, label=f"P60: {p60:.2f}"
        )
        plt.legend()
        plt.title(title)
        plt.xlabel("Time (s)")
        plt.ylabel("Frequency")
        plt.savefig(os.path.join(self.current_dir, self.run_id, f"{title}.png"))
        plt.clf()  # 清空当前图像，防止下一个图包含上一个图的信息

    async def stats(self, benchmark_result: list[OutputCase]) -> str:
        total = len(benchmark_result)
        success_count = len([result for result in benchmark_result if result.success])
        correct_count = len([result for result in benchmark_result if result.correct])
        # 只计算retry=0的样本
        samples = [result for result in benchmark_result if result.retry == 0]
        # 去掉最高耗时和最低耗时的时间，取平均值
        avg_cost_time = (
            sum([result.total_cost_time for result in samples])
            - max([result.total_cost_time for result in samples])
            - min([result.total_cost_time for result in samples])
        ) / (len(samples) - 2)

        # 使用lib计算中位数
        median_cost_time = pd.Series(
            [result.total_cost_time for result in samples]
        ).median()

        # 计算 select_action_cost_time
        avg_select_action_cost_time = (
            sum([result.select_action_cost_time for result in samples])
            - max([result.select_action_cost_time for result in samples])
            - min([result.select_action_cost_time for result in samples])
        ) / (len(samples) - 2)

        median_select_action_cost_time = pd.Series(
            [result.select_action_cost_time for result in samples]
        ).median()

        summary_count = len(
            [result for result in benchmark_result if result.summary_cost_time > 0]
        )

        # summary_time 平均耗时
        avg_summary_time = (
            sum([result.summary_cost_time for result in benchmark_result])
            - max([result.summary_cost_time for result in benchmark_result])
            - min([result.summary_cost_time for result in benchmark_result])
        ) / (summary_count - 2)

        embedding_count = len(
            [result for result in benchmark_result if result.embedded_cost_time > 0]
        )
        # embedding_cost_time 平均耗时
        avg_embedding_cost_time = (
            sum([result.embedded_cost_time for result in benchmark_result])
            - max([result.embedded_cost_time for result in benchmark_result])
            - min([result.embedded_cost_time for result in benchmark_result])
        ) / (embedding_count - 2)

        failed = total - success_count

        avg_input_tokens = (
            sum([result.input_tokens for result in benchmark_result]) / total
        )
        avg_output_tokens = (
            sum([result.output_tokens for result in benchmark_result]) / total
        )
        avg_cached_tokens = (
            sum([result.cached_tokens for result in benchmark_result]) / total
        )

        return f"Total: {total}, Success: {success_count}, Failed: {failed}, Success Rate: {success_count / total * 100:.2f}% | Correct: {correct_count}, Correct Rate: {correct_count / total * 100:.2f}% | Avg Cost Time: {avg_cost_time:.2f}s | median_cost_time: {median_cost_time} | Avg Select Action Cost Time: {avg_select_action_cost_time:.2f}s | median_select_action_cost_time: {median_select_action_cost_time} | Avg Summary Time: {avg_summary_time:.2f}s | Avg Embedding Cost Time: {avg_embedding_cost_time:.2f}s | Avg Input Tokens: {avg_input_tokens:.2f} | Avg Output Tokens: {avg_output_tokens:.2f} | Avg Cached Tokens: {avg_cached_tokens:.2f}"

    def load_input_case(self) -> list[InputCase]:
        with open(
            os.path.join(self.current_dir, self.case_file_path), "r", encoding="utf-8"
        ) as f:
            reader = csv.reader(f)
            return [
                InputCase(
                    input=row[0],
                    expects=row[1].split(", "),
                    parameter=row[2],
                    multiple_rounds=row[3],
                    advanced_mode=row[4],
                )
                for idx, row in enumerate(reader)
                if idx != 0
            ][: (self.limit or reader.line_num) + 1]

    async def run_case(self, case: InputCase) -> OutputCase:
        start_time = time.time()
        try:
            p = AgentParameter(
                query=case.input,
                candidate_actions=self.candidate_actions,
                memory=Memory(
                    redis_client=Redis(
                        host=agent_setting.redis_host,
                        port=agent_setting.redis_port,
                        db=3,
                    ),
                    device_id="benchmark",
                ),
                robot=Robot(
                    device_id="M03SCN1A14024430N038",
                    enterprise_id="orion.ovs.entprise.1429922673",
                ),
            )

            agent_result = await SingleActionAgent.a_invoke(p)

            success = correct = False
            if agent_result.return_type == "ask":
                success, correct = True, False
            elif agent_result.return_type == "failed":
                success, correct = False, False
            else:
                success = True
                for expect in case.expects:
                    if (
                        f'="orion.agent.action.{expect}"' in agent_result.plan.content
                        and case.parameter in agent_result.plan.content
                    ):
                        correct = success = True
                        break

            print(
                f"{case.input} Pass, Success: {success}, Correct: {correct}. Debug:{agent_result.debug.model_dump_json()}"
            )

            return OutputCase(
                input=case.input,
                expects=case.expects,
                output=agent_result.plan.content,
                success=success,
                return_type=agent_result.return_type,
                correct=correct,
                total_cost_time=agent_result.debug.total_elapsed_time,
                select_action_cost_time=agent_result.debug.agent_call_cost_time,
                summary_cost_time=agent_result.debug.summary_cost_time,
                embedded_cost_time=agent_result.debug.embedded_cost_time,
                retrieval_cost_time=agent_result.debug.retrieval_cost_time,
                embedding_cost_time=agent_result.debug.embedded_cost_time,
                strategy_check_cost_time=agent_result.debug.strategy_check_cost_time,
                retry=agent_result.debug.retry,
                model_name=agent_result.debug.llm_config.llm_model_name,
                input_tokens=agent_result.debug.agent_token_cost.get(
                    "prompt_tokens", 0
                ),
                cached_tokens=(
                    agent_result.debug.agent_token_cost.get("prompt_tokens_details", {})
                    or {}
                ).get("cached_tokens", 0),
                output_tokens=agent_result.debug.agent_token_cost.get(
                    "completion_tokens", 0
                ),
                load_action_cost_time=agent_result.debug.load_action_cost_time,
                load_context_cost_time=agent_result.debug.load_context_cost_time,
                action_flag=agent_result.debug.action_flag,
            )
        except Exception as e:
            print(traceback.format_exc())
            logger.error("Error:" + str(e))
            return OutputCase(
                input=case.input,
                expects=case.expects,
                output="",
                success=False,
                correct=False,
                error_msg=str(e),
                cost_time=time.time() - start_time,
            )

    def save_result_to_local(self, stats: str, benchmark_result: list[OutputCase]):
        with open(
            os.path.join(self.current_dir, self.run_id, self.output_file_path),
            "w",
        ) as f:
            writer = csv.writer(f)
            writer.writerow([stats])
            writer.writerow(
                [
                    "input",
                    "return_type",
                    "output",
                    "expects",
                    "success",
                    "correct",
                    "action_flag",
                    "error_msg",
                    "retry",
                    "load_action_cost_time",
                    "load_context_cost_time",
                    "embedded_cost_time",
                    "retrieval_cost_time",
                    "summary_cost_time",
                    "strategy_check_cost_time",
                    "select_action_cost_time",
                    "total_cost_time",
                    "model_name",
                    "input_tokens",
                    "cached_tokens",
                    "output_tokens",
                ]
            )
            for case in benchmark_result:
                writer.writerow(
                    [
                        case.input,
                        case.return_type,
                        case.output,
                        case.expects,
                        case.success,
                        case.correct,
                        case.action_flag,
                        case.error_msg,
                        case.retry,
                        case.load_action_cost_time,
                        case.load_context_cost_time,
                        case.embedded_cost_time,
                        case.retrieval_cost_time,
                        case.summary_cost_time,
                        case.strategy_check_cost_time,
                        case.select_action_cost_time,
                        case.total_cost_time,
                        case.model_name,
                        case.input_tokens,
                        case.cached_tokens,
                        case.output_tokens,
                    ]
                )


if __name__ == "__main__":
    import asyncio

    current_dir = os.path.dirname(os.path.abspath(__file__))
    agent_benchmark = AgentBenchMark(
        current_dir=current_dir,
        case_file_path="Agent 测试集_Case.csv",
        output_file_path="agent_benchmark_result.csv",
        limit=50,
    )
    asyncio.run(agent_benchmark.run())
