# -*- coding: utf-8 -*-
import argparse
from pathlib import Path
from loguru import logger
import asyncio
import aiohttp
from src.intervene.intervene_action import (
    ActionIntervenorConfig,
    ActionIntervenor,
    ACTION_INTERVENOR_CONFIG,
)
from src.intervene.intervene_common import get_collection_name_for_intervene
from src.settings import agent_setting


"""
```bash
cd /path/to/easyNLP
source .venv/bin/activate
source .env_xxx
python -m scripts.reload_intervene_action \
    --resource_file_path "./src/intervene/resources/intervene_action/yyy.json"
```
"""


def get_args():
    def is_file_type(arg):
        if Path(arg).is_file():
            return arg
        else:
            raise FileNotFoundError(f"{arg}")

    parser = argparse.ArgumentParser(
        description="reload intervene action",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--resource_file_path",
        type=is_file_type,
        required=True,
        help=f"resource file path, e.g. src/intervene/resources/intervene_action/{agent_setting.env}.json",
    )

    parser.add_argument(
        "--embedding_api_key",
        type=str,
        default=ACTION_INTERVENOR_CONFIG.embedding_api_key,
        help="embedding api key, e.g. sk-xx, Bearer sk-xx",
    )
    parser.add_argument(
        "--embedding_model_base_url",
        type=str,
        default=ACTION_INTERVENOR_CONFIG.embedding_model_base_url,
        help="embedding model base url, e.g. https://api.openai.com/v1, http://*************:8080/embed",
    )
    parser.add_argument(
        "--embedding_model",
        type=str,
        default=ACTION_INTERVENOR_CONFIG.embedding_model,
        help="embedding model, e.g. bge, text-embedding-3-small",
    )
    parser.add_argument(
        "--embedding_dim",
        type=int,
        default=ACTION_INTERVENOR_CONFIG.embedding_dim,
        help="embedding dim, e.g. 1024",
    )
    parser.add_argument(
        "--embedding_batch_size",
        type=int,
        default=ACTION_INTERVENOR_CONFIG.embedding_batch_size,
        help="embedding batch size, e.g. 16",
    )
    parser.add_argument(
        "--embedding_enable_redis",
        type=bool,
        default=ACTION_INTERVENOR_CONFIG.embedding_enable_redis,
        help="embedding enable redis, e.g. True",
    )
    parser.add_argument(
        "--qdrant_host",
        type=str,
        default=ACTION_INTERVENOR_CONFIG.qdrant_host,
        help="qdrant host, e.g. *************",
    )
    parser.add_argument(
        "--qdrant_insert_batch_size",
        type=int,
        default=ACTION_INTERVENOR_CONFIG.qdrant_insert_batch_size,
        help="qdrant insert batch size, e.g. 16",
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()
    logger.info(f"{args}")

    resource_file_path = args.resource_file_path

    action_intervenor_config = ActionIntervenorConfig(
        embedding_api_key=args.embedding_api_key,
        embedding_model_base_url=args.embedding_model_base_url,
        embedding_model=args.embedding_model,
        embedding_dim=args.embedding_dim,
        embedding_batch_size=args.embedding_batch_size,
        embedding_enable_redis=args.embedding_enable_redis,
        qdrant_host=args.qdrant_host,
        qdrant_insert_batch_size=args.qdrant_insert_batch_size,
        webapp_resource_base_url="",
    )

    async def main():
        async with aiohttp.ClientSession() as session:
            collection_name = get_collection_name_for_intervene(
                enterprise_id="",
                embedding_model=args.embedding_model,
                embedding_dim=args.embedding_dim,
            )
            action_intervenor = ActionIntervenor(action_intervenor_config, session)
            (
                ia_list,
                ia_list_failed,
            ) = await action_intervenor.reload_intervene_action_collection(
                resource_file_path=resource_file_path
            )
            if ia_list_failed:
                logger.error(
                    "reload intervene action collection %s failed, ia_list_failed: %s"
                    % (collection_name, ia_list_failed)
                )
            else:
                logger.info(
                    "reload intervene action collection %s success, all upserted: %s"
                    % (collection_name, len(ia_list))
                )

    asyncio.run(main())
