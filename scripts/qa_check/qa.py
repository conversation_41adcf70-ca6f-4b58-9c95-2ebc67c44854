import argparse
import asyncio
import csv
import os
import time
from pathlib import Path
from typing import Tuple

import requests
from google import genai
from google.genai import types
from loguru import logger


before_content = r"""根据人设信息：اسمك هو `Xiao Bao`، أنت `A reception robot created by ORIONSTAR`، في الشركة التالية: `ORIONSTAR was founded in September 2016, it is an intelligent service robot company invested by Cheetah Mobile.`
再结合以下内容，生成一句自然的欢迎语：
当前用户外貌：黑色短发男，黑框眼镜，灰色短袖，表情严肃
历史外貌记录：

当前地点：未知
当前时间：2025-07-28 11:24:53
今天星期几：الاثنين
今天的励志格言：عندما تزهر، سيأتي النسيم بشكل طبيعي.

要求：
* 禁用"欢迎"等客套话，**字数限制20字内**
* 在打招呼时要提及用户的显著特征，但仅限于特别之处。例如，无需提及男性短发、女性长发、戴眼镜等普遍特征，但要提及染发、独特的服装等显著特点。
* 提及用户穿着时要采用含蓄优雅的表达方式，避免直接描述具体服装细节
* 你要先根据特殊事件进行打招呼，如果没有特殊事件，再结合当前状态信息进行创意的打招呼。
* 不要直接复制示例和**互动示例**的内容，要根据示例灵活创新地表达，使用多样化且贴近用户的表达方式
* 在整个欢迎语中（包括已有部分和续写部分），必须至少出现一次对用户的称呼：直接用'你'来称呼用户。你要直接续写新内容，避免重复已有内容
* 仔细对比历史外貌记录，只有在非常大的变化时（比如发型、穿着等重大改变）才礼貌地提及，避免过分关注小的变化。

下方列举的是当前用户触发的所有的特殊事件。在打招呼时，你要选择最突出的特殊事件进
特殊时间事件：中午(10:30-12:00) 状态：还没到午餐时间
你可以根据时间互动，互动示例：
- 还没到午餐时间呢，今天这气质和颜值，是不是要配点高档美食？想好吃啥了吗？
- 哎呀，这么靓仔/靓女的你，中午准备吃点啥精致美味？
- 状态这么好，光看外貌就觉得你适合吃点丰盛的！中午有计划吗？


### 示例回复：
- "不是命苦，是辛苦！天冷成这样，你还穿这么少，真拼！"
- "你的发型咋这么帅，是不是特意为今天造型加分？"
- "这件夹克这么抢眼，你是要参加时装秀吗？"
- "你这身黑色大衣配金丝眼镜，你是不是在cos王一博？"
- "这身红色连帽卫衣配黑裤，你是在cos《海贼王》路飞？"
- "这么晚还不走，一个月两百块钱玩什么命？"

### 输出语言：
阿拉伯语"""


after_content = r"""根据人设信息：اسمك هو `Xiao Bao`، أنت `A reception robot created by ORIONSTAR`، في الشركة التالية: `ORIONSTAR was founded in September 2016, it is an intelligent service robot company invested by Cheetah Mobile.`
再结合以下内容，生成一句自然的欢迎语：
当前用户外貌：黑色短发男，黑框眼镜，灰色短袖，表情严肃
历史外貌记录：

当前地点：未知
当前时间：2025-07-28 11:24:53
今天星期几：الاثنين
今天的励志格言：عندما تزهر، سيأتي النسيم بشكل طبيعي.

**输出语言：阿拉伯语**

要求：
* 禁用"欢迎"等客套话，**字数限制20字内**
* 在打招呼时要提及用户的显著特征，但仅限于特别之处。例如，无需提及男性短发、女性长发、戴眼镜等普遍特征，但要提及染发、独特的服装等显著特点。
* 提及用户穿着时要采用含蓄优雅的表达方式，避免直接描述具体服装细节
* 你要先根据特殊事件进行打招呼，如果没有特殊事件，再结合当前状态信息进行创意的打招呼。
* 不要直接复制示例和**互动示例**的内容，要根据示例灵活创新地表达，使用多样化且贴近用户的表达方式
* 在整个欢迎语中（包括已有部分和续写部分），必须至少出现一次对用户的称呼：直接用'你'来称呼用户。你要直接续写新内容，避免重复已有内容
* 仔细对比历史外貌记录，只有在非常大的变化时（比如发型、穿着等重大改变）才礼貌地提及，避免过分关注小的变化。

下方列举的是当前用户触发的所有的特殊事件。在打招呼时，你要选择最突出的特殊事件进
特殊时间事件：中午(10:30-12:00) 状态：还没到午餐时间
你可以根据时间互动，互动示例：
- 还没到午餐时间呢，今天这气质和颜值，是不是要配点高档美食？想好吃啥了吗？
- 哎呀，这么靓仔/靓女的你，中午准备吃点啥精致美味？
- 状态这么好，光看外貌就觉得你适合吃点丰盛的！中午有计划吗？


### 示例回复：
- "不是命苦，是辛苦！天冷成这样，你还穿这么少，真拼！"
- "你的发型咋这么帅，是不是特意为今天造型加分？"
- "这件夹克这么抢眼，你是要参加时装秀吗？"
- "你这身黑色大衣配金丝眼镜，你是不是在cos王一博？"
- "这身红色连帽卫衣配黑裤，你是在cos《海贼王》路飞？"
- "这么晚还不走，一个月两百块钱玩什么命？"

**输出语言：阿拉伯语**"""


async def req_gemini(c: str) -> Tuple[str, float]:
    """请求 Gemini 模型并返回结果和成本"""
    # 获取当前脚本所在目录
    current_dir = Path(__file__)
    # 构建JSON文件的完整路径
    json_file_path = (
        current_dir.parent.parent / "speedup" / "orionstar-ai-gemini-488de8d2fe77.json"
    )
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = str(json_file_path.absolute())

    MODEL_NAME = "gemini-2.5-flash-lite-preview-06-17"
    PROJECT_ID = "orionstar-ai-gemini"
    client = genai.Client(
        vertexai=True,
        project=PROJECT_ID,
        location="global",
    )
    generate_content_config = types.GenerateContentConfig(
        temperature=0.7,
        # top_p=0.95,
        max_output_tokens=65535,
        safety_settings=[
            types.SafetySetting(
                category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"
            ),
            types.SafetySetting(category="HARM_CATEGORY_HARASSMENT", threshold="OFF"),
        ],
    )
    contents = [
        types.Content(role="user", parts=[{"text": c}]),
    ]

    start_time = time.time()
    result = await client.aio.models.generate_content(
        model=MODEL_NAME,
        contents=contents,
        config=generate_content_config,
    )
    return result.text, time.time() - start_time


def req_qwen(c: str) -> Tuple[str, float]:
    """请求 Qwen 模型并返回结果和成本"""
    OPENAI_BASE_URL = (
        "https://prem.dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    )
    OPENAI_KEY = "sk-2f99e60920ee4e22bde8fda877567b99"
    MODEL_NAME = "qwen-max"
    messages = [
        {
            "role": "user",
            "content": c,
        },
    ]

    start_time = time.time()
    response = requests.post(
        OPENAI_BASE_URL,
        headers={"Authorization": f"Bearer {OPENAI_KEY}"},
        json={
            "messages": messages,
            "model": MODEL_NAME,
            "temperature": 0.7,
            "max_tokens": None,
            "chat_template_kwargs": {"enable_thinking": False},
        },
    )
    response_json = response.json()
    result_text = response_json["choices"][0]["message"]["content"]

    return result_text, time.time() - start_time


def save_results_to_csv(results: list, filename: str = "qa_results.csv"):
    """将结果保存到CSV文件"""
    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["model", "lang", "result", "cost"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for result in results:
            writer.writerow(result)

    logger.info(f"Results saved to {filename}")


async def run_test(model: str, lang: str, content: str, run_times: int) -> list:
    """运行测试并收集结果"""
    results = []

    for i in range(run_times):
        logger.info(f"Running test {i + 1}/{run_times} for {model} model")

        # 运行测试
        if model == "gemini":
            result_text, cost = await req_gemini(content)
        elif model == "qwen":
            result_text, cost = req_qwen(content)
        else:
            raise ValueError(f"Unsupported model: {model}")

        # 收集结果
        result_row = {
            "model": model,
            "lang": lang,
            "result": result_text.strip(),
            "cost": cost,
        }
        results.append(result_row)

        # 打印进度
        print(f"Test {i + 1} completed - Cost: {cost:.4f}")
        print(f"Result preview: {result_text[:100]}...")

    return results


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="QA Check Tool")
    parser.add_argument(
        "--model",
        choices=["gemini", "qwen"],
        required=True,
        help="Model to use for testing",
    )
    parser.add_argument(
        "--times", type=int, default=1, help="Number of times to run the test"
    )
    parser.add_argument(
        "--lang", type=str, default="arabic", help="Language for the test"
    )
    parser.add_argument(
        "--content",
        choices=["before", "after"],
        required=True,
        help="Content type to test (before or after)",
    )
    parser.add_argument(
        "--output", type=str, default="qa_results.csv", help="Output CSV file name"
    )

    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()

    # 选择内容
    content = before_content if args.content == "before" else after_content

    logger.info(
        f"Starting QA test with model: {args.model}, times: {args.times}, lang: {args.lang}, content: {args.content}"
    )

    # 运行测试
    results = await run_test(args.model, args.lang, content, args.times)

    # 保存结果到CSV
    save_results_to_csv(results, args.output)

    logger.info(f"Test completed. Total tests: {len(results)}")


if __name__ == "__main__":
    asyncio.run(main())
