
### install
```bash
# 在easyNLP项目的基础上安装额外的依赖
uv pip install fire
```

### run
```bash
# （如果需要）查看帮助
python run_main.py --help

# 国内
python run_main.py .env_domestic_test.sh

# 海外
python run_main.py .env_oversea_test.sh
```


# AgentOS自动化评估

模型评估可以分为如下四步：
1. prepare_dataset，准备用于评估的测试数据集，每个测试case都应包含输入和预期结果（预期结果可能不止一个）
2. get_answer，获取每个测试case的模型预测结果，以及各个子阶段的时间消耗、token消耗等信息
3. get_judgment，评估每个测试case的模型预测结果
4. compute_statistics，计算统计量：各项得分、时间消耗、token量、token成本

## 写在前面
项目目前处于多人协同持续开发中，测试数据集的迭代跟不上研发更新的节奏和线上机器人的配置变化。
当代码、数据有增删改时，如何自动化地及时地得到可信的“用户满意度得分”？
每次版本发布需要一个公认的评估报告，一个能让产品同学、研发同学、算法同学、测试同学互相之间都认可的评估报告。

既要纯模型效果又要加上工程逻辑的效果。

用户满意度得分是一个范围，我们可以给出一个用户满意度得分的下限。
我们希望尽量少人工改动测试数据集。
我们希望做数据持久化，评估过程中每个阶段的信息，都可以通过新增字段的方式追加到一个大宽表中，可以根据需要筛选不同的字段和组织字段顺序组成新的大宽表，方便阅读。
各个步骤新增了哪些字段，需要统一记录到飞书表格中（）。

我们希望既可以固化指定的字段，也可以动态加载指定的字段。


## SingleActionSelection任务
当前版本的prompt设计需要的粗粒度信息：
- action候选集合
- instructions
- 人机交互历史记录
- 机器人人设信息
- 机器人基础信息
- 机器人实时信息
- 机器人屏幕信息
- 机器人对用户的记忆
- 回复语言


### step1/4 prepare_dataset

为了方便协作，数据集统一保存成Excel文件，每个字段都是字符串类型（可以根据需要，通过参数控制，转换成dict、list等对象）。

我们希望凡是能用代码自动解析、自动生成的信息，都可以不用手动填写。

#### 测试case的来源

##### 人工创作的测试case


##### 线上拉取的测试case
我们希望直接根据sid，自动化拉取当时的相关信息，再人工标注预期结果，形成测试case。


### step2/4 get_answer （有待讨论）

非侵入式的方式是否满足需求？

要不要覆盖工程逻辑：
- 重试机制：将错误信息作为反馈，追加到messages进行重试，可配置。
- 兜底机制一：没有工具选择出message content的时候，转成say
- 兜底机制二：SAY("Sorry, I didn't catch that. Could you please repeat?"、"不好意思，刚才开小差了，您能再说一遍么？")
- 后处理机制：action_post_process

需要调用的主流程接口：
- `ActionLib().load_support_action`
- `SingleActionAgent.select_action_by_llm`


### step3/4 get_judgment
通过多种方式进行混合评估：
- 严格匹配（rule-exact-match）
- 关键词匹配（rule-keyword-match）
- 大模型评估（llm-as-a-judge）


### step4/4 compute_statistics

计算各种统计量、分布，形成评估报告
