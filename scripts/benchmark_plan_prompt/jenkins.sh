#!/bin/bash
# 设置变量
DOCKER_IMAGE="reg.ainirobot.com/speech/easy-nlp/easynlp:lw01"
CONTAINER_NAME="easynlp_container"
HOST_WORKSPACE="/workspace/source/easyNLP"
CONTAINER_WORKSPACE="/workspace/easynlp"
cd ${HOST_WORKSPACE}
git checkout ${branch}
git pull
script_path="/workspace/easynlp/scripts/benchmark_plan_prompt"

if [ "${env}" = "domestic" ]; then
    env_source="${script_path}/.env_domestic_test.sh"
elif [ "${env}" = "oversea" ]; then
    env_source="${script_path}/.env_oversea_test.sh"
else
    echo "环境设置错误，请检查后重新执行！"
    exit 1
fi


# 检查容器是否已经运行
if [ ! "$(docker ps -q -f name=$CONTAINER_NAME)" ]; then
    # 检查是否存在停止的容器
    if [ "$(docker ps -aq -f status=exited -f name=$CONTAINER_NAME)" ]; then
        # 删除停止的容器
        docker rm "$CONTAINER_NAME"
    fi
    
    # 启动新容器
    echo "Starting new container..."
    docker run -d \
        --name "$CONTAINER_NAME" \
        -v "$HOST_WORKSPACE":"$CONTAINER_WORKSPACE" \
        -w "$CONTAINER_WORKSPACE" \
        -e PYTHONPATH="$CONTAINER_WORKSPACE" \
        --restart unless-stopped \
        "$DOCKER_IMAGE" \
        tail -f /dev/null
fi

# 在容器中执行命令
echo "Executing commands in container..."
docker exec "$CONTAINER_NAME" bash -c "
    echo '=== 环境信息 ==='
    python --version
    pwd
    ls -la 
    cd /workspace/easynlp
    env
    echo '=== 执行命令 ==='
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
    pip install fire
    apt-get update
    apt-get install -y git
    cd ${script_path}
    python run_main.py ${env_source} -n ${benchmark_count} -s "${send_msg}" -o "${only_run_these_cases}"  -r ${prefix} --custom_case_file ${custom_case_file} --custom_action_version ${custom_action_version}
    
    # 保持bash会话打开
    /bin/bash
"