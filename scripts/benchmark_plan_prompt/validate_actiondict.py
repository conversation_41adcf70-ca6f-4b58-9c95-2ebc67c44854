import jsonschema


_invalid_actiondict_like_this = {"SAY_FOR_CLARIFICATION": {"text": "xxx"}}
_valid_actiondict_like_this = {"SAY_FOR_CLARIFICATION": {"request_clarify_text": "xxx"}}
_tools_like_this = [
    {
        "type": "function",
        "function": {
            "name": "say_for_clarification",
            "description": "xxx",
            "parameters": {
                "type": "object",
                "properties": {
                    "request_clarify_text": {"type": "string", "description": "xxx"}
                },
                "required": ["request_clarify_text"],
            },
        },
    }
]


def validate_actiondict_by_jsonschema(
    actiondict: dict, tools: list[dict], skip_these_action_names: list[str] = []
) -> tuple[bool, str]:
    try:
        action_name = list(actiondict.keys())[0]
        assert isinstance(action_name, str) and action_name.strip() != "", (
            f"action_name: {action_name} 不是字符串"
        )
    except Exception as e:
        error_msg = f"actiondict不合法, actiondict: {actiondict}, error: {e}"
        return False, error_msg
    # NOTE：这里统一以action_name的小写形式进行比较
    lowercase_action_name = action_name.lower()
    for action_name_to_skip in skip_these_action_names:
        lowercase_action_name_to_skip = action_name_to_skip.lower()
        if lowercase_action_name_to_skip == lowercase_action_name:
            return (
                True,
                f"跳过校验, action_name: {action_name}, action_name_to_skip: {action_name_to_skip}",
            )
    param_jsonschema = {}
    try:
        for tool in tools:
            if (
                tool.get("function", {}).get("name", "").lower()
                == lowercase_action_name
            ):
                param_jsonschema = tool.get("function", {}).get("parameters", {})
                break
        else:
            error_msg = (
                f"action_name不在tools中, action_name: {action_name}, tools: {tools}"
            )
            return False, error_msg
    except Exception as e:
        error_msg = f"不是合法的tools, tools: {tools}, error: {e}"
        return False, error_msg
    try:
        param_dict = actiondict[action_name]
        jsonschema.validate(param_dict, param_jsonschema)
    except Exception as e:
        error_msg = f"参数不合法, actiondict: {actiondict}, param_jsonschema: {param_jsonschema}, error: {e}"
        return False, error_msg
    return True, ""


def validate_actiondictlist_by_jsonschema(
    actiondictlist: list[dict],
    tools: list[dict],
    skip_these_action_names: list[str] = [],
) -> tuple[list[bool], list[str]]:
    is_valid_list, error_msg_list = [], []
    for actiondict in actiondictlist:
        is_valid, error_msg = validate_actiondict_by_jsonschema(
            actiondict, tools, skip_these_action_names
        )
        is_valid_list.append(is_valid)
        error_msg_list.append(error_msg)
    return is_valid_list, error_msg_list


if __name__ == "__main__":
    import argparse
    from tqdm import tqdm
    from loguru import logger

    from src.common.constant import KNOWLEDGE_QA_ACTION_NAME
    from scripts.benchmark_plan_prompt.dictlist_utils import DictlistUtils
    from scripts.benchmark_plan_prompt.parse_utils import parse_expected_answer

    parser = argparse.ArgumentParser(
        description="validate_actiondict",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--input_case_filename", type=str, required=True, help="输入的case文件名"
    )
    parser.add_argument(
        "--output_case_filename", type=str, required=True, help="输出的case文件名"
    )
    parser.add_argument(
        "--case_id_field", type=str, default="caseID", help="caseID字段名"
    )
    parser.add_argument(
        "--expected_answer_field",
        type=str,
        default="expected result",
        help="expected result字段名",
    )
    parser.add_argument(
        "--tools_field",
        type=str,
        default="tools",
        help="tools字段名",
    )
    parser.add_argument(
        "--output_is_valid_field",
        type=str,
        default="is_expected_answer_valid",
        help="输出的is_expected_answer_valid字段名",
    )
    parser.add_argument(
        "--output_error_msg_field",
        type=str,
        default="validate_expected_answer_error_msg",
        help="输出的validate_expected_answer_error_msg字段名",
    )
    args = parser.parse_args()
    logger.info(f"args: {args}")

    input_case_filename = args.input_case_filename
    output_case_filename = args.output_case_filename
    case_id_field = args.case_id_field
    expected_answer_field = args.expected_answer_field
    tools_field = args.tools_field
    output_is_valid_field = args.output_is_valid_field
    output_error_msg_field = args.output_error_msg_field

    dictlist = DictlistUtils.read_dictlist(input_case_filename)
    for d in tqdm(dictlist):
        caseID = d[case_id_field]
        expected_answer = parse_expected_answer(caseID, d[expected_answer_field])
        tools = DictlistUtils.custom_convert_to_object(d[tools_field], [])
        is_valid_list, error_msg_list = validate_actiondictlist_by_jsonschema(
            expected_answer, tools, [KNOWLEDGE_QA_ACTION_NAME]
        )
        d[output_is_valid_field] = is_valid_list
        d[output_error_msg_field] = error_msg_list
        for is_valid, error_msg in zip(is_valid_list, error_msg_list):
            if not is_valid:
                logger.error(f"caseID: {caseID}, error_msg: {error_msg}")
    DictlistUtils.write_dictlist(
        filename=output_case_filename, dictlist=dictlist, output_type="excel"
    )
    logger.info(f"success write to {output_case_filename}")
