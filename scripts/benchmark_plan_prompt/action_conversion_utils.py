from loguru import logger


"""
将MX_actions_str转换为action_obj_list。

注意：当前代码存在的bug，当enum_constant中存在
不成对出现的英文小括号parentheses（()）、
英文逗号comma（,）、
英文冒号colon（:）、
英文中括号square brackets（[]）
时，转换结果是错误的，需要人工标注。

bug修复思路：需要提供更多先验信息，例如：action参数的名称、是否为枚举类型、枚举值是静态的还是动态的等。

经验：字符串解析还原是欠定问题，需要提供尽可能多的先验信息作为约束条件。
"""

MX_ACTION_STR_FORMAT_DESCRIPTION = """
Format: action:usage_conditions(param1[is_required,type[enum]]:desc[**usage_restrictions**],...)
""".strip()
ALL_FIRST_CHARS_IN_ACTION_NAME = "abcdefghijklmnopqrstuvwxyz"
_ACTION_OBJ_TEMPLATE = {
    "name": "action name",
    "desc": "action description",
    "parameters": [
        {
            "name": "parameter name",
            "type": "parameter type",
            "desc": "parameter description",
            "is_required": "if this parameter is required, bool type, true or false",
            "enum_constant": ["constant1", "constant2", "constant3"],
        }
    ],
}


def parse_param_type(param_type: str) -> tuple[str, list[str]]:
    """Parse parameter type and enum values if present."""
    if "[" in param_type:
        base_type, enum_str = param_type.split("[", 1)
        enum_str = enum_str.rstrip("]")
        if enum_str.startswith("'") or enum_str.startswith('"'):
            # Handle enum values
            # Remove the outer quotes and split by comma
            enum_str = enum_str.strip("'\"")
            # Split by comma and clean up each value
            enum_values = []
            for value in enum_str.split(","):
                value = value.strip().strip("'\"")  # Remove both spaces and quotes
                if value:  # Only add non-empty values
                    enum_values.append(value)
            return base_type, enum_values
    return param_type, []


def parse_action_string(action_str: str) -> dict:
    """Parse a single action string into an Action object."""
    # Split action name and description by colon
    # Format: action:usage_conditions(param1[is_required,type[enum]]:desc[**usage_restrictions**],...)
    action_parts = action_str.split(":", 1)
    if len(action_parts) != 2:
        raise ValueError(f"Invalid action format: {action_str}")

    action_name = action_parts[0]
    rest = action_parts[1]

    # Split usage conditions and parameters
    if "(" in rest:
        usage_conditions, params_str = rest.split("(", 1)
        params_str = params_str.rstrip(")")
    else:
        usage_conditions = rest
        params_str = ""

    usage_conditions = usage_conditions

    # Parse parameters
    params = []
    if params_str:
        # Split parameters by comma, but be careful with commas inside enum values and descriptions
        param_parts: list[str] = []
        current_part: list[str] = []
        bracket_count = 0
        angle_bracket_count = 0
        in_param_desc = False

        for char in params_str:
            if char == "[":
                bracket_count += 1
            elif char == "]":
                bracket_count -= 1
            elif char == "<":
                angle_bracket_count += 1
            elif char == ">":
                angle_bracket_count -= 1
            elif char == ":":
                in_param_desc = True
            elif (
                char == ","
                and bracket_count == 0
                and angle_bracket_count == 0
                and not in_param_desc
            ):
                param_parts.append("".join(current_part))
                current_part = []
                in_param_desc = False
                continue
            current_part.append(char)

        if current_part:
            param_parts.append("".join(current_part))

        for param_part in param_parts:
            if not param_part.strip():
                continue

            # Split parameter name and description
            if ":" not in param_part:
                continue

            param_name_part, param_desc = param_part.split(":", 1)
            param_name_part = param_name_part
            param_desc = param_desc

            # Extract parameter name and type info
            if "[" not in param_name_part:
                continue

            param_name = param_name_part.split("[")[0]
            type_info = param_name_part[
                param_name_part.find("[") + 1 : param_name_part.rfind("]")
            ]

            # Parse required flag and type
            if "," not in type_info:
                continue

            is_required_str, param_type = type_info.split(",", 1)
            is_required = is_required_str.lower() == "true"
            param_type, enum_values = parse_param_type(param_type)

            param_obj = {
                "name": param_name,
                "type": param_type,
                "desc": param_desc,
                "is_required": is_required,
                "enum_constant": enum_values,
            }
            params.append(param_obj)

    return {
        "name": action_name,
        "desc": usage_conditions,
        "parameters": params,
    }


def parse_actions(input_actions_str: str) -> list[dict]:
    """Parse the entire actions string into a list of Action objects."""
    # Remove the format line and split into individual actions
    actions_str = input_actions_str.lstrip()
    if not actions_str.startswith(MX_ACTION_STR_FORMAT_DESCRIPTION):
        logger.warning(
            f"input_actions_str does not start with {MX_ACTION_STR_FORMAT_DESCRIPTION}"
        )
        return []
    actions_str = actions_str[len(MX_ACTION_STR_FORMAT_DESCRIPTION) :].lstrip()
    if actions_str[0].lower() not in ALL_FIRST_CHARS_IN_ACTION_NAME:
        logger.warning(
            f"actions_str must start with a lowercase letter, but got {actions_str[0]}"
        )
        return []
    line_list = actions_str.split("\n")
    actions = []
    prev_action_lines: list[str] = []
    for line in line_list:
        if not line:
            continue
        if line[0].lower() not in ALL_FIRST_CHARS_IN_ACTION_NAME:
            prev_action_lines.append(line)
        else:
            if len(prev_action_lines) == 0:
                prev_action_lines = [line]
            else:
                action_str = "\n".join(prev_action_lines)
                try:
                    action = parse_action_string(action_str)
                    actions.append(action)
                except Exception as e:
                    logger.error(f"Error parsing action: {action_str}")
                    logger.error(f"Error: {str(e)}")
                prev_action_lines = [line]
    # Handle any remaining action
    if prev_action_lines:
        action_str = "\n".join(prev_action_lines)
        try:
            action = parse_action_string(action_str)
            actions.append(action)
        except Exception as e:
            logger.error(f"Error parsing action: {action_str}")
            logger.error(f"Error: {str(e)}")

    return actions


def format_actions(actions: list[dict]) -> str:
    if not actions:
        return ""
    action_str_list = [MX_ACTION_STR_FORMAT_DESCRIPTION + "\n"]
    for action in actions:
        param_strs = []
        for param in action["parameters"]:
            enum_str = (
                f"[{', '.join([f"'{v}'" for v in param['enum_constant']])}]"
                if param["enum_constant"]
                else ""
            )
            param_strs.append(
                f"{param['name']}[{str(param['is_required'])},{param['type']}{enum_str}]:{param['desc']}"
            )

        params_str = f"({','.join(param_strs)})" if param_strs else "()"
        action_str_list.append(f"{action['name']}:{action['desc']}{params_str}")

    return "\n".join(action_str_list)


def get_action_by_name(*, actions: list[dict], action_name: str) -> dict:
    for action in actions:
        if action["name"] == action_name:
            return action
    return {}


def get_param_by_name(
    *, actions: list[dict], action_name: str, param_name: str
) -> dict:
    action = get_action_by_name(actions=actions, action_name=action_name)
    if not action:
        return {}
    for param in action.get("parameters", {}):
        if param.get("name", "") == param_name:
            return param
    return {}


if __name__ == "__main__":
    import json
    from datetime import datetime

    experiment_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    MX_actions_str_original = """
Format: action:usage_conditions(param1[is_required,type[enum]]:desc[**usage_restrictions**],...)

SET_VOLUME:调整音量。调整的幅度是10或30，根据用户语气选择(volume_level[True,int]:The volume level to be set **(取值范围: 0 - 100)**)
SAY:说话。例如回答机器人状态信息。(text[True,string]:Speak in the first person, words limit 30)
CANCEL:取消当前动作。例如跳舞、点头、导航等()
EXIT:退出当前应用()
BACK:返回上一级()
NEXT:下一步()
CONFIRM:用户确认操作()
COMMON_REPLAY:Triggered when there is a paused video on the screen information.()
MULTIMEDIA_PLAY:Triggered when there is a video to be played on the screen information.()
COMMON_PAUSE:暂停导览讲解过程；媒体资源播放时，暂停播放。()
ADJUST_SPEED:调整<移动速度>。(adjusted_speed[True,float]:新的移动速度。 **(取值范围: 0.1 - 1.2)**)
KNOWLEDGE_QA:查询知识回答用户的问题，包含「百科知识」、「公司产品及人员信息」等(question[True,string]:The question to ask.)
NAVIGATE_REC_START:室内点位导航意图。只能带用户去下面提供的位置，不支持去室外位置。如果用户没有明确指定目的地，则返回最多4个可用的导航点位列表，并按照相似性排序，最靠近的点位在前面(destinations[False,String array['老板办公区', '测试点', '厕所', '南极洲会议室', '休息区', '电梯1', '晓曦工位', '拉斯维加斯会议室', '彦礼工位', '回充点', '接待点', '会议室', '测试部']]:The user wants to navigate to a ranked list of recommended destinations (maximum 4 destinations),guide_text[True,string]:A natural guiding response that varies based on the number of destinations:
    - For single destination: Direct guidance to that location
    - For multiple destinations: Present options and ask for user's choice
    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in')

CRUISE:巡视，巡逻。()
NOT_MOVE:停止走路。()
COME_FAR:让路()
OUTDOOR_NAVIGATE_START:室外路线导航。(origin[True,string]:The starting point provided by the user. If not provided, the default is the `<current location of the robot>`.,destination[True,string]:The destination to navigate to,mode[True,enum['driving', 'transit']]:The mode to navigate, select from enumerated values,region[True,string]:The region to navigate, default is `<current city of the robot>`)
TURN_DIRECTION:身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。(direction[True,enum['left', 'right']]:The direction to turn, default is left,angle[False,int]:The value of the rotation angle **(最大值: 3600)**,turns[False,float]:Number of turns **(最大值: 10)**)
HEAD_NOD:点头、鞠躬()
START_DANCE:跳舞、播放音乐。()
REGISTER:注册。包含姓名和人脸注册(nick_name[False,string]:The nickname can be extracted from the user query, MUST BE REAL NICKNAME,welcome_message[False,string]:message to greet the user. MUST NOT CONTAIN USER'S NICKNAME)
MOVE_DIRECTION:前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。(direction[True,enum['forward', 'backward']]:The direction to move in, select from enumerated values,moving_distance[True,float]:单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。 **(取值范围: 0.1 - 5)**)
INTERVIEW_START:访客接待流程，支持面试、会议签到、访客登记。()
WEATHER_GET:查询「明天及未来10天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！(area_level[True,enum['province', 'city', 'area']]:city 对应的区域等级,city[True,string]:行政区域名称，默认查询`<所在城市>`。)
CALENDAR:日历功能，包含日期或节假日的查询，注意：无法查询天气(user_question[True,string]:question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.)
GUIDE_INTRODUCTION:导览功能，带领用户参观，在没有明确的参观目的下使用。()
OPEN_WEB_URL:模拟浏览器访问网址，例如股票、门票、新闻等。搜索引擎推荐使用「百度」搜索引擎；公司官网等指定网站直接通过对应网址打开。(url[True,HttpUrl]:google or a specified URL. Must be a legitimate https or http link.)
FLIGHT_TICKET_QUERY:飞机票查询(departure_city_code[True,string]:The IATA code of the departure city,arrival_city_code[True,string]:The IATA code of the arrival city,departure_date[True,string]:The departure date, for example: 2024-01-01)
TRAIN_TICKET_QUERY:火车票查询(departure_city[True,string]:城市名称，例如`北京`不要带`市`字,arrival_city[True,string]:城市名称，例如`北京`不要带`市`字,departure_date[True,string]:出发日期，例如`2024-01-01`)
CONFIGURE_WELCOME_MESSAGE:设置机器人看到用户后的说的话(nick_name[False,string]:The nickname can be extracted from the user query, MUST BE REAL NICKNAME,welcome_message[False,string]:message to greet the user. MUST NOT CONTAIN USER'S NICKNAME)
GENERATE_MESSAGE:文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等(goal[True,string]:user instructions)
SEND_MESSAGE:通过「飞书」给某人发送消息。适用于找人、留言等场景(recipient_name[True,string]:Absolutely real names, not personal pronouns,message_content[True,string]:Send the message content. The content should be polished first, not too direct or stiff,message_type[True,enum['urgent', 'normal']]:Specifies the type of message to be sent.)
RECOMMEND:搜索推荐酒店、路线（到地铁站、景点等）、景点、购物等。(place[True,string]:Name of the place of actual meaning,near_by[True,string]:The starting point provided by the user. If not provided, the default is the current location.)
FACE_RECOGNITION:人脸识别。()
GUIDE_ROUTE_SELECTION_FROM_MAP:用户参观游览。带领用户去下面提供的多个地点。(points[True,String array]:顺序选择地点，决不能捏造不存在的点位。)
INTERVIEW_START_PHOTO:合影()
WEATHER_GET_REALTIME:查询「当天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！(city[True,string]:行政区域名称，默认查询`<所在城市>`。)
CLICK:模拟网页点击，根据“应用页面HTML”信息，理解用户需求，帮助用户点击网页上的元素。如果存在可点击元素，则优先考虑使用，而不是生成 URL。(element_tag[True,enum['手写', '拼音', '关闭', '百度首页', '设置', '登录', '抗击肺炎', '新闻', 'hao123', '地图', '视频', '贴吧', '学术', '登录', '设置', '更多产品', '图片', '资讯', '视频', '笔记', '地图', '贴吧', '文库', '更多', '登录百度帐号', '《保障服务协议》', '免保范围', '申请保障', '加入商家保障', '\ue687\ue613', '\ue619换一换', '总书记强调教育科技人才一起抓', '李家超回应李嘉诚拟出售港口', '最敢生的省份也开始“抢人”了', '这些最新部署与你息息相关', '张碧晨方：“偷生孩子逼上位”不实', '做全世界的生意 用全世界的贝宝', '315红黑榜也被打假了', '#李嘉诚卖港口给美国公司该挨骂吗#', '56岁翁虹联合国演讲 真正的大女主', '女生攒百万被DeepSeek辣评无效存钱', 'A股收评：三大指数集体收涨', '妻子跳楼致残被丈夫送回娘家', '中国第一“睡省”有些尴尬', '45岁以上不能当外卖员？谣言', '代购坐高铁给县城贵妇送上万元蛋糕', '保时捷利润暴跌搬出油车救命', '樊振东决定不参加澳门世界杯', '一家人住进新房3天孩子生病住院', '美的集团副总裁赵磊被内部通报批评', '美国娱乐公司宣称正改编哪吒', '中国“七巨头”超越美国“七雄”', '李兆基罕见采访视频：孙仔越多越好', '香港地产大亨李兆基去世 享年97岁', '女子160cm110斤被确诊隐形肥胖', '黄子韬吸完烟把烟头递给助理', '赵丽颖对都敏俊人设的评价成真了', '张晚意穿短袖短裤拍摄冻到搓手', '华莱士或被低价低利模式反噬', '女子发现男友家很多公章吵架后报警', '男子退休前花10元买彩票中5000万', '90后AI创业者为什么多是广东籍', '金赛纶遗属欲以谋杀罪起诉李镇浩', '▶想在此推广您的产品吗?', '咨询热线：400-800-8888', 'e.baidu.com', '猎户星空-智能语音服务机器人', '官方', '智能AI内核主动问候迎宾,路线导览指引,无需培训访客问题快速解答.实现企业前台,展厅展馆,商超,酒店,政务,教育,医疗,大健康等行业场景广泛应用.', '猎户星空 2025-03\ue62b广告', '猎户星空官网-服务机器人-送餐机器人-讲解机器人', '官方', 'AI机器人', '大模型', '招商代理', '解决方案', '\ue631', '北京猎户星空科技有限公司(公司) - 百度百科', '详情', '\ue734', '公司创始人', '使命与愿景', '全链条AI技术', '核心产品', '百度百科', '擎朗智能机器人-畅销60国-城市合伙人火热招募中', '擎朗智能机器人,畅销超60个国家,遍布600+全国城市,日活跃机器总数50000+现推出代理商扶持政策,1v1带你了解市场,开发客户,提供客户资源,销售扶持', '擎朗送餐机器人 去看看', '擎朗送餐机器人', '多点送餐 去看看', '多点送餐', '无接触配送 去看看', '无接触配送', '节省人力成本 去看看', '节省人力成本', '上海擎朗智能科技有限公司 2025-03\ue62b广告', '北京猎户星空科技有限公司 - 企业信息', '联系', '基本信息', '认领企业', '企业年报', '变更记录', '企业股东', '更多 >', '【企业动态】：2024-12-24 新增 法院公告', '【企业动态】：2024-12-24 新增 法院公告', '【企业动态】：2024-12-24 新增 法院公告', '爱企查', '猎户星空机器人官网', '猎户星空科技有限公司', '猎户星空股票代码', '猎户星空算大公司吗', '猎户星空属于什么档次', '猎户星空面试', '猎户星座图片', '猎户星空值得去吗', '普渡智能机器人降本增效,给您规划线路高端服务体验!', '最近1小时前有人咨询相关问题', '[高端品牌]普渡酒楼/政务单位/商超/养老/办公楼/工厂可使用!普渡小可爱降本增效普渡机器人已经销往全球60多个国家和地区,[工业][服务][清洁]机器人提升生产生活效率!', '北京普渡光年机器人科技 2025-03\ue62b广告', '北京猎户星空科技有限公司', '猎户星空', '猎户星空 - 百度图片', '免费修图', '变清晰', '变清晰', '变清晰', '变清晰', '变清晰', '变清晰', '变清晰', '变清晰', '查看全部439张图片', '猎户星空取得悬挂装置和具有该悬挂装置的底盘和机器人专利', '金融界', '\ue62b', '🌟每个人都能找到的七个星座🌟阿阿蒙双林王', '冬季观星指南:猎户座与星座大三角西街小霸王赤域', '🌟 猎户座:夜空中的明星 🌟偏爱辣椒红的赛车火焰', '秋季星空中的福禄寿三星,你找到了吗?遗世独立的犬南橘', '查看更多笔记 >', '猎户座(天文学星座) - 百度百科', '历史文化', '观测', '恒星及系外行星', '深空天体', '流星雨', '更多 >', '百度百科', '北京猎户星空科技有限公司 - 爱企查', '爱企查', '夜空中的王者——猎户座:参宿七星的璀璨秘密奥秘', '兔斯基聊科学', '猎户星空机器人官网', '北京猎户星空科技有限公司', '猎户星空算大公司吗', '猎户星空股票代码', '猎户星空面试', '去猎户星空上班咋样', '漂在江湖', '猎户星空上市了吗', '猎户星空属于什么档次', '猎户星空值得去吗', '2', '3', '4', '5', '6', '7', '8', '9', '10', '下一页 >', '帮助', '举报', '用户反馈', '企业推广']]:可点击元素的标签)
ANSWER_QUESTION_FROM_VISION:通过机器人的摄像头观察并回答问题，包括外貌识别、周边环境描述、物体识别等(question[True,string]:根据图片要回答的问题，必须总结为第一人称的问题，生成的问题要简单)
OPEN_WEB_URL_DEFINED:模拟浏览器访问网址，例如股票、门票、新闻等。搜索引擎推荐使用「百度」搜索引擎；公司官网等指定网站直接通过对应网址打开。*只要*预定义问题列表中存在与用户当前问题*语义相同*的问题，则选择此action，因为可以找到用户设置好的URL直接打开。(predefined_question[True,enum['打开地图工具', '我看看建图', '帮我查下快递', '我的快递到哪了', '寄快递', '我要邮寄快递', '我要查快递', '查快递', '打开设置功能', '网购', '打开淘宝', '我要看热门微博', '打开微博', '我要看微博热搜', '今天有哪些热门微博', '打开微博热搜']]:list of predefined question.)
""".strip()
    with open("a1.txt", "w") as f:
        f.write(MX_actions_str_original)
    # Parse the actions
    actions = parse_actions(MX_actions_str_original)
    with open("a2.json", "w") as f:
        json.dump(actions, f, ensure_ascii=False, indent=2)
    MX_actions_str_reconstructed = format_actions(actions)
    with open("a2.txt", "w") as f:
        f.write(MX_actions_str_reconstructed)
