from typing import Literal

from loguru import logger


# 按人民币计算，单位：元/token，需要根据实际价格更新
TOKEN_PRICE = {
    "qwen-max": {
        "input_cached_token": 0.0000048,
        "input_not_cached_token": 0.000012,
        "output_token": 0.000048,
    },
    "gpt-4o": {
        "input_cached_token": 0.00000875,
        "input_not_cached_token": 0.0000175,
        "output_token": 0.00007,
    },
}


class TokenPriceUtils:
    @staticmethod
    def calc_token_money(
        *,
        api_base_url: str = "",  # 暂时没用
        model: str = "",
        token_type: Literal[
            "input_cached_token", "input_not_cached_token", "output_token"
        ] = "output_token",
        token_count: int = 0,
    ) -> float:
        try:
            model = model.strip().lower()
            if model in ["qwen-max"]:
                return TOKEN_PRICE[model][token_type] * token_count
            elif model.startswith("gpt-4o"):
                return TOKEN_PRICE["gpt-4o"][token_type] * token_count
            else:
                raise ValueError(f"model {model} not supported")
        except Exception as e:
            logger.error(f"get_token_money error: {e}")
            return 0.0
