import jsonschema
from loguru import logger
from typing import Literal
from copy import deepcopy


# `expected_answers_in_jsonschema`字段的jsonschema
JSONSCHEMA_OF_EXPECTED_ANSWERS_IN_JSONSCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "judge_type": {"type": "string", "enum": ["rule-based-judge"]},
            "name": {
                "type": "string",
                "pattern": "^[A-Z_]+$",
                "description": "expected action name",
            },
            "type": {"type": "string", "enum": ["object"]},
            "properties": {"type": "object"},
            "required": {"type": "array", "items": {"type": "string"}},
        },
        "oneOf": [
            {"required": ["judge_type", "name", "type", "properties", "required"]},
            {"required": ["judge_type", "name", "type", "properties", "oneOf"]},
        ],
    },
    "minItems": 1,
    "uniqueItems": True,
}
_expected_answers_in_jsonschema_like_this = [
    {
        "judge_type": "rule-based-judge",
        "name": "SEND_MESSAGE",
        "type": "object",
        "properties": {
            "recipient_name": {"type": "string", "enum": ["梁伟"]},
            "message_content": {
                "type": "string",
                "pattern": "^(?=.*面试官)(?=.*半小时).*$",
            },
            "message_type": {"type": "string", "enum": ["urgent"]},
        },
        "required": ["recipient_name", "message_content", "message_type"],
    }
]
_actual_answer_like_this = {
    "SEND_MESSAGE": {
        "message_content": "梁伟，有人在等你面试已经半小时了，请问面试官什么时候能见到他们？",
        "message_type": "urgent",
        "recipient_name": "梁伟",
        "this_is_redundant_key": "this_is_redundant_value",
    }
}
# 参数互斥的情况，比如angle和turns不能同时出现
_parameter_mutually_exclusive_jsonschema_like_this = [
    {
        "judge_type": "rule-based-judge",
        "name": "ROTATE",
        "type": "object",
        "properties": {
            "direction": {"type": "string", "enum": ["left", "right"]},
            "angle": {"type": "number", "enum": [180]},
            "turns": {"type": "number", "enum": [0.5]},
        },
        "oneOf": [
            {"required": ["direction", "angle"]},
            {"required": ["direction", "turns"]},
        ],
    }
]
_invalid_actual_answer_like_this = {
    "ROTATE": {
        "direction": "right",
        "turns": 0.5,
        "angle": 180,
    }
}


def judge_actual_answer_by_one_jsonschema(
    actual_answer: dict,
    schema: dict,
) -> tuple[bool, bool, str]:
    is_action_correct, is_parameter_correct, error_msg = False, False, ""
    if not actual_answer:
        return is_action_correct, is_parameter_correct, "actual_answer is empty"
    try:
        actual_action_name = list(actual_answer.keys())[0]
        actual_param_dict = actual_answer[actual_action_name]
        expected_action_name = schema["name"]
        if expected_action_name == actual_action_name:
            is_action_correct = True
            try:
                jsonschema.validate(actual_param_dict, schema)
                is_parameter_correct = True
            except Exception as e:
                error_msg = f"{e}, actual_answer: {actual_answer}, schema: {schema}"
        else:
            error_msg = f"action name not match, actual_action_name: {actual_action_name}, expected_action_name: {expected_action_name}"
    except Exception as e:
        error_msg = f"{e}, actual_answer: {actual_answer}, schema: {schema}"
    return is_action_correct, is_parameter_correct, error_msg


def judge_one_case_by_jsonschema(
    d: dict,
    *,
    inplace: bool = False,
    caseID_field: str = "caseID",
    expected_answers_in_jsonschema_field: str = "expected_answers_in_jsonschema",
    actual_answer_field: str = "actual_answer",
    output_score_field: str = "Score",
    output_format_score_field: str = "format_score",
    output_action_score_field: str = "action_score",
    output_parameter_score_field: str = "parameter_score",
    output_redundancy_score_field: str = "redundancy_score",
    output_invalid_reason_field: str = "invalid_reason",
    output_judged_by_field: str = "judged_by",
) -> dict:
    if not inplace:
        d = deepcopy(d)
    caseID = d[caseID_field]
    try:
        expected_answers_in_jsonschema = d[expected_answers_in_jsonschema_field]
        jsonschema.validate(
            expected_answers_in_jsonschema, JSONSCHEMA_OF_EXPECTED_ANSWERS_IN_JSONSCHEMA
        )
    except Exception as e:
        logger.error(
            f"预期答案校验失败，caseID: {caseID}, expected_answers_in_jsonschema: {expected_answers_in_jsonschema}, error: {e}"
        )
        return d
    try:
        actual_answer = d[actual_answer_field]
        assert isinstance(actual_answer, dict), "实际答案不是dict类型"
        actual_action_name = list(actual_answer.keys())[0]
        assert isinstance(actual_action_name, str), "实际答案的action name不是str类型"
        actual_param_dict = actual_answer[actual_action_name]
        assert isinstance(actual_param_dict, dict), "实际答案的参数不是dict类型"
    except Exception as e:
        logger.error(
            f"实际答案校验失败，caseID: {caseID}, actual_answer: {actual_answer}, error: {e}"
        )
        d[output_score_field] = 0.0
        d[output_format_score_field] = 0.0
        d[output_action_score_field] = 0.0
        d[output_parameter_score_field] = 0.0
        d[output_redundancy_score_field] = 0.0
        d[output_invalid_reason_field] = ["格式错误"]
        d[output_judged_by_field] = "rule-based-judge"
        return d

    format_score, redundancy_score = (
        1.0,
        1.0,
    )  # NOTE：如果有工程逻辑兜底，格式一定对，参数的冗余一定不会出现
    action_score, parameter_score = 0.0, 0.0
    invalid_reason: Literal["", "action错误", "参数错误"] = ""
    error_msg_list = []
    for schema in expected_answers_in_jsonschema:
        is_action_correct, is_parameter_correct, error_msg = (
            judge_actual_answer_by_one_jsonschema(actual_answer, schema)
        )
        error_msg_list.append(error_msg)
        # NOTE：如果action错了，参数对错没有意义
        if is_action_correct and is_parameter_correct:
            action_score, parameter_score, invalid_reason = 1.0, 1.0, ""
            break
        elif is_action_correct:
            action_score, invalid_reason = 1.0, "参数错误"
        else:
            invalid_reason = "action错误"
    if error_msg_list and all(error_msg_list):
        logger.error(f"caseID: {caseID}, error_msg_list: {error_msg_list}")

    d[output_score_field] = (
        format_score + action_score + parameter_score + redundancy_score
    )
    d[output_format_score_field] = format_score
    d[output_action_score_field] = action_score
    d[output_parameter_score_field] = parameter_score
    d[output_redundancy_score_field] = redundancy_score
    d[output_invalid_reason_field] = [invalid_reason]
    d[output_judged_by_field] = "rule-based-judge"
    return d
