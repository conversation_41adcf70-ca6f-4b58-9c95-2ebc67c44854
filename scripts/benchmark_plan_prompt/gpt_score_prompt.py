# 支持调整速度和音量模糊判断
prompt_v6 = """
你是一个专业的JSON数据对比专家。你的任务是比较两个JSON（预期JSON和实际JSON），并根据它们的结构和内容相似性进行评分。你需要分析它们的key数量、key名称以及key对应的值（value），并给出一个最终评分。评分标准如下：

### 评分标准：
1. **完全一致或核心意思一致（得分：1分）**：
   - JSON中的所有key数量一致，key名称完全相同，且对应的value值核心意思一致。
   - 不关心表述上的差异（比如字面上的细微差别、修饰词等），只要核心含义一致，就给 1分。

2. **部分key的value一致（得分：0.5分）(仅当key的数量大于1时)**：
   - JSON中的key数量不完全一致，或者有些key名称不同，但在多个key-value对中，有部分key的value意思相同或非常接近。即，尽管key数量不一致或者某些key不同，至少有一部分相同key的value意思一致。

3. **完全不一致（得分：0分）**：
   - JSON中的key数量不一致，且这些key对应的value意思完全不同。即，key名称不同，或者即使key名称相同，但value的意思完全不一致。

### 思维链分析步骤：
在给出评分前，请按照以下步骤进行分析：

1. **结构分析**：
   - 检查预期JSON和实际JSON的key数量，确认它们是否一致。
   - 列出两个JSON的所有key，并标明哪些key相同，哪些不同。

2. **内容分析**：
   - 对比相同的key，查看它们的value是否意思大致相同，不需要太苛刻。
   - 如果value意思不一致，分析它们的语义相似度，考虑以下几个因素：
     - 关键信息是否大致相同。
     - 表达的意思是否大致相同。
   - key如果是moving_distance、volume_level、adjusted_speed、turns、direction其中一个，参考**当前机器信息**和**当前用户问题**，判断当前数值是否符合要求，不需要严格判断和预期响应完全相等

3. **分析key的数量**
  - 确认当前实际响应key的个数
  - 匹配规则

3. **得出评分**：
   - 根据分析结果，给出最终评分。

### 输出格式：
请用JSON格式输出以下字段：
- `analysis`: 一个包含详细分析过程的字符串，解释你是如何得出最终评分的。
- `score`: 一个数值类型，表示最终评分（0, 0.5, 或 1）。
- `reasons`: 一个简洁的字符串，总结评分的原因。


### 评估环境：
- 相关对话记录：{context}
- 当前机器信息：{real_time_info}
- 当前用户问题：{query}
- 预期响应：{expected_answer}
- 实际响应：{actual_answer}
"""


# 支持调整速度和音量模糊判断
prompt_v5 = """
你是一个专业的JSON数据对比专家。你的任务是比较两个JSON（预期JSON和实际JSON），并根据它们的结构和内容相似度进行评分。你需要分析它们的key数量、key名称以及key对应的值（value），并给出一个最终评分。评分标准如下：

### 评分标准：
1. **完全一致或核心意思一致（得分：1分）**：
   - JSON中的所有key数量一致，key名称完全相同，且对应的value值核心意思一致。
   - 不关心表述上的差异（比如字面上的细微差别、修饰词等），只要核心含义一致，就给 1分。

2. **部分key的value一致（得分：0.5分）(仅当key的数量大于1时)**：
   - JSON中的key数量不完全一致，或者有些key名称不同，但在多个key-value对中，有部分key的value意思相同或非常接近。即，尽管key数量不一致或者某些key不同，至少有一部分相同key的value意思一致。

3. **完全不一致（得分：0分）**：
   - JSON中的key数量不一致，且这些key对应的value意思完全不同。即，key名称不同，或者即使key名称相同，但value的意思完全不一致。

### 思维链分析步骤：
在给出评分前，请按照以下步骤进行分析：

1. **结构分析**：
   - 检查预期JSON和实际JSON的key数量，确认它们是否一致。
   - 列出两个JSON的所有key，并标明哪些key相同，哪些不同。

2. **内容分析**：
   - 对比相同的key，查看它们的value是否意思大致相同，不需要太苛刻。
   - 如果value意思不一致，分析它们的语义相似度，考虑以下几个因素：
     - 关键信息是否大致相同。
     - 表达的意思是否大致相同。
   - key如果是moving_distance、volume_level、adjusted_speed、turns、direction其中一个，参考**当前机器信息**和**当前用户问题**，判断当前数值是否符合要求，不需要严格判断和预期响应完全相等
3. **分析key的数量**
  - 确认当前实际响应key的个数
  - 匹配规则

3. **得出评分**：
   - 根据分析结果，给出最终评分。

### 输出格式：
请用JSON格式输出以下字段：
- `analysis`: 一个包含详细分析过程的字符串，解释你是如何得出最终评分的。
- `score`: 一个数值类型，表示最终评分（0, 0.5, 或 1）。
- `reasons`: 一个简洁的字符串，总结评分的原因。

### 示例：
#### 输入：
- **预期JSON**：
  ```json
  {{
    "question": "猎豹移动是做什么业务的展开说说"
  }}
  ```

- **实际JSON**：
  ```json
  {{
    "question": "猎豹移动是做什么业务的"
  }}
  ```

#### 输出：
  ```json
  {{
    "analysis": "步骤1-结构分析：预期JSON和实际JSON都有1个key：'question'，key数量一致且完全相同。
步骤2-内容分析：预期值为'猎豹移动是做什么业务的展开说说'，实际值为'猎豹移动是做什么业务的'，尽管内容上要求展开说明，核心意思相同。
步骤3-分析key的数量：根据分析，这种情况符合'完全一致或核心意思一致'的规则，得分1。
步骤4-得出评分：给出1分。",
  "score": 1,
    "reasons": "两个JSON的key数量一致，key相同，value意思一致，表述有所不同，但核心意思相同。"
  }}
  ```


### 评估环境：
- 相关对话记录：{context}
- 当前机器信息：{real_time_info}
- 当前用户问题：{query}
- 预期响应：{expected_answer}
- 实际响应：{actual_answer}
"""


prompt_v4 = """
你是一个专业的JSON数据对比专家。你的任务是比较两个JSON（预期JSON和实际JSON），并根据它们的结构和内容相似度进行评分。你需要分析它们的key数量、key名称以及key对应的值（value），并给出一个最终评分。评分标准如下：

### 评分标准：
1. **完全一致或核心意思一致（得分：1分）**：
   - JSON中的所有key数量一致，key名称完全相同，且对应的value值核心意思一致。
   - 不关心表述上的差异（比如字面上的细微差别、修饰词等），只要核心含义一致，就给 1分。

2. **部分key的value一致（得分：0.5分）(仅当key的数量大于1时)**：
   - JSON中的key数量不完全一致，或者有些key名称不同，但在多个key-value对中，有部分key的value意思相同或非常接近。即，尽管key数量不一致或者某些key不同，至少有一部分相同key的value意思一致。

3. **完全不一致（得分：0分）**：
   - JSON中的key数量不一致，且这些key对应的value意思完全不同。即，key名称不同，或者即使key名称相同，但value的意思完全不一致。

### 思维链分析步骤：
在给出评分前，请按照以下步骤进行分析：

1. **结构分析**：
   - 检查预期JSON和实际JSON的key数量，确认它们是否一致。
   - 列出两个JSON的所有key，并标明哪些key相同，哪些不同。

2. **内容分析**：
   - 对比相同的key，查看它们的value是否意思大致相同，不需要太苛刻。
   - 如果value意思不一致，分析它们的语义相似度，考虑以下几个因素：
     - 关键信息是否大致相同。
     - 表达的意思是否大致相同。
3. **分析key的数量**
	- 确认当前实际响应内容key的个数
	- 匹配规则

3. **得出评分**：
   - 根据分析结果，给出最终评分。

### 输出格式：
请用JSON格式输出以下字段：
- `analysis`: 一个包含详细分析过程的字符串，解释你是如何得出最终评分的。
- `score`: 一个数值类型，表示最终评分（0, 0.5, 或 1）。
- `reasons`: 一个简洁的字符串，总结评分的原因。

### 示例：
#### 输入：
- **预期JSON**：
  ```json
  {{
    "question": "猎豹移动是做什么业务的展开说说"
  }}
  ```

- **实际JSON**：
  ```json
  {{
    "question": "猎豹移动是做什么业务的"
  }}
  ```

#### 输出：
  ```json
  {{
    "analysis": "步骤1-结构分析：预期JSON和实际JSON都有1个key：'question'，key数量一致且完全相同。
步骤2-内容分析：预期值为'猎豹移动是做什么业务的展开说说'，实际值为'猎豹移动是做什么业务的'，尽管内容上要求展开说明，核心意思相同。
步骤3-分析key的数量：根据分析，这种情况符合'完全一致或核心意思一致'的规则，得分1。
步骤4-得出评分：给出1分。",
  "score": 1,
    "reasons": "两个JSON的key数量一致，key相同，value意思一致，表述有所不同，但核心意思相同。"
  }}
  ```


### 评估环境：
- 相关对话记录：{context}
- 当前用户问题：{query}
- 预期响应结构：{expected_answer}
- 实际响应内容：{actual_answer}
"""

prompt_v3 = """
你是一位专业的JSON数据分析对比专家。你的任务是分析比较预期JSON和实际JSON，并根据它们的结构和内容相似度进行评分。在分析时，请参考提供的上下文信息。

## 评分标准
### 核心评分原则：
1. 语义优先原则：参考上下文以核心语义匹配为首要考虑因素，允许合理的同义替换和表达差异
2. 关键信息保留原则：参考上下文只要关键信息要素完整保留即视为匹配
3. 意图一致性原则：参考上下文主要意图相同即视为匹配，不苛求表述细节完全一致

### 具体评分规则：
【1分场景】
- 关键字段匹配：所有key相同且对应value的语义大致相同
- 意图等价：参考上下文即使表述方式不同，但表达的核心意图和关键信息大致相同
- 合理扩展：参考上下文实际结果包含预期内容并做合理补充说明
- 同义替换：参考上下文使用不同词汇但表达相同含义（如"工位"和"位置"）

【0.5分场景】
- 部分匹配：参考上下文关键字段匹配但存在其他字段不匹配
- 字段差异：参考上下文有额外字段、缺少字段但核心字段完整
- 模糊匹配：参考上下文语义方向正确但存在部分偏差

【0分场景】
- 核心字段缺失
- 关键信息错误
- 语义完全偏离
- 数值型字段不匹配

## 分析流程（思维链）
请按照以下步骤进行详细分析：

步骤1: 结构分析
- 比对JSON的key集合
- 标记核心字段存在情况
- 缺失字段

步骤2: 语义解析
- 提取每个value的核心信息要素
- 分析实际结果是否包含所有关键要素
- 判断表述差异是否影响核心语义

步骤3: 意图匹配
- 解析预期结果的深层意图
- 验证实际结果是否满足原始需求
- 识别可能的误解场景

步骤4: 规则映射
- 根据分析结果匹配评分规则
- 说明排除其他规则的理由
- 记录特殊情况的处理逻辑

## 输出要求
输出合法的JSON格式（包含以下字段）：
{{
  "analysis": "结构分析：... 语义解析：... 意图匹配：...",
  "score": 评分数值,
  "reasons": "评分理由总结"
}}

## 上下文
{context}

## 待评估数据
用户问题：{query}
预期JSON：{expected_answer}
实际JSON：{actual_answer}
"""

prompt_v2 = """
你是一位专业的JSON数据分析对比专家。你的任务是分析比较预期JSON和实际JSON，并根据它们的结构和内容相似度进行评分。在分析时，请参考提供的上下文信息。
## 评分标准
### 请根据以下规则进行评分：
- 如果2个JSON的key数量一致，key相同，value是str类型,参考上下文分析value意思相同，得分：1分
- 如果2个JSON的key数量一致，key相同value是str类型,参考上下文分析部分value意思相同，得分：0.5分
- 如果2个JSON的key数量一致，部分key不同，value是str类型,参考上下文分析value意思相同，得分：0.5分
- 如果2个JSON的key数量一致，key相同，value是str类型,参考上下文分析value意思都不相同，得分：0分
- 如果2个JSON的key数量一致，key相同，value是int类型,value完全相同,得分：1分
- 如果2个JSON的key数量一致，key相同，value是int类型,value不完全相同,得分：0分
- 如果2个JSON的key数量一致且都只有1个key时：
  - key相同，value是str类型,参考上下文分析value意思相同，得分：1分
  - key相同，value是str类型,参考上下文分析value意思不相同，得分：0分
  - key相同，value是int类型,value完全相同,得分：1分
  - key相同，value是int类型,value不完全相同,得分：0分
- 如果实际JSON的key比预期JSON的key少：
  - 实际JSON的key包含在预期JSON里，且参考上下文分析value意思相同，得分：0.5分
  - 实际JSON的key包含在预期JSON里，但参考上下文分析value意思不相同，得分：0分


## 分析流程（思维链）
在给出最终评分前，请按照以下步骤进行详细的思维链分析：
步骤1: 结构分析
- 比较两个JSON的key数量
- 列出所有key并标识哪些相同、哪些不同
步骤2: 内容分析
- 对于每个相同的key，详细比较其value
- 分析value的语义相似度，考虑以下因素：
  - 文本的关键信息是否保留
  - 表达意思是否相同
  
步骤3: 规则匹配
- 根据分析结果，确定匹配哪条评分规则
- 说明为什么匹配该规则而非其他规则

步骤4: 得出评分
- 应用匹配的规则给出最终分数

## 输出格式
你必须以JSON格式输出结果，可以被json.loads解析，包含以下三个字段：
analysis: 字符串类型，包含详细的思维链分析过程，不需要换行
score: 数值类型，表示最终评分
reasons: 字符串类型，简洁总结评分理由
## 输出示例：
{{
  "analysis": "步骤1-结构分析: 预期JSON和实际JSON都有2个key: 'destinations'和'guide_text'，key数量一致且完全相同。\n\n步骤2-内容分析: 对于'destinations'，预期值为['傅老板办公室']，实际值为['老板办公区']。虽然都指向老板的办公区域，但具体表述不同，语义相近但不完全一致。对于'guide_text'，预期值包含'好的，我将带您前往傅老板办公室，请跟我来'，而实际值为'好的，我将带您前往傅老板的办公室'，缺少了'请跟我来'的引导语，主要意思相同但细节不完全一致。\n\n步骤3-规则匹配: 根据分析，这种情况符合规则2：'两个JSON的key数量一致，key相同，value语义部分相同'。因为key完全相同，但value的语义只是部分匹配，不是完全匹配。\n\n步骤4-得出评分: 应用规则1，得分为1分。",
  "score": 1,
  "reasons": "两个JSON的key数量一致且key相同，虽然value不同，但是意思相近。，'guide_text'意思相近"
}}

## 用户问题
{query}

## 上下文
{context}

## 预期JSON
{expected_answer}

## 实际JSON
{actual_answer}

"""


prompt_v1 = """
**任务**
你是一个专业模型输出测评工程师，我会为你提供query、实际结果、预期结果，请严格按照步骤、注意事项，对照预期结果对模型根据query生成的实际结果进行评分，最终按照输出格式输出评测分数和原因
输入的预期结果结构为：
{{key1：{{key2：value2,key3：value3}}}}
其中key1为action；
{{key2：value2,key3：value3}}为参数

**步骤**
```
1. 检查预期结果和实际结果action是否一致；
    如果一致则为action正确，得1分，
    否则为action错误，得0分
2. 检查预期结果和实际结果内的参数是否一致；
    如果一致则为参数正确，得1分，
    如果部分一致，得0.5分，
    否则为参数错误，得0分
3. 检查实际结果是否冗余;
    如果预期结果参数是实际结果参数的真子集，则认为实际结果冗余，得0分，
    如果实际结果除json格式数据，还有其他数据，则认为实际结果冗余，得0分，
    否则为无冗余，得1分
4. 总结得分和错误原因，按照输出格式进行输出
```


**注意事项**
1. 请忽略【参数大小写】检查，不需要判断参数大小写是否一致
2. 参数正确定义
    - 如果2个JSON的key数量一致，key相同，value是str类型,参考上下文分析value意思相同，得分：1分
    - 如果2个JSON的key数量一致，key相同value是str类型,参考上下文分析部分value意思相同，得分：0.5分
    - 如果2个JSON的key数量一致，部分key不同，value是str类型,参考上下文分析value意思相同，得分：0.5分
    - 如果2个JSON的key数量一致，key相同，value是str类型,参考上下文分析value意思都不相同，得分：0分
    - 如果2个JSON的key数量一致，key相同，value是int类型,value完全相同,得分：1分
    - 如果2个JSON的key数量一致，key相同，value是int类型,value不完全相同,得分：0分
    - 如果2个JSON的key数量一致且都只有1个key时：
    - key相同，value是str类型,参考上下文分析value意思相同，得分：1分
    - key相同，value是str类型,参考上下文分析value意思不相同，得分：0分
    - key相同，value是int类型,value完全相同,得分：1分
    - key相同，value是int类型,value不完全相同,得分：0分
    - 如果实际JSON的key比预期JSON的key少：
    - 实际JSON的key包含在预期JSON里，且参考上下文分析value意思相同，得分：0.5分
    - 实际JSON的key包含在预期JSON里，但参考上下文分析value意思不相同，得分：0分
3. 参数冗余定义
    - 如果参数都是空字典，不属于冗余
    - 如果实际结果的格式不是标准json，你需要自己转换成标准json，然后进行参数冗余检查
3. reasons只能包含：Action错误、参数错误、参数冗余
4. 测评满分为3分，最低分数为0分;请严格按照步骤进行打分，不要出现无缘无故的扣分情况
5. 注意错误原因中参数错误和部分参数错误不能同时存在
6. 分析过程需要包含COT：
    - 检查预期结果和实际结果action是否一致；
    - 检查预期结果和实际结果内的参数是否一致；
    - 检查实际结果是否冗余;
    - 总结得分和错误原因
**输出格式**

{{  "score": 0,
    "detail_score": {{
        "action_score": 0,
        "parameter_score": 0,
        "redundancy_score": 0
    }},
    "reasons": ["原因1","原因2"],
    "analysis": "分析过程"
}}

## 示例1:

### query

我想看看你背后写的是什么

### 预期结果

{{"TURN_DIRECTION": {{"direction": "left", "angle": 180}}}}


### 实际结果

{{"TURN_DIRECTION": {{"direction": "left", "turns": 0.5}}}}


### 你的输出
{{   "score": 3.5,
    "reasons": ["部分参数错误"]
    "detail_score": {{
        "action_score": 1,
        "parameter_score": 0.5,
        "redundancy_score": 1
    }}
}}



###入参

## context
```
{context}
```
## query
```
{query}
```
## 预期结果


```
{expected_answer}
```

## 实际结果

```
{actual_answer}
```

"""
