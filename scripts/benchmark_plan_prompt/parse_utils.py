import ast
import json
from typing import Any

from loguru import logger


def parse_expected_answer(caseID: str, text: str) -> list[dict]:
    try:
        expected_answer = json.loads(text)
    except Exception as e:
        logger.warning(
            f"caseID:{caseID}. 预期结果被json.loads解析失败，{text} error: {e}"
        )
        try:
            expected_answer = ast.literal_eval(text)
        except Exception as e:
            logger.error(
                f"caseID:{caseID}. 预期结果被ast.literal_eval解析失败，{text} error: {e}"
            )
            return []
    if isinstance(expected_answer, dict):
        expected_answer = [expected_answer]
    if not isinstance(expected_answer, list):
        logger.error(f"caseID:{caseID}. 预期结果不合法，{text}")
        return []
    return expected_answer


def safe_convert_to_object(
    inputs: Any, value_if_empty: Any, value_if_error: Any
) -> Any:
    if inputs in ["", None, dict(), list(), set(), tuple()]:
        return value_if_empty
    if isinstance(inputs, str):
        try:
            return json.loads(inputs)
        except Exception:
            try:
                return ast.literal_eval(inputs)
            except Exception:
                return value_if_error
    return inputs
