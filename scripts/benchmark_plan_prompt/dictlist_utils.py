import ast
import json
import openpyxl  # noqa: F401
import pandas as pd
from copy import deepcopy
from typing import Literal, Any
from pydantic import BaseModel
from datetime import datetime
from loguru import logger


class DictlistUtils:
    """
    这个模块是希望可以取jsonl和excel的最大公约数，方便在jsonl文件和excel文件之间转换
    jsonl 文件是文本文件，直接读取，然后转换为 list[dict]
    excel 文件是二进制文件，使用 pandas 库读取，然后转换为 list[dict]
    注意，JSON没有定义NaN、Inf、datetime等，而pandas有定义
    """

    @staticmethod
    def custom_process_df(
        df: pd.DataFrame,
        *,
        field_names_to_dropna_row: list[str] = [],
        fillna_with_emptystr: bool = True,
    ) -> pd.DataFrame:
        if field_names_to_dropna_row:
            df = df.dropna(subset=field_names_to_dropna_row)
        if fillna_with_emptystr:
            df = df.fillna("")
        return df

    @staticmethod
    def safe_convert_to_object(
        inputs: Any, value_if_empty: Any, value_if_error: Any
    ) -> Any:
        if inputs in ["", None, dict(), list(), set(), tuple()]:
            return value_if_empty
        if isinstance(inputs, str):
            try:
                return json.loads(inputs)
            except Exception:
                try:
                    return ast.literal_eval(inputs)
                except Exception:
                    return value_if_error
        return inputs

    @staticmethod
    def read_dictlist(
        filename: str,
        *,
        field_names_to_dropna_row: list[str] = [],
        fillna_with_emptystr: bool = True,
    ) -> list[dict]:
        if filename.endswith(".jsonl"):
            dictlist = []
            with open(filename, "r", encoding="utf-8") as fin:
                for line in fin:
                    try:
                        d = json.loads(line)
                    except Exception as e:
                        logger.error(f"Error in loading: {line}")
                        raise e
                    dictlist.append(d)
            return dictlist
        elif filename.endswith(".xlsx"):
            df = pd.read_excel(filename)
            df = DictlistUtils.custom_process_df(
                df,
                field_names_to_dropna_row=field_names_to_dropna_row,
                fillna_with_emptystr=fillna_with_emptystr,
            )
            return df.to_dict(orient="records")
        elif filename.endswith(".csv"):
            df = pd.read_csv(filename)
            df = DictlistUtils.custom_process_df(
                df,
                field_names_to_dropna_row=field_names_to_dropna_row,
                fillna_with_emptystr=fillna_with_emptystr,
            )
            return df.to_dict(orient="records")
        else:
            raise ValueError(f"Unsupported file format: {filename}")

    @staticmethod
    def convert_datetime_to_str(d: dict) -> dict:
        d_copy = deepcopy(d)
        for k, v in d_copy.items():
            if isinstance(v, datetime):
                d_copy[k] = v.strftime("%Y/%m/%d %H:%M:%S")
        return d_copy

    @staticmethod
    def write_dictlist_to_jsonl(
        *,
        filename: str,
        dictlist: list[dict],
        need_convert_elements_to_str: bool = True,
    ):
        assert filename.endswith(".jsonl"), (
            f"filename must end with .jsonl, but got {filename}"
        )
        if need_convert_elements_to_str:
            dictlist = DictlistUtils.convert_elements_to_str(dictlist)
        try:
            with open(filename, "w", encoding="utf-8") as fout:
                for d in dictlist:
                    line = json.dumps(d, ensure_ascii=False, separators=(",", ":"))
                    fout.write(line + "\n")
        except Exception as e:
            logger.error(f"Error in writing {filename}: {e}")
            raise e

    @staticmethod
    def convert_elements_to_str(
        dictlist: list[dict], *, exclude_types: tuple[type, ...] = (int, float, bool)
    ) -> list[dict]:
        dictlist_copy = deepcopy(dictlist)

        def json_dumps(v: Any) -> str:
            return json.dumps(v, ensure_ascii=False, indent=2)

        def to_str(v: Any) -> str:
            try:
                if isinstance(v, (list, dict)):
                    result = json_dumps(v)
                elif isinstance(v, (set, tuple)):
                    result = json_dumps(list(v))
                elif isinstance(v, BaseModel):
                    result = json_dumps(json.loads(v.model_dump_json()))
                elif isinstance(v, datetime):
                    result = v.strftime(
                        "%Y/%m/%d %H:%M:%S"
                    )  # e.g. "2025/4/17 18:30:00"
                else:
                    result = str(v)
            except Exception as e:
                logger.error(f"Error in converting {v} to str: {e}")
                raise e
            return result

        for d in dictlist_copy:
            for k, v in d.items():
                if isinstance(v, exclude_types):
                    continue
                d[k] = to_str(v)
        return dictlist_copy

    @staticmethod
    def convert_str_to_obj(s: str):
        if s.strip() == "":
            return None
        try:
            obj = json.loads(s)
        except Exception as e:
            logger.warning(f"str 被json.loads解析失败，{s} error: {e}")
            try:
                obj = ast.literal_eval(s)
            except Exception as e:
                logger.error(f"str 被ast.literal_eval解析失败，{s} error: {e}")
                raise e
        return obj

    @staticmethod
    def convert_elements_to_obj(
        dictlist: list[dict], *, field_names: list[str], inplace: bool
    ) -> list[dict]:
        if not field_names:
            return dictlist
        if inplace:
            dictlist_copy = dictlist
        else:
            dictlist_copy = deepcopy(dictlist)
        for i, d in enumerate(dictlist_copy):
            for field_name in field_names:
                if field_name not in d:
                    logger.warning(
                        f"record_index:{i}, field_name “{field_name}” not in dict, set to None"
                    )
                    d[field_name] = None
                else:
                    v = d[field_name]
                    if not isinstance(v, str):
                        continue
                    try:
                        d[field_name] = DictlistUtils.convert_str_to_obj(v)
                    except Exception as e:
                        logger.error(
                            f"record_index:{i}, field_name “{field_name}” error in converting value to obj: {e}"
                        )
                        raise e
        return dictlist_copy

    @staticmethod
    def write_dictlist_to_excel(
        *,
        filename: str,
        dictlist: list[dict],
        need_convert_elements_to_str: bool = True,
    ):
        if not filename.endswith(".xlsx"):
            filename = f"{filename}.xlsx"
        if need_convert_elements_to_str:
            dictlist = DictlistUtils.convert_elements_to_str(dictlist)
        df = pd.DataFrame(dictlist)
        df.to_excel(filename, index=False)

    @staticmethod
    def write_dictlist(
        *,
        filename: str,
        dictlist: list[dict],
        need_convert_elements_to_str: bool = True,
        output_type: Literal["both", "jsonl", "excel"] = "both",
    ):
        if output_type == "both":
            jsonl_filename = f"{filename}.jsonl"
            excel_filename = f"{filename}.xlsx"
            DictlistUtils.write_dictlist_to_jsonl(
                filename=jsonl_filename,
                dictlist=dictlist,
                need_convert_elements_to_str=need_convert_elements_to_str,
            )
            DictlistUtils.write_dictlist_to_excel(
                filename=excel_filename,
                dictlist=dictlist,
                need_convert_elements_to_str=need_convert_elements_to_str,
            )
        elif output_type == "jsonl":
            jsonl_filename = (
                filename if filename.endswith(".jsonl") else f"{filename}.jsonl"
            )
            DictlistUtils.write_dictlist_to_jsonl(
                filename=jsonl_filename,
                dictlist=dictlist,
                need_convert_elements_to_str=need_convert_elements_to_str,
            )
        elif output_type == "excel":
            excel_filename = (
                filename if filename.endswith(".xlsx") else f"{filename}.xlsx"
            )
            DictlistUtils.write_dictlist_to_excel(
                filename=excel_filename,
                dictlist=dictlist,
                need_convert_elements_to_str=need_convert_elements_to_str,
            )

    @staticmethod
    def keep_desired_fields(
        dictlist: list[dict], desired_fields: list[str], default_value: str = ""
    ) -> list[dict]:
        dictlist2 = []
        missing_fields_info: dict[str, list[int]] = {}
        for i, d in enumerate(dictlist):
            d2 = {}
            for field in desired_fields:
                if field not in d:
                    d2[field] = default_value
                    missing_fields_info.setdefault(field, []).append(i)
                else:
                    d2[field] = d[field]
            dictlist2.append(d2)
        if missing_fields_info:
            logger.warning(f"missing_fields_info: {missing_fields_info}")
        return dictlist2


if __name__ == "__main__":
    from rich import print

    original_dictlist: list[dict] = [
        {
            "str": "str",
            "bool_True": True,
            "bool_False": False,
            "int_1": 1,
            "float_1.0": 1.0,
            "float_1.23": 1.23,
            "list_1_2_3": [1, 2, 3],
            "dict_a_1_b_2": {"a": 1, "b": 2},
        },
    ]
    print(f"original_dictlist: {original_dictlist}")
    jsonl_filename = ".dictlist.jsonl"
    excel_filename = ".dictlist.xlsx"
    DictlistUtils.write_dictlist(
        filename=jsonl_filename, dictlist=original_dictlist, output_type="jsonl"
    )
    DictlistUtils.write_dictlist(
        filename=excel_filename, dictlist=original_dictlist, output_type="excel"
    )

    dictlist_from_jsonl = DictlistUtils.read_dictlist(filename=jsonl_filename)
    print(f"dictlist_from_jsonl: {dictlist_from_jsonl}")
    dictlist_from_excel = DictlistUtils.read_dictlist(filename=excel_filename)
    print(f"dictlist_from_excel: {dictlist_from_excel}")
