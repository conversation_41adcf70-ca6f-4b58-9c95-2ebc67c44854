export LIVEKIT_URL=wss://speech-rtc-oversea-test.orionstar.com
export LIVEKIT_API_KEY=orionstar_ec2_oversea_test
export LIVEKIT_API_SECRET=pdlsNyKuXhLD1xA9Webi2rFt7GPcBZ8m
export ELEVEN_API_KEY=sk-e2230f3f05f6128b3b7a09849c30c9fc16410b4133e066de
export DEEPGRAM_API_KEY=****************************************
export OPENAI_API_KEY=***************************************************
export ENV=oversea_test
export REGION_VERSION=oversea
export embedding_model=text-embedding-3-small
export EMBEDDING_MODEL_BASE_URL=https://api.openai.com/v1
export EMBEDDING_MODEL_API_KEY=***************************************************

export PLAN_MODEL_BASE_URL=https://api.openai.com/v1
export PLAN_MODEL_API_KEY=***************************************************
export PLAN_MODEL=gpt-4o-2024-11-20

export SUMMARY_MODEL_BASE_URL=https://api.openai.com/v1
export SUMMARY_MODEL_API_KEY=***************************************************
export SUMMARY_MODEL=gemini-2.5-flash-lite-preview-06-17


export GENERATE_TEXT_MODEL_BASE_URL=https://api.openai.com/v1
export GENERATE_TEXT_MODEL_API_KEY=***************************************************
export GENERATE_TEXT_MODEL=gemini-2.5-flash-lite-preview-06-17

export EMBEDDING_MODEL_BASE_URL=https://api.openai.com/v1
export EMBEDDING_MODEL_API_KEY=***************************************************
export EMBEDDING_MODEL=text-embedding-3-small
export EMBEDDING_DIM=1024
export MAX_BATCH_SIZE=32

export TURBO_PLAN_MODEL_BASE_URL=https://api.openai.com/v1
export TURBO_PLAN_MODEL_API_KEY=***************************************************
export TURBO_PLAN_MODEL=gpt-4o-2024-11-20

export FULL_POWER_PLAN_MODEL_BASE_URL=https://api.openai.com/v1
export FULL_POWER_PLAN_MODEL_API_KEY=***************************************************
export FULL_POWER_PLAN_MODEL=gpt-4o-2024-11-20

export ASR_WS_URL=ws://speech-test.orionstar.com/ws/streaming-asr
export TTS_BASE_URL=https://speech-global.orionstar.com

export VISION_MODEL=gpt-4o-2024-11-20
export VISION_BASE_URL=https://api.openai.com/v1
export VISION_API_KEY=***************************************************

export REDIS_DB=2
export FACE_ID_CACHE_DB=3
export TEST_CASE_REDIS_DB=4
export ROBOT_SUPPORT_MAP_POINTS_URL=http://dev-voice-lite.ainirobot.com/speech-manager/v1/grpc/resource/dict
export LOG_LEVEL=DEBUG

export ENABLE_SINGLE_ACTION_FEWSHOT="False"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_API_KEY="***************************************************"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL_BASE_URL="https://api.openai.com/v1"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL="text-embedding-3-small"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_DIM="1024"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_BATCH_SIZE="16"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_ENABLE_REDIS="False"
export SINGLE_ACTION_FEWSHOT_QDRANT_HOST="*************"
export SINGLE_ACTION_FEWSHOT_QDRANT_INSERT_BATCH_SIZE="16"

 export AGENT_CORE_BASE_URL="http://aos-core-test.orionstar.com/"
# export AGENT_CORE_BASE_URL="http://agent-core:8000/"
# export AGENT_CORE_PATH="/api/v1/agent/run"
export AGENT_CORE_TIMEOUT="300"

export KNOWLEDGE_SCORE_THRESHOLD=0.824


# 日服测试
#export ASSISTANT_REDIS_HOST="redis-nlp-robot-tokyo.pf8krd.ng.0001.apne1.cache.amazonaws.com"
#export ASSISTANT_REDIS_PORT="6379"
#export ASSISTANT_REDIS_DB="14"
#
#export AGENT_CORE_BASE_URL="http://agent-core-tokyo:8000/"
