import ast
import argparse
import asyncio
import datetime
import json
import os
import sys
import traceback
import re
from typing import List, Callable, Literal
import uuid

import pandas as pd
from livekit.agents.utils import http_context
from loguru import logger
from redis import Redis
from rich import print
from tqdm import tqdm
from pathlib import Path
from copy import deepcopy

from src.rag.rag_manager import RAGManager, RAGProviderType
from scripts.benchmark_plan_prompt.dictlist_utils import DictlistUtils
from scripts.benchmark_plan_prompt.send_msg import send_card_message
from scripts.benchmark_plan_prompt.token_price_utils import TokenPriceUtils
from scripts.benchmark_plan_prompt.parse_utils import (
    parse_expected_answer,
    safe_convert_to_object,
)
from scripts.benchmark_plan_prompt.validate_actiondict import (
    validate_actiondictlist_by_jsonschema,
)
from scripts.benchmark_plan_prompt.rule_based_judge import judge_one_case_by_jsonschema
from src.action.action_version.version_manager import ActionVersionManager
from src.action.actions import ActionLib
from src.agent_core.models.model import AgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.settings import agent_setting
from src.utils.llm import LLMConfig, LLMManager, parse_output_to_dict
from src.common.constant import Area, KNOWLEDGE_QA_ACTION_NAME
from src.session_manager.chat_context import (
    ChatAction,
    ChatEvent,
    ChatMessage,
    ChatResult,
    ChatParameter,
)


sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)


DEVICE_ID = "M03SCN2B19025008383B"
REGION_TO_ENTERPRISE_ID = {
    Area.domestic: "orion.ovs.entprise.9945420568",
    # Area.overseas: "orion.ovs.entprise.2130335619",  # 日本
    Area.overseas: "orion.ovs.entprise.2465488082",
}
REGION_TO_ROBOT_LANGUAGE_CODE = {
    Area.domestic: "zh_CN",
    Area.overseas: "en_US",
}
REGION_TO_DEFAULT_CASE_FILENAME_STEM = {
    Area.domestic: "domestic_cases",
    Area.overseas: "oversea_cases",
}
REGION_TO_DEFAULT_ACTION_VERSION = {
    Area.domestic: "draft",
    Area.overseas: "oversea_draft",
}
DESIRED_FIELDS_TO_SEND_FEISHU = [
    "caseID",
    "效果周会讨论结果",
    "query",
    "expected result",
    "expected_answers_in_jsonschema",
    "actual_answer",
    "judged_by",
    "Score",
    "invalid_reason",
    "action_score",
    "format_score",
    "parameter_score",
    "redundancy_score",
    "# ROBOT BASIC INFORMATION",
    "# ROBOT REAL-TIME INFORMATION",
    "# Screen Info",
    "# User's Relevant information",
    "# CHAT CONVERSATION",
    "conversation_progress",
    "expected_action_names",
    "overrided_enums",
    "llm_api_return_type",
    "confirmed_action",
    "agent_messages",
    "tools",
    "retry",
    "evlue_prompt",
    "evlue_raw_llm_output",
    "evlue_reason",
    "actions_for_llm",
    "action_selected_by_SingleActionAgent",
    "actions_from_load",
    "agent_debug",
    "frozen_ActionDictList",
    "is_expected_answers_valid",
    "validate_expected_answers_error_msg",
    "call_agent_core_cost_time",
    "inner_agent_core_cost_time",
    "run_agent_response_total_elapsed_time",
    "query_rewrite_time",
    "knowledge_search_time",
    "knowledge_qa_total_time",
    "query_id",
    "llm_api_total_timecost",
    "llm_api_response",
]


def format_chat_conversation_strategy(chat_conversation):
    """
    <Robot> Triggered Event [desc='App Switch: 通用场景 → 推销场景']
    <Robot> Executed Action. ENTER_PROMOTE_APP() Result:{}
    <Robot> said '嗨， 很高兴见到您！我是您的专属销售助手小宝。想了解一下您是打算在展厅、企业还是工厂使用我们的机器人呢？这样我能为您推荐最适合的产品，让您的业务更上一层楼！'
    <User> said '有比较成功的案例吗可以看一看'
    <Robot> said '当然可以，这里有一个成功的企业展厅案例供您参考。'
    <User> said '好了暂停吧不想看了'
    <Robot> Executed Action. COMMON_PAUSE() Result:{}
    <User> JUST SAID '嗯你接着播吧'
    ==>to
    Robot: Triggered Event [desc='App Switch: 通用场景 → 推销场景']
    Robot: Executed Action. enter_promote_app() Result:{}
    Robot: 嗨， 很高兴见到您！我是您的专属销售助手小宝。想了解一下您是打算在展厅、企业还是工厂使用我们的机器人呢？这样我能为您推荐最适合的产品，让您的业务更上一层楼！
    User: 有比较成功的案例吗可以看一看
    Robot: 当然可以，这里有一个成功的企业展厅案例供您参考。
    User: 好了暂停吧不想看了
    Robot: Executed Action. common_pause() Result:{}
    User: 嗯你接着播吧

    <User> said '杭州的客户到了今天要商量业务合作的事情还要我下周去出差你先帮我找个会议室'
    <Robot> Triggered Event [desc='App Switch: 问路引领场景 → 通用场景']
    <Robot> Triggered Event [desc='App Switch: 通用场景 → 问路引领场景']
    <Robot> Triggered Event [desc='App Switch: 问路引领场景 → 通用场景']
    <Robot> said '这里有几间会议室可以选择，请问您想去哪一间？'
    <User> JUST SAID '那我下周三去他们那儿需要准备雨具吗'
    ==>to
    User: 杭州的客户到了今天要商量业务合作的事情还要我下周去出差你先帮我找个会议室
    Robot: Triggered Event [desc='App Switch: 问路引领场景 → 通用场景']
    Robot: Triggered Event [desc='App Switch: 通用场景 → 问路引领场景']
    Robot: Triggered Event [desc='App Switch: 问路引领场景 → 通用场景']
    Robot: 这里有几间会议室可以选择，请问您想去哪一间？
    User: 那我下周三去他们那儿需要准备雨具吗
    """
    # 删除包含Triggered Event的行，需要用正则
    # chat_conversation = re.sub(r"<Robot> Triggered Event \[desc='.*?'\]\n?", "", chat_conversation, flags=re.MULTILINE)
    # 转换Triggered Event行
    chat_conversation = re.sub(
        r"<Robot> Triggered Event \[desc='(.*?)'\]\n?",
        r"Robot: Triggered Event [desc='\1']\n",
        chat_conversation,
        flags=re.MULTILINE,
    )
    chat_conversation = re.sub(
        r"<Robot> Executed Action\. ([A-Z_]+)\(",
        lambda m: f"Robot: Executed Action. {m.group(1).lower()}(",
        chat_conversation,
    )
    chat_conversation = re.sub(r"<Robot> said '", "Robot: ", chat_conversation)
    chat_conversation = re.sub(r"<Robot> SAID '", "Robot: ", chat_conversation)
    chat_conversation = re.sub(r"<User> said '", "User: ", chat_conversation)
    chat_conversation = re.sub(r"<User> SAID '", "User: ", chat_conversation)
    chat_conversation = re.sub(r"<User> JUST SAID '", "User: ", chat_conversation)
    chat_conversation = chat_conversation.replace("'\n", "\n").strip("'")
    return chat_conversation


def format_conversation_progress_to_chat_messages(
    conversation_progress: str,
) -> List[ChatMessage]:
    """
    将对话进度转换为ChatMessage
    ## chat conversation
    Robot: Triggered Event [desc='App Switch: 通用场景 → 推销场景']
    Robot: Executed Action. enter_promote_app() Result:{}
    Robot: 嗨， 很高兴见到您！我是您的专属销售助手小宝。想了解一下您是打算在展厅、企业还是工厂使用我们的机器人呢？这样我能为您推荐最适合的产品，让您的业务更上一层楼！
    User: 有比较成功的案例吗可以看一看
    Robot: 当然可以，这里有一个成功的企业展厅案例供您参考。
    User: 好了暂停吧不想看了
    Robot: Executed Action. common_pause() Result:{}
    User: 嗯你接着播吧
    """
    logger.debug(
        f"format_conversation_progress_to_chat_messages: {conversation_progress}"
    )
    lines = [line.strip() for line in conversation_progress.split("\n")]
    chat_messages = []
    # 转换包含Triggered Event的行，需要用正则
    for line in lines:
        logger.debug(f"line: {line}")
        if "Triggered Event" in line:
            event = ChatEvent(desc=line.split("[desc='")[1].split("']")[0])

            if "Robot:" in line:
                msg = ChatMessage(role="assistant", event=event)
            else:
                msg = ChatMessage(role="user", content=line)
            chat_messages.append(msg)
        elif "Executed Action" in line:
            if "Robot:" in line:
                # Robot: Executed Action. weather_get(area_level=city,city=杭州) Result:{'answer_text': '明天杭州晴天，气温4到13度，建议带件薄...'}
                action_name = line.split("Executed Action. ")[1].split("(")[0].strip()
                action_parameters = (
                    line.split(f"{action_name}(")[1].split(")")[0].strip()
                )
                parameters = []
                if action_parameters:
                    # 先用=分割
                    parts = action_parameters.split("=")
                    param_name = parts[0].strip()
                    rest = "=".join(parts[1:]).strip()  # 重新拼接防止value里有=
                    # 尝试用第一个逗号分割
                    if "," in rest:
                        # 多参数情况
                        param_values = rest.split(",")
                        for i, val in enumerate(param_values):
                            if i == 0:
                                parameters.append(
                                    ChatParameter(name=param_name, value=val.strip())
                                )
                            else:
                                # 后续参数直接用=分割
                                if "=" in val:
                                    k, v = val.split("=", 1)
                                    parameters.append(
                                        ChatParameter(name=k.strip(), value=v.strip())
                                    )
                    else:
                        # 单参数情况
                        parameters.append(ChatParameter(name=param_name, value=rest))

                # line_like_this = 'Robot: Executed Action. [orion.app.promote.get_product_details], Parameters:(production=豹小秘2) Result:{\'product\': \'{"id": 1, "image_url\', \'product_detail\': \'[{"title": "功能介绍", "\', \'text_content\': \'{}\'}'
                _special_substr = "Result:{"
                _special_substr_start_index = line.rfind(_special_substr)
                results = []
                if _special_substr_start_index != -1:
                    raw_result = line[
                        _special_substr_start_index + len(_special_substr) - 1 :
                    ]  # 从Result:开始截取
                    logger.debug(f"raw_result: {raw_result}")
                    for ret_name, ret_value in ast.literal_eval(raw_result).items():
                        logger.debug(
                            f"ret_name: {ret_name}, ret_value: {ret_value} type: {type(ret_value)}"
                        )
                        results.append(
                            ChatResult(name=ret_name, value=ret_value, type="string")
                        )  # TODO: better type. 构造历史记录的result时，目前没用到这个字段

                msg = ChatMessage(
                    role="assistant",
                    action=ChatAction(
                        name=action_name, parameters=parameters, result=results
                    ),
                )
                chat_messages.append(msg)
        else:  # <User> said '杭州的客户到了今天要商量业务合作的事情还要我下周去出差你先帮我找个会议室'
            logger.debug(f"Dialogue line: {line}")
            separator = ":"

            role, content = line.split(separator, 1)
            if role.strip().lower() == "user":
                role = "user"
            else:
                role = "assistant"

            content = content.strip()
            chat_messages.append(ChatMessage(role=role, content=content))

    logger.info(f"chat_messages: {chat_messages}")
    return chat_messages


def prepare_dataset(filename: str, output_filename_prefix: str) -> list[dict]:
    logger.info(f"prepare_dataset start, filename: {filename}")
    _dictlist = DictlistUtils.read_dictlist(
        filename=filename, field_names_to_dropna_row=["caseID"]
    )
    dictlist = []
    # TODO(<EMAIL>) 等评测集更新后，删除这部分逻辑，临时根据推销Action处理agent_id
    PROMOTE_ACTIONS = [
        "ENTER_PROMOTE_APP",
        "EXIT_PROMOTE_APP",
        "SHOW_HOME_PAGE",
        "GET_PRODUCT_DETAILS",
        "GET_PRODUCT_FEATURE_DETAILS",
        "SHOW_CONTACT_INFORMATION",
        "GENERAL_SALES_SERVICE",
        "ANSWER_COMPETITOR_INQUIRY",
        "SHOW_PRODUCT_CASES",
        "GET_PRODUCT_PARAMETERS_SPECIFICATIONS",
        "COLLECT_USER_INFO",
    ]
    for d in _dictlist:
        if d["属性"] not in ["admin", "normal"]:
            continue
        expected_result_str = d["expected result"].strip()
        if not expected_result_str:
            continue
        expected_result_str = expected_result_str.replace(
            "WEATHER_GET_REALTIME", "WEATHER"
        ).replace(
            "WEATHER_GET", "WEATHER"
        )  # TODO(<EMAIL>) 等评测集稍微稳定后，删除这部分逻辑
        caseID = d["caseID"]
        expected_answer = parse_expected_answer(caseID, expected_result_str)
        if not expected_answer:
            if expected_answers_in_jsonschema := DictlistUtils.safe_convert_to_object(
                d.get("expected_answers_in_jsonschema", ""), None, None
            ):
                d["expected_answer"] = []
                d["expected_answers_in_jsonschema"] = expected_answers_in_jsonschema
            else:
                logger.error(
                    f"caseID: {caseID}, 没有找到expected_answer，也没有找到expected_answers_in_jsonschema"
                )
                continue
        d["expected_answer"] = expected_answer
        d["# CHAT CONVERSATION"] = d["# CHAT CONVERSATION"].replace("SAY", "SAID")
        if "agent_id" not in d:
            expect_single_action = d["Action"].strip()
            if expect_single_action in PROMOTE_ACTIONS:
                d["agent_id"] = "agent_H0Ej9nfDvGxOK6WB"
            else:
                d["agent_id"] = "agent_B9SLEgAM2c2AJNxw"
        if "language_code" not in d:
            language_name_from_dataset = d.get("language", "Chinese").strip()
            if language_name_from_dataset == "Chinese":
                d["language_code"] = "zh_CN"
            elif language_name_from_dataset == "English":
                d["language_code"] = "en_US"
            else:
                raise ValueError(f"当前还不支持这种语言: {language_name_from_dataset}")
        # TODO(<EMAIL>) 机器人人设信息
        if "robot_basic_info" not in d:  # 机器人基础信息
            d["robot_basic_info"] = d["# ROBOT BASIC INFORMATION"].strip()
        if "robot_base_info" not in d:
            d["robot_base_info"] = d["# ROBOT BASIC INFORMATION"].strip()
        if "robot_real_time_info" not in d:  # 机器人实时信息
            d["robot_real_time_info"] = d["# ROBOT REAL-TIME INFORMATION"].strip()
        if "robot_realtime_info" not in d:
            d["robot_realtime_info"] = d["# ROBOT REAL-TIME INFORMATION"].strip()
        if "screen_info" not in d:  # 机器人屏幕信息
            d["screen_info"] = d["# Screen Info"].strip()
        if "user_info" not in d:  # 机器人对用户的记忆
            d["user_info"] = d["# User's Relevant information"].strip()
        if "user_memory" not in d:  # 机器人对用户的记忆
            d["user_memory"] = d["# User's Relevant information"].strip()
        if (
            "chat_conversation" not in d
        ):  # 人机交互历史记录，不仅仅是对话`said`，还有`Triggered Event`、`Executed Action`。
            d["chat_conversation"] = d["# CHAT CONVERSATION"].strip()
        if "conversation_progress" not in d:
            d["conversation_progress"] = format_chat_conversation_strategy(
                d["chat_conversation"]
            )

        if "frozen_ActionDictList" not in d:
            d["frozen_ActionDictList"] = "[]"

        # 这些字段做一下strip，Excel中可能多了空格
        d["agent_id"] = d["agent_id"].strip()
        d["app_id"] = d["app_id"].strip()
        d["package_name"] = d["package_name"].strip()

        dictlist.append(d)
    MUST_HAVE_FIELDS = [
        "caseID",
        "query",
        "属性",
        "expected_answer",
        "language_code",
        # "robot_personality_info",
        "robot_basic_info",  # 机器人基础信息
        "robot_base_info",  # 机器人基础信息
        "robot_real_time_info",  # 机器人实时信息
        "robot_realtime_info",  # 机器人实时信息
        "screen_info",  # 机器人屏幕信息
        "user_info",  # 机器人对用户的记忆
        "user_memory",  # 机器人对用户的记忆
        "chat_conversation",  # 人机交互历史记录
        "conversation_progress",  # 人机交互历史记录(格式化方式不同)
        "frozen_ActionDictList",
        "agent_id",
        "app_id",
        "package_name",
    ]
    for d in dictlist:
        missing_fields = [field for field in MUST_HAVE_FIELDS if field not in d]
        if missing_fields:
            raise ValueError(f"caseID:{d['caseID']}. 缺少必要字段: {missing_fields}")
    if output_filename_prefix:
        if not not_save_intermediate_files:
            DictlistUtils.write_dictlist(
                filename=output_filename_prefix, dictlist=dictlist
            )
    logger.info(
        f"prepare_dataset end, output_filename_prefix: {output_filename_prefix}"
    )
    return dictlist


def override_action_parameter_enum_value(
    frozen_ActionDictList: list[dict], action_list: list[dict]
) -> tuple[list[dict], dict]:
    overrided_enums: dict[
        str, dict[str, dict[str, list]]
    ] = {}  # {action_name: {param_name: {old: old_enum_values, new: new_enum_values}}}
    if not frozen_ActionDictList:
        return action_list, overrided_enums
    # NOTE：frozen_ActionDictList中的action_name是全大写的，action_list中的action_name是全小写的，这里统一转成全小写的
    frozen_enum_zoo: dict[
        str, dict[str, list]
    ] = {}  # {action_name: {param_name: param_enum_value}}
    for action in frozen_ActionDictList:
        if action.get("type", "") == "function":
            if function := action.get("function", {}):
                if action_name := function.get("name", "").lower():
                    if parameters := function.get("parameters", {}):
                        if properties := parameters.get("properties", {}):
                            for param_name, param_properties in properties.items():
                                param_type = param_properties.get("type", "")
                                if param_type in ["string", "number", "integer"]:
                                    if enum_values := param_properties.get("enum", []):
                                        if action_name not in frozen_enum_zoo:
                                            frozen_enum_zoo[action_name] = {}
                                        frozen_enum_zoo[action_name][param_name] = (
                                            deepcopy(enum_values)
                                        )
                                elif param_type in ["array"]:
                                    # 兼容旧数据集，旧数据集中enum与items在同一级别，不在items内
                                    if enum_values := param_properties.get("enum", []):
                                        if action_name not in frozen_enum_zoo:
                                            frozen_enum_zoo[action_name] = {}
                                        frozen_enum_zoo[action_name][param_name] = (
                                            deepcopy(enum_values)
                                        )
                                    if items := param_properties.get("items", {}):
                                        item_type = items.get("type", "")
                                        if item_type in ["string", "number", "integer"]:
                                            if enum_values := items.get("enum", []):
                                                if action_name not in frozen_enum_zoo:
                                                    frozen_enum_zoo[action_name] = {}
                                                frozen_enum_zoo[action_name][
                                                    param_name
                                                ] = deepcopy(enum_values)
                                        else:
                                            logger.error(
                                                f"当前不支持的array中的item参数类型: {item_type}, action_name: {action_name}, param_name: {param_name}"
                                            )
                                else:
                                    logger.error(
                                        f"当前不支持的参数类型: {param_type}, action_name: {action_name}, param_name: {param_name}"
                                    )
            continue
        action_name = action["name"].lower()
        for param in action["parameters"]:
            param_name = param["name"]
            param_enum_value = param.get(
                "enum_constant", []
            )  # NOTE：这里叫enum_constant，不是enum
            if param_enum_value:
                if action_name not in frozen_enum_zoo:
                    frozen_enum_zoo[action_name] = {}
                frozen_enum_zoo[action_name][param_name] = deepcopy(param_enum_value)
    for action in action_list:
        action_name = action["name"].lower()
        if action_name in frozen_enum_zoo:
            for param in action["parameters"]:
                param_name = param["name"]
                param_type = param.get("type", "")
                if "enum" in param or param_type in ["enum", "String array"]:
                    original_enum_values = param.get("enum", [])
                    if param_name in frozen_enum_zoo[action_name]:
                        new_enum_values = frozen_enum_zoo[action_name].get(
                            param_name, []
                        )
                        if not new_enum_values:
                            logger.error(
                                f"覆盖action参数枚举值失败，没有匹配到对应枚举值，action_name: {action_name}, param_name: {param_name}, original_enum_values: {original_enum_values}"
                            )
                        elif original_enum_values != new_enum_values:
                            param["enum"] = new_enum_values
                            if action_name not in overrided_enums:
                                overrided_enums[action_name] = {}
                            overrided_enums[action_name][param_name] = {
                                "old": deepcopy(original_enum_values),
                                "new": deepcopy(new_enum_values),
                            }
                            logger.debug(
                                f"覆盖action参数枚举值成功，action_name: {action_name}, param_name: {param_name}, original_enum_values: {original_enum_values}, new_enum_values: {new_enum_values}"
                            )
                    elif original_enum_values:
                        logger.error(
                            f"覆盖action参数枚举值失败，没有匹配到对应枚举值，action_name: {action_name}, param_name: {param_name}, original_enum_values: {original_enum_values}"
                        )
    return action_list, overrided_enums


def parse_actual_answer(
    action_selected_by_SingleActionAgent: dict, actions_for_llm: list[dict]
) -> dict:
    """
    action_selected_by_SingleActionAgent_example =
    {
        "orion.agent.action.navigate_rec_start": {
            "destinations": [
                "南极洲会议室",
                "拉斯维加斯会议室",
                "老板办公区"
            ],
            "guide_text": "我们这里有南极洲会议室、拉斯维加斯会议室和老板办公区，您想去哪个呢？",
            "extend_destinations": [
                "回充点",
            ],
            "_USER_QUERY": "",
            "_CURRENT_SUMMARY": "",
            "SYNTHESIZE_TYPE": "user_query"
        },
        "ACTION": "orion.agent.action.NAVIGATE_REC_START",
        "PARAMETERS": {
            "destinations": [
                "南极洲会议室",
                "拉斯维加斯会议室",
                "老板办公区"
            ],
            "guide_text": "我们这里有南极洲会议室、拉斯维加斯会议室和老板办公区，您想去哪个呢？",
            "extend_destinations": [
                "回充点",
            ],
            "_USER_QUERY": "",
            "_CURRENT_SUMMARY": "",
            "SYNTHESIZE_TYPE": "user_query"
        },
        "_ORIGINAL_ACTION_NAME": "orion.agent.action.navigate_rec_start",
        "_ORIGINAL_PARAMETERS": {
            "destinations": [
                "南极洲会议室",
                "拉斯维加斯会议室",
                "老板办公区"
            ],
            "guide_text": "我们这里有南极洲会议室、拉斯维加斯会议室和老板办公区，您想去哪个呢？",
            "extend_destinations": [
                "回充点",
            ],
            "_USER_QUERY": "",
            "_CURRENT_SUMMARY": "",
            "SYNTHESIZE_TYPE": "user_query"
        },
        "display_name": "准备领位",
        "execute_side": "robot",
        "execute_timeout_limit": "300"
    }
    one_action_from_actions_for_llm_example =
    {
        "name": "navigate_rec_start",
        "fullname": "orion.agent.action.navigate_rec_start",
        "namespace": "orion.agent.action",
        "source": "builtin",
        "purpose": "室内导航意图。不支持去室外位置，应直接根据「CHAT CONVERSATION」中的对话历史和用户特征（包括但不限于性别信息），为用户选择最符合其真实意图的具体地点。",
        "level": "global",
        "category": "navigation",
        "only_intervention": false,
        "parameters": [
            {
                "name": "destinations",
                "desc": "Navigation points that match user intent. You can choose multiple. ",
                "type": "String array",
                "is_required": false,
                "enum": [
                    "南极洲会议室",
                    "拉斯维加斯会议室",
                    "老板办公区"
                ]
            },
            {
                "name": "guide_text",
                "desc": "A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'",
                "type": "string",
                "is_required": true
            }
        ]
    }
    """
    try:
        # NOTE：为了与测试集保持一致，这里将action_name转换为大写
        action_name = (
            action_selected_by_SingleActionAgent["_ORIGINAL_ACTION_NAME"]
            .split(".")[-1]
            .upper()
        )
        allowed_action_parameter_names = []
        for action in actions_for_llm:
            if action["name"].upper() == action_name:
                parameters = action.get("parameters", [])  # NOTE：这是个list
                if parameters:
                    allowed_action_parameter_names = [p["name"] for p in parameters]
                    break
        if not allowed_action_parameter_names:
            return {action_name: {}}
        action_parameter_kv = action_selected_by_SingleActionAgent.get(
            "_ORIGINAL_PARAMETERS", {}
        )  # NOTE：这是个dict
        if not action_parameter_kv:
            return {action_name: {}}
        output_parameter_kv = {}
        for param_name in allowed_action_parameter_names:
            if param_name in action_parameter_kv:
                output_parameter_kv[param_name] = deepcopy(
                    action_parameter_kv[param_name]
                )
        return {action_name: output_parameter_kv}
    except Exception as e:
        logger.error(
            f"parse_actual_answer 失败 {action_selected_by_SingleActionAgent}, error: {e}\n{traceback.format_exc()}"
        )
        return {}


async def get_answer_for_one_case(
    d: dict,
    action_version: str = "draft",
    disable_override_action_parameter_enum_value: bool = False,
    enterprise_id: str = "orion.ovs.entprise.9945420568",
    robot_language_code: str = "zh_CN",
    robot_turn_on_clarify: bool = True,
    robot_turn_on_confirm: bool = True,
) -> dict:
    caseID = d["caseID"]
    logger.info(f"caseID:{caseID}. get_answer_for_one_case start")
    d["overrided_enums"] = {}
    d["ok_get_answer"] = False
    d["actions_from_load"] = []
    d["actions_for_llm"] = []
    d["action_selected_by_SingleActionAgent"] = {}
    d["agent_debug"] = None
    d["retry"] = 0
    d["confirmed_action"] = {}
    d["agent_messages"] = []
    d["llm_model_name"] = ""
    d["tools"] = []
    d["actual_answer"] = {}
    d["actual_answer_str"] = ""
    d["query_id"] = uuid.uuid4().hex
    d["agent_core_call_select_action_llm_time"] = 0.0
    d["call_agent_core_cost_time"] = 0.0
    d["inner_agent_core_cost_time"] = 0.0
    d["run_agent_response_total_elapsed_time"] = 0.0
    d["query_rewrite_time"] = 0.0
    d["knowledge_search_time"] = 0.0
    d["knowledge_qa_total_time"] = 0.0
    d["input_not_cached_tokens"] = 0.0
    d["input_not_cached_tokens_cost(元)"] = 0.0
    d["input_cached_tokens"] = 0.0
    d["input_cached_tokens_cost(元)"] = 0.0
    d["output_tokens"] = 0.0
    d["output_tokens_cost(元)"] = 0.0
    d["prompt_tokens"] = 0.0
    d["completion_tokens"] = 0.0
    d["cached_tokens"] = 0.0
    d["total_tokens"] = 0.0
    d["total_cost(元)"] = 0.0
    d["llm_api_total_timecost"] = (
        0.0  # 单条case请求llm-api包含重试的总耗时（不包含超时的情况）
    )
    d[
        "llm_api_response"
    ] = []  # 单条case请求llm-api包含重试的response（不包含超时的情况）
    d["llm_api_return_type"] = (
        "unknown"  # unknown, only_content, only_tool_calls, content_plus_tool_calls
    )

    # prepare short_circuit_data_for_test
    try:
        context_messages_all = format_conversation_progress_to_chat_messages(
            d["conversation_progress"]
        )
        short_circuit_data_for_test = {
            "robot_base_info": safe_convert_to_object(
                d["robot_base_info"], d["robot_base_info"], d["robot_base_info"]
            ),
            "robot_real_time_info": safe_convert_to_object(
                d["robot_real_time_info"],
                d["robot_real_time_info"],
                d["robot_real_time_info"],
            ),
            "screen_info": d["screen_info"],
            "user_memory": d["user_memory"],
            "context_messages": context_messages_all[:-1],
        }
    except Exception as e:
        logger.error(
            f"caseID:{caseID}. 准备short_circuit_data_for_test失败，{e}\n{traceback.format_exc()}"
        )
        return d

    # 后期看是否能在测试执行的时候指定机器人
    robot = Robot(
        device_id=DEVICE_ID,
        enterprise_id=enterprise_id,
        language=robot_language_code,
        turn_on_clarify=robot_turn_on_clarify,
        turn_on_confirm=robot_turn_on_confirm,
    )
    candidate_actions = ActionVersionManager().fetch_actions_by_version(action_version)
    ActionLib().update_builtin_actions(candidate_actions)
    try:
        agent_parameter = AgentParameter(
            query=context_messages_all[-1].content,  # type: ignore
            query_id=d["query_id"],
            candidate_actions=candidate_actions,
            rag_manager=RAGManager(provider_type=RAGProviderType.CHATMAX)
            if agent_setting.region_version == Area.domestic
            else RAGManager(provider_type=RAGProviderType.OPENAI_VECTOR),
            robot=robot,
            memory=Memory(
                redis_client=Redis(
                    host=agent_setting.redis_host,
                    port=agent_setting.redis_port,
                    db=3,
                ),
                device_id=DEVICE_ID,
            ),
            run_step_queue=asyncio.Queue(),
        )
    except Exception as e:
        logger.error(
            f"caseID:{caseID}. 创建AgentParameter失败，{e}\n{traceback.format_exc()}"
        )
        return d

    agent_parameter.robot.interface_state.app_id = d["app_id"]
    agent_parameter.robot.interface_state.package_name = d["package_name"]
    agent_parameter.robot.agent_id = d["agent_id"]
    is_admin_mode = True if d["属性"] == "admin" else False
    actions_from_load = await ActionLib().load_support_action(
        parameter=agent_parameter,
        use_candidate_actions_directly=False,
        is_admin_mode=is_admin_mode,
    )
    d["actions_from_load"] = deepcopy(actions_from_load)
    actions_for_llm = [
        a for a in deepcopy(actions_from_load) if not a["only_intervention"]
    ]  # 排除干预类动作

    if not disable_override_action_parameter_enum_value:
        try:
            frozen_ActionDictList = DictlistUtils.convert_str_to_obj(
                d.get("frozen_ActionDictList", "[]")
            )
        except Exception as e:
            logger.error(f"caseID:{caseID}. 解析frozen_ActionDictList失败，error: {e}")
            frozen_ActionDictList = []
        if not frozen_ActionDictList:
            logger.warning(
                f"caseID:{caseID}. frozen_ActionDictList: {frozen_ActionDictList}"
            )
        try:
            actions_for_llm, overrided_enums = override_action_parameter_enum_value(
                frozen_ActionDictList, actions_for_llm
            )
            d["overrided_enums"] = overrided_enums
        except Exception as e:
            logger.error(f"caseID:{caseID}. 覆盖action参数枚举值失败，error: {e}")
    d["actions_for_llm"] = deepcopy(actions_for_llm)
    logger.debug(f"caseID:{caseID}. actions_for_llm: {actions_for_llm}")

    (
        action_selected_by_SingleActionAgent,
        debug,
    ) = await SingleActionAgent.select_action_by_agent_core(
        parameter=agent_parameter,
        actions=actions_for_llm,
        context_messages=[],  # 通过`short_circuit_data_for_test`中的`context_messages`进行覆盖
        _logger=logger,
        language=d["language_code"],
        short_circuit_data_for_test=short_circuit_data_for_test,
    )

    d["action_selected_by_SingleActionAgent"] = action_selected_by_SingleActionAgent
    if (
        debug.run_agent_response
        and debug.run_agent_response.content
        and not debug.run_agent_response.run_action
    ):
        # NOTE：特殊处理，后续可能要改
        emergency_actual_answer = {
            "SAY": {
                "text": debug.run_agent_response.content,
            },
        }
        d["actual_answer"] = emergency_actual_answer
        d["actual_answer_str"] = json.dumps(emergency_actual_answer, ensure_ascii=False)
        d["llm_api_return_type"] = "only_content"
    else:
        parsed_action_selected_by_SingleActionAgent = parse_actual_answer(
            action_selected_by_SingleActionAgent, actions_for_llm
        )
        d["actual_answer"] = parsed_action_selected_by_SingleActionAgent
        d["actual_answer_str"] = json.dumps(
            parsed_action_selected_by_SingleActionAgent, ensure_ascii=False
        )
        d["llm_api_return_type"] = (
            "content_plus_tool_calls"
            if debug.run_agent_response
            and debug.run_agent_response.content
            and debug.run_agent_response.run_action
            else "only_tool_calls"
        )

    d["confirmed_action"] = (
        debug.run_agent_response.confirmed_action.model_dump()
        if debug.run_agent_response.confirmed_action
        else {}
    )
    d["agent_debug"] = debug
    d["retry"] = debug.retry
    d["agent_messages"] = debug.agent_messages
    d["llm_model_name"] = debug.llm_config.llm_model_name
    d["tools"] = debug.tools
    d["agent_core_call_select_action_llm_time"] = debug.call_agent_core_cost_time

    # 增加更多耗时信息
    call_agent_core_cost_time = 0
    inner_agent_core_cost_time = 0
    run_agent_response_total_elapsed_time = 0
    query_rewrite_time = 0
    knowledge_search_time = 0
    knowledge_qa_total_time = 0
    try:
        agent_debug = json.loads(debug.model_dump_json())
        call_agent_core_cost_time = agent_debug.get("call_agent_core_cost_time", 0)
        inner_agent_core_cost_time = agent_debug.get("inner_agent_core_cost_time", 0)
        run_agent_response_total_elapsed_time = agent_debug["run_agent_response"][
            "debug"
        ]["total_elapsed_time"]
        knowledge_debug_info = agent_debug.get("knowledge_debug_info", {})
        query_rewrite_time = knowledge_debug_info.get("query_rewrite_time_ms", 0) / 1000
        knowledge_search_time = (
            knowledge_debug_info.get("knowledge_search_time_ms", 0) / 1000
        )
        knowledge_qa_total_time = knowledge_debug_info.get("total_time_ms", 0) / 1000
    except Exception as e:
        logger.error(
            f"caseID:{caseID}. 解析debug失败，error: {e}\n{traceback.format_exc()}"
        )
    d["call_agent_core_cost_time"] = call_agent_core_cost_time
    d["inner_agent_core_cost_time"] = inner_agent_core_cost_time
    d["run_agent_response_total_elapsed_time"] = run_agent_response_total_elapsed_time
    d["query_rewrite_time"] = query_rewrite_time
    d["knowledge_search_time"] = knowledge_search_time
    d["knowledge_qa_total_time"] = knowledge_qa_total_time

    agent_token_cost = debug.agent_token_cost
    if agent_token_cost:  # TODO(<EMAIL>): agent_token_cost为空，怎么回事？
        prompt_tokens = agent_token_cost["prompt_tokens"]
        completion_tokens = agent_token_cost["completion_tokens"]
        total_tokens = agent_token_cost["total_tokens"]
        cached_tokens = agent_token_cost["prompt_tokens_details"].get(
            "cached_tokens", 0
        )
        d["prompt_tokens"] = prompt_tokens
        d["completion_tokens"] = completion_tokens
        d["cached_tokens"] = cached_tokens
        d["total_tokens"] = total_tokens
        d["input_not_cached_tokens"] = prompt_tokens - cached_tokens
        d["input_cached_tokens"] = cached_tokens
        d["output_tokens"] = completion_tokens
        d["input_not_cached_tokens_cost(元)"] = TokenPriceUtils.calc_token_money(
            model=d["llm_model_name"],
            token_type="input_not_cached_token",
            token_count=d["input_not_cached_tokens"],
        )
        d["input_cached_tokens_cost(元)"] = TokenPriceUtils.calc_token_money(
            model=d["llm_model_name"],
            token_type="input_cached_token",
            token_count=d["input_cached_tokens"],
        )
        d["output_tokens_cost(元)"] = TokenPriceUtils.calc_token_money(
            model=d["llm_model_name"],
            token_type="output_token",
            token_count=d["output_tokens"],
        )
        d["total_cost(元)"] = (
            d["input_not_cached_tokens_cost(元)"]
            + d["input_cached_tokens_cost(元)"]
            + d["output_tokens_cost(元)"]
        )
    d["ok_get_answer"] = True
    logger.info(f"caseID:{caseID}. get_answer_for_one_case end")
    return d


async def get_answer_for_case_list(
    dictlist: list[dict],
    action_version: str = "draft",
    disable_override_action_parameter_enum_value: bool = False,
    enterprise_id: str = "orion.ovs.entprise.9945420568",
    robot_language_code: str = "zh_CN",
    robot_turn_on_clarify: bool = True,
    robot_turn_on_confirm: bool = True,
) -> list[dict]:
    for d in tqdm(dictlist, desc="step2/4 get_answer", total=len(dictlist)):
        try:
            d = await get_answer_for_one_case(
                d,
                action_version,
                disable_override_action_parameter_enum_value,
                enterprise_id,
                robot_language_code,
                robot_turn_on_clarify=robot_turn_on_clarify,
                robot_turn_on_confirm=robot_turn_on_confirm,
            )
        except Exception as e:
            logger.error(
                f"caseID:{d['caseID']}. step2/4 get_answer失败，{e}\n{traceback.format_exc()}"
            )
    return dictlist


async def get_judgment_for_one_case(d: dict) -> dict:
    caseID = d["caseID"]
    logger.info(f"caseID:{caseID}. get_judgment_for_one_case start")
    d["judged_by"] = ""
    d["Score"] = 0.0
    d["format_score"] = 0.0
    d["action_score"] = 0.0
    d["parameter_score"] = 0.0
    d["redundancy_score"] = 0.0
    d["invalid_reason"] = []
    d["evlue_prompt_token"] = 0.0
    d["evlue_completion_token"] = 0.0
    d["evlue_input_token_cost(元)"] = 0.0
    d["evlue_completion_token_cost(元)"] = 0.0
    d["evlue_total_cost(元)"] = 0.0
    d["evlue_reason"] = ""
    d["evlue_prompt"] = ""
    d["evlue_raw_llm_output"] = ""

    try:
        # 用jsonschema校验expected_answer，需要输入tools
        skip_these_action_names = [KNOWLEDGE_QA_ACTION_NAME]
        is_valid_list, error_msg_list = validate_actiondictlist_by_jsonschema(
            DictlistUtils.safe_convert_to_object(d["expected_answer"], [], []),
            DictlistUtils.safe_convert_to_object(d["tools"], [], []),
            skip_these_action_names,
        )
        d["is_expected_answers_valid"] = is_valid_list
        d["validate_expected_answers_error_msg"] = error_msg_list
    except Exception as e:
        error_msg = (
            f"caseID:{caseID}. 验证expected_answer失败，{e}\n{traceback.format_exc()}"
        )
        d["is_expected_answers_valid"] = [False]
        d["validate_expected_answers_error_msg"] = [error_msg]
        logger.error(error_msg)

    actual_answer = DictlistUtils.safe_convert_to_object(
        d.get("actual_answer"), value_if_empty={}, value_if_error={}
    )
    if not isinstance(actual_answer, dict) or not actual_answer:
        logger.error(f"caseID:{caseID}. actual_answer为空")
        return d
    d["actual_answer"] = actual_answer
    try:
        # 如果标注了expected_answers_in_jsonschema，则用jsonschema校验actual_answer，否则使用原来的score方法
        expected_answers_in_jsonschema = DictlistUtils.safe_convert_to_object(
            d.get("expected_answers_in_jsonschema", ""), None, None
        )
        if expected_answers_in_jsonschema:
            d["expected_action_names"] = ",".join(
                sorted((set(a["name"] for a in expected_answers_in_jsonschema)))
            )
            d["expected_answers_in_jsonschema"] = expected_answers_in_jsonschema
            d = judge_one_case_by_jsonschema(
                d,
                inplace=True,
                expected_answers_in_jsonschema_field="expected_answers_in_jsonschema",
                actual_answer_field="actual_answer",
                output_score_field="Score",
                output_format_score_field="format_score",
                output_action_score_field="action_score",
                output_parameter_score_field="parameter_score",
                output_redundancy_score_field="redundancy_score",
                output_invalid_reason_field="invalid_reason",
                output_judged_by_field="judged_by",
            )
    except Exception as e:
        logger.error(
            f"caseID:{caseID}. 验证expected_answers_in_jsonschema失败，{e}\n{traceback.format_exc()}"
        )
    if d["judged_by"] not in ["rule-based-judge"]:
        d["expected_action_names"] = ",".join(
            sorted(
                (
                    set(
                        list(a.keys())[0]
                        for a in DictlistUtils.safe_convert_to_object(
                            d["expected_answer"], [], []
                        )
                    )
                )
            )
        )
        d = await score(d)
        d["judged_by"] = "original-score-method"
        evlue_input_token_cost = d["evlue_prompt_token"] * 0.0000175
        evlue_completion_token_cost = d["evlue_completion_token"] * 0.00007
        d["evlue_input_token_cost(元)"] = evlue_input_token_cost
        d["evlue_completion_token_cost(元)"] = evlue_completion_token_cost
        d["evlue_total_cost(元)"] = evlue_input_token_cost + evlue_completion_token_cost
    logger.info(f"caseID:{caseID}. get_judgment_for_one_case end")
    return d


async def get_judgment_for_case_list(dictlist: list[dict]) -> list[dict]:
    for d in tqdm(dictlist, desc="step3/4 get_judgment", total=len(dictlist)):
        try:
            d = await get_judgment_for_one_case(d)
        except Exception as e:
            logger.error(
                f"caseID:{d['caseID']}. step3/4 get_judgment失败，{e}\n{traceback.format_exc()}"
            )
    return dictlist


async def do_concurrent_processing(
    func: Callable, alist: list, num_concurrent: int = 1, kwargs: dict = {}
) -> list:
    """
    Process a list using asyncio concurrency instead of multiprocessing.
    This is more appropriate for I/O-bound tasks like HTTP requests.
    """
    if num_concurrent <= 1 or len(alist) <= 1:
        return await func(alist, **kwargs)
    else:
        num_tasks = len(alist)
        if num_tasks <= num_concurrent:
            num_concurrent = num_tasks
        print(f"num_tasks_total: {num_tasks} real_num_concurrent: {num_concurrent}")

        # Split the list into chunks
        chunk_size = int((num_tasks - 1) / num_concurrent + 1)
        chunks = []
        for i in range(num_concurrent):
            start_idx = i * chunk_size
            end_idx = min((i + 1) * chunk_size, num_tasks)
            if start_idx < num_tasks:
                chunks.append(alist[start_idx:end_idx])

        # Create tasks for each chunk
        tasks = []
        for chunk in chunks:
            task = asyncio.create_task(func(chunk, **kwargs))
            tasks.append(task)

        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)

        # Flatten the results
        flattened_results = []
        for result in results:
            flattened_results.extend(result)

        return flattened_results


async def main(
    experiment_id: str,
    file_path: str,
    benchmark_count: int,
    action_version: str = "draft",
    *,
    enterprise_id: str = "orion.ovs.entprise.9945420568",
    robot_language_code: str = "zh_CN",
    num_parallel_get_answer: int = 1,
    num_parallel_get_judgment: int = 1,
    only_run_caseID_list: list[str] = [],
    skip_prepare_dataset: bool = False,
    skip_get_answer: bool = False,
    skip_get_judgment: bool = False,
    disable_override_action_parameter_enum_value: bool = False,
    not_save_intermediate_files: bool = False,
    robot_turn_on_clarify: bool = True,
    robot_turn_on_confirm: bool = True,
) -> tuple[list[dict], list[str]]:
    error_messages = []
    http_context._new_session_ctx()()

    try:
        # step1/4 prepare_dataset
        if not skip_prepare_dataset:
            dictlist = prepare_dataset(
                file_path,
                file_path + "_preprocessed_" + experiment_id,
            )
        else:
            dictlist = DictlistUtils.read_dictlist(
                file_path, field_names_to_dropna_row=["caseID"]
            )
        num_prepared = len(dictlist)
        dictlist = dictlist[:benchmark_count] if benchmark_count > 0 else dictlist
        if only_run_caseID_list:

            def _str(x) -> str:
                return str(int(x)) if isinstance(x, float) else str(x)

            dictlist = [
                d for d in dictlist if _str(d["caseID"]) in only_run_caseID_list
            ]
        num_to_evaluate = len(dictlist)
        print(
            f"已准备case数量共计{num_prepared}条，测试case数量共计{num_to_evaluate}条"
        )

        # step2/4 get_answer
        if not skip_get_answer:
            dictlist = await do_concurrent_processing(
                get_answer_for_case_list,
                dictlist,
                num_concurrent=num_parallel_get_answer,
                kwargs={
                    "action_version": action_version,
                    "disable_override_action_parameter_enum_value": disable_override_action_parameter_enum_value,
                    "enterprise_id": enterprise_id,
                    "robot_language_code": robot_language_code,
                    "robot_turn_on_clarify": robot_turn_on_clarify,
                    "robot_turn_on_confirm": robot_turn_on_confirm,
                },
            )
            if not not_save_intermediate_files:
                DictlistUtils.write_dictlist(
                    filename=file_path + "_get_answer_" + experiment_id,
                    dictlist=dictlist,
                )

        # step3/4 get_judgment
        if not skip_get_judgment:
            fields_to_convert_to_obj = []
            if skip_prepare_dataset:
                fields_to_convert_to_obj.append("expected_answer")
            if skip_get_answer:
                fields_to_convert_to_obj.append("actual_answer")
            if fields_to_convert_to_obj:
                dictlist = DictlistUtils.convert_elements_to_obj(
                    dictlist, field_names=fields_to_convert_to_obj, inplace=True
                )
            dictlist = await do_concurrent_processing(
                get_judgment_for_case_list,
                dictlist,
                num_concurrent=num_parallel_get_judgment,
                kwargs={},
            )
            if not not_save_intermediate_files:
                DictlistUtils.write_dictlist(
                    filename=file_path + "_get_judgment_" + experiment_id,
                    dictlist=dictlist,
                )
    except Exception as e:
        dictlist = []
        error_messages.append(f"Error: {e}")
        print(traceback.format_exc())
    finally:
        # 确保关闭http会话
        await http_context._close_http_ctx()
    return dictlist, error_messages


async def judge_all_by_llm(d: dict, prompt_template: str) -> dict:
    detail_score = {
        "action_score": 0,
        "parameter_score": 0,
        "redundance_score": 0,
    }
    llm_result = {
        "score": 0,
        "reason": [],
        "evlue_prompt_token": 0,
        "evlue_completion_token": 0,
        "evlue_reason": "",
        "prompt": "",
        "detail_score": detail_score,
    }
    prompt = prompt_template.format(
        query=d["query"],
        actual_answer=str(d["actual_answer_str"]),
        expected_answer=str(d["expected_answer"]),
        context=d["chat_conversation"],
    )
    # prompt = prompt_template.format(
    #     query=query,
    #     actual_answer=actual_answer,
    #     expected_answer=expected_answer,
    #     context=context,
    # )
    evlue_prompt_token = 0
    evlue_completion_token = 0
    # 调用大模型评分出现异常，增加一次重试
    llm_res = await llm(prompt)
    if not llm_res:
        llm_res = await llm(prompt)
        if not llm_res:
            logger.error(f"=====>>调用模型重试1次仍然异常。\n{traceback.format_exc()}")
            return {}, False
    try:
        evlue_prompt_token = llm_res.token_cost.get("prompt_tokens")
        evlue_completion_token = llm_res.token_cost.get("completion_tokens")
        llm_judge_result, failed_reason = parse_output_to_dict(llm_res.content)
        if failed_reason:
            llm_result["reason"] = failed_reason
            llm_result["evlue_prompt_token"] = evlue_prompt_token
            llm_result["evlue_completion_token"] = evlue_completion_token
            llm_result["prompt"] = prompt
            return llm_result

        llm_result["reason"] = llm_judge_result.get("reasons")
        llm_result["score"] = llm_judge_result.get("score")
        llm_result["detail_score"] = llm_judge_result.get("detail_score")
        llm_result["evlue_prompt_token"] = evlue_prompt_token
        llm_result["evlue_completion_token"] = evlue_completion_token
        llm_result["prompt"] = prompt
        return llm_result
    except Exception as e:
        logger.error(
            f"判题的结果不符合要求，json 解析失败。{e}\n{traceback.format_exc()}"
        )
        return {}, False


async def llm(prompt: str):
    try:
        llm_res = await LLMManager.invoke_model_from_llm_config(
            llm_config=LLMConfig(
                base_url="https://api.openai.com/v1",
                llm_model_name="gpt-4o",
                api_key="***************************************************",
                timeout=10,
            ),
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                },
            ],
        )
        print(f"=====>>大模型调用结果llm_result: {llm_res.content}")
        llm_result, failed_reason = parse_output_to_dict(llm_res.content)
        if failed_reason:
            logger.error(
                f"判题的结果不合法，json 解析失败。{failed_reason}\n{traceback.format_exc()}"
            )
            return {}
    except Exception as e:
        logger.error(f"调用模型异常。{e}\n{traceback.format_exc()}")
        return {}
    return llm_res


async def redundance_check(
    actual_answer: dict, expected_answer: dict
) -> tuple[float, str]:
    if not expected_answer and actual_answer:
        return 0, "冗余"
    if not expected_answer and not actual_answer:
        return 1, ""
    # 预期结果是实际结果的真子集，冗余
    if set(expected_answer.keys()) < set(actual_answer.keys()):
        return 0, "冗余"
    return 1, ""


async def parameter_check(
    actual_parameters: dict, expected_parameters: dict, d: dict, num_retry: int = 6
) -> tuple[float, str]:
    if actual_parameters == expected_parameters:
        return 1, ""
    elif not expected_parameters and actual_parameters:
        return 0, "参数错误"
    else:
        # 参数不完全一致的时候，需要逐一判断
        actual_parameters_keys = actual_parameters.keys()
        expected_parameters_keys = expected_parameters.keys()
        # 检查是否有任何actual_key不在expected_keys中
        if all(key not in expected_parameters_keys for key in actual_parameters_keys):
            return 0, "参数错误"

        all_error = True
        has_key_error = False
        for expected_key, expected_value in expected_parameters.items():
            #   判断预期的key在实际输出中不存在，则认为有错误
            if not actual_parameters.get(expected_key):
                has_key_error = True
            if expected_value == actual_parameters.get(expected_key):
                all_error = False
        else:
            # 如果部分参数key在实际返回的参数中不存在，则认为部分参数错误，不需要大模型再评分
            if has_key_error and not all_error:
                return 0.5, "部分参数错误"
            from gpt_score_prompt import prompt_v5

            prompt = prompt_v5.format(
                real_time_info=d["robot_real_time_info"],
                query=d["query"],
                actual_answer=actual_parameters,
                expected_answer=expected_parameters,
                context=d["chat_conversation"],
            )
            d["evlue_prompt"] = prompt
            # 通过llm评判参数是否正确
            llm_result = None
            for i in range(num_retry):
                llm_result = await llm(prompt)
                if llm_result:
                    break
                else:
                    if i < num_retry - 1:
                        logger.warning(
                            f"llm评判参数是否正确已失败{i + 1}次，即将进行第{i + 2}次尝试"
                        )
            if not llm_result:
                logger.error(f"llm评判参数是否正确，经过{num_retry}次尝试后仍然失败")
                return 0, "参数部分模型评测失败"
        try:
            evlue_prompt_token = llm_result.token_cost.get("prompt_tokens")
            evlue_completion_token = llm_result.token_cost.get("completion_tokens")
            llm_judge_result, failed_reason = parse_output_to_dict(llm_result.content)
            d["evlue_prompt_token"] = evlue_prompt_token
            d["evlue_completion_token"] = evlue_completion_token
            d["evlue_raw_llm_output"] = llm_result.content
            d["evlue_reason"] = llm_judge_result.get("reasons")

            if failed_reason:
                return 0, failed_reason
            return llm_judge_result.get("score"), llm_judge_result.get("reasons")
        except Exception as e:
            logger.error(
                f"判题的结果不符合要求，json 解析失败。{e}\n{traceback.format_exc()}"
            )
            return ({},)


async def score(d: dict) -> dict:
    d["Score"] = 0.0
    d["format_score"] = 0.0
    d["action_score"] = 0.0
    d["parameter_score"] = 0.0
    d["redundancy_score"] = 0.0
    d["invalid_reason"] = []

    score: float = 0  # 格式已经是合法的了
    json_str = d[
        "actual_answer_str"
    ]  # .replace("```json", "").replace("```", "").strip()
    try:
        json.loads(json_str)
        # 格式合法
        score += 1
        d["format_score"] = 1
    except json.JSONDecodeError:
        d["invalid_reason"].append("格式错误")
        d["format_score"] = 0
        logger.error(f"模型原始输出不合法，json 解析失败。{d['actual_answer_str']}")
        # 如果格式错误,使用大模型判断评分
        # TODO 这里需要修改，使用大模型判断评分的prompt，不需要模型判断格式，需要返回详细的得分detail_score
        from gpt_score_prompt import prompt_v1

        llm_result = await judge_all_by_llm(d, prompt_v1)
        if not llm_result:
            print(f"===========>>评分失败case: {d['caseID']} score: {d['Score']}")
            return d
        d["Score"] = llm_result["score"]
        d["invalid_reason"].extend(llm_result["reason"])
        d["action_score"] = llm_result["detail_score"]["action_score"]
        d["parameter_score"] = llm_result["detail_score"]["parameter_score"]
        d["redundancy_score"] = llm_result["detail_score"]["redundancy_score"]
        d["evlue_reason"] = llm_result["evlue_reason"]
        d["evlue_prompt"] = llm_result["prompt"]
        d["evlue_prompt_token"] = llm_result["evlue_prompt_token"]
        d["evlue_completion_token"] = llm_result["evlue_completion_token"]
        score += llm_result["score"]
        print(f"===========>>case 最终得分:case: {d['caseID']} score: {d['Score']}")
        return d

    all_score_msg = {}
    """{
    "4": {
        "reason": [],
        "detail_score": {}
        }
    }"""
    final_score = 0
    # 如果expected_answer有多个，先遍历所有预期答案是否有存在完全一致的，如果有则直接返回
    if len(d["expected_answer"]) > 1:
        for expected_answer in d["expected_answer"]:
            if d["actual_answer"] == expected_answer:
                d["Score"] = 4
                d["action_score"] = 1
                d["format_score"] = 1
                d["parameter_score"] = 1
                d["redundancy_score"] = 1
                return d
    for expected_answer in d["expected_answer"]:
        reason_list = []
        _score = 0
        _detail_score = {}
        # 如果原始输出是合法的json，并且和预期答案一致，则认为score为4
        if d["actual_answer"] == expected_answer:
            score = 4
            d["Score"] = score
            d["action_score"] = 1
            d["format_score"] = 1
            d["parameter_score"] = 1
            d["redundancy_score"] = 1
            return d
        if not d["actual_answer"]:
            logger.error(f"===========>case: {d['caseID']} actual_answer is empty")
            d["action_score"] = 0
            d["parameter_score"] = 0
            d["redundancy_score"] = 1
            d["Score"] = score + 1
            d["invalid_reason"].extend(["action错误", "参数错误"])
            return d
        actual_action_name = list(d["actual_answer"].keys())[0]
        expected_action_name = list(expected_answer.keys())[0]
        if actual_action_name == expected_action_name:
            _score += 1
            _detail_score["action_score"] = 1
        else:
            reason_list.append("action错误")

        actual_answer = (
            d["actual_answer"][actual_action_name]
            if d["actual_answer"][actual_action_name]
            else {}
        )
        expected_answer = expected_answer[expected_action_name]

        # 冗余检查
        redundancy_score, redundance_reason = await redundance_check(
            actual_answer, expected_answer
        )
        if redundancy_score == 0:
            reason_list.append(redundance_reason)
            _detail_score["redundancy_score"] = 0
        else:
            _score += 1
            _detail_score["redundancy_score"] = 1
        # 参数检查
        parameter_score, parameter_reason = await parameter_check(
            actual_answer, expected_answer, d
        )
        if parameter_score == 0:
            reason_list.append("参数错误")
            _detail_score["parameter_score"] = 0
        elif parameter_score == 0.5:
            reason_list.append("部分参数错误")
            _detail_score["parameter_score"] = 0.5
            _score += 0.5
        else:
            _score += 1
            _detail_score["parameter_score"] = 1
        # 得分计算
        reason_detail_score = {"reason": reason_list, "detail_score": _detail_score}
        all_score_msg[_score] = reason_detail_score
        print(
            f"===========>>case: {d['caseID']} : _score: {_score} reason_list: {reason_list}, detail_score: {_detail_score}"
        )
        final_score = max(final_score, _score)
        print(f"===========>>case: {d['caseID']} : final_score: {final_score}")
        if final_score == 3:  # 如果action正确 & 参数正确 & 无冗余，则跳出循环
            break
    d["invalid_reason"].extend(all_score_msg[final_score]["reason"])
    d["action_score"] = all_score_msg[final_score]["detail_score"].get(
        "action_score", 0
    )
    d["parameter_score"] = all_score_msg[final_score]["detail_score"].get(
        "parameter_score", 0
    )
    d["redundancy_score"] = all_score_msg[final_score]["detail_score"].get(
        "redundancy_score", 0
    )
    d["Score"] = final_score + score
    print(f"===========>>case 最终得分:case: {d['caseID']} score: {d['Score']}")
    return d


def add_record_to_feishu(file_name, table_name, record_list, folder_token):
    """创建新的飞书文档并添加记录"""
    try:
        from scripts.benchmark_plan_prompt.feishu_api import add_feishu_record

        if not record_list:
            logger.warning("record_list为空，跳过创建飞书文档")
            return "", ""

        result = add_feishu_record(file_name, table_name, record_list, folder_token)

        if result.get("status", False):
            url = result.get("url", "")
            file_id = result.get("file_id", "")
            logger.info(f"成功创建飞书文档: {file_name}, URL: {url}")
            return url, file_id
        else:
            error_msg = result.get("url", "创建失败")
            logger.error(f"创建飞书文档失败: {file_name}, {error_msg}")
            return "", ""
    except Exception as e:
        logger.error(f"调用飞书API异常: {e}\n{traceback.format_exc()}")
        return "", ""


def add_table_record_to_feishu(folder_token, table_name, record_list):
    """在现有的飞书文档下创建飞书表格里的table"""
    try:
        from scripts.benchmark_plan_prompt.feishu_api import add_feishu_record_by_table

        if not record_list:
            logger.warning("record_list为空，跳过创建飞书表格")
            return 1  # 返回非0值表示失败

        success, message = add_feishu_record_by_table(
            table_name, record_list, folder_token
        )
        if success:
            logger.info(f"成功创建飞书表格: {table_name}, {message}")
            return 0  # 成功返回0
        else:
            logger.error(f"创建飞书表格失败: {table_name}, {message}")
            return 1  # 失败返回非0值
    except Exception as e:
        logger.error(f"调用飞书API异常: {e}\n{traceback.format_exc()}")
        return 1  # 异常情况返回非0值


def stats_timecost_in_milliseconds_for_one_field(
    dictlist: list[dict],
    field: str,
    *,
    skip_zero: bool,
    original_unit: Literal["seconds", "milliseconds"],
    ndigits_to_round: int = 3,
) -> dict:
    timecost_list: list[float] = []
    for d in dictlist:
        try:
            timecost = float(d.get(field, 0.0))
            assert timecost >= 0 and timecost < float("inf"), (
                f"timecost must >= 0 and < inf, but got {timecost}"
            )
            timecost_list.append(timecost)
        except Exception as e:
            logger.warning(f"{e}")
            timecost_list.append(0.0)

    if skip_zero:
        timecost_list = [t for t in timecost_list if t > 0]

    if original_unit == "seconds":
        timecost_list = [t * 1000 for t in timecost_list]

    _len = len(timecost_list)
    _sum = 0.0
    _mean = 0.0
    _std = 0.0
    _max = 0.0
    _min = 0.0
    _p70 = 0.0
    _p90 = 0.0
    _p95 = 0.0
    if _len > 0:
        desc = pd.Series(timecost_list).describe([0.7, 0.9, 0.95]).to_dict()
        _mean = desc["mean"] if pd.notna(desc["mean"]) else 0.0
        _std = desc["std"] if pd.notna(desc["std"]) else 0.0
        _max = desc["max"] if pd.notna(desc["max"]) else 0.0
        _min = desc["min"] if pd.notna(desc["min"]) else 0.0
        _p70 = desc["70%"] if pd.notna(desc["70%"]) else 0.0
        _p90 = desc["90%"] if pd.notna(desc["90%"]) else 0.0
        _p95 = desc["95%"] if pd.notna(desc["95%"]) else 0.0
        _sum = _mean * _len
    return {
        "指标": field,
        "样本数量": _len,
        "总耗时(毫秒)": round(_sum, ndigits_to_round),
        "平均耗时(毫秒)": round(_mean, ndigits_to_round),
        "最大耗时(毫秒)": round(_max, ndigits_to_round),
        "最小耗时(毫秒)": round(_min, ndigits_to_round),
        "P70耗时(毫秒)": round(_p70, ndigits_to_round),
        "P90耗时(毫秒)": round(_p90, ndigits_to_round),
        "P95耗时(毫秒)": round(_p95, ndigits_to_round),
        "标准差(毫秒)": round(_std, ndigits_to_round),
    }


async def format_times_cost(output_cases):
    field_list = [
        "agent_core_call_select_action_llm_time",
        "call_agent_core_cost_time",
        "inner_agent_core_cost_time",
        "run_agent_response_total_elapsed_time",
        "query_rewrite_time",
        "knowledge_search_time",
        "knowledge_qa_total_time",
        "llm_api_total_timecost",
    ]
    return [
        stats_timecost_in_milliseconds_for_one_field(
            output_cases, field, skip_zero=False, original_unit="seconds"
        )
        for field in field_list
    ]


async def format_fields(output_cases):
    # 处理时间字段
    time_cost_list = await format_times_cost(output_cases)

    def safe_greater_than_zero(d: dict, field: str) -> bool:
        try:
            return d.get(field, 0) > 0
        except Exception:
            return False

    output_cases = [
        d for d in output_cases if safe_greater_than_zero(d, "total_cost(元)")
    ]
    if len(output_cases) == 0:
        token_cost_list = [
            {
                "指标": "费用信息",
                "样本数量": 0,
                "总prompt_token": 0,
                "总cached_token": 0,
                "总input_token": 0,
                "总output_token": 0,
                "总token": 0,
                "总费用(元)": 0,
                "单条case平均费用(元)": 0,
                "单条case最大费用(元)": 0,
                "单条case最小费用(元)": 0,
                "单条case费用P90(元)": 0,
                "单条case费用P95(元)": 0,
                "单条case费用P99(元)": 0,
                "单条case费用中位数(元)": 0,
                "评测脚本总费用(元)": 0,
            }
        ]
        return token_cost_list, time_cost_list
    #   计算token_total_cost费用,总计、平均、最大、最小、中位数、90%、95%、99%
    df = pd.DataFrame(output_cases)
    # TODO 添加token消耗数据
    total_token = float(round(df["total_tokens"].sum(), 2))
    total_cached_token = float(round(df["cached_tokens"].sum(), 2))
    total_output_token = float(round(df["completion_tokens"].sum(), 2))
    totalprompt_token = float(round(df["prompt_tokens"].sum(), 2))
    total_input_token = float(round(totalprompt_token - total_cached_token, 2))
    total_cost = float(round(df["total_cost(元)"].sum(), 6))
    avg_cost = float(round(df["total_cost(元)"].mean(), 6))
    max_cost = float(round(df["total_cost(元)"].max(), 6))
    min_cost = float(round(df["total_cost(元)"].min(), 6))
    median_cost = float(round(df["total_cost(元)"].median(), 6))
    ninety_cost = float(round(df["total_cost(元)"].quantile(0.9), 6))
    ninety_five_cost = float(round(df["total_cost(元)"].quantile(0.95), 6))
    ninety_nine_cost = float(round(df["total_cost(元)"].quantile(0.99), 6))
    total_case_count = len(output_cases)
    evlue_total_cost = float(round(df["evlue_total_cost(元)"].sum(), 6))

    # 创建一个只包含统计信息的新行
    #   把对应的key从第一列开始，顺序写，然后把value数据添加到对应
    token_cost_list = []
    #   添加一个空行,分割数据
    # cost_list.append({})
    # token_cost_list.append({
    #     "Case ID": "指标",
    #     "Query": "总费用(元)",
    #     "System Prompt": "单条case总费用(元)",
    #     "Robot Base Info": "",
    #     "Robot Realtime Info": "单条case最小费用(元)",
    #     "Screen Info": "单条case费用中位数(元)",
    #     "Examples": "单条case费用P90(元)",
    #     "User History Memory": "单条case费用P95(元)",
    #     "Chat Conversation": "单条case费用P99(元)",
    # })
    token_cost_list.append(
        {
            "指标": "费用信息",
            "样本数量": total_case_count,
            "总prompt_token": totalprompt_token,
            "总cached_token": total_cached_token,
            "总input_token": total_input_token,
            "总output_token": total_output_token,
            "总token": total_token,
            "总费用(元)": total_cost,
            "单条case平均费用(元)": avg_cost,
            "单条case最大费用(元)": max_cost,
            "单条case最小费用(元)": min_cost,
            "单条case费用P90(元)": ninety_cost,
            "单条case费用P95(元)": ninety_five_cost,
            "单条case费用P99(元)": ninety_nine_cost,
            "单条case费用中位数(元)": median_cost,
            "评测脚本总费用(元)": evlue_total_cost,
        }
    )
    #   合并费用到详细表格
    # output_cases.extend(new_row_list)
    return token_cost_list, time_cost_list


async def calculate_metrics(output_cases, final_score):
    n = len(output_cases)
    if n == 0:
        logger.warning("没有case数据，无法计算指标")
        return [], ""
    metrics = []

    # 计算reason为空的case数量
    all_correct_count = len([case for case in output_cases if case.get("Score") == 4])
    three_correct_count = len(
        [
            case
            for case in output_cases
            if case.get("parameter_score") == 1
            and case.get("action_score") == 1
            and case.get("format_score") == 1
        ]
    )
    all_correct_rate = round(all_correct_count / len(output_cases) * 100, 2)
    three_correct_rate = round(three_correct_count / len(output_cases) * 100, 2)

    #   计算action正确率
    action_correct_count = len(
        [case for case in output_cases if case.get("action_score") == 1]
    )
    action_correct_rate = round(action_correct_count / len(output_cases) * 100, 2)

    #   计算参数正确率
    parameter_correct_count = len(
        [case for case in output_cases if case.get("parameter_score") == 1]
    )
    parameter_correct_rate = round(parameter_correct_count / len(output_cases) * 100, 2)

    #   计算参数冗余率
    no_redundancy_count = len(
        [case for case in output_cases if case.get("redundancy_score") == 1]
    )
    no_redundancy_rate = round(no_redundancy_count / len(output_cases) * 100, 2)

    #   计算格式正确率
    format_correct_count = len(
        [case for case in output_cases if case.get("format_score") == 1]
    )
    format_correct_rate = round(format_correct_count / len(output_cases) * 100, 2)

    metrics.append({"指标": "总样本数", "值": len(output_cases)})

    metrics.append({"指标": "满分case数量", "值": all_correct_count})
    metrics.append({"指标": "3分case数量", "值": three_correct_count})

    metrics.append({"指标": "模型的能力得分", "值": f"{all_correct_rate}"})

    metrics.append({"指标": "用户最终满意度得分", "值": f"{three_correct_rate}"})

    metrics.append({"指标": "整体得分(百分制平均分)", "值": f"{final_score}"})

    metrics.append({})

    metrics.append({"指标": "格式正确case数量", "值": format_correct_count})
    metrics.append({"指标": "action正确case数量", "值": action_correct_count})

    metrics.append({"指标": "参数正确case数量", "值": parameter_correct_count})
    metrics.append({"指标": "无冗余case数量", "值": no_redundancy_count})

    metrics.append({})
    metrics.append({"指标": "action正确率", "值": f"{action_correct_rate}%"})
    metrics.append({"指标": "格式正确率", "值": f"{format_correct_rate}%"})
    metrics.append({"指标": "参数正确率", "值": f"{parameter_correct_rate}%"})

    metrics.append({"指标": "无冗余率", "值": f"{no_redundancy_rate}%"})

    # 统计“重试计为零分”的各项正确率
    metrics.append({})
    num_all_correct_first_round = len(
        [x for x in output_cases if x.get("retry", 0) == 0 and x.get("Score", 0) == 4]
    )
    all_correct_rate_first_round = round(num_all_correct_first_round / n * 100, 2)
    metrics.append(
        {"指标": "(重试计为零分)模型能力得分", "值": f"{all_correct_rate_first_round}%"}
    )

    num_three_correct_first_round = len(
        [
            x
            for x in output_cases
            if x.get("retry", 0) == 0
            and x.get("parameter_score", 0) == 1
            and x.get("action_score", 0) == 1
            and x.get("format_score", 0) == 1
        ]
    )
    three_correct_rate_first_round = round(num_three_correct_first_round / n * 100, 2)
    metrics.append(
        {
            "指标": "(重试计为零分)用户满意度得分",
            "值": f"{three_correct_rate_first_round}%",
        }
    )

    num_action_correct_first_round = len(
        [
            x
            for x in output_cases
            if x.get("retry", 0) == 0 and x.get("action_score", 0) == 1
        ]
    )
    action_correct_rate_first_round = round(num_action_correct_first_round / n * 100, 2)
    metrics.append(
        {
            "指标": "(重试计为零分)action正确率",
            "值": f"{action_correct_rate_first_round}%",
        }
    )

    num_parameter_correct_first_round = len(
        [
            x
            for x in output_cases
            if x.get("retry", 0) == 0 and x.get("parameter_score", 0) == 1
        ]
    )
    parameter_correct_rate_first_round = round(
        num_parameter_correct_first_round / n * 100, 2
    )
    metrics.append(
        {
            "指标": "(重试计为零分)参数正确率",
            "值": f"{parameter_correct_rate_first_round}%",
        }
    )

    num_format_correct_first_round = len(
        [
            x
            for x in output_cases
            if x.get("retry", 0) == 0 and x.get("format_score", 0) == 1
        ]
    )
    format_correct_rate_first_round = round(num_format_correct_first_round / n * 100, 2)
    metrics.append(
        {
            "指标": "(重试计为零分)格式正确率",
            "值": f"{format_correct_rate_first_round}%",
        }
    )

    num_no_redundancy_first_round = len(
        [
            x
            for x in output_cases
            if x.get("retry", 0) == 0 and x.get("redundancy_score", 0) == 1
        ]
    )
    no_redundancy_rate_first_round = round(num_no_redundancy_first_round / n * 100, 2)
    metrics.append(
        {"指标": "(重试计为零分)无冗余率", "值": f"{no_redundancy_rate_first_round}%"}
    )

    msg = f"""
2.整体分数
用户满意度得分: {three_correct_rate}%
模型能力得分: {all_correct_rate}%
加权平均得分: {final_score}%

(重试计为零分)用户满意度得分: {three_correct_rate_first_round}%
(重试计为零分)模型能力得分: {all_correct_rate_first_round}%


3.子项分数
格式正确率: {format_correct_rate}%
action正确率: {action_correct_rate}%
参数正确率: {parameter_correct_rate}%
无冗余率: {no_redundancy_rate}%

(重试计为零分)格式正确率: {format_correct_rate_first_round}%
(重试计为零分)action正确率: {action_correct_rate_first_round}%
(重试计为零分)参数正确率: {parameter_correct_rate_first_round}%
(重试计为零分)无冗余率: {no_redundancy_rate_first_round}%
    """
    return metrics, msg


if __name__ == "__main__":
    import asyncio

    def custom_to_bool(x) -> bool:
        first_char = x.lower()[0] if isinstance(x, str) and len(x) > 0 else ""
        if first_char in ["y", "t", "1"]:  # yes, true, 1, ...
            return True
        return False

    START_TIME = datetime.datetime.now().strftime(
        "%Y%m%d_%H%M%S"
    )  # 整个实验开始的时间，不可变

    logger.info(f"os.environ: {os.environ}")
    logger.info(f"agent_setting: {agent_setting}")
    region = agent_setting.region_version
    assert region in [Area.overseas, Area.domestic], (
        f"region: {region} 必须是 {Area.overseas} 或 {Area.domestic}"
    )
    logger.info(f"region: {region}")

    final_score = 0
    current_dir = os.path.dirname(os.path.abspath(__file__))

    parser = argparse.ArgumentParser(
        description="AgentOS效果评测脚本",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    # 并发数量参数化，不填默认为1
    parser.add_argument("-n", type=int, default=0, help="执行条数，为0则执行全部")
    parser.add_argument(
        "--only_run_these_cases",
        type=str,
        default="",
        help="只执行这些case，用英文逗号分隔的caseID列表，例如: “1,2,3,a,b,c”，如果为空则执行全部",
    )
    # parser.add_argument("-p", type=str, required=False, help="检查参数,n/y")
    parser.add_argument("-v", type=str, default="default", help="tag版本")
    parser.add_argument(
        "-f",
        type=str,
        default=REGION_TO_DEFAULT_CASE_FILENAME_STEM[region],
        help="默认的case文件名前缀",
    )
    parser.add_argument(
        "--custom_case_file",
        type=str,
        default="",
        help="自定义的case文件名，如果不为空，会覆盖-f参数",
    )
    parser.add_argument(
        "--custom_action_version",
        type=str,
        default="",
        help="自定义的action版本，如果不为空，会覆盖默认的action版本，请注意国内和海外的action版本不同",
    )
    parser.add_argument(
        "--experiment_id",
        type=str,
        default=START_TIME,
        help="实验id",
    )
    parser.add_argument(
        "--num_parallel_get_answer",
        type=int,
        default=1,
        help="多进程get_answer的进程数量，大于1时开启多进程，默认1",
    )
    parser.add_argument(
        "--num_parallel_get_judgment",
        type=int,
        default=1,
        help="多进程get_judgment的进程数量，大于1时开启多进程，默认1",
    )
    parser.add_argument(
        "--skip_prepare_dataset",
        action="store_true",
        default=False,
        help="是否跳过准备数据集",
    )
    parser.add_argument(
        "--skip_get_answer", action="store_true", default=False, help="是否跳过获取答案"
    )
    parser.add_argument(
        "--skip_get_judgment",
        action="store_true",
        default=False,
        help="是否跳过获取judgment",
    )
    parser.add_argument(
        "--skip_compute_statistics",
        action="store_true",
        default=False,
        help="是否跳过计算统计信息",
    )
    parser.add_argument(
        "--skip_send_to_feishu",
        action="store_true",
        default=False,
        help="是否跳过写入飞书多维表格",
    )
    parser.add_argument(
        "--skip_send_card_message",
        action="store_true",
        default=False,
        help="是否跳过发送飞书卡片消息",
    )
    parser.add_argument(
        "--disable_override_action_parameter_enum_value",
        action="store_true",
        default=False,
        help="是否禁用对action参数枚举值的覆盖",
    )
    parser.add_argument(
        "--report_filename_prefix",
        type=str,
        default="",
        help="报告文件名前缀",
    )
    parser.add_argument(
        "--not_save_intermediate_files",
        action="store_true",
        default=False,
        help="是否不保存中间文件",
    )
    parser.add_argument(
        "--robot_turn_on_clarify",
        type=str,
        default="yes",
        help="机器人澄清开关，yes/no, 默认yes",
    )
    parser.add_argument(
        "--robot_turn_on_confirm",
        type=str,
        default="yes",
        help="机器人重action开关，yes/no, 默认yes",
    )

    # 解析参数
    args = parser.parse_args()
    logger.info(f"args: {args}")
    experiment_id = args.experiment_id
    benchmark_count = args.n
    only_run_caseID_list = [
        x.strip() for x in args.only_run_these_cases.split(",") if x.strip()
    ]
    # is_check_parameter = args.p if args.p else "n"
    tag_version = args.v
    case_filename = (
        args.custom_case_file
        if args.custom_case_file
        else os.path.join(current_dir, f"{args.f}.xlsx")
    )
    if not Path(case_filename).is_file():
        raise FileNotFoundError(f"case文件不存在: {case_filename}")
    num_parallel_get_answer = max(1, args.num_parallel_get_answer)
    num_parallel_get_judgment = max(1, args.num_parallel_get_judgment)
    skip_prepare_dataset = args.skip_prepare_dataset
    skip_get_answer = args.skip_get_answer
    skip_get_judgment = args.skip_get_judgment
    skip_compute_statistics = args.skip_compute_statistics
    skip_send_to_feishu = args.skip_send_to_feishu
    skip_send_card_message = args.skip_send_card_message
    disable_override_action_parameter_enum_value = (
        args.disable_override_action_parameter_enum_value
    )
    report_filename_prefix = args.report_filename_prefix
    not_save_intermediate_files = args.not_save_intermediate_files
    robot_turn_on_clarify: bool = custom_to_bool(args.robot_turn_on_clarify)
    robot_turn_on_confirm: bool = custom_to_bool(args.robot_turn_on_confirm)

    action_version = (
        args.custom_action_version
        if args.custom_action_version
        else REGION_TO_DEFAULT_ACTION_VERSION[region]
    )
    logger.info(f"action_version: {action_version}")
    enterprise_id = REGION_TO_ENTERPRISE_ID[region]
    robot_language_code = REGION_TO_ROBOT_LANGUAGE_CODE[region]
    logger.info(
        f"enterprise_id: {enterprise_id}, robot_language_code: {robot_language_code}"
    )

    async def run_main():
        try:
            output_cases, error_messages = await main(
                experiment_id,
                case_filename,
                benchmark_count,
                action_version=action_version,
                enterprise_id=enterprise_id,
                robot_language_code=robot_language_code,
                num_parallel_get_answer=num_parallel_get_answer,
                num_parallel_get_judgment=num_parallel_get_judgment,
                only_run_caseID_list=only_run_caseID_list,
                skip_prepare_dataset=skip_prepare_dataset,
                skip_get_answer=skip_get_answer,
                skip_get_judgment=skip_get_judgment,
                disable_override_action_parameter_enum_value=disable_override_action_parameter_enum_value,
                not_save_intermediate_files=not_save_intermediate_files,
                robot_turn_on_clarify=robot_turn_on_clarify,
                robot_turn_on_confirm=robot_turn_on_confirm,
            )
        finally:
            # 确保所有未关闭的异步会话都被正确关闭
            await http_context._close_http_ctx()
        end_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        if error_messages:
            logger.error(f"error_messages: {error_messages}")
            return

        # step4/4 compute_statistics
        if all([skip_compute_statistics, skip_send_to_feishu, skip_send_card_message]):
            return

        final_score = round(
            float(sum([c.get("Score", 0) for c in output_cases]))
            / (len(output_cases) * 4)
            * 100,
            2,
        )
        token_cost_list, time_cost_list = await format_fields(output_cases)
        metrics, msg = await calculate_metrics(output_cases, final_score)

        excel_filename = os.path.join(
            current_dir,
            f"{report_filename_prefix}_plan_prompt_result_{agent_setting.plan_model}_{START_TIME}.xlsx"
            if report_filename_prefix
            else f"plan_prompt_result_{agent_setting.plan_model}_{START_TIME}.xlsx",
        )

        # 使用ExcelWriter创建一个Excel文件并写入多个sheet
        output_cases = DictlistUtils.convert_elements_to_str(
            output_cases, exclude_types=(int, float, bool)
        )
        with pd.ExcelWriter(excel_filename, engine="openpyxl") as writer:
            # 将原始详细数据写入"detail"sheet
            pd.DataFrame(output_cases).to_excel(
                writer, sheet_name="detail", index=False
            )

            # 将token成本数据写入"token_cost"sheet
            pd.DataFrame(token_cost_list).to_excel(
                writer, sheet_name="token_cost", index=False
            )

            # 将时间成本数据写入"time_cost"sheet
            pd.DataFrame(time_cost_list).to_excel(
                writer, sheet_name="time_cost", index=False
            )

            # 将指标数据写入"metrics"sheet
            pd.DataFrame(metrics).to_excel(
                writer, sheet_name="详细得分统计", index=False
            )

        print(f"结果已保存到: {excel_filename}")
        if all([skip_send_to_feishu, skip_send_card_message]):
            return
        file_name = (
            f"{report_filename_prefix}_{START_TIME}_AgentOS"
            if report_filename_prefix
            else f"{START_TIME}_AgentOS"
        )
        output_cases = DictlistUtils.keep_desired_fields(
            output_cases, DESIRED_FIELDS_TO_SEND_FEISHU
        )
        # 写详细数据到飞书
        url, file_id = add_record_to_feishu(
            file_name=file_name,
            table_name=tag_version,
            record_list=output_cases,
            folder_token="BKEWf3794llP5QdWJhVc1b8En6e",
        )
        logger.info(f"add_record_to_feishu, url: {url}, file_id: {file_id}")
        # 写token_cost_list,time_cost_list到飞书
        token_cost_response = add_table_record_to_feishu(
            folder_token=file_id, table_name="费用汇总表", record_list=token_cost_list
        )
        logger.info(
            f"add_table_record_to_feishu, 费用汇总表, token_cost_response: {token_cost_response}"
        )
        time_cost_response = add_table_record_to_feishu(
            folder_token=file_id, table_name="耗时汇总表", record_list=time_cost_list
        )
        logger.info(
            f"add_table_record_to_feishu, 耗时汇总表, time_cost_response: {time_cost_response}"
        )

        # 写指标数据到飞书
        metrics_response = add_table_record_to_feishu(  # noqa
            folder_token=file_id, table_name="指标汇总表", record_list=metrics
        )
        logger.info(
            f"add_table_record_to_feishu, 指标汇总表, metrics_response: {metrics_response}"
        )

        # 确保数值类型正确转换
        def safe_float(value):
            """安全地将值转换为float类型"""
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0
            else:
                return 0.0

        evlue_total_cost = round(
            safe_float(token_cost_list[0]["评测脚本总费用(元)"]), 2
        )
        system_token_cost = round(safe_float(token_cost_list[0]["总费用(元)"]), 2)
        all_cost = round(evlue_total_cost + system_token_cost, 2)
        select_action_p90_time = round(
            safe_float(
                [
                    x
                    for x in time_cost_list
                    if x["指标"] == "agent_core_call_select_action_llm_time"
                ][0].get("P90耗时(毫秒)", 0)
            ),
            2,
        )
        if not skip_send_card_message:
            if token_cost_response == 0 and time_cost_response == 0:
                await send_card_message(
                    title="AgentOS效果评测结果",
                    msg=f"\n1.环境信息\n当前版本信息: {tag_version}\n执行环境: {region}\nAction版本: {action_version}\n执行case数: {len(output_cases)}条\n使用模型: {agent_setting.plan_model}\n执行机器SN: {DEVICE_ID}\n\n{msg}\n\n4.其他信息\n系统token共计花费: {system_token_cost}元\n评测token共计花费: {evlue_total_cost}元\n总花费: {all_cost}元\n\n5.核心耗时\n选择Action P90耗时: {select_action_p90_time}ms\n\n6.时间信息\n开始时间: {START_TIME}\n结束时间: {end_time}\n",
                    down_url=url,
                    tag="\n点击跳转飞书查看详情",
                )
            else:
                await send_card_message(
                    title="AgentOS效果评测结果",
                    msg=f"\n当前部署版本信息:{tag_version}\n总共执行case数{len(output_cases)}条\n执行环境:{region}\nAction版本: {action_version}\n\n效果评测得分情况:{final_score}\n\n写入飞书失败！\n\n系统token共计花费:{system_token_cost}元\n评测token共计花费:{evlue_total_cost}元\n总花费:{all_cost}元\n\n开始时间:{START_TIME}\n结束时间:{end_time}\n",
                    down_url=url,
                    tag="\n点击跳转飞书查看详情",
                )

    asyncio.run(run_main())
