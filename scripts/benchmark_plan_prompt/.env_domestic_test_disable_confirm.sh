export ENV="test"
export REGION_VERSION="domestic"
export LIVEKIT_URL="wss://speech-rtc-dev.orionstar.com"
export LIVEKIT_API_KEY="orionstar_test"
export LIVEKIT_API_SECRET="IPa4ltevBiNF9u1wksEUGD6C3S0zAXyr"

export OPENAI_BASE_URL="http://proxy-ai.smartsales.vip/v1"
export OPENAI_API_KEY="***************************************************"

export TTS_BASE_URL="https://speech-bxm.ainirobot.com"
export TTS_TOKEN="eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Im9yaW9uLm92cy5jbGllbnQuMTUxNDI1OTUxMjQ3MSIsIm9wZW5JZCI6IjNhMWY3OWMzMzlkZmI5MWNjMmQ3OTMwODcxMjljZDY1Iiwic2NvcGUiOiJvdnM6YXBwIiwiaXNzIjoib3Jpb24iLCJleHAiOjE2NDUwOTUxNzMsInR5cGUiOiJhY2Nlc3NfdG9rZW4iLCJpYXQiOjE2NDUwODc5NzMsInZlcnNpb24iOiIxLjAifQ.1wGORkN5Ik2SC2pi1F_yRaHlFYQ-6UqiBxpOiUItZ2k"

export DIAGNOSTIC_HOST="https://speech-test.orionstar.com"
export DIAGNOSTIC_SECRET_KEY="d6487560941d4d2c8d58975d9e47c15c"

export ROBOT_OPENAPI_HOST="https://test-openapi.orionstar.com"
export ROBOT_OPENAPI_KEY="7ef1f8909e3da64c67f3e7e839070b6b"
export ROBOT_SUPPORT_MAP_POINTS_URL="http://resource-online-test.ainirobot.com/open/v1/resource/dict"

export PLAN_MODEL_BASE_URL="https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
export PLAN_MODEL_API_KEY="sk-2f99e60920ee4e22bde8fda877567b99"
export PLAN_MODEL="qwen-max"

export SUMMARY_MODEL_BASE_URL="https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
export SUMMARY_MODEL_API_KEY="sk-2f99e60920ee4e22bde8fda877567b99"
export SUMMARY_MODEL="qwen-max"

export GENERATE_TEXT_MODEL_BASE_URL="https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
export GENERATE_TEXT_MODEL_API_KEY="sk-2f99e60920ee4e22bde8fda877567b99"
export GENERATE_TEXT_MODEL="qwen-max"

export EMBEDDING_MODEL_BASE_URL="http://*************:8080/embed"
export EMBEDDING_MODEL_API_KEY="***************************************************"
export EMBEDDING_MODEL="bge"
export EMBEDDING_DIM="1024"
export MAX_BATCH_SIZE="32"
export FEW_SHOT_SCORE_THRESHOLD="0.45"

export TURBO_PLAN_MODEL_BASE_URL="https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
export TURBO_PLAN_MODEL_API_KEY="sk-2f99e60920ee4e22bde8fda877567b99"
export TURBO_PLAN_MODEL="qwen-max"
export TURBO_CHAT_MODEL="qwen-max"
export TURBO_GENERATE_TEXT_MODEL="qwen-max"

export FULL_POWER_PLAN_MODEL_BASE_URL="http://proxy-ai.smartsales.vip/v1"
export FULL_POWER_PLAN_MODEL_API_KEY="***************************************************"
export FULL_POWER_PLAN_MODEL="gpt-4o-2024-11-20"
export FULL_POWER_CHAT_MODEL="gpt-4o-2024-11-20"
export FULL_POWER_GENERATE_TEXT_MODEL="gpt-4o-2024-11-20"

export INTERVENE_QA_PAIR_URL="http://resource-online-test.ainirobot.com/open/v1/resource/qa_pair"
export INTERVENE_NUM_RETRIES_FOR_QA_PAIR="3"
export INTERVENE_POLISH_ANSWER_MODEL_BASE_URL="https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
export INTERVENE_POLISH_ANSWER_MODEL_API_KEY="sk-2f99e60920ee4e22bde8fda877567b99"
export INTERVENE_POLISH_ANSWER_MODEL_NAME="qwen-max"
export INTERVENE_POLISH_ANSWER_MODEL_MAX_COMPLETION_TOKENS="512"
export INTERVENE_POLISH_ANSWER_MODEL_TEMPERATURE="0.0"
export INTERVENE_POLISH_ANSWER_MODEL_REPETITION_PENALTY="1.0"
export INTERVENE_NUM_RETRIES_FOR_POLISH_ANSWER="1"
export INTERVENE_EMBEDDING_ENABLE_REDIS="True"

export AOS_STUDIO_HOST="http://aos-backend-test.orionstar.com/"
export AOS_STUDIO_BLOCK_ACTION_URL="/capi/v1/corp/gateway_agentos/block-actions/"
export AOS_STUDIO_WEBAPP_RESOURCE_URL="/capi/v1/corp/gateway_agentos/app_webapp/"

export ENABLE_SINGLE_ACTION_FEWSHOT="False"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_API_KEY="***************************************************"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL_BASE_URL="http://*************:8080/embed"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL="bge"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_DIM="1024"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_BATCH_SIZE="16"
export SINGLE_ACTION_FEWSHOT_EMBEDDING_ENABLE_REDIS="False"
export SINGLE_ACTION_FEWSHOT_QDRANT_HOST="*************"
export SINGLE_ACTION_FEWSHOT_QDRANT_INSERT_BATCH_SIZE="16"

export AGENT_CORE_BASE_URL="http://aos-core-test.orionstar.com/"
# export AGENT_CORE_BASE_URL="http://127.0.0.1:8000/"
# export AGENT_CORE_PATH="/api/v1/agent/run"

export TURN_ON_CONFIRM="False"
