import json
from copy import deepcopy


from loguru import logger


"""
对测试集文件中的“expected result”和“frozen_ActionDictList”字段进行相应的修改。

modify_cases.example.json 是修改的示例，请参考该文件的格式进行修改。

这里主要对于action名称、action参数名称的删除和重命名，用该脚本进行操作。

对于action名称和action参数名称的增加，当前脚本不支持，需要人工标注“expected result”和“frozen_ActionDictList”。

"""


# 在“expected result”中，action的结构
_action_example_in_expected_answer = {
    "OUTDOOR_NAVIGATE_START": {
        "origin": "首都机场",
        "destination": "王府井",
        "mode": "driving",
    }
}
# 在“frozen_ActionDictList”中，action的结构
_action_example_in_frozen_ActionDictList = {
    "name": "NAVIGATE_REC_START",
    "desc": "点位导航意图，当用户说'带我去XX'、'去XX'等导航需求时使用此动作，如果用户没有明确指定目的地，则返回最多4个可用的导航点位列表，并按照相似性排序，最靠近的点位在前面",
    "parameters": [
        {
            "name": "destinations",
            "type": "String array",
            "desc": "The user wants to navigate to a ranked list of recommended destinations (maximum 4 destinations),guide_text[True,string]:A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'",
            "is_required": False,
            "enum_constant": [
                "厕所",
                "测试点",
                "南极洲会议室",
                "休息区",
                "彦礼工位",
                "接待点",
                "电梯1",
                "晓曦工位",
                "拉斯维加斯会议室",
                "老板办公区",
                "回充点",
                "测试部",
                "会议室",
            ],
        }
    ],
}


def modify_action_param_in_one_expected_action(
    *,
    caseID: str,
    expected_action: dict,
    action_param_renames: dict,
    action_param_deletes: dict,
) -> dict:
    result = deepcopy(expected_action)
    for a, p_list in action_param_deletes.items():
        if a in result:
            for p in p_list:
                if p in result[a]:
                    result[a].pop(p)
                    logger.info(
                        f"caseID:{caseID}. 已为预期结果删除action参数:  action: {a} 参数: {p}"
                    )
    for a, p_dict in action_param_renames.items():
        if a in result:
            for old_p, new_p in p_dict.items():
                if old_p in result[a]:
                    result[a][new_p] = deepcopy(result[a][old_p])
                    result[a].pop(old_p)
                    logger.info(
                        f"caseID:{caseID}. 已为预期结果重命名action参数: action: {a} 参数: {old_p} -> {new_p}"
                    )
    return result


def modify_action_param_in_expected_answer(
    *,
    caseID: str,
    expected_answer: list[dict],
    action_param_renames: dict,
    action_param_deletes: dict,
) -> list[dict]:
    result = []
    for expected_action in expected_answer:
        result.append(
            modify_action_param_in_one_expected_action(
                caseID=caseID,
                expected_action=expected_action,
                action_param_renames=action_param_renames,
                action_param_deletes=action_param_deletes,
            )
        )
    return result


def modify_action_param_for_one_frozen_action(
    *,
    caseID: str,
    frozen_action: dict,
    action_param_renames: dict,
    action_param_deletes: dict,
) -> dict:
    result = deepcopy(frozen_action)
    for a, delete_p_name_list in action_param_deletes.items():
        if result["name"] == a:
            result["parameters"] = [
                p for p in result["parameters"] if p["name"] not in delete_p_name_list
            ]
            logger.info(
                f"caseID:{caseID}. 已为frozen_ActionDictList删除action参数: action: {a} 参数: {delete_p_name_list}"
            )
    for a, rename_p_name_map in action_param_renames.items():
        if result["name"] == a:
            parameters = result["parameters"]
            for p in parameters:
                p_name = p["name"]
                if p_name in rename_p_name_map:
                    new_p_name = rename_p_name_map[p_name]
                    p["name"] = new_p_name
                    logger.info(
                        f"caseID:{caseID}. 已为frozen_ActionDictList重命名action参数: action: {a} 参数: {p_name} -> {new_p_name}"
                    )
    return result


def modify_action_param_in_frozen_ActionDictList(
    *,
    caseID: str,
    frozen_ActionDictList: list[dict],
    action_param_renames: dict,
    action_param_deletes: dict,
) -> list[dict]:
    result = []
    for frozen_action in frozen_ActionDictList:
        result.append(
            modify_action_param_for_one_frozen_action(
                caseID=caseID,
                frozen_action=frozen_action,
                action_param_renames=action_param_renames,
                action_param_deletes=action_param_deletes,
            )
        )
    return result


def modify_action_name_in_expected_answer(
    *,
    caseID: str,
    expected_answer: list[dict],
    action_renames: dict,
    action_deletes: list[str],
) -> list[dict]:
    result = []
    for expected_action in expected_answer:
        action_name = list(expected_action.keys())[
            0
        ]  # NOTE: 约定第一个key是action_name
        if action_name in action_deletes:
            logger.info(f"caseID:{caseID}. 已为预期结果删除action: {action_name}")
            continue
        new_action = deepcopy(expected_action)
        if action_name in action_renames:
            new_action_name = action_renames[action_name]
            new_action = {new_action_name: deepcopy(expected_action[action_name])}
            logger.info(
                f"caseID:{caseID}. 已为预期结果重命名action: {action_name} -> {new_action_name}"
            )
        result.append(new_action)
    return result


def modify_action_name_in_frozen_ActionDictList(
    *,
    caseID: str,
    frozen_ActionDictList: list[dict],
    action_renames: dict,
    action_deletes: list[str],
) -> list[dict]:
    result = []
    for frozen_action in frozen_ActionDictList:
        action_name = frozen_action["name"]
        if action_name in action_deletes:
            logger.info(
                f"caseID:{caseID}. 已为frozen_ActionDictList删除action: {action_name}"
            )
            continue
        new_frozen_action = deepcopy(frozen_action)
        if action_name in action_renames:
            new_action_name = action_renames[action_name]
            new_frozen_action["name"] = new_action_name
            logger.info(
                f"caseID:{caseID}. 已为frozen_ActionDictList重命名action: {action_name} -> {new_action_name}"
            )
        result.append(new_frozen_action)
    return result


if __name__ == "__main__":
    import argparse
    from tqdm import tqdm

    from scripts.benchmark_plan_prompt.dictlist_utils import DictlistUtils
    from scripts.benchmark_plan_prompt.parse_utils import parse_expected_answer

    parser = argparse.ArgumentParser(
        description="modify_cases",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--input_case_filename", type=str, required=True, help="输入的case文件名"
    )
    parser.add_argument(
        "--output_case_filename", type=str, required=True, help="输出的case文件名"
    )
    parser.add_argument(
        "--input_json_filename", type=str, required=True, help="输入的json文件名"
    )
    parser.add_argument(
        "--case_id_field_name", type=str, default="caseID", help="caseID字段名"
    )
    parser.add_argument(
        "--expected_answer_field_name",
        type=str,
        default="expected result",
        help="expected result字段名",
    )
    parser.add_argument(
        "--frozen_ActionDictList_field_name",
        type=str,
        default="frozen_ActionDictList",
        help="frozen_ActionDictList字段名",
    )

    args = parser.parse_args()
    logger.info(f"args: {args}")

    input_case_filename = args.input_case_filename
    output_case_filename = args.output_case_filename
    input_json_filename = args.input_json_filename
    case_id_field_name = args.case_id_field_name
    expected_answer_field_name = args.expected_answer_field_name
    frozen_ActionDictList_field_name = args.frozen_ActionDictList_field_name

    with open(input_json_filename, "r") as f:
        modify_cases_json = json.load(f)
    action_param_renames = modify_cases_json.get("action_param_renames", {})
    action_param_deletes = modify_cases_json.get("action_param_deletes", {})
    action_renames = modify_cases_json.get("action_renames", {})
    action_deletes = modify_cases_json.get("action_deletes", [])

    dictlist = DictlistUtils.read_dictlist(input_case_filename)
    logger.info(f"modify_cases start: {input_case_filename} -> {output_case_filename}")
    for d in tqdm(dictlist, desc="modify_cases", total=len(dictlist)):
        caseID = d[case_id_field_name]
        expected_answer = parse_expected_answer(caseID, d[expected_answer_field_name])
        expected_answer = modify_action_param_in_expected_answer(
            caseID=caseID,
            expected_answer=expected_answer,
            action_param_renames=action_param_renames,
            action_param_deletes=action_param_deletes,
        )
        expected_answer = modify_action_name_in_expected_answer(
            caseID=caseID,
            expected_answer=expected_answer,
            action_renames=action_renames,
            action_deletes=action_deletes,
        )
        d[expected_answer_field_name] = expected_answer

        frozen_ActionDictList = json.loads(d[frozen_ActionDictList_field_name])
        frozen_ActionDictList = modify_action_param_in_frozen_ActionDictList(
            caseID=caseID,
            frozen_ActionDictList=frozen_ActionDictList,
            action_param_renames=action_param_renames,
            action_param_deletes=action_param_deletes,
        )
        frozen_ActionDictList = modify_action_name_in_frozen_ActionDictList(
            caseID=caseID,
            frozen_ActionDictList=frozen_ActionDictList,
            action_renames=action_renames,
            action_deletes=action_deletes,
        )
        d[frozen_ActionDictList_field_name] = frozen_ActionDictList
    DictlistUtils.write_dictlist(
        filename=output_case_filename,
        dictlist=dictlist,
        need_convert_elements_to_str=True,
        output_type="excel",
    )
    logger.info(f"modify_cases done: {input_case_filename} -> {output_case_filename}")
