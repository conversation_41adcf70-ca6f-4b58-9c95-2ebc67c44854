import requests
import json


async def send_card_message(down_url="", msg="\n请查看:", title="AgentOS效果评测得分", tag=""):
    """
    发送飞书卡片消息
    :param title:
    :param down_url:
    :param msg:
    :param tag:
    :return:
    """

    tag = "点击查看飞书链接"
    # 测试组监控
    # url = "https://open.feishu.cn/open-apis/bot/v2/hook/b611f11b-63de-48c5-9964-9268a69376e0"
    # 产研群
    url = "https://open.feishu.cn/open-apis/bot/v2/hook/0593f94f-bc3d-48d3-9f1d-adec97b49942"
    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    content = [
        # {"tag": "text", "text": at_msg},
        #            {"tag": "at", "user_id": "d4cg61de"},
        #            {"tag": "at", "user_id": "489bf19b"},
        #            {"tag": "at", "user_id": "8c669gcd"},
        {"tag": "text", "text": msg},
        {"tag": "a", "href": down_url, "text": tag}
    ]

    payload_message = {
        "msg_type": "post",
        "content": {
            "post": {
                "zh_cn": {
                    "title": title,
                    "content": [
                        content
                    ]
                }
            }
        }
    }
    response = requests.post(url=url, data=json.dumps(
        payload_message), headers=headers)
    return response.json
