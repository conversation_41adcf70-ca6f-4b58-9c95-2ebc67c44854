#!/usr/bin/env python3

import os
import sys
import subprocess
import datetime
from pathlib import Path
import fire
from loguru import logger


def run(
    env_shell_script: str,
    num_cases: int = 0,
    send_card_message: str = "",
    custom_case_file: str = "",
    custom_action_version: str = "",
    only_run_these_cases: str = "",
    report_filename_prefix: str = "",
    num_parallel_get_judgment: int = 1,
    robot_turn_on_clarify: str = "yes",
    robot_turn_on_confirm: str = "yes",
):
    """
    Run the benchmark plan prompt script.

    Args:
        env_shell_script: Environment variable configuration script
        num_cases: Number of cases to execute (0 means execute all cases)
        send_card_message: Whether to send card message, "true" means send, otherwise not
        custom_case_file: Custom case file, if not empty, will override the default case file
        custom_action_version: Custom action version, if not empty, will override the default action version
        only_run_these_cases: 只执行这些case，用英文逗号分隔的caseID列表，例如: “1,2,3,a,b,c”，如果为空则执行全部
        report_filename_prefix: 报告文件名前缀
        num_parallel_get_judgment: 并行获取judgment的线程数
        robot_turn_on_clarify: 机器人澄清开关，yes/no, 默认yes
        robot_turn_on_confirm: 机器人重action开关，yes/no, 默认yes
    """
    env_shell_script = os.path.abspath(env_shell_script)
    custom_case_file = os.path.abspath(custom_case_file) if custom_case_file else ""
    logger.info(f"env_shell_script: {env_shell_script}")
    logger.info(f"num_cases: {num_cases}")
    logger.info(f"send_card_message: {send_card_message}")
    logger.info(f"custom_case_file: {custom_case_file}")
    logger.info(f"only_run_these_cases: {only_run_these_cases}")
    logger.info(f"report_filename_prefix: {report_filename_prefix}")
    logger.info(f"num_parallel_get_judgment: {num_parallel_get_judgment}")
    logger.info(f"robot_turn_on_clarify: {robot_turn_on_clarify}")
    logger.info(f"robot_turn_on_confirm: {robot_turn_on_confirm}")

    if not os.path.exists(env_shell_script):
        logger.error(f"环境变量配置脚本不存在: {env_shell_script}")
        sys.exit(1)

    skip_send_card_message = ""
    if send_card_message.lower() != "true":
        skip_send_card_message = "--skip_send_card_message"
        logger.warning("将跳过发送卡片消息")

    if custom_case_file:
        if not os.path.exists(custom_case_file):
            logger.error(f"自定义case文件不存在: {custom_case_file}")
            sys.exit(1)
        logger.info(f"使用自定义case文件: {custom_case_file}")
    else:
        logger.info("没有指定自定义case文件")

    # Get git information
    commit_short_id = subprocess.check_output(
        "git log | head -n 1 | awk '{print $2}' | head -c 8",
        shell=True,
        executable="/bin/bash",
        text=True,
    ).strip()
    logger.info(f"当前commit id（头8位）：{commit_short_id}")

    commit_author = subprocess.check_output(
        "git show -s --format='%an' HEAD", shell=True, executable="/bin/bash", text=True
    ).strip()
    logger.info(f"提交作者: {commit_author}")

    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    logger.info(f"当前日期 (YYYY-MM-DD): {current_date}")

    version = f"{commit_short_id}_{commit_author}_{current_date}"
    logger.info(f"当前拼接的版本信息：{version}")

    logger.info("=== 执行命令 ===")

    # Get script directory and set up Python path
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir.parent.parent)
    os.environ["PYTHONPATH"] = f"{os.environ.get('PYTHONPATH', '')}:{os.getcwd()}"

    # Run the main Python script
    cmd = f"source {env_shell_script} && python3 {script_dir / 'main.py'} -v {version} -n {num_cases} {skip_send_card_message} --num_parallel_get_judgment {num_parallel_get_judgment} --robot_turn_on_clarify {robot_turn_on_clarify} --robot_turn_on_confirm {robot_turn_on_confirm}"
    if custom_case_file:
        cmd += f" --custom_case_file {custom_case_file}"
    if custom_action_version:
        cmd += f" --custom_action_version {custom_action_version}"
    if only_run_these_cases:
        # NOTE: fire will convert the comma separated string to a tuple, so we need to join it back to a string
        only_run_these_cases = (
            ",".join([str(x) for x in only_run_these_cases])
            if isinstance(only_run_these_cases, tuple)
            else only_run_these_cases
        )
        cmd += f" --only_run_these_cases {only_run_these_cases}"
    if report_filename_prefix:
        cmd += f" --report_filename_prefix {report_filename_prefix}"
    subprocess.run(cmd, shell=True, executable="/bin/bash")


if __name__ == "__main__":
    fire.Fire(run)
