# 2025-07-21 15:25:51:06456 |INFO |job_proc:25:MainThread:139624260995968 |src.assistant.planner.single_action:_async_plan_text:single_action.py:208 - 过渡话术 Prompt: [{'role': 'system', 'content': 'You are a helpful assistant. Your language style is 简明，可爱，幽默，对话感，互动性'}, {'role': 'user', 'content': '你是一个机器人，你需要完成两个步骤的任务：\n\n## 步骤1：判断用户输入类型\n根据以下标准，结合用户的语音输入和对话历史，判断用户的语音输入是"闲聊内容"还是"任务导向请求"。\n\n### 闲聊内容的特征：\n- 社交性问候：问好、告别、礼貌性寒暄，比如：早上好、你好、再见等\n- 情感表达：感叹、赞美、抱怨、情绪分享\n- 询问个人信息：比如姓名、年龄等\n- 不完整或模糊的表达：单词片段、语义不清、上下文不足\n\n### 任务导向请求的特征：\n- 信息获取需求：用户想要了解、查询、获得具体信息，天气查询、周边推荐等\n- 明确的功能调用：用户清楚知道想要什么服务或功能，如导航请求（带我去xx）\n- 娱乐功能请求：用户要求唱歌、跳舞、表演、播放音乐、讲故事、讲笑话等功能\n\n## 步骤2：生成transition_phrase（仅针对任务导向请求）\n如果判断为任务导向请求，则生成transition_phrase，要求：\n- 富有创意个性，口语化、生动、自然的一个陈述句，避免官方套话，接近15个字\n- 不要重复用户的语音输入\n- 不进行用户问题的澄清，不提问，因为后续环节会进行澄清\n- 保持过场话术为陈述句，不要使用疑问句，此环节不需要用户回答\n- **使用语言：英语回复，只使用纯文本，句号和逗号。不能使用任何超链接和表情。**\n\n### 过场话术举例\n- "马上帮你探探老天爷的脾气"\n- "我去问问云朵今天的安排"\n- "好嘞，咱们这就出发"\n- "路线规划师上线啦"\n- "让我当回你的专属向导"\n- "搜索雷达启动中"\n- "好歌正在路上"\n\n## 对话历史\n<Robot> said \'大家好！ 我是小豹， 一位来自猎户星空旗下的AI助手。很高兴能在这里与您相遇！ 我不仅能听懂您的话，还能理解您的需求， 是您工作和生活的得力助手。通过先进的自然语言处理技术， 我可以为您提供智能对话、信息查询、任务协助等多样化服务。让我们一起探索人工智能的精彩世界， 创造更智能、更便捷的未来！\'\n\n## 用户语音输入：Show me around.\n\n## 输出格式\n请严格按照以下JSON格式输出：\n{"is_chitchat": true/false, "transition_phrase": "如果is_chitchat为true则为空字符串，否则生成语言为英语的过渡话术"}\n直接输出JSON'}]
import aiohttp


messages = [
    {
        "role": "system",
        "content": "You are a helpful assistant. Your language style is 简明，可爱，幽默，对话感，互动性",
    },
    {
        "role": "user",
        "content": '你是一个机器人，你需要完成两个步骤的任务：\n\n## 步骤1：判断用户输入类型\n根据以下标准，结合用户的语音输入和对话历史，判断用户的语音输入是"闲聊内容"还是"任务导向请求"。\n\n### 闲聊内容的特征：\n- 社交性问候：问好、告别、礼貌性寒暄，比如：早上好、你好、再见等\n- 情感表达：感叹、赞美、抱怨、情绪分享\n- 询问个人信息：比如姓名、年龄等\n- 不完整或模糊的表达：单词片段、语义不清、上下文不足\n\n### 任务导向请求的特征：\n- 信息获取需求：用户想要了解、查询、获得具体信息，天气查询、周边推荐等\n- 明确的功能调用：用户清楚知道想要什么服务或功能，如导航请求（带我去xx）\n- 娱乐功能请求：用户要求唱歌、跳舞、表演、播放音乐、讲故事、讲笑话等功能\n\n## 步骤2：生成transition_phrase（仅针对任务导向请求）\n如果判断为任务导向请求，则生成transition_phrase，要求：\n- 富有创意个性，口语化、生动、自然的一个陈述句，避免官方套话，接近15个字\n- 不要重复用户的语音输入\n- 不进行用户问题的澄清，不提问，因为后续环节会进行澄清\n- 保持过场话术为陈述句，不要使用疑问句，此环节不需要用户回答\n- **使用语言：英语回复，只使用纯文本，句号和逗号。不能使用任何超链接和表情。**\n\n### 过场话术举例\n- "马上帮你探探老天爷的脾气"\n- "我去问问云朵今天的安排"\n- "好嘞，咱们这就出发"\n- "路线规划师上线啦"\n- "让我当回你的专属向导"\n- "搜索雷达启动中"\n- "好歌正在路上"\n\n## 对话历史\n<Robot> said \'大家好！ 我是小豹， 一位来自猎户星空旗下的AI助手。很高兴能在这里与您相遇！ 我不仅能听懂您的话，还能理解您的需求， 是您工作和生活的得力助手。通过先进的自然语言处理技术， 我可以为您提供智能对话、信息查询、任务协助等多样化服务。让我们一起探索人工智能的精彩世界， 创造更智能、更便捷的未来！\'\n\n## 用户语音输入：Show me around.\n\n## 输出格式\n请严格按照以下JSON格式输出：\n{"is_chitchat": true/false, "transition_phrase": "如果is_chitchat为true则为空字符串，否则生成语言为英语的过渡话术"}\n直接输出JSON',
    },
]


async def main():
    aiohttp_client = aiohttp.ClientSession()
    async with aiohttp_client as session:
        async with session.post(
            "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers={
                "Authorization": "Bearer sk-2f99e60920ee4e22bde8fda877567b99",
                "Content-Type": "application/json",
            },
            json={
                "model": "qwen-turbo",
                "messages": messages,
                "temperature": 0.7,
            },
        ) as response:
            print(await response.json())


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
