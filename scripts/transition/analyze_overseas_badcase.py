#!/usr/bin/env python3
"""
海外版本 badcase 分析
"""

import pandas as pd
from collections import Counter


def analyze_overseas_badcase():
    """分析海外版本的badcase"""
    df = pd.read_csv("real_user_transition_test_results.csv")

    # 筛选海外版本
    overseas_df = df[df["region"] == "overseas"]

    # 转换预测结果
    overseas_df = overseas_df.copy()
    overseas_df["predicted"] = overseas_df["response"].apply(
        lambda x: "无" if x == "##" else "有"
    )
    overseas_df["expected"] = overseas_df["expected_need_transition"]

    print("=" * 80)
    print("🌍 海外版本 (Gemini) Badcase 分析")
    print("=" * 80)

    total = len(overseas_df)
    correct = (overseas_df["predicted"] == overseas_df["expected"]).sum()
    accuracy = correct / total * 100

    print(f"海外版本总体情况: {accuracy:.1f}% ({correct}/{total})")
    print()

    # 假阳性分析 (期望无，但预测有)
    false_positives = overseas_df[
        (overseas_df["expected"] == "无") & (overseas_df["predicted"] == "有")
    ]
    print(f"🔴 假阳性 (不该生成过渡话术但生成了): {len(false_positives)}")

    if len(false_positives) > 0:
        print("\n主要假阳性案例:")
        for i, (_, row) in enumerate(false_positives.iterrows()):
            if i >= 20:  # 只显示前20个
                print(f"  ... 还有 {len(false_positives) - 20} 个案例")
                break
            response_preview = row["response"][:60] + (
                "..." if len(row["response"]) > 60 else ""
            )
            print(f'  {i + 1:2d}. "{row["query"]}" -> "{response_preview}"')

    # 假阴性分析 (期望有，但预测无)
    false_negatives = overseas_df[
        (overseas_df["expected"] == "有") & (overseas_df["predicted"] == "无")
    ]
    print(f"\n🟡 假阴性 (该生成过渡话术但没生成): {len(false_negatives)}")

    if len(false_negatives) > 0:
        print("\n主要假阴性案例:")
        for i, (_, row) in enumerate(false_negatives.iterrows()):
            print(f'  {i + 1:2d}. "{row["query"]}"')

    print()

    # 分析假阳性的查询模式
    print("🔍 假阳性查询模式分析:")
    fp_queries = false_positives["query"].tolist()

    # 按查询类型分类
    categories = {
        "问候类": ["你好", "hello", "Hello", "下午好", "嗨"],
        "简单表达": ["是啊", "好的", "谢谢", "可以", "然后", "不知道", "没有"],
        "模糊指令": ["开始", "这个", "那个", "什么", "为什么"],
        "问题询问": ["多少", "什么时候", "哪里", "如何", "干嘛", "什么"],
        "单词/短语": ["关机", "定位", "水母馆", "出口", "厕所"],
        "其他": [],
    }

    for query in fp_queries:
        categorized = False
        for category, keywords in categories.items():
            if category == "其他":
                continue
            if any(keyword in query for keyword in keywords):
                categories[category].append(query)
                categorized = True
                break
        if not categorized:
            categories["其他"].append(query)

    for category, queries in categories.items():
        if queries:
            unique_queries = list(set(queries))  # 去重
            if category in ["问候类", "简单表达", "模糊指令", "问题询问", "单词/短语"]:
                count = len(
                    [q for q in fp_queries if any(kw in q for kw in unique_queries)]
                )
            else:
                count = len(unique_queries)
            print(f"  {category}: {count} 个")
            if count <= 5:  # 如果数量不多，显示具体例子
                examples = unique_queries[:3]
                print(f"    示例: {', '.join(examples)}")

    print()

    # 分析响应长度
    print("📏 假阳性响应长度分析:")
    fp_response_lengths = false_positives["response"].str.len()
    print(f"  平均长度: {fp_response_lengths.mean():.1f} 字符")
    print(f"  最短响应: {fp_response_lengths.min()} 字符")
    print(f"  最长响应: {fp_response_lengths.max()} 字符")

    # 找出最常见的错误响应模式
    print("\n🔤 常见错误响应模式:")
    responses = false_positives["response"].tolist()

    # 提取响应开头的模式
    response_starts = []
    for resp in responses:
        if len(resp) > 0:
            # 取前10个字符作为开头模式
            start = resp[:10].strip()
            if start and start != "##":
                response_starts.append(start)

    start_counter = Counter(response_starts)
    for start, count in start_counter.most_common(10):
        print(f'  "{start}...": {count} 次')

    print()
    print("💡 主要问题总结:")
    print("1. 对简单问候和表达过度响应")
    print("2. 对模糊不清的表达强行生成过渡话术")
    print("3. 对单纯的信息询问也生成过渡话术")
    print("4. 可能需要调整prompt，提高判断的精确性")


if __name__ == "__main__":
    analyze_overseas_badcase()
