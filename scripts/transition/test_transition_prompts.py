#!/usr/bin/env python3
"""
过渡话术prompt测试脚本
"""

import asyncio
import os
import sys
import time
import pandas as pd
import csv
from typing import List, Dict, Optional
from dataclasses import dataclass

# 添加项目根目录到path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", ".."))

from src.assistant.planner.transition_prompts import TransitionPrompts
from src.common.constant import Area
from src.settings import agent_setting
from src.utils.llm import LLMManager, LLMConfig, ModelResult, parse_output_to_dict

# GOOGLE_APPLICATION_CREDENTIALS
# 获取当前脚本所在目录
json_file_path = (
    "/Users/<USER>/Codes/livekit-demo/easyNLP/src/orionstar-ai-gemini-488de8d2fe77.json"
)
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = json_file_path


@dataclass
class TestCase:
    """测试用例"""

    query: str
    expected_need_transition: Optional[str] = None  # 有/无
    skill_type: Optional[str] = None  # 技能类型
    is_valid_query: Optional[str] = None  # 是/否
    source: str = "manual"  # manual/skill_test/real_user


@dataclass
class TestResult:
    """测试结果"""

    query: str
    model: str
    prompt: str
    raw_output: str  # 原始LLM输出
    is_chitchat: Optional[bool] = None  # 模型判断是否是闲聊
    transition_phrase: str = ""  # 过渡话术
    expected_is_chitchat: Optional[bool] = None  # 预期是否是闲聊
    is_correct: Optional[bool] = None  # 是否正确
    error: Optional[str] = None
    elapsed_time: float = 0.0
    region: str = "domestic"
    source: str = "manual"


class TestLLMManager:
    """测试用的LLM调用管理器"""

    @staticmethod
    async def call_qwen_turbo(messages: List[Dict]) -> str:
        """调用qwen-turbo模型"""
        try:
            # 构建qwen-turbo的LLMConfig
            llm_config = LLMConfig(
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                llm_model_name="qwen-turbo",
                api_key=agent_setting.guest_text_model_api_key,
                temperature=0.7,
                max_tokens=50,
                timeout=10,
                repetition_penalty=1.0,
            )

            result: ModelResult = await LLMManager.invoke_model_from_llm_config(
                messages=messages,
                llm_config=llm_config,
            )

            return result.content or "（无响应内容）"

        except Exception as e:
            return f"调用失败: {e}"

    @staticmethod
    async def call_gemini(messages: List[Dict]) -> str:
        """调用gemini模型"""
        try:
            # 构建gemini的LLMConfig
            llm_config = LLMConfig(
                base_url="",  # Gemini不需要base_url
                llm_model_name="gemini-2.5-flash-lite-preview-06-17",
                api_key="",  # Gemini通过其他方式认证
                temperature=0.7,
                max_tokens=50,
                timeout=10,
            )

            result: ModelResult = await LLMManager.invoke_model_from_llm_config(
                messages=messages,
                llm_config=llm_config,
            )

            return result.content or "（No response content）"

        except Exception as e:
            return f"Call failed: {e}"


class DatasetLoader:
    """测试集加载器"""

    @staticmethod
    def load_skill_dataset(file_path: str = "技能测试集.xlsx") -> List[TestCase]:
        """加载技能测试集"""
        try:
            df = pd.read_excel(file_path, engine="openpyxl")
            test_cases = []

            for _, row in df.iterrows():
                test_case = TestCase(
                    query=str(row["用户表达语句"]),
                    expected_need_transition=str(row["有无过场话术"]),
                    skill_type=str(row["技能"]),
                    source="skill_test",
                )
                test_cases.append(test_case)

            return test_cases

        except Exception:
            return []

    @staticmethod
    def load_real_user_dataset(
        file_path: str = "valid_queries_only.csv",
    ) -> List[TestCase]:
        """加载真实用户测试集"""
        try:
            test_cases = []

            with open(file_path, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                for row in reader:
                    test_case = TestCase(
                        query=row["query"],
                        expected_need_transition=row["是否需要过场话术"],
                        is_valid_query=row["是否是有效query"],
                        source="real_user",
                    )
                    test_cases.append(test_case)

            return test_cases

        except Exception:
            return []


class TransitionPromptsTest:
    """过渡话术测试类"""

    def __init__(self, use_real_llm: bool = False, mock_mode: bool = True):
        self.use_real_llm = use_real_llm
        self.mock_mode = mock_mode
        self.results: List[TestResult] = []

        # 手动测试用例
        self.manual_test_cases = [
            TestCase("帮我查一下明天的天气", source="manual"),
            TestCase("我想知道北京到上海的机票价格", source="manual"),
            TestCase("请帮我搜索一下苹果手机的最新价格", source="manual"),
            TestCase("给我讲个笑话吧", source="manual"),
            TestCase("你好呀，今天心情怎么样？", source="manual"),
            TestCase(
                "Can you help me find the weather forecast for tomorrow?",
                source="manual",
            ),
            TestCase("Tell me a joke please", source="manual"),
            TestCase("What's your favorite color?", source="manual"),
        ]

    async def test_all_datasets(self):
        """测试所有数据集"""
        # 加载数据集
        skill_cases = DatasetLoader.load_skill_dataset()
        real_user_cases = DatasetLoader.load_real_user_dataset()

        # 合并所有测试用例
        all_test_cases = []
        all_test_cases.extend(self.manual_test_cases)
        all_test_cases.extend(skill_cases)
        all_test_cases.extend(real_user_cases)

        # 为每个测试用例测试两个版本
        for i, test_case in enumerate(all_test_cases):
            # 测试国内版本
            await self._test_case_for_region(
                test_case, Area.domestic, "qwen-turbo", "中文"
            )

            # 测试海外版本
            await self._test_case_for_region(
                test_case, Area.overseas, "gemini", "English"
            )

            # 每10个用例暂停一下，避免频率过高
            if (i + 1) % 10 == 0:
                await asyncio.sleep(1)

        # 输出统计结果
        self._print_statistics()

    async def test_real_user_dataset_only(self, limit=None):
        """只测试真实用户数据集"""
        print("\n" + "=" * 100)
        print("开始测试真实用户数据集 - 海外和国内版本")
        print("=" * 100)

        # 只加载真实用户数据集
        real_user_cases = DatasetLoader.load_real_user_dataset()

        # 如果指定了限制数量，则只取前N个
        if limit:
            real_user_cases = real_user_cases[:limit]
            print(f"🔹 限制测试数量: {limit}")

        print(f"真实用户测试用例数: {len(real_user_cases)}")
        print(
            f"每个用例会测试2个版本（国内+海外），总共需要: {len(real_user_cases) * 2} 次LLM调用"
        )
        print()

        # 为每个测试用例测试两个版本
        for i, test_case in enumerate(real_user_cases):
            current_progress = i + 1
            total_cases = len(real_user_cases)

            print(f"\n{'=' * 80}")
            print(
                f"进度: {current_progress}/{total_cases} ({current_progress / total_cases * 100:.1f}%)"
            )
            print(f"当前查询: {test_case.query}")
            print(f"期望过场话术: {test_case.expected_need_transition}")
            print(f"数据源: {test_case.source}")
            print("=" * 80)

            # 测试国内版本
            print("🇨🇳 测试国内版本 (qwen-turbo)...")
            await self._test_case_for_region(
                test_case, Area.domestic, "qwen-turbo", "中文"
            )

            # 测试海外版本
            print("🌍 测试海外版本 (gemini)...")
            await self._test_case_for_region(
                test_case, Area.overseas, "gemini", "English"
            )

            print(f"✅ 完成第 {current_progress} 个用例测试")

            # 每10个用例暂停一下，避免频率过高
            if current_progress % 10 == 0:
                await asyncio.sleep(1)

        # 输出统计结果
        self._print_statistics()

    async def test_skill_dataset_only(self):
        """只测试技能数据集"""
        print("\n" + "=" * 100)
        print("开始测试技能数据集 - 海外和国内版本")
        print("=" * 100)

        # 只加载技能数据集
        skill_cases = DatasetLoader.load_skill_dataset()

        print(f"技能测试用例数: {len(skill_cases)}")
        print(
            f"每个用例会测试2个版本（国内+海外），总共需要: {len(skill_cases) * 2} 次LLM调用"
        )
        print()

        # 为每个测试用例测试两个版本
        for i, test_case in enumerate(skill_cases):
            current_progress = i + 1
            total_cases = len(skill_cases)

            print(f"\n{'=' * 80}")
            print(
                f"进度: {current_progress}/{total_cases} ({current_progress / total_cases * 100:.1f}%)"
            )
            print(f"当前查询: {test_case.query}")
            print(f"技能类型: {test_case.skill_type}")
            print(f"期望过场话术: {test_case.expected_need_transition}")
            print("=" * 80)

            # 测试国内版本
            print("🇨🇳 测试国内版本 (qwen-turbo)...")
            await self._test_case_for_region(
                test_case, Area.domestic, "qwen-turbo", "中文"
            )

            # 测试海外版本
            print("🌍 测试海外版本 (gemini)...")
            await self._test_case_for_region(
                test_case, Area.overseas, "gemini", "English"
            )

            print(f"✅ 完成第 {current_progress} 个用例测试")

            # 每10个用例暂停一下，避免频率过高
            if current_progress % 10 == 0:
                await asyncio.sleep(1)

        # 输出统计结果
        self._print_statistics()

    async def _test_case_for_region(
        self, test_case: TestCase, region: Area, model: str, language: str
    ):
        """为特定区域测试用例"""
        # 临时设置区域
        original_region = agent_setting.region_version
        agent_setting.region_version = region

        try:
            # 获取prompt
            prompt = TransitionPrompts.get_prompt(test_case.query, language)

            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Your language style is 亲切、自然、有对话感",
                },
                {"role": "user", "content": prompt},
            ]

            # 调用模型
            start_time = time.time()
            response = ""
            error = None

            try:
                if self.mock_mode:
                    # 模拟响应 - 使用新的JSON格式
                    await asyncio.sleep(0.1)  # 模拟网络延迟
                    if model == "qwen-turbo":
                        # 判断是否为闲聊（简单规则：包含"你好"、"心情"等关键词）
                        is_chitchat = any(
                            keyword in test_case.query
                            for keyword in ["你好", "心情", "怎么样", "笑话"]
                        )
                        if is_chitchat:
                            response = '{"is_chitchat": true, "transition_phrase": ""}'
                        else:
                            response = '{"is_chitchat": false, "transition_phrase": "好的，我来帮你查一下这个信息"}'
                    else:
                        # 英文版本
                        is_chitchat = any(
                            keyword in test_case.query.lower()
                            for keyword in ["hello", "how are you", "joke", "color"]
                        )
                        if is_chitchat:
                            response = '{"is_chitchat": true, "transition_phrase": ""}'
                        else:
                            response = '{"is_chitchat": false, "transition_phrase": "Alright, let me look that up for you"}'
                elif self.use_real_llm:
                    # 真实LLM调用
                    if model == "qwen-turbo":
                        response = await TestLLMManager.call_qwen_turbo(messages)
                    else:
                        response = await TestLLMManager.call_gemini(messages)
                else:
                    response = "（跳过LLM调用）"

            except Exception as e:
                error = str(e)
                response = f"调用失败: {e}"

            elapsed_time = time.time() - start_time

            # 解析JSON响应
            is_chitchat = None
            transition_phrase = ""
            raw_output = response

            if response and not error:
                try:
                    # 使用parse_output_to_dict解析JSON
                    response_data, err_msg = parse_output_to_dict(response)
                    if err_msg:
                        raise ValueError(err_msg)

                    is_chitchat = response_data.get("is_chitchat", None)
                    transition_phrase = response_data.get("transition_phrase", "")

                except (ValueError, KeyError, AttributeError):
                    # JSON解析失败，设置为None
                    is_chitchat = None
                    transition_phrase = ""

            # 转换预期结果
            expected_is_chitchat = None
            if test_case.expected_need_transition == "有":
                expected_is_chitchat = False  # 需要过场话术 = 不是闲聊
            elif test_case.expected_need_transition == "无":
                expected_is_chitchat = True  # 不需要过场话术 = 是闲聊

            # 判断是否正确
            is_correct = None
            if is_chitchat is not None and expected_is_chitchat is not None:
                is_correct = is_chitchat == expected_is_chitchat

            # 记录结果
            result = TestResult(
                query=test_case.query,
                model=model,
                prompt=prompt,
                raw_output=raw_output,
                is_chitchat=is_chitchat,
                transition_phrase=transition_phrase,
                expected_is_chitchat=expected_is_chitchat,
                is_correct=is_correct,
                error=error,
                elapsed_time=elapsed_time,
                region="domestic" if region == Area.domestic else "overseas",
                source=test_case.source,
            )
            self.results.append(result)

        finally:
            # 恢复原始设置
            agent_setting.region_version = original_region

    def _print_statistics(self):
        """打印统计结果"""
        print(f"\n测试结果 (共 {len(self.results)} 个):")
        print("=" * 100)

        # 显示所有结果
        for i, result in enumerate(self.results):
            print(f"{i + 1:3d}. 查询: {result.query}")
            print(f"     模型: {result.model} | 区域: {result.region}")
            print(
                f"     判断: {'闲聊' if result.is_chitchat else '任务'} | 预期: {'闲聊' if result.expected_is_chitchat else '任务'} | {'✓' if result.is_correct else '✗'}"
            )
            if result.transition_phrase:
                print(f"     话术: {result.transition_phrase}")
            if result.error:
                print(f"     错误: {result.error}")
            print()

    def save_results_to_csv(self, filename: str = "transition_test_results.csv"):
        """保存结果到CSV文件"""
        if not self.results:
            print("没有结果可保存")
            return

        fieldnames = [
            "query",
            "model",
            "prompt",
            "raw_output",
            "is_chitchat",
            "transition_phrase",
            "expected_is_chitchat",
            "is_correct",
            "error",
            "elapsed_time",
            "region",
            "source",
        ]

        with open(filename, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in self.results:
                writer.writerow(
                    {
                        "query": result.query,
                        "model": result.model,
                        "prompt": result.prompt,
                        "raw_output": result.raw_output,
                        "is_chitchat": result.is_chitchat,
                        "transition_phrase": result.transition_phrase,
                        "expected_is_chitchat": result.expected_is_chitchat,
                        "is_correct": result.is_correct,
                        "error": result.error or "",
                        "elapsed_time": f"{result.elapsed_time:.3f}",
                        "region": result.region,
                        "source": result.source,
                    }
                )

    def print_prompt_comparison(self):
        """打印两个版本的prompt对比"""
        test_query = "帮我查一下天气"
        test_language_cn = "中文"
        test_language_en = "English"

        # 国内版本
        original_region = agent_setting.region_version
        agent_setting.region_version = Area.domestic
        domestic_prompt = TransitionPrompts.get_prompt(test_query, test_language_cn)

        # 海外版本
        agent_setting.region_version = Area.overseas
        overseas_prompt = TransitionPrompts.get_prompt(test_query, test_language_en)

        # 恢复原始设置
        agent_setting.region_version = original_region

        print("=" * 80)
        print("PROMPT 对比")
        print("=" * 80)
        print("\n【国内版本 Prompt】:")
        print(domestic_prompt)
        print(f"\n字符数: {len(domestic_prompt)}")
        print("\n" + "=" * 80)
        print("\n【海外版本 Prompt】:")
        print(overseas_prompt)
        print(f"\n字符数: {len(overseas_prompt)}")
        print("=" * 80)

    async def run_all_tests(self, dataset_type="all", limit=None):
        """运行所有测试"""
        # 打印prompt对比
        self.print_prompt_comparison()

        # 根据数据集类型选择测试方法
        if dataset_type == "real_user":
            await self.test_real_user_dataset_only(limit=limit)
        elif dataset_type == "skill":
            await self.test_skill_dataset_only()
        else:
            # 测试所有数据集
            await self.test_all_datasets()

        # 统一保存到一个文件
        self.save_results_to_csv("transition_test_results.csv")

        # 分析准确率
        self.analyze_accuracy("transition_test_results.csv")

    def analyze_accuracy(self, csv_file):
        """分析测试结果的准确率"""
        try:
            import pandas as pd

            print("\n" + "=" * 60)
            print("📊 准确率分析结果")
            print("=" * 60)

            df = pd.read_csv(csv_file)

            # 直接使用is_correct字段计算准确率
            correct = df["is_correct"].sum()
            total = len(df)
            accuracy = correct / total * 100

            print(f"🎯 整体准确率: {accuracy:.1f}% ({correct}/{total})")

            # 按区域分析
            print("\n📍 按区域分析:")
            for region in df["region"].unique():
                region_df = df[df["region"] == region]
                region_correct = region_df["is_correct"].sum()
                region_total = len(region_df)
                region_accuracy = region_correct / region_total * 100
                print(
                    f"  {region}: {region_accuracy:.1f}% ({region_correct}/{region_total})"
                )

            # 按模型分析
            print("\n🤖 按模型分析:")
            for model in df["model"].unique():
                model_df = df[df["model"] == model]
                model_correct = model_df["is_correct"].sum()
                model_total = len(model_df)
                model_accuracy = model_correct / model_total * 100
                print(
                    f"  {model}: {model_accuracy:.1f}% ({model_correct}/{model_total})"
                )

            # 计算详细指标 - 基于新的数据结构
            valid_df = df.dropna(subset=["is_chitchat", "expected_is_chitchat"])

            if len(valid_df) > 0:
                # 计算混淆矩阵
                tp = len(
                    valid_df[
                        ~valid_df["expected_is_chitchat"] & ~valid_df["is_chitchat"]
                    ]
                )  # 正确识别任务
                fn = len(
                    valid_df[
                        ~valid_df["expected_is_chitchat"] & valid_df["is_chitchat"]
                    ]
                )  # 任务误判为闲聊
                fp = len(
                    valid_df[
                        valid_df["expected_is_chitchat"] & ~valid_df["is_chitchat"]
                    ]
                )  # 闲聊误判为任务

                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = (
                    2 * precision * recall / (precision + recall)
                    if (precision + recall) > 0
                    else 0
                )

                print("\n📈 详细指标:")
                print(f"  精确率: {precision:.3f}")
                print(f"  召回率: {recall:.3f}")
                print(f"  F1分数: {f1:.3f}")

                print("\n❌ 错误统计:")
                print(f"  假阳性(不该有但生成了): {fp}")
                print(f"  假阴性(该有但没生成): {fn}")
            else:
                print("\n❌ 没有有效数据进行详细分析")

            print("=" * 60)

        except ImportError:
            print("\n⚠️ 需要安装pandas来分析准确率: pip install pandas")
        except FileNotFoundError:
            print(f"\n⚠️ 未找到结果文件: {csv_file}")
        except Exception as e:
            print(f"\n❌ 分析准确率时出错: {e}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="测试过渡话术 Prompts")
    parser.add_argument(
        "--mode",
        choices=["mock", "real", "prompt-only"],
        default="mock",
        help="测试模式: mock=模拟调用, real=真实LLM调用, prompt-only=仅显示prompt",
    )
    parser.add_argument(
        "--dataset",
        choices=["all", "real_user", "skill"],
        default="all",
        help="数据集选择: all=所有数据集, real_user=真实用户数据集, skill=技能数据集",
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细输出")
    parser.add_argument(
        "--output", "-o", default="transition_test_results.csv", help="结果输出文件名"
    )
    parser.add_argument(
        "--limit", "-l", type=int, help="限制测试数量（仅对real_user数据集有效）"
    )

    args = parser.parse_args()

    # 根据模式配置测试
    if args.mode == "mock":
        test = TransitionPromptsTest(use_real_llm=False, mock_mode=True)
    elif args.mode == "real":
        test = TransitionPromptsTest(use_real_llm=True, mock_mode=False)
    else:  # prompt-only
        test = TransitionPromptsTest(use_real_llm=False, mock_mode=False)

    print(f"运行模式: {args.mode}")
    print(f"数据集: {args.dataset}")
    print(f"详细输出: {args.verbose}")
    print(f"输出文件: {args.output}")
    if args.limit:
        print(f"测试数量限制: {args.limit}")
    print()

    await test.run_all_tests(dataset_type=args.dataset, limit=args.limit)


if __name__ == "__main__":
    asyncio.run(main())
