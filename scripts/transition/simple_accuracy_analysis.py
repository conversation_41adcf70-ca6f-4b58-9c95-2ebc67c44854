#!/usr/bin/env python3
"""
过渡话术准确率分析
"""

import pandas as pd
from datetime import datetime


def analyze_accuracy(csv_file, save_to_file=True):
    """分析过渡话术判断准确率"""
    df = pd.read_csv(csv_file)

    # 重定向输出到变量和控制台
    results = []

    def print_and_save(text=""):
        print(text)
        results.append(text)

    print_and_save(f"总测试数据: {len(df)} 条")
    print_and_save("=" * 60)

    # 将实际响应转换为预测结果
    # "##" 表示没有生成过渡话术，其他内容表示生成了过渡话术
    df["predicted"] = df["response"].apply(lambda x: "无" if x == "##" else "有")
    df["expected"] = df["expected_need_transition"]

    # 计算准确率
    correct = (df["predicted"] == df["expected"]).sum()
    total = len(df)
    accuracy = correct / total * 100

    print_and_save(f"🎯 整体准确率: {accuracy:.1f}% ({correct}/{total})")
    print_and_save()

    # 按区域分析
    print_and_save("📊 按区域分析:")
    for region in df["region"].unique():
        region_df = df[df["region"] == region]
        region_correct = (region_df["predicted"] == region_df["expected"]).sum()
        region_total = len(region_df)
        region_accuracy = region_correct / region_total * 100
        print_and_save(
            f"  {region}: {region_accuracy:.1f}% ({region_correct}/{region_total})"
        )
    print_and_save()

    # 按模型分析
    print_and_save("🤖 按模型分析:")
    for model in df["model"].unique():
        model_df = df[df["model"] == model]
        model_correct = (model_df["predicted"] == model_df["expected"]).sum()
        model_total = len(model_df)
        model_accuracy = model_correct / model_total * 100
        print_and_save(
            f"  {model}: {model_accuracy:.1f}% ({model_correct}/{model_total})"
        )
    print_and_save()

    # 混淆矩阵
    print_and_save("📋 混淆矩阵分析:")
    print_and_save("  期望\\预测     有      无")
    print_and_save("-" * 25)

    # 期望有，预测有 (True Positive)
    tp = len(df[(df["expected"] == "有") & (df["predicted"] == "有")])
    # 期望有，预测无 (False Negative)
    fn = len(df[(df["expected"] == "有") & (df["predicted"] == "无")])
    # 期望无，预测有 (False Positive)
    fp = len(df[(df["expected"] == "无") & (df["predicted"] == "有")])
    # 期望无，预测无 (True Negative)
    tn = len(df[(df["expected"] == "无") & (df["predicted"] == "无")])

    print_and_save(f"     有      {tp:3d}    {fn:3d}")
    print_and_save(f"     无      {fp:3d}    {tn:3d}")
    print_and_save()

    # 计算精确率、召回率、F1
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = (
        2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    )

    print_and_save("📈 详细指标:")
    print_and_save(
        f"  精确率 (Precision): {precision:.3f} - 预测有过渡话术中实际正确的比例"
    )
    print_and_save(
        f"  召回率 (Recall):    {recall:.3f} - 实际需要过渡话术中被正确预测的比例"
    )
    print_and_save(f"  F1 分数:           {f1:.3f} - 精确率和召回率的调和平均")
    print_and_save()

    # 错误案例分析
    print_and_save("❌ 所有错误案例分析:")
    wrong_cases = df[df["predicted"] != df["expected"]]
    print_and_save(f"  总错误数: {len(wrong_cases)}")
    print_and_save()

    # 假阳性：期望无但预测有
    false_positives = df[(df["expected"] == "无") & (df["predicted"] == "有")]
    print_and_save(f"🔴 假阳性 (不该有过渡话术但生成了): {len(false_positives)}")
    if len(false_positives) > 0:
        for i, (_, row) in enumerate(false_positives.iterrows()):
            response_preview = row["response"][:50] + (
                "..." if len(row["response"]) > 50 else ""
            )
            print_and_save(
                f'  {i + 1:2d}. [{row["region"]}-{row["model"]}] "{row["query"]}" -> "{response_preview}"'
            )
    print_and_save()

    # 假阴性：期望有但预测无
    false_negatives = df[(df["expected"] == "有") & (df["predicted"] == "无")]
    print_and_save(f"🟡 假阴性 (该有过渡话术但没生成): {len(false_negatives)}")
    if len(false_negatives) > 0:
        for i, (_, row) in enumerate(false_negatives.iterrows()):
            print_and_save(
                f'  {i + 1:2d}. [{row["region"]}-{row["model"]}] "{row["query"]}"'
            )
    print_and_save()

    # 保存结果到文件
    if save_to_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"accuracy_analysis_report_{timestamp}.txt"

        with open(output_file, "w", encoding="utf-8") as f:
            f.write("过渡话术准确率分析报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            f.write("\n".join(results))

        print(f"\n✅ 分析结果已保存到: {output_file}")

        # 同时保存一个简化版本
        summary_file = f"accuracy_summary_{timestamp}.txt"
        with open(summary_file, "w", encoding="utf-8") as f:
            f.write("过渡话术准确率简要报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"整体准确率: {accuracy:.1f}% ({correct}/{total})\n\n")
            f.write("按区域分析:\n")
            for region in df["region"].unique():
                region_df = df[df["region"] == region]
                region_correct = (region_df["predicted"] == region_df["expected"]).sum()
                region_total = len(region_df)
                region_accuracy = region_correct / region_total * 100
                f.write(
                    f"  {region}: {region_accuracy:.1f}% ({region_correct}/{region_total})\n"
                )
            f.write("\n按模型分析:\n")
            for model in df["model"].unique():
                model_df = df[df["model"] == model]
                model_correct = (model_df["predicted"] == model_df["expected"]).sum()
                model_total = len(model_df)
                model_accuracy = model_correct / model_total * 100
                f.write(
                    f"  {model}: {model_accuracy:.1f}% ({model_correct}/{model_total})\n"
                )
            f.write("\n详细指标:\n")
            f.write(f"  精确率: {precision:.3f}\n")
            f.write(f"  召回率: {recall:.3f}\n")
            f.write(f"  F1分数: {f1:.3f}\n")
            f.write("\n错误统计:\n")
            f.write(f"  假阳性: {len(false_positives)} (不该有过渡话术但生成了)\n")
            f.write(f"  假阴性: {len(false_negatives)} (该有过渡话术但没生成)\n")

        print(f"✅ 简要报告已保存到: {summary_file}")


if __name__ == "__main__":
    analyze_accuracy("real_user_transition_test_results.csv")
