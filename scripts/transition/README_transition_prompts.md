# 过渡话术Prompt测试脚本

## 概述

本测试脚本用于测试国内版和海外版的过渡话术prompt，支持不同的测试模式。

## 文件结构

- `src/assistant/planner/transition_prompts.py` - 过渡话术prompt管理模块
- `scripts/test_transition_prompts.py` - 测试脚本
- `scripts/README_transition_prompts.md` - 本文档

## 模块说明

### TransitionPrompts 类

管理过渡话术的prompt，根据`agent_setting.region_version`自动选择对应版本：

- **国内版本** (`Area.domestic`): 中文prompt，包含中文回复模板
- **海外版本** (`Area.overseas`): 英文prompt，包含英文回复模板

#### 主要方法

```python
# 获取国内版本prompt
TransitionPrompts.get_domestic_prompt(query_text, language_prompt)

# 获取海外版本prompt
TransitionPrompts.get_overseas_prompt(query_text, language_prompt)

# 自动选择对应版本的prompt
TransitionPrompts.get_prompt(query_text, language_prompt)
```

## 测试脚本使用

### 运行模式

```bash
# 模拟模式（默认）- 使用模拟LLM响应
python scripts/transition/test_transition_prompts.py --mode mock

# 真实LLM调用模式 - 调用qwen-turbo和gemini
python scripts/transition/test_transition_prompts.py --mode real

# 仅显示prompt模式 - 不调用LLM
python scripts/transition/test_transition_prompts.py --mode prompt-only

# 显示详细输出
python scripts/transition/test_transition_prompts.py --mode mock --verbose
```

### 测试内容

脚本会测试以下场景：

#### 任务导向查询 (Task Queries)
- 中文：天气查询、机票价格、搜索请求等
- 英文：Weather forecast, flight prices, search requests等

#### 闲聊查询 (Casual Queries)
- 中文：问候、笑话、个人问题等
- 英文：Greetings, jokes, personal questions等

### 期望行为

根据prompt设计：

- **任务导向请求**: 应返回简短的过渡话术（最多15字）
- **闲聊内容**: 应返回 "##"

### 输出说明

测试脚本会显示：

1. **Prompt对比**: 展示国内版和海外版的prompt差异
2. **国内版本测试**: 使用qwen-turbo模型测试中文查询
3. **海外版本测试**: 使用gemini模型测试英文查询

每个查询会显示：
- 查询内容
- 使用的语言和模型
- LLM响应
- 响应时间

## 配置说明

### LLM配置

- **Qwen-turbo**: 使用阿里云DashScope API
- **Gemini**: 使用Google Vertex AI

确保相关API密钥已正确配置在环境变量中。

### 区域配置

通过`agent_setting.region_version`控制：
- `Area.domestic`: 国内版本
- `Area.overseas`: 海外版本

## 集成说明

### 在SingleActionPlanner中使用

```python
from src.assistant.planner.transition_prompts import TransitionPrompts

# 获取prompt
prompt = TransitionPrompts.get_prompt(query_text, language_prompt)

# 调用LLM
messages = [{"role": "user", "content": prompt}]
result = await LLMManager.invoke_generate_text_model(messages=messages)
```

### 自定义prompt

可以继承`TransitionPrompts`类或修改其中的prompt模板来适应不同需求。

## 故障排除

### 常见问题

1. **LLM调用失败**: 检查API密钥和网络连接
2. **导入错误**: 确保在项目根目录运行脚本
3. **配置错误**: 检查agent_setting中的配置

### 调试模式

使用`--verbose`参数获取详细输出，或使用`--mode prompt-only`仅查看prompt而不调用LLM。


python test_transition_prompts.py --mode real --dataset real_user


准确率:
qwen-turbo: 84.0% (184/219)
gemini-2.5-flash-lite-preview-06-17: 81.3% (178/219)
