#!/usr/bin/env python3
"""
视觉模型测试结果分析脚本
"""

import json
import sys
import os
from typing import Dict

# 添加项目根目录到路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)


def analyze_test_results(report_file: str = "vision_test_report.json") -> Dict:
    """分析测试结果"""

    if not os.path.exists(report_file):
        print(f"报告文件 {report_file} 不存在")
        return {}

    with open(report_file, "r", encoding="utf-8") as f:
        report = json.load(f)

    print("=" * 60)
    print("视觉模型测试结果分析")
    print("=" * 60)

    # 基础信息
    test_summary = report.get("test_summary", {})
    print(f"测试模型: {test_summary.get('model', 'Unknown')}")
    print(f"测试时间: {test_summary.get('test_time', 'Unknown')}")
    print()

    # 基础统计
    basic_stats = report.get("basic_statistics", {})
    print("基础统计:")
    print(f"  总测试数: {basic_stats.get('total_tests', 0)}")
    print(f"  成功测试数: {basic_stats.get('successful_tests', 0)}")
    print(f"  失败测试数: {basic_stats.get('failed_tests', 0)}")
    print(f"  成功率: {basic_stats.get('success_rate', 0):.2%}")
    print(f"  总测试时间: {basic_stats.get('total_test_time', 0):.2f}秒")
    print(f"  平均响应时间: {basic_stats.get('average_response_time', 0):.2f}秒")
    print()

    # 性能分析
    perf_stats = report.get("performance_analysis", {})
    print("性能分析:")
    print(f"  最快响应时间: {perf_stats.get('min_response_time', 0):.2f}秒")
    print(f"  最慢响应时间: {perf_stats.get('max_response_time', 0):.2f}秒")
    print(f"  平均响应时间: {perf_stats.get('avg_response_time', 0):.2f}秒")
    print(f"  慢响应数量: {perf_stats.get('slow_responses', 0)}")
    print()

    # 评估分析
    eval_stats = report.get("evaluation_analysis", {})
    if eval_stats:
        print("评估分析:")
        print(f"  平均评分: {eval_stats.get('average_overall_score', 0):.2f}")
        print(f"  最低评分: {eval_stats.get('min_score', 0):.2f}")
        print(f"  最高评分: {eval_stats.get('max_score', 0):.2f}")
        print(f"  高质量回答数 (≥0.8): {eval_stats.get('high_quality_responses', 0)}")
        print(f"  低质量回答数 (<0.5): {eval_stats.get('low_quality_responses', 0)}")
        print()

    # 详细结果分析
    detailed_results = report.get("detailed_results", [])
    if detailed_results:
        print("详细结果分析:")
        print("-" * 40)

        for result in detailed_results:
            test_id = result.get("test_id", "Unknown")
            test_name = result.get("test_name", "Unknown")
            question = result.get("question", "")
            actual_answer = result.get("actual_answer", "")
            expected_answer = result.get("expected_answer", "")
            success = result.get("success", False)
            elapsed_time = result.get("elapsed_time", 0)

            print(f"测试 {test_id}: {test_name}")
            print(f"  问题: {question}")
            print(f"  实际回答: {actual_answer}")
            if expected_answer:
                print(f"  期望回答: {expected_answer}")
            print(f"  成功: {'✓' if success else '✗'}")
            print(f"  响应时间: {elapsed_time:.2f}秒")

            # 评估详情
            if "evaluation" in result:
                evaluation = result["evaluation"]
                overall_score = evaluation.get("scores", {}).get("overall", 0)
                print(f"  评分: {overall_score:.2f}")

                # 显示评估详情
                scores = evaluation.get("scores", {})
                if "length" in scores:
                    print(f"    长度检查: {'✓' if scores['length'] == 1.0 else '✗'}")
                if "keyword_match" in scores:
                    print(f"    关键词匹配: {scores['keyword_match']:.2f}")
                if "criteria" in scores:
                    criteria_scores = scores["criteria"]
                    for criterion, score in criteria_scores.items():
                        print(f"    {criterion}: {'✓' if score == 1.0 else '✗'}")

            print("-" * 40)

    # 问题分析
    print("\n问题分析:")

    # 分析回答质量
    quality_issues = []
    consistency_issues = []

    for result in detailed_results:
        test_name = result.get("test_name", "")
        actual_answer = result.get("actual_answer", "")
        expected_answer = result.get("expected_answer", "")

        # 检查长度问题
        if len(actual_answer) > 35:
            quality_issues.append(
                f"测试 {result.get('test_id')}: 回答超长 ({len(actual_answer)}字)"
            )

        # 检查是否使用了"在图片中"等短语
        if "在图片中" in actual_answer or "图片中" in actual_answer:
            quality_issues.append(f"测试 {result.get('test_id')}: 使用了'在图片中'短语")

        # 检查第一人称使用
        if test_name in ["询问穿什么衣服", "再次询问衣服（纠正错误）"]:
            if not ("我" in actual_answer or "你" in actual_answer):
                consistency_issues.append(
                    f"测试 {result.get('test_id')}: 未使用第一人称"
                )

    if quality_issues:
        print("  质量问题:")
        for issue in quality_issues:
            print(f"    - {issue}")

    if consistency_issues:
        print("  一致性问题:")
        for issue in consistency_issues:
            print(f"    - {issue}")

    if not quality_issues and not consistency_issues:
        print("  未发现明显问题")

    # 建议
    print("\n改进建议:")

    avg_score = eval_stats.get("average_overall_score", 0)
    if avg_score < 0.6:
        print("  - 整体评分较低，需要优化提示词或模型参数")
    elif avg_score < 0.8:
        print("  - 评分中等，可以进一步优化特定场景的处理")
    else:
        print("  - 整体表现良好，可以考虑增加更多测试用例")

    low_quality_count = eval_stats.get("low_quality_responses", 0)
    if low_quality_count > 0:
        print(f"  - 有 {low_quality_count} 个低质量回答，需要重点关注")

    slow_responses = perf_stats.get("slow_responses", 0)
    if slow_responses > 0:
        print(f"  - 有 {slow_responses} 个慢响应，可能需要优化性能")

    return report


def compare_with_expected():
    """与期望结果对比"""
    print("\n与期望结果对比:")
    print("-" * 40)

    expected_behaviors = {
        1: "应该避免直接评价，引导用户自己判断",
        2: "应该准确描述服装，使用第一人称",
        3: "应该纠正之前的错误描述，准确识别服装",
        4: "应该准确识别性别",
    }

    # 这里可以添加更详细的对比逻辑
    for test_id, expected in expected_behaviors.items():
        print(f"测试 {test_id}: {expected}")


def main():
    """主函数"""
    report_file = "vision_test_report.json"

    if len(sys.argv) > 1:
        report_file = sys.argv[1]

    analyze_test_results(report_file)
    compare_with_expected()


if __name__ == "__main__":
    main()
