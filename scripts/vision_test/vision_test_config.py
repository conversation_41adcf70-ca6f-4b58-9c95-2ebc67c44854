"""
视觉模型测试配置文件
"""

# 测试用例配置
TEST_CASES = [
    {
        "id": 1,
        "name": "询问衣服是否帅气",
        "question": "你看看我今天穿的衣服帅不帅",
        "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_d4c5f308f446624e2ab988655db86d3d.jpeg",
        "conversation_history": """<Robot> said '麻烦请您让一下吧'
<Robot> said '交通台提醒您， 请记得给机器人让路哦'
<Robot> Triggered Event [desc='The scene has switched: 问路引领场景 → 通用场景']
<Robot> Executed Action. guide_indoor_navigation(destinations=['休息区'],guide_text=好的，我将带您前往休息区，那里是一个相对...) Result:{}
<Robot> said '孙明焱， 见到你很高兴'
<User> said '你看看我今天穿的衣服帅不帅'""",
        "expected_answer": "你自己觉得呢？",
    },
    {
        "id": 2,
        "name": "询问穿什么衣服",
        "question": "那你看我穿啥衣服啊",
        "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_8fb7e149839f12311712f830ba2352a7.jpeg",
        "conversation_history": """<Robot> Triggered Event [desc='The scene has switched: 问路引领场景 → 通用场景']
<Robot> Executed Action. guide_indoor_navigation(destinations=['休息区'],guide_text=好的，我将带您前往休息区，那里是一个相对...) Result:{}
<Robot> said '孙明焱， 见到你很高兴'
<User> said '你看看我今天穿的衣服帅不帅'
<Robot> said '你自己觉得呢？'
<User> said '那你看我穿啥衣服啊'""",
        "expected_answer": "你在黑色T恤里面穿着一件深色衬衫。",
    },
    {
        "id": 3,
        "name": "再次询问衣服（纠正错误）",
        "question": "我穿啥衣服",
        "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_463c5a2e1486f3c8cd08922c4f0ada36.jpeg",
        "conversation_history": """<Robot> said '孙明焱， 见到你很高兴'
<User> said '你看看我今天穿的衣服帅不帅'
<Robot> said '你自己觉得呢？'
<User> said '嗯我没有穿上黑色的修身的身材你是不是看错了我到底穿啥衣服啊'""",
        "expected_answer": "我在灰色上衣里穿着一件浅绿色内搭。",
    },
    {
        "id": 4,
        "name": "询问性别",
        "question": "我是男的还是女的",
        "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_a7e32e4e022118e0929ec2366a396b0a.jpeg",
        "conversation_history": """<Robot> said '你自己觉得呢？'
<User> said '那你看我穿啥衣服啊'
<Robot> said '你在黑色T恤里面穿着一件深色衬衫。'
<User> said '嗯我没有穿上黑色的修身的身材你是不是看错了我到底穿啥衣服啊'
<Robot> said '我在灰色上衣里穿着一件浅绿色内搭。'
<User> said '看着有点问题...那我现在是我是男的还是女的'""",
        "expected_answer": "根据照片中的特征判断，您是一位男性。",
    },
]

# 额外测试问题（用于批量测试）
ADDITIONAL_TEST_QUESTIONS = [
    "描述一下我的外观",
    "我看起来几岁",
    "我的发型怎么样",
    "我戴眼镜了吗",
    "我的表情怎么样",
    "我在做什么",
    "背景是什么",
    "我手里拿着什么",
    "我的身高大概多少",
    "我看起来健康吗",
]

# 评估配置
EVALUATION_CONFIG = {
    "max_response_length": 35,
    "timeout_seconds": 30,
    "retry_attempts": 3,
    "expected_response_time": 5.0,  # 期望响应时间（秒）
}

# 关键词评估规则
KEYWORD_EVALUATION_RULES = {
    "clothing_keywords": [
        "衣服",
        "T恤",
        "衬衫",
        "上衣",
        "外套",
        "裤子",
        "裙子",
        "穿",
        "戴",
    ],
    "appearance_keywords": ["外观", "长相", "样子", "看起来", "模样"],
    "gender_keywords": ["男", "女", "性别", "先生", "女士"],
    "age_keywords": ["岁", "年龄", "老", "年轻", "成年"],
    "positive_keywords": ["帅", "好看", "漂亮", "不错", "很棒"],
    "negative_keywords": ["不好", "难看", "一般", "普通"],
    "conversational_keywords": ["觉得", "认为", "看法", "意见", "你说呢"],
}

# 测试报告配置
REPORT_CONFIG = {
    "output_file": "vision_test_report.json",
    "detailed_output_file": "vision_test_detailed.json",
    "include_raw_responses": True,
    "include_timing_analysis": True,
    "include_error_analysis": True,
}

# 天气信息（用于测试提示词）
WEATHER_INFO = {"city": "北京市", "weather": "多云", "temperature": "33"}

# 系统提示词
SYSTEM_PROMPT = """你的名字是`小豹`，你是`我是猎户星空创造的接待导览讲解机器人豹小秘第二代。`，所在公司介绍 ：`猎户星空是猎豹移动的控股子公司，成立于2016年9月，致力于开发"真正有用"的机器人。公司具备自主研发的全链条AI技术，实现了软硬一体化和云端大脑服务能力。借助7年来的AI经验积累，猎户星空发布猎户星空大模型，并升级了大模型机器人，始终秉持着"助力合作伙伴成功"的价值主张，助推实体经济和场景的数字化发展。`"""

# 提示词模板
PROMPT_TEMPLATE = """你是一个可以帮助分析图片并回答用户问题的助手。回答必须为纯文本，且不超过35个字。
## 当前天气信息
城市: {city}
天气: {weather}
温度: {temperature}

## 对话历史
{conversation_history}

## 用户问题
{question}

注意：
1. 如果图片中出现多个人，当问题涉及具体个人信息时，请针对每个人分别回答。
2. 避免使用"在图片中"之类的短语，并以第一人称视角用Chinese来回答用户的问题。"""

# 模型对比配置
MODEL_COMPARISON_CONFIG = {
    "models": [
        {
            "name": "qwen2.5-vl-7b-instruct",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": "sk-2f99e60920ee4e22bde8fda877567b99",
            "type": "qwen",
        },
        {
            "name": "qwen-vl-plus",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": "sk-2f99e60920ee4e22bde8fda877567b99",
            "type": "qwen",
        },
        {
            "name": "gpt-4o",
            "base_url": "https://api.openai.com/v1",
            "api_key": "***************************************************",
            "type": "openai",
        },
    ],
    "system_prompt": "你的名字是`小豹`，你是`我是猎户星空创造的接待导览讲解机器人豹小秘第二代。`",
}
