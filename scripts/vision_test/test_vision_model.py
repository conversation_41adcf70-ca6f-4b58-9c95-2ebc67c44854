#!/usr/bin/env python3
"""
视觉模型测试脚本
用于测试视觉模型在不同场景下的表现
"""

import asyncio
import json
import time
from typing import Dict, List
import aiohttp
from loguru import logger
import sys
import os

# 添加项目根目录到路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from src.settings import agent_setting
from src.utils.handle_image import get_image_content


class VisionModelTester:
    def __init__(self):
        self.base_url = agent_setting.vision_base_url
        self.api_key = agent_setting.vision_api_key
        self.model = agent_setting.vision_model
        self.results = []

    async def call_vision_model(self, image_url: str, prompt: str) -> Dict:
        """调用视觉模型"""
        image_content = await get_image_content(image_url)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

        payload = {
            "model": self.model,
            "temperature": 0.0,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": image_content},
                        },
                        {"text": prompt, "type": "text"},
                    ],
                }
            ],
        }

        start_time = time.time()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30),
                ) as response:
                    result = await response.json()
                    elapsed_time = time.time() - start_time

                    model_result = (
                        result.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                    )

                    return {
                        "success": True,
                        "result": model_result,
                        "elapsed_time": elapsed_time,
                        "raw_response": result,
                    }
        except Exception as e:
            elapsed_time = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "elapsed_time": elapsed_time,
                "raw_response": None,
            }

    def create_prompt(self, question: str, conversation_history: str = "") -> str:
        """创建测试提示词"""
        prompt = f"""你是一个可以帮助分析图片并回答用户问题的助手。回答必须为纯文本，且不超过35个字。
## 当前天气信息
城市: 北京市
天气: 多云
温度: 33

## 对话历史
{conversation_history}

## 用户问题
{question}

注意：
1. 如果图片中出现多个人，当问题涉及具体个人信息时，请针对每个人分别回答。
2. 避免使用"在图片中"之类的短语，并以第一人称视角用Chinese来回答用户的问题。"""
        return prompt

    async def run_test_case(self, test_case: Dict) -> Dict:
        """运行单个测试用例"""
        logger.info(f"运行测试用例: {test_case['id']}")

        prompt = self.create_prompt(
            test_case["question"], test_case.get("conversation_history", "")
        )

        response = await self.call_vision_model(test_case["image_url"], prompt)

        result = {
            "test_id": test_case["id"],
            "question": test_case["question"],
            "image_url": test_case["image_url"],
            "expected_answer": test_case.get("expected_answer", ""),
            "actual_answer": response.get("result", ""),
            "success": response["success"],
            "elapsed_time": response["elapsed_time"],
            "conversation_history": test_case.get("conversation_history", ""),
        }

        if not response["success"]:
            result["error"] = response["error"]

        return result

    def evaluate_result(self, result: Dict) -> Dict:
        """评估测试结果"""
        evaluation = {
            "test_id": result["test_id"],
            "question": result["question"],
            "actual_answer": result["actual_answer"],
            "success": result["success"],
            "elapsed_time": result["elapsed_time"],
        }

        if result["success"]:
            # 简单的评估逻辑，可以根据需要扩展
            answer = result["actual_answer"].lower()
            question = result["question"].lower()

            # 评估答案质量
            if "帅不帅" in question or "帅" in question:
                if any(
                    word in answer for word in ["帅", "好看", "不错", "很棒", "觉得"]
                ):
                    evaluation["relevance"] = "good"
                else:
                    evaluation["relevance"] = "poor"
            elif "穿啥衣服" in question or "穿什么" in question:
                if any(
                    word in answer
                    for word in ["衣服", "T恤", "衬衫", "上衣", "外套", "穿"]
                ):
                    evaluation["relevance"] = "good"
                else:
                    evaluation["relevance"] = "poor"
            elif "男的还是女的" in question or "性别" in question:
                if any(word in answer for word in ["男", "女", "性"]):
                    evaluation["relevance"] = "good"
                else:
                    evaluation["relevance"] = "poor"
            else:
                evaluation["relevance"] = "unknown"

            # 检查答案长度
            if len(result["actual_answer"]) <= 35:
                evaluation["length_check"] = "pass"
            else:
                evaluation["length_check"] = "fail"

        else:
            evaluation["relevance"] = "error"
            evaluation["length_check"] = "error"
            evaluation["error"] = result.get("error", "")

        return evaluation

    async def run_all_tests(self, test_cases: List[Dict]) -> List[Dict]:
        """运行所有测试用例"""
        results = []
        evaluations = []

        for test_case in test_cases:
            try:
                result = await self.run_test_case(test_case)
                results.append(result)

                evaluation = self.evaluate_result(result)
                evaluations.append(evaluation)

                logger.info(f"测试 {test_case['id']} 完成")
                logger.info(f"问题: {test_case['question']}")
                logger.info(f"回答: {result['actual_answer']}")
                logger.info(f"相关性: {evaluation.get('relevance', 'unknown')}")
                logger.info(f"长度检查: {evaluation.get('length_check', 'unknown')}")
                logger.info("-" * 50)

            except Exception as e:
                logger.error(f"测试用例 {test_case['id']} 执行失败: {e}")

        return results, evaluations

    def generate_report(self, results: List[Dict], evaluations: List[Dict]) -> Dict:
        """生成测试报告"""
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r["success"])

        relevance_stats = {}
        length_stats = {}

        for eval_result in evaluations:
            relevance = eval_result.get("relevance", "unknown")
            length_check = eval_result.get("length_check", "unknown")

            relevance_stats[relevance] = relevance_stats.get(relevance, 0) + 1
            length_stats[length_check] = length_stats.get(length_check, 0) + 1

        avg_response_time = (
            sum(r["elapsed_time"] for r in results) / total_tests
            if total_tests > 0
            else 0
        )

        report = {
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "success_rate": successful_tests / total_tests
                if total_tests > 0
                else 0,
                "average_response_time": avg_response_time,
            },
            "relevance_analysis": relevance_stats,
            "length_analysis": length_stats,
            "detailed_results": results,
            "evaluations": evaluations,
        }

        return report


async def main():
    """主函数"""
    # 定义测试用例
    test_cases = [
        {
            "id": 1,
            "question": "你看看我今天穿的衣服帅不帅",
            "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_d4c5f308f446624e2ab988655db86d3d.jpeg",
            "conversation_history": "<Robot> said '麻烦请您让一下吧'\n<Robot> said '交通台提醒您， 请记得给机器人让路哦'\n<Robot> Triggered Event [desc='The scene has switched: 问路引领场景 → 通用场景']\n<Robot> Executed Action. guide_indoor_navigation(destinations=['休息区'],guide_text=好的，我将带您前往休息区，那里是一个相对...) Result:{} \n<Robot> said '孙明焱， 见到你很高兴'\n<User> said '你看看我今天穿的衣服帅不帅'",
            "expected_answer": "你自己觉得呢？",
        },
        {
            "id": 2,
            "question": "那你看我穿啥衣服啊",
            "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_8fb7e149839f12311712f830ba2352a7.jpeg",
            "conversation_history": "<Robot> Triggered Event [desc='The scene has switched: 问路引领场景 → 通用场景']\n<Robot> Executed Action. guide_indoor_navigation(destinations=['休息区'],guide_text=好的，我将带您前往休息区，那里是一个相对...) Result:{} \n<Robot> said '孙明焱， 见到你很高兴'\n<User> said '你看看我今天穿的衣服帅不帅'\n<Robot> said '你自己觉得呢？'\n<User> said '那你看我穿啥衣服啊'",
            "expected_answer": "你在黑色T恤里面穿着一件深色衬衫。",
        },
        {
            "id": 3,
            "question": "我穿啥衣服",
            "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_463c5a2e1486f3c8cd08922c4f0ada36.jpeg",
            "conversation_history": "<Robot> said '孙明焱， 见到你很高兴'\n<User> said '你看看我今天穿的衣服帅不帅'\n<Robot> said '你自己觉得呢？'\n<User> said '那你看我穿啥衣服啊'\n<Robot> said '你在黑色T恤里面穿着一件深色衬衫。'\n<User> said '嗯我没有穿上黑色的修身的身材你是不是看错了我到底穿啥衣服啊'",
            "expected_answer": "我在灰色上衣里穿着一件浅绿色内搭。",
        },
        {
            "id": 4,
            "question": "我是男的还是女的",
            "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_a7e32e4e022118e0929ec2366a396b0a.jpeg",
            "conversation_history": "<Robot> said '你自己觉得呢？'\n<User> said '那你看我穿啥衣服啊'\n<Robot> said '你在黑色T恤里面穿着一件深色衬衫。'\n<User> said '嗯我没有穿上黑色的修身的身材你是不是看错了我到底穿啥衣服啊'\n<Robot> said '我在灰色上衣里穿着一件浅绿色内搭。'\n<User> said '看着有点问题...那我现在是我是男的还是女的'",
            "expected_answer": "根据照片中的特征判断，您是一位男性。",
        },
    ]

    # 创建测试器
    tester = VisionModelTester()

    logger.info("开始视觉模型测试...")
    logger.info(f"使用模型: {tester.model}")
    logger.info(f"API地址: {tester.base_url}")
    logger.info("=" * 60)

    # 运行测试
    results, evaluations = await tester.run_all_tests(test_cases)

    # 生成报告
    report = tester.generate_report(results, evaluations)

    # 输出报告
    logger.info("=" * 60)
    logger.info("测试报告")
    logger.info("=" * 60)
    logger.info(f"总测试数: {report['summary']['total_tests']}")
    logger.info(f"成功测试数: {report['summary']['successful_tests']}")
    logger.info(f"成功率: {report['summary']['success_rate']:.2%}")
    logger.info(f"平均响应时间: {report['summary']['average_response_time']:.2f}秒")

    logger.info("\n相关性分析:")
    for relevance, count in report["relevance_analysis"].items():
        logger.info(f"  {relevance}: {count}")

    logger.info("\n长度检查:")
    for length_check, count in report["length_analysis"].items():
        logger.info(f"  {length_check}: {count}")

    # 保存详细报告到文件
    with open("vision_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    logger.info("\n详细报告已保存到 vision_test_report.json")


if __name__ == "__main__":
    asyncio.run(main())
