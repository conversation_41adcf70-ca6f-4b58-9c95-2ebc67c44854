# 视觉模型测试工具

这是一套用于测试视觉模型性能的工具，基于你提供的测试用例开发。

## 文件说明

- `test_vision_model.py` - 基础测试脚本
- `enhanced_vision_test.py` - 增强版测试脚本，包含详细评估
- `run_vision_test.py` - 简化的运行脚本，支持多种测试模式
- `vision_test_config.py` - 测试配置文件
- `analyze_results.py` - 结果分析脚本
- `README_vision_test.md` - 使用说明

## 快速开始

**注意：所有命令都需要在项目根目录下运行，并激活虚拟环境**

```bash
cd /path/to/easyNLP
source .venv/bin/activate
```

### 1. 基础测试

运行完整的测试套件：

```bash
python scripts/vision_test/test_vision_model.py
```

### 2. 快速测试

快速测试单个用例：

```bash
python scripts/vision_test/run_vision_test.py quick
```

### 3. 批量问题测试

使用同一张图片测试多个问题：

```bash
python scripts/vision_test/run_vision_test.py batch
```

### 4. 测试单张图片

测试自定义图片和问题：

```bash
python scripts/vision_test/run_vision_test.py single "图片URL" "你的问题"
```

### 5. 增强版测试（推荐）

运行包含详细评估的测试：

```bash
python scripts/vision_test/enhanced_vision_test.py
```

### 6. 分析测试结果

分析生成的测试报告：

```bash
python scripts/vision_test/analyze_results.py
```

## 测试用例

当前包含4个测试用例，基于你提供的实际对话：

1. **询问衣服是否帅气** - 测试模型对主观评价问题的处理
2. **询问穿什么衣服** - 测试模型的服装识别能力
3. **再次询问衣服（纠正错误）** - 测试模型的错误纠正能力
4. **询问性别** - 测试模型的性别识别能力

## 评估指标

### 基础指标
- 成功率
- 响应时间
- 答案长度（≤35字）

### 增强指标
- 关键词匹配度
- 特定评估标准（如是否使用第一人称、是否避免直接判断等）
- 综合评分

## 配置说明

在 `vision_test_config.py` 中可以调整：

- 测试用例
- 评估标准
- 超时设置
- 重试次数
- 关键词规则

## 输出报告

测试完成后会生成：

- `vision_test_report.json` - 详细测试报告
- 控制台输出 - 实时测试结果

## 报告内容

### 基础统计
- 总测试数
- 成功/失败数量
- 成功率
- 平均响应时间

### 性能分析
- 最快/最慢响应时间
- 超时响应数量

### 评估分析
- 平均评分
- 高质量/低质量回答数量

### 错误分析
- 失败原因统计
- 重试统计

## 使用示例

### 测试你的用例

```python
# 运行完整测试
python enhanced_vision_test.py

# 查看结果
cat vision_test_report.json | jq '.basic_statistics'
```

### 添加新测试用例

在 `vision_test_config.py` 的 `TEST_CASES` 中添加：

```python
{
    "id": 5,
    "name": "新测试用例",
    "question": "你的问题",
    "image_url": "图片URL",
    "conversation_history": "对话历史",
    "expected_answer": "期望答案",
    "expected_keywords": ["关键词1", "关键词2"],
    "evaluation_criteria": {
        "should_describe_something": True,
        "max_length": 35
    }
}
```

## 注意事项

1. 确保网络连接正常，能够访问图片URL
2. 检查API配置是否正确（在 `src/settings.py` 中）
3. 测试可能需要一些时间，请耐心等待
4. 如果遇到网络问题，脚本会自动重试

## 故障排除

### 常见问题

1. **导入错误** - 确保在项目根目录运行脚本
2. **网络超时** - 检查网络连接或增加超时时间
3. **API错误** - 检查API密钥和端点配置

### 调试模式

设置日志级别为DEBUG：

```python
from loguru import logger
logger.remove()
logger.add(sys.stderr, level="DEBUG")
```

## 扩展功能

你可以根据需要扩展：

1. 添加更多评估指标
2. 支持批量图片测试
3. 添加性能基准测试
4. 集成到CI/CD流程

## 联系支持

如果遇到问题或需要添加新功能，请提供：
- 错误信息
- 测试用例详情
- 期望的功能描述
