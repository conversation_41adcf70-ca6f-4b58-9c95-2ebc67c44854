#!/usr/bin/env python3
"""
简化版三模型对比测试脚本
专注于速度和回答对比，去掉复杂的评估逻辑
"""

import asyncio
import json
import time
import base64
from typing import Dict, List
import aiohttp
from loguru import logger
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from src.utils.handle_image import get_image_content
from vision_test_config import (
    TEST_CASES,
    MODEL_COMPARISON_CONFIG,
    WEATHER_INFO,
    PROMPT_TEMPLATE,
)


class SimpleModelComparator:
    def __init__(self):
        self.models = MODEL_COMPARISON_CONFIG["models"]
        self.system_prompt = MODEL_COMPARISON_CONFIG["system_prompt"]

    async def get_image_content_for_model(
        self, model_config: Dict, image_url: str
    ) -> str:
        """根据模型类型获取图片内容"""
        if model_config.get("type") == "openai":
            # OpenAI需要base64格式
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(image_url) as response:
                        image_data = await response.read()
                base64_image = base64.b64encode(image_data).decode("utf-8")
                return f"data:image/jpeg;base64,{base64_image}"
            except Exception as e:
                logger.error(f"Error converting image to base64: {e}")
                return image_url
        else:
            # Qwen模型使用原有逻辑
            return await get_image_content(image_url)

    async def call_model(self, model_config: Dict, image_url: str, prompt: str) -> Dict:
        """调用指定模型"""
        image_content = await self.get_image_content_for_model(model_config, image_url)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {model_config['api_key']}",
        }

        # 根据模型类型构建不同的payload
        if model_config.get("type") == "openai":
            payload = {
                "model": model_config["name"],
                "temperature": 0.0,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": image_content},
                            },
                            {"type": "text", "text": prompt},
                        ],
                    },
                ],
                "max_tokens": 100,
            }
        else:
            # Qwen模型的payload格式
            payload = {
                "model": model_config["name"],
                "temperature": 0.0,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": image_content},
                            },
                            {"text": prompt, "type": "text"},
                        ],
                    },
                ],
            }

        start_time = time.time()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{model_config['base_url']}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30),
                ) as response:
                    result = await response.json()
                    elapsed_time = time.time() - start_time

                    model_result = (
                        result.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                    )

                    return {
                        "success": True,
                        "result": model_result,
                        "elapsed_time": elapsed_time,
                        "model_name": model_config["name"],
                    }
        except Exception as e:
            elapsed_time = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "elapsed_time": elapsed_time,
                "model_name": model_config["name"],
            }

    def create_prompt(self, question: str, conversation_history: str = "") -> str:
        """创建测试提示词"""
        return PROMPT_TEMPLATE.format(
            city=WEATHER_INFO["city"],
            weather=WEATHER_INFO["weather"],
            temperature=WEATHER_INFO["temperature"],
            conversation_history=conversation_history,
            question=question,
        )

    async def test_single_case(self, test_case: Dict) -> Dict:
        """测试单个用例在所有模型上的表现"""
        logger.info(f"测试用例 {test_case['id']}: {test_case['name']}")

        prompt = self.create_prompt(
            test_case["question"], test_case.get("conversation_history", "")
        )

        case_results = {
            "test_id": test_case["id"],
            "test_name": test_case["name"],
            "question": test_case["question"],
            "expected_answer": test_case.get("expected_answer", ""),
            "model_results": {},
        }

        # 并发测试所有模型
        tasks = []
        for model_config in self.models:
            task = self.call_model(model_config, test_case["image_url"], prompt)
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # 整理结果
        for result in results:
            model_name = result["model_name"]
            case_results["model_results"][model_name] = {
                "success": result["success"],
                "answer": result.get("result", ""),
                "elapsed_time": result["elapsed_time"],
                "error": result.get("error", ""),
            }

            # 输出结果
            if result["success"]:
                logger.info(
                    f"  {model_name}: {result['result']} ({result['elapsed_time']:.2f}s)"
                )
            else:
                logger.error(
                    f"  {model_name}: 错误 - {result.get('error', 'Unknown')} ({result['elapsed_time']:.2f}s)"
                )

        return case_results

    async def run_comparison(self) -> List[Dict]:
        """运行完整对比测试"""
        logger.info("开始简化版三模型对比测试...")
        logger.info(f"对比模型: {[model['name'] for model in self.models]}")
        logger.info(f"System Prompt: {self.system_prompt}")
        logger.info("=" * 80)

        all_results = []

        for test_case in TEST_CASES:
            case_result = await self.test_single_case(test_case)
            all_results.append(case_result)
            logger.info("-" * 60)

        return all_results

    def print_summary(self, results: List[Dict]):
        """打印简化摘要"""
        logger.info("=" * 80)
        logger.info("测试结果摘要")
        logger.info("=" * 80)

        # 计算平均响应时间
        model_times = {}
        for model in self.models:
            model_name = model["name"]
            times = []
            for result in results:
                model_result = result["model_results"].get(model_name, {})
                if model_result.get("success"):
                    times.append(model_result["elapsed_time"])
            model_times[model_name] = {
                "avg_time": sum(times) / len(times) if times else 0,
                "min_time": min(times) if times else 0,
                "max_time": max(times) if times else 0,
                "success_count": len(times),
            }

        # 速度排名
        speed_ranking = sorted(model_times.items(), key=lambda x: x[1]["avg_time"])

        logger.info("速度排名:")
        for i, (model_name, stats) in enumerate(speed_ranking, 1):
            logger.info(
                f"  {i}. {model_name}: 平均 {stats['avg_time']:.2f}s (范围: {stats['min_time']:.2f}s - {stats['max_time']:.2f}s)"
            )

        logger.info("")
        logger.info("详细对比:")
        logger.info("-" * 80)

        for result in results:
            logger.info(f"测试 {result['test_id']}: {result['question']}")
            logger.info(f"期望回答: {result['expected_answer']}")

            # 按速度排序显示结果
            model_results = []
            for model_name in [model["name"] for model in self.models]:
                model_result = result["model_results"].get(model_name, {})
                if model_result.get("success"):
                    model_results.append((model_name, model_result))

            # 按响应时间排序
            model_results.sort(key=lambda x: x[1]["elapsed_time"])

            for model_name, model_result in model_results:
                logger.info(
                    f"  {model_name}: {model_result['answer']} ({model_result['elapsed_time']:.2f}s)"
                )

            # 显示失败的模型
            for model_name in [model["name"] for model in self.models]:
                model_result = result["model_results"].get(model_name, {})
                if not model_result.get("success"):
                    logger.info(
                        f"  {model_name}: 失败 - {model_result.get('error', 'Unknown')}"
                    )

            logger.info("-" * 60)


async def main():
    """主函数"""
    comparator = SimpleModelComparator()

    # 运行对比测试
    results = await comparator.run_comparison()

    # 打印摘要
    comparator.print_summary(results)

    # 保存简化报告
    report = {
        "test_time": datetime.now().isoformat(),
        "models": [model["name"] for model in comparator.models],
        "system_prompt": comparator.system_prompt,
        "results": results,
    }

    report_file = "simple_comparison_report.json"
    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    logger.info(f"\n简化报告已保存到 {report_file}")


if __name__ == "__main__":
    asyncio.run(main())
