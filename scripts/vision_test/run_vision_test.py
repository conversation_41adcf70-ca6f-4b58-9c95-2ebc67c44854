#!/usr/bin/env python3
"""
简化的视觉模型测试运行脚本
"""

import asyncio
import sys
import os
from loguru import logger

# 添加项目根目录到路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from test_vision_model import VisionModelTester


async def quick_test():
    """快速测试单个用例"""
    tester = VisionModelTester()

    # 测试用例1：询问衣服是否帅气
    test_case = {
        "id": 1,
        "question": "你看看我今天穿的衣服帅不帅",
        "image_url": "https://jiedai.ainirobot.com/orics/down/aios001_20250714_d4c5f308f446624e2ab988655db86d3d.jpeg",
        "conversation_history": "<Robot> said '孙明焱， 见到你很高兴'\n<User> said '你看看我今天穿的衣服帅不帅'",
    }

    logger.info("开始快速测试...")
    result = await tester.run_test_case(test_case)

    logger.info("测试结果:")
    logger.info(f"问题: {result['question']}")
    logger.info(f"回答: {result['actual_answer']}")
    logger.info(f"成功: {result['success']}")
    logger.info(f"响应时间: {result['elapsed_time']:.2f}秒")

    if not result["success"]:
        logger.error(f"错误: {result.get('error', '未知错误')}")


async def test_single_image(image_url: str, question: str):
    """测试单张图片"""
    tester = VisionModelTester()

    test_case = {
        "id": "custom",
        "question": question,
        "image_url": image_url,
        "conversation_history": "",
    }

    logger.info(f"测试图片: {image_url}")
    logger.info(f"问题: {question}")

    result = await tester.run_test_case(test_case)

    logger.info("=" * 50)
    logger.info("测试结果:")
    logger.info(f"回答: {result['actual_answer']}")
    logger.info(f"成功: {result['success']}")
    logger.info(f"响应时间: {result['elapsed_time']:.2f}秒")

    if not result["success"]:
        logger.error(f"错误: {result.get('error', '未知错误')}")

    return result


async def batch_test_questions():
    """批量测试不同问题"""
    tester = VisionModelTester()

    # 使用同一张图片测试不同问题
    image_url = "https://jiedai.ainirobot.com/orics/down/aios001_20250714_d4c5f308f446624e2ab988655db86d3d.jpeg"

    questions = [
        "你看看我今天穿的衣服帅不帅",
        "我穿的什么衣服",
        "我是男的还是女的",
        "描述一下我的外观",
        "我看起来几岁",
        "我的发型怎么样",
    ]

    logger.info("开始批量测试...")

    for i, question in enumerate(questions, 1):
        logger.info(f"\n测试 {i}/{len(questions)}: {question}")

        test_case = {
            "id": i,
            "question": question,
            "image_url": image_url,
            "conversation_history": "",
        }

        result = await tester.run_test_case(test_case)

        logger.info(f"回答: {result['actual_answer']}")
        logger.info(f"响应时间: {result['elapsed_time']:.2f}秒")

        if not result["success"]:
            logger.error(f"错误: {result.get('error', '未知错误')}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python run_vision_test.py quick          # 快速测试")
        print("  python run_vision_test.py full           # 完整测试")
        print("  python run_vision_test.py batch          # 批量问题测试")
        print(
            "  python run_vision_test.py single <image_url> <question>  # 测试单张图片"
        )
        return

    command = sys.argv[1]

    if command == "quick":
        asyncio.run(quick_test())
    elif command == "full":
        # 运行完整测试
        from test_vision_model import main as full_main

        asyncio.run(full_main())
    elif command == "batch":
        asyncio.run(batch_test_questions())
    elif command == "single":
        if len(sys.argv) < 4:
            print("单张图片测试需要提供图片URL和问题")
            print("例如: python run_vision_test.py single <image_url> <question>")
            return
        image_url = sys.argv[2]
        question = " ".join(sys.argv[3:])
        asyncio.run(test_single_image(image_url, question))
    else:
        print(f"未知命令: {command}")


if __name__ == "__main__":
    main()
