#!/usr/bin/env python3
"""
增强版视觉模型测试脚本
包含更详细的评估和分析功能
"""

import asyncio
import json
import time
from typing import Dict, List
import aiohttp
from loguru import logger
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from src.settings import agent_setting
from src.utils.handle_image import get_image_content
from vision_test_config import (
    TEST_CASES,
    EVALUATION_CONFIG,
    KEYWORD_EVALUATION_RULES,
    REPORT_CONFIG,
    WEATHER_INFO,
    PROMPT_TEMPLATE,
)


class EnhancedVisionTester:
    def __init__(self):
        self.base_url = agent_setting.vision_base_url
        self.api_key = agent_setting.vision_api_key
        self.model = agent_setting.vision_model
        self.results = []
        self.start_time = None

    async def call_vision_model(
        self, image_url: str, prompt: str, retry_count: int = 0
    ) -> Dict:
        """调用视觉模型，支持重试"""
        image_content = await get_image_content(image_url)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

        payload = {
            "model": self.model,
            "temperature": 0.0,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": image_content},
                        },
                        {"text": prompt, "type": "text"},
                    ],
                }
            ],
        }

        start_time = time.time()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(
                        total=EVALUATION_CONFIG["timeout_seconds"]
                    ),
                ) as response:
                    result = await response.json()
                    elapsed_time = time.time() - start_time

                    model_result = (
                        result.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                    )

                    return {
                        "success": True,
                        "result": model_result,
                        "elapsed_time": elapsed_time,
                        "raw_response": result
                        if REPORT_CONFIG["include_raw_responses"]
                        else None,
                        "retry_count": retry_count,
                    }
        except Exception as e:
            elapsed_time = time.time() - start_time

            # 如果还有重试次数，则重试
            if retry_count < EVALUATION_CONFIG["retry_attempts"]:
                logger.warning(
                    f"请求失败，正在重试 ({retry_count + 1}/{EVALUATION_CONFIG['retry_attempts']}): {e}"
                )
                await asyncio.sleep(1)  # 等待1秒后重试
                return await self.call_vision_model(image_url, prompt, retry_count + 1)

            return {
                "success": False,
                "error": str(e),
                "elapsed_time": elapsed_time,
                "raw_response": None,
                "retry_count": retry_count,
            }

    def create_prompt(self, question: str, conversation_history: str = "") -> str:
        """创建测试提示词"""
        return PROMPT_TEMPLATE.format(
            city=WEATHER_INFO["city"],
            weather=WEATHER_INFO["weather"],
            temperature=WEATHER_INFO["temperature"],
            conversation_history=conversation_history,
            question=question,
        )

    def evaluate_response(self, test_case: Dict, actual_answer: str) -> Dict:
        """详细评估响应质量"""
        evaluation = {
            "test_id": test_case["id"],
            "question": test_case["question"],
            "actual_answer": actual_answer,
            "scores": {},
            "details": {},
        }

        # 长度检查
        length_score = (
            1.0
            if len(actual_answer) <= EVALUATION_CONFIG["max_response_length"]
            else 0.0
        )
        evaluation["scores"]["length"] = length_score
        evaluation["details"]["length"] = (
            f"答案长度: {len(actual_answer)}/{EVALUATION_CONFIG['max_response_length']}"
        )

        # 关键词匹配
        if "expected_keywords" in test_case:
            keyword_matches = sum(
                1
                for keyword in test_case["expected_keywords"]
                if keyword in actual_answer
            )
            keyword_score = (
                keyword_matches / len(test_case["expected_keywords"])
                if test_case["expected_keywords"]
                else 0.0
            )
            evaluation["scores"]["keyword_match"] = keyword_score
            evaluation["details"]["keyword_match"] = (
                f"匹配关键词: {keyword_matches}/{len(test_case['expected_keywords'])}"
            )

        # 特定评估标准
        if "evaluation_criteria" in test_case:
            criteria = test_case["evaluation_criteria"]
            criteria_scores = {}

            if criteria.get("should_describe_clothing"):
                clothing_keywords = KEYWORD_EVALUATION_RULES["clothing_keywords"]
                has_clothing_desc = any(
                    keyword in actual_answer for keyword in clothing_keywords
                )
                criteria_scores["clothing_description"] = (
                    1.0 if has_clothing_desc else 0.0
                )

            if criteria.get("should_identify_gender"):
                gender_keywords = KEYWORD_EVALUATION_RULES["gender_keywords"]
                has_gender_info = any(
                    keyword in actual_answer for keyword in gender_keywords
                )
                criteria_scores["gender_identification"] = (
                    1.0 if has_gender_info else 0.0
                )

            if criteria.get("should_use_first_person"):
                first_person_indicators = ["我", "你"]
                uses_first_person = any(
                    indicator in actual_answer for indicator in first_person_indicators
                )
                criteria_scores["first_person"] = 1.0 if uses_first_person else 0.0

            if criteria.get("should_avoid_direct_judgment"):
                avoids_judgment = (
                    "觉得" in actual_answer
                    or "你说" in actual_answer
                    or "认为" in actual_answer
                )
                criteria_scores["avoids_judgment"] = 1.0 if avoids_judgment else 0.0

            evaluation["scores"]["criteria"] = criteria_scores
            evaluation["details"]["criteria"] = criteria_scores

        # 计算总分
        all_scores = []
        all_scores.append(evaluation["scores"].get("length", 0))
        all_scores.append(evaluation["scores"].get("keyword_match", 0))

        if "criteria" in evaluation["scores"]:
            all_scores.extend(evaluation["scores"]["criteria"].values())

        evaluation["scores"]["overall"] = (
            sum(all_scores) / len(all_scores) if all_scores else 0.0
        )

        return evaluation

    async def run_test_case(self, test_case: Dict) -> Dict:
        """运行单个测试用例"""
        logger.info(f"运行测试用例 {test_case['id']}: {test_case['name']}")

        prompt = self.create_prompt(
            test_case["question"], test_case.get("conversation_history", "")
        )

        response = await self.call_vision_model(test_case["image_url"], prompt)

        result = {
            "test_id": test_case["id"],
            "test_name": test_case["name"],
            "question": test_case["question"],
            "image_url": test_case["image_url"],
            "expected_answer": test_case.get("expected_answer", ""),
            "actual_answer": response.get("result", ""),
            "success": response["success"],
            "elapsed_time": response["elapsed_time"],
            "retry_count": response.get("retry_count", 0),
            "conversation_history": test_case.get("conversation_history", ""),
            "timestamp": datetime.now().isoformat(),
        }

        if not response["success"]:
            result["error"] = response["error"]
        else:
            # 进行详细评估
            evaluation = self.evaluate_response(test_case, result["actual_answer"])
            result["evaluation"] = evaluation

        if REPORT_CONFIG["include_raw_responses"] and response.get("raw_response"):
            result["raw_response"] = response["raw_response"]

        return result

    async def run_all_tests(self, test_cases: List[Dict] = None) -> List[Dict]:
        """运行所有测试用例"""
        if test_cases is None:
            test_cases = TEST_CASES

        self.start_time = time.time()
        results = []

        for test_case in test_cases:
            try:
                result = await self.run_test_case(test_case)
                results.append(result)

                # 输出简要结果
                logger.info(f"✓ 测试 {test_case['id']} 完成")
                logger.info(f"  问题: {test_case['question']}")
                logger.info(f"  回答: {result['actual_answer']}")

                if result["success"] and "evaluation" in result:
                    overall_score = result["evaluation"]["scores"]["overall"]
                    logger.info(f"  评分: {overall_score:.2f}")

                logger.info(f"  响应时间: {result['elapsed_time']:.2f}秒")

                if result.get("retry_count", 0) > 0:
                    logger.info(f"  重试次数: {result['retry_count']}")

                logger.info("-" * 50)

            except Exception as e:
                logger.error(f"测试用例 {test_case['id']} 执行失败: {e}")

        return results

    def generate_comprehensive_report(self, results: List[Dict]) -> Dict:
        """生成综合测试报告"""
        total_time = time.time() - self.start_time if self.start_time else 0
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r["success"])

        # 基础统计
        basic_stats = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": total_tests - successful_tests,
            "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "total_test_time": total_time,
            "average_response_time": sum(r["elapsed_time"] for r in results)
            / total_tests
            if total_tests > 0
            else 0,
        }

        # 性能分析
        response_times = [r["elapsed_time"] for r in results if r["success"]]
        performance_stats = {
            "min_response_time": min(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "avg_response_time": sum(response_times) / len(response_times)
            if response_times
            else 0,
            "slow_responses": sum(
                1
                for t in response_times
                if t > EVALUATION_CONFIG["expected_response_time"]
            ),
        }

        # 评估分析
        evaluation_stats = {}
        if any("evaluation" in r for r in results):
            successful_evaluations = [
                r["evaluation"] for r in results if r["success"] and "evaluation" in r
            ]

            if successful_evaluations:
                overall_scores = [
                    e["scores"]["overall"] for e in successful_evaluations
                ]
                evaluation_stats = {
                    "average_overall_score": sum(overall_scores) / len(overall_scores),
                    "min_score": min(overall_scores),
                    "max_score": max(overall_scores),
                    "high_quality_responses": sum(
                        1 for score in overall_scores if score >= 0.8
                    ),
                    "low_quality_responses": sum(
                        1 for score in overall_scores if score < 0.5
                    ),
                }

        # 错误分析
        error_stats = {}
        if REPORT_CONFIG["include_error_analysis"]:
            failed_results = [r for r in results if not r["success"]]
            retry_stats = [
                r["retry_count"] for r in results if r.get("retry_count", 0) > 0
            ]

            error_stats = {
                "total_failures": len(failed_results),
                "total_retries": sum(r.get("retry_count", 0) for r in results),
                "tests_requiring_retry": len(retry_stats),
                "common_errors": {},  # 可以进一步分析错误类型
            }

        report = {
            "test_summary": {
                "model": self.model,
                "test_time": datetime.now().isoformat(),
                "config": EVALUATION_CONFIG,
            },
            "basic_statistics": basic_stats,
            "performance_analysis": performance_stats,
            "evaluation_analysis": evaluation_stats,
            "error_analysis": error_stats,
            "detailed_results": results,
        }

        return report


async def main():
    """主函数"""
    logger.info("开始增强版视觉模型测试...")

    tester = EnhancedVisionTester()

    logger.info(f"使用模型: {tester.model}")
    logger.info(f"API地址: {tester.base_url}")
    logger.info(f"测试用例数量: {len(TEST_CASES)}")
    logger.info("=" * 60)

    # 运行测试
    results = await tester.run_all_tests()

    # 生成报告
    report = tester.generate_comprehensive_report(results)

    # 输出报告摘要
    logger.info("=" * 60)
    logger.info("测试报告摘要")
    logger.info("=" * 60)

    basic_stats = report["basic_statistics"]
    logger.info(f"总测试数: {basic_stats['total_tests']}")
    logger.info(f"成功测试数: {basic_stats['successful_tests']}")
    logger.info(f"失败测试数: {basic_stats['failed_tests']}")
    logger.info(f"成功率: {basic_stats['success_rate']:.2%}")
    logger.info(f"总测试时间: {basic_stats['total_test_time']:.2f}秒")
    logger.info(f"平均响应时间: {basic_stats['average_response_time']:.2f}秒")

    if report["evaluation_analysis"]:
        eval_stats = report["evaluation_analysis"]
        logger.info(f"平均评分: {eval_stats.get('average_overall_score', 0):.2f}")
        logger.info(f"高质量回答数: {eval_stats.get('high_quality_responses', 0)}")
        logger.info(f"低质量回答数: {eval_stats.get('low_quality_responses', 0)}")

    # 保存详细报告
    with open(REPORT_CONFIG["output_file"], "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    logger.info(f"\n详细报告已保存到 {REPORT_CONFIG['output_file']}")


if __name__ == "__main__":
    asyncio.run(main())
