#!/usr/bin/env python3
"""
视觉模型对比测试脚本
对比 qwen2.5-vl-7b-instruct 和 qwen-vl-plus 的速度和效果
"""

import asyncio
import json
import time
import base64
from typing import Dict, List
import aiohttp
from loguru import logger
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from src.utils.handle_image import get_image_content
from vision_test_config import (
    TEST_CASES,
    MODEL_COMPARISON_CONFIG,
    WEATHER_INFO,
    PROMPT_TEMPLATE,
)


class ModelComparator:
    def __init__(self):
        self.models = MODEL_COMPARISON_CONFIG["models"]
        self.system_prompt = MODEL_COMPARISON_CONFIG["system_prompt"]
        self.results = {}

    async def get_image_content_for_model(
        self, model_config: Dict, image_url: str
    ) -> str:
        """根据模型类型获取图片内容"""
        if model_config.get("type") == "openai":
            # OpenAI需要base64格式
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(image_url) as response:
                        image_data = await response.read()
                base64_image = base64.b64encode(image_data).decode("utf-8")
                return f"data:image/jpeg;base64,{base64_image}"
            except Exception as e:
                logger.error(f"Error converting image to base64: {e}")
                return image_url
        else:
            # Qwen模型使用原有逻辑
            return await get_image_content(image_url)

    async def call_model(self, model_config: Dict, image_url: str, prompt: str) -> Dict:
        """调用指定模型"""
        image_content = await self.get_image_content_for_model(model_config, image_url)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {model_config['api_key']}",
        }

        # 根据模型类型构建不同的payload
        if model_config.get("type") == "openai":
            payload = {
                "model": model_config["name"],
                "temperature": 0.0,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": image_content},
                            },
                            {"type": "text", "text": prompt},
                        ],
                    },
                ],
                "max_tokens": 100,
            }
        else:
            # Qwen模型的payload格式
            payload = {
                "model": model_config["name"],
                "temperature": 0.0,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": image_content},
                            },
                            {"text": prompt, "type": "text"},
                        ],
                    },
                ],
            }

        start_time = time.time()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{model_config['base_url']}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30),
                ) as response:
                    result = await response.json()
                    elapsed_time = time.time() - start_time

                    model_result = (
                        result.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                    )

                    return {
                        "success": True,
                        "result": model_result,
                        "elapsed_time": elapsed_time,
                        "model_name": model_config["name"],
                    }
        except Exception as e:
            elapsed_time = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "elapsed_time": elapsed_time,
                "model_name": model_config["name"],
            }

    def create_prompt(self, question: str, conversation_history: str = "") -> str:
        """创建测试提示词"""
        return PROMPT_TEMPLATE.format(
            city=WEATHER_INFO["city"],
            weather=WEATHER_INFO["weather"],
            temperature=WEATHER_INFO["temperature"],
            conversation_history=conversation_history,
            question=question,
        )

    async def test_single_case(self, test_case: Dict) -> Dict:
        """测试单个用例在所有模型上的表现"""
        logger.info(f"测试用例 {test_case['id']}: {test_case['name']}")

        prompt = self.create_prompt(
            test_case["question"], test_case.get("conversation_history", "")
        )

        case_results = {
            "test_id": test_case["id"],
            "test_name": test_case["name"],
            "question": test_case["question"],
            "image_url": test_case["image_url"],
            "expected_answer": test_case.get("expected_answer", ""),
            "model_results": {},
        }

        # 并发测试所有模型
        tasks = []
        for model_config in self.models:
            task = self.call_model(model_config, test_case["image_url"], prompt)
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # 整理结果
        for result in results:
            model_name = result["model_name"]
            case_results["model_results"][model_name] = {
                "success": result["success"],
                "answer": result.get("result", ""),
                "elapsed_time": result["elapsed_time"],
                "error": result.get("error", ""),
            }

            # 输出结果
            if result["success"]:
                logger.info(
                    f"  {model_name}: {result['result']} ({result['elapsed_time']:.2f}s)"
                )
            else:
                logger.error(
                    f"  {model_name}: 错误 - {result.get('error', 'Unknown')} ({result['elapsed_time']:.2f}s)"
                )

        return case_results

    async def run_comparison(self) -> Dict:
        """运行完整对比测试"""
        logger.info("开始模型对比测试...")
        logger.info(f"对比模型: {[model['name'] for model in self.models]}")
        logger.info(f"System Prompt: {self.system_prompt}")
        logger.info("=" * 80)

        start_time = time.time()
        all_results = []

        for test_case in TEST_CASES:
            case_result = await self.test_single_case(test_case)
            all_results.append(case_result)
            logger.info("-" * 60)

        total_time = time.time() - start_time

        # 生成对比报告
        comparison_report = self.generate_comparison_report(all_results, total_time)

        return comparison_report

    def generate_comparison_report(
        self, results: List[Dict], total_time: float
    ) -> Dict:
        """生成对比报告"""
        model_names = [model["name"] for model in self.models]

        # 初始化统计数据
        model_stats = {}
        for model_name in model_names:
            model_stats[model_name] = {
                "total_tests": 0,
                "successful_tests": 0,
                "total_time": 0,
                "response_times": [],
                "answers": [],
            }

        # 收集统计数据
        for result in results:
            for model_name in model_names:
                model_result = result["model_results"].get(model_name, {})
                stats = model_stats[model_name]

                stats["total_tests"] += 1
                if model_result.get("success", False):
                    stats["successful_tests"] += 1
                    stats["response_times"].append(model_result["elapsed_time"])
                    stats["answers"].append(
                        {
                            "test_id": result["test_id"],
                            "question": result["question"],
                            "answer": model_result["answer"],
                            "elapsed_time": model_result["elapsed_time"],
                        }
                    )
                stats["total_time"] += model_result.get("elapsed_time", 0)

        # 计算性能指标
        performance_comparison = {}
        for model_name, stats in model_stats.items():
            response_times = stats["response_times"]
            performance_comparison[model_name] = {
                "success_rate": stats["successful_tests"] / stats["total_tests"]
                if stats["total_tests"] > 0
                else 0,
                "avg_response_time": sum(response_times) / len(response_times)
                if response_times
                else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "total_time": stats["total_time"],
            }

        report = {
            "comparison_summary": {
                "models": model_names,
                "system_prompt": self.system_prompt,
                "test_time": datetime.now().isoformat(),
                "total_test_time": total_time,
                "test_cases_count": len(TEST_CASES),
            },
            "performance_comparison": performance_comparison,
            "detailed_results": results,
            "model_stats": model_stats,
        }

        return report

    def print_comparison_summary(self, report: Dict):
        """打印对比摘要"""
        logger.info("=" * 80)
        logger.info("模型对比测试结果摘要")
        logger.info("=" * 80)

        perf_comparison = report["performance_comparison"]

        # 性能对比表格
        logger.info("性能对比:")
        logger.info(
            f"{'模型名称':<30} {'成功率':<10} {'平均响应时间':<15} {'最快响应':<12} {'最慢响应':<12}"
        )
        logger.info("-" * 80)

        for model_name, stats in perf_comparison.items():
            logger.info(
                f"{model_name:<30} {stats['success_rate']:.2%}    {stats['avg_response_time']:.2f}s        {stats['min_response_time']:.2f}s      {stats['max_response_time']:.2f}s"
            )

        logger.info("")

        # 速度对比
        fastest_model = min(
            perf_comparison.items(), key=lambda x: x[1]["avg_response_time"]
        )
        slowest_model = max(
            perf_comparison.items(), key=lambda x: x[1]["avg_response_time"]
        )

        logger.info("速度对比:")
        logger.info(
            f"  最快模型: {fastest_model[0]} (平均 {fastest_model[1]['avg_response_time']:.2f}s)"
        )
        logger.info(
            f"  最慢模型: {slowest_model[0]} (平均 {slowest_model[1]['avg_response_time']:.2f}s)"
        )

        if fastest_model[1]["avg_response_time"] > 0:
            speed_diff = (
                (
                    slowest_model[1]["avg_response_time"]
                    - fastest_model[1]["avg_response_time"]
                )
                / fastest_model[1]["avg_response_time"]
                * 100
            )
            logger.info(f"  速度差异: {speed_diff:.1f}%")

        logger.info("")

        # 成功率对比
        best_success = max(perf_comparison.items(), key=lambda x: x[1]["success_rate"])
        worst_success = min(perf_comparison.items(), key=lambda x: x[1]["success_rate"])

        logger.info("成功率对比:")
        logger.info(
            f"  最高成功率: {best_success[0]} ({best_success[1]['success_rate']:.2%})"
        )
        logger.info(
            f"  最低成功率: {worst_success[0]} ({worst_success[1]['success_rate']:.2%})"
        )

        logger.info("")

        # 详细回答对比
        logger.info("详细回答对比:")
        logger.info("-" * 80)

        for result in report["detailed_results"]:
            logger.info(f"测试 {result['test_id']}: {result['question']}")
            logger.info(f"期望回答: {result['expected_answer']}")

            for model_name in report["comparison_summary"]["models"]:
                model_result = result["model_results"].get(model_name, {})
                if model_result.get("success"):
                    logger.info(
                        f"  {model_name}: {model_result['answer']} ({model_result['elapsed_time']:.2f}s)"
                    )
                else:
                    logger.info(
                        f"  {model_name}: 失败 - {model_result.get('error', 'Unknown')}"
                    )

            logger.info("-" * 60)


async def main():
    """主函数"""
    comparator = ModelComparator()

    # 运行对比测试
    report = await comparator.run_comparison()

    # 打印摘要
    comparator.print_comparison_summary(report)

    # 保存详细报告
    report_file = "model_comparison_report.json"
    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    logger.info(f"\n详细对比报告已保存到 {report_file}")


if __name__ == "__main__":
    asyncio.run(main())
