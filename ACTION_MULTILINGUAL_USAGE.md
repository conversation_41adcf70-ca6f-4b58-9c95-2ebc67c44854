# Action 多语言 Display Name 支持

## 概述

现在Action模型直接支持多语言display name，无需单独维护映射文件。这个功能完全向后兼容，现有的Action定义和JSON文件都能正常工作。

## 使用方法

### 在Action定义中添加多语言支持

```python
from src.common.constant import LanguageEnum

Action(
    name="EXAMPLE_ACTION",
    display_name="示例动作",
    en_display_name="Example Action",
    # 新增多语言支持（可选）
    display_names={
        LanguageEnum.zh: "示例动作",           # 简体中文
        LanguageEnum.en: "Example Action",     # 英语
        LanguageEnum.zh_tw: "示例動作",        # 繁体中文
        LanguageEnum.ja_jp: "サンプルアクション",  # 日语
        LanguageEnum.ko_kr: "예시 동작",        # 韩语
        LanguageEnum.fr_fr: "Action d'exemple", # 法语
        LanguageEnum.de: "Beispiel-Aktion",    # 德语
        LanguageEnum.es_es: "Acción de ejemplo", # 西班牙语
    },
    # ... 其他字段
)
```

### 获取显示名称

```python
# 获取不同语言的显示名称
action = ActionLib().get_one_action(name="EXAMPLE_ACTION")

chinese_name = action.get_display_name(LanguageEnum.zh)      # "示例动作"
english_name = action.get_display_name(LanguageEnum.en)      # "Example Action"
japanese_name = action.get_display_name(LanguageEnum.ja_jp)  # "サンプルアクション"
```

## 向后兼容性

✅ **完全兼容** - `display_names` 字段是可选的，现有代码无需修改
✅ **自动回退** - 如果没有多语言定义，自动使用 `display_name` 和 `en_display_name`
✅ **渐进迁移** - 可以选择性地为需要的Action添加多语言支持

## 优先级规则

1. 如果 `display_names` 中有对应语言，使用该值
2. 如果是中文系语言且没有多语言定义，使用 `display_name`
3. 如果是英文且没有多语言定义，使用 `en_display_name`
4. 其他情况使用 `en_display_name` 或 `display_name`

## 支持的语言

支持所有 `LanguageEnum` 中定义的语言，包括：
- 简体中文 (zh_CN)
- 繁体中文 (zh_TW)
- 英语 (en_US)
- 日语 (ja_JP)
- 韩语 (ko_KR)
- 法语 (fr_FR)
- 德语 (de_DE)
- 西班牙语 (es_ES)
- 等等...

## 示例

参见 `src/action/actions.py` 中的 `CRUISE`、`SAY`、`WEATHER_GET`、`REGISTER` 等Action的定义示例。
