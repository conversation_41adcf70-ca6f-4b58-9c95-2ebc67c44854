## 写在前面
这个工程里从qdrant读取的内容是在二开平台那里[agentos_studio_backend](http://git.ainirobot.com/ai.character/aos/agentos_studio_backend)写入的，所以需要二者匹配。

机器级问答优先级高于企业级，支持问答同时生效于多台机器

企业ID（enterprise_id）一定能拿到，设备ID（device_id）不一定能拿到，因为可能是某个企业下所有的设备。

## qa干预

### qdrant 相关
#### qdrant collection 分配
```python
def get_collection_name_for_intervene_query(
    *,
    enterprise_id: str,
    embedding_model: str = "bge",
    embedding_dim: int = 1024,
) -> str:
    size, num_digits = 100, 2
    index = (
        int.from_bytes(
            hashlib.md5(enterprise_id.encode("utf-8")).digest(), byteorder="big"
        )
        % size
    )
    return f"intervene_query_embeddings_{embedding_model}_{embedding_dim}_{index:0{num_digits}d}"
```

#### qdrant point id 可追溯
使用 `uuid.uuid4().hex` 作为 `qdrant_point_id`，这可能导致无法根据特定逻辑生成唯一且可追溯的 ID，可能影响数据的一致性和可追溯性。
```python
def get_qdrant_point_id_for_intervene_query(
    *,
    uuid_from_frontend: str,
    question: str,
    output_length: Literal[32, 36] = 36,
) -> str:
    # NOTE: 在qdrant中，以下两种point_id格式是等价的：
    # (1) 不带横杠的长度为32的uuid字符串: "0d8305fce10dd4eb05a51f96c6e24aee"
    # (2) 带横杠的长度为36的uuid字符串: "0d8305fc-e10d-d4eb-05a5-1f96c6e24aee"
    # qdrant内部会自动转换为带横杠的长度为36的uuid字符串
    # 查询出来的point_id是带横杠的长度为36的uuid字符串
    question_hash = hashlib.md5(question.encode("utf-8")).hexdigest()
    _id = uuid_from_frontend.replace("-", "")[:16] + question_hash[:16]
    if output_length == 32:
        output_id = _id
    elif output_length == 36:
        output_id = f"{_id[:8]}-{_id[8:12]}-{_id[12:16]}-{_id[16:20]}-{_id[20:]}"
    else:
        raise ValueError(f"Invalid output_length: {output_length}")
    return output_id
```


### 根据qa问答对资源id获取问答详情
```python
import requests
from loguru import logger

# 国内online环境：
QA_PAIR_ONLINE_URL = "http://resource-readonly-online.ainirobot.com/open/v1/resource/qa_pair"
# 国内test环境:
QA_PAIR_TEST_URL = "http://resource-online-test.ainirobot.com/open/v1/resource/qa_pair"
PAIR_ID = 1413550285258752

# curl  -H "Content-Type: application/json" -X POST -d '{"pair_id": 1413550285258752}' http://resource-online-test.ainirobot.com/open/v1/resource/qa_pair
def do_request_qa_pair(*, url: str, pair_id: int, num_retries: int = 3):
    # 根据qa问答对资源id获取问答详情
    headers = {
        "Content-Type": "application/json"
    }
    obj = {
        "pair_id": pair_id
    }
    last_error = None
    for _ in range(num_retries):
        response = requests.post(url, headers=headers, json=obj)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to request qa pair: {response.json()}")
            last_error = response.json()
    return last_error

do_request_qa_pair(url=QA_PAIR_ONLINE_URL, pair_id=PAIR_ID)
response_like_this = {'code': 404}

do_request_qa_pair(url=QA_PAIR_TEST_URL, pair_id=PAIR_ID)
response_like_this = {'header': {'msg': 'OK', 'code': 0, 'time': 1744866775, 'request_id': ''},
 'data': {'pair_id': 1413550285258752,
  'library_id': 1393122422489088,
  'org_id': 1383063583129600,
  'now_status': 1,
  'uuid': 'a3f54d972bf60020729cafb9caf0331b',
  'question': ['测试设备级qa001', '相视问答001', '乡试问答002', '详细信息显示'],
  'normal_question': ['测试设备级qa001', '相视问答001', '乡试问答002', '详细信息显示', '详细信息显示'],
  'cutted_words': ['', '', '', ''],
  'answer': ['测试设备级answer001', '答案也是多个003', '水水水水水水水水水水水水水水'],
  'image_info': '',
  'radio_info': '',
  'video_info': '',
  'is_delete': 0,
  'created_at': '2025-04-14 20:29:12.515296444 +0800 CST m=+22911509.387875017',
  'intent': 'orion_baike',
  'src_pair_id': '',
  'group_id': 'M03SCN2B19025008383B'}}
```

## action干预

```Plain Text
v1.2.0.C、v1.2.0.O：支持全局技能干预
1. 根据用户query 命中技能。
2. 影响action候选列表变更为该action
3. 先使用配置文件配置
```

### reload_intervene_action
```bash
cd /path/to/easyNLP
source .venv/bin/activate
source .env_xxx
python -m scripts.reload_intervene_action \
    --resource_file_path "./src/intervene/resources/intervene_action/yyy.json"
```


## 轻应用干预


### 资源获取接口

```bash
curl --location 'http://aos-backend-test.orionstar.com/capi/v1/corp/gateway_agentos/app_webapp/9c44b988c1a940ccbdcdf9c8fdd6df7e' \
--header 'x-origws-c-c-ov-corp-id: orion.ovs.entprise.8449838034'
```

```json
{
   "code": 200,
   "message": "获取成功",
   "success": true,
   "data": {
      "lang": "zh_CN",
      "org_id": "orion.ovs.entprise.8449838034",
      "name": "测试应用25",
      "command": "你好啊",
      "similar_commands": [
         "nihao",
         "nihao1"
      ],
      "type": 0,
      "app_url": "https://example.com",
      "param_json": {
         "key": "value"
      },
      "screen_scale_percent": 100,
      "deal_status": 1,
      "id": 2,
      "uuid": "9c44b988c1a940ccbdcdf9c8fdd6df7e",
      "created_at": "2025-05-08 16:00:09",
      "updated_at": "2025-05-08 16:00:09"
   }
}
```


## TODO(<EMAIL>):

1. 召回策略细化

2. 获取用户配置的阈值

3. action干预接入主流程
