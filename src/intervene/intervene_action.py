import asyncio
import json
import time
import uuid
from pathlib import Path
from typing import List, Literal
from urllib.parse import urljoin

import aiohttp
from livekit.agents.utils import http_context
from loguru import logger
from pydantic import BaseModel, field_validator
from qdrant_client import AsyncQdrantClient
from qdrant_client import models as qdrant_models

from src.common.constant import SUPPORTED_LANGUAGE_CODES
from src.intervene.intervene_common import (
    get_collection_name_for_intervene,
    get_qdrant_point_id_for_intervene,
    get_default_query2query_similarity_threshold,
)
from src.settings import agent_setting
from src.utils.embedding_utils import async_get_embeddings, async_get_one_embedding
from src.utils.double_cache import ttl_cache


class ActionIntervenorConfig(BaseModel):
    embedding_api_key: str
    embedding_model_base_url: str
    embedding_model: str
    embedding_dim: int
    embedding_batch_size: int = 16
    embedding_enable_redis: bool = False

    qdrant_host: str
    qdrant_insert_batch_size: int = 16

    webapp_resource_base_url: str
    enterprise_level_threshold_url: str = ""
    aos_studio_timeout: int = 8


ACTION_INTERVENOR_CONFIG = ActionIntervenorConfig(
    embedding_api_key=agent_setting.embedding_api_key,
    embedding_model_base_url=agent_setting.embedding_model_base_url,
    embedding_model=agent_setting.embedding_model,
    embedding_dim=agent_setting.embedding_dim,
    embedding_batch_size=16,
    qdrant_host=agent_setting.qdrant_host,
    qdrant_insert_batch_size=16,
    embedding_enable_redis=agent_setting.intervene_embedding_enable_redis,
    webapp_resource_base_url=urljoin(
        agent_setting.aos_studio_host, agent_setting.aos_studio_webapp_resource_url
    ),
    enterprise_level_threshold_url=urljoin(
        agent_setting.aos_studio_host,
        agent_setting.aos_studio_intervene_action_enterprise_level_threshold_url,
    ),
    aos_studio_timeout=agent_setting.aos_studio_timeout,
)


class EnterpriseLevelInterveneActionQdrantPayload(BaseModel):
    uuid: str  # 前端传过来的uuid，一个uuid对应多个question
    question: str  # “功能问法”列表中的一个问法
    lang: str  # 标准语言代码，如"zh_CN", "en_US", "ja_JP", "de_DE"
    enterprise_id: str  # 企业ID
    action_namespace: str  # 例如"orion.agent.action"，全小写
    action_name: str  # 例如"set_volume"，全小写
    action_fullname: str  # 例如"orion.agent.action.set_volume"，全小写
    action_client_alias: str  # 例如"orion.agent.action.SET_VOLUME"，action_name全大写
    app_id: str = ""  # 空字符串表示app级别的全局作用域
    level: Literal["E"] = (
        "E"  # 此处固定为"E"，G表示全局（Global）配置，E表示企业级（Enterprise）配置，D表示设备级（Device）配置
    )
    source: Literal["action"] = "action"  # 此处固定为"action"
    resource_type: Literal[""] = ""
    have_slots: Literal["N"] = (
        "N"  # 此处固定为“N”，是否带槽位，"Y"表示带槽位，"N"表示不带槽位，带槽位需要调用资源接口获取槽位信息
    )

    @field_validator(
        "question",
        "enterprise_id",
        "lang",
        "action_namespace",
        "action_name",
        "action_fullname",
        "action_client_alias",
    )
    def validate_non_empty_str(cls, v: str) -> str:
        if not isinstance(v, str) or v.strip() == "":
            raise ValueError("Field must be nonempty string after stripping whitespace")
        return v

    @field_validator("uuid")
    def validate_uuid(cls, v: str) -> str:
        """验证UUID格式"""
        v = v.replace("-", "")
        if len(v) != 32:
            raise ValueError("UUID must be 32 characters long")
        if not all(c in "0123456789abcdef" for c in v.lower()):
            raise ValueError("UUID must contain only hexadecimal characters")
        return v


class WebappQdrantPayload(BaseModel):
    uuid: str  # 前端传过来的webapp_id，一个webapp_id对应多个问题，所以这个webapp_id不能作为qdrant_point_id，兼职resource_id（获取“功能网址”、“第三方应用”的“包名”、“类名”以及可能的Key和Value等信息）
    question: str  # “功能问法”列表中的一个问法
    lang: str  # 标准语言代码，如"zh_CN", "en_US", "ja_JP", "de_DE"
    enterprise_id: str  # 企业ID
    level: Literal["E"] = (
        "E"  # 此处固定为"E"，G表示全局（Global）配置，E表示企业级（Enterprise）配置，D表示设备级（Device）配置
    )
    source: Literal["action"] = "action"  # 此处固定为"action"
    resource_type: Literal["webapp"] = "webapp"  # 此处固定为"webapp"
    action_fullname: Literal["orion.agent.action.open_web_url_defined"] = (
        "orion.agent.action.open_web_url_defined"  # 此处固定为"orion.agent.action.open_web_url_defined"
    )
    have_slots: Literal["Y"] = (
        "Y"  # 此处固定为“Y”，是否带槽位，"Y"表示带槽位，"N"表示不带槽位，带槽位需要调用资源接口获取槽位信息
    )

    @field_validator(
        "question",
        "enterprise_id",
        "lang",
    )
    def validate_non_empty_str(cls, v: str) -> str:
        if not isinstance(v, str) or v.strip() == "":
            raise ValueError("Field must be nonempty string after stripping whitespace")
        return v

    @field_validator("uuid")
    def validate_uuid(cls, v: str) -> str:
        """验证UUID格式"""
        v = v.replace("-", "")
        if len(v) != 32:
            raise ValueError("UUID must be 32 characters long")
        if not all(c in "0123456789abcdef" for c in v.lower()):
            raise ValueError("UUID must contain only hexadecimal characters")
        return v


class GlobalInterveneActionQdrantPayload(BaseModel):
    uuid: str
    question: str
    action_namespace: str
    action_name: str
    action_fullname: str
    lang: str  # e.g. "zh_CN", "en_US", "ja_JP", "de_DE"
    enterprise_id: Literal[""] = ""  # 空字符串表示企业级别的全局作用域
    level: Literal["G"] = "G"
    source: Literal["action"] = "action"
    resource_type: Literal[""] = ""
    have_slots: Literal["N"] = "N"
    app_id: str = ""  # 空字符串表示app级别的全局作用域

    @field_validator("action_namespace", "action_name", "action_fullname")
    def convert_to_lowercase(cls, v: str) -> str:
        return v.lower()

    @field_validator(
        "question",
        "action_namespace",
        "action_name",
        "action_fullname",
        "lang",
    )
    def validate_non_empty_str(cls, v: str) -> str:
        if not isinstance(v, str) or v.strip() == "":
            raise ValueError("Field must be nonempty string after stripping whitespace")
        return v

    @field_validator("uuid")
    def validate_uuid(cls, v: str) -> str:
        """验证UUID格式"""
        v = v.replace("-", "")
        if len(v) != 32:
            raise ValueError("UUID must be 32 characters long")
        if not all(c in "0123456789abcdef" for c in v.lower()):
            raise ValueError("UUID must contain only hexadecimal characters")
        return v


class ActionResponse(BaseModel):
    enterprise_id: str
    resource_type: Literal[
        "", "webapp"
    ]  # 空字符串表示全局技能干预、企业级技能干预，"webapp"表示轻应用干预
    resource_id: str  # 轻应用干预时，resource_id为webapp_id（uuid），全局技能干预、企业级技能干预时，resource_id为空字符串
    question: str
    action_fullname: str
    action_client_alias: str = (
        ""  # 为空字符串是为了兼容全局技能干预、轻应用干预，企业级技能干预不为空字符串
    )
    app_id: str = ""
    language_code: str
    source: Literal["action"] = "action"
    level: Literal[
        "E", "G"
    ]  # 轻应用干预、企业级技能干预时，level为"E"，全局技能干预时，level为"G"
    have_slots: bool  # 轻应用干预时，have_slots为True，全局技能干预、企业级技能干预时，have_slots为False
    score: float


class WebappResource(BaseModel):
    enterprise_id: str
    resource_id: str
    lang: str
    org_id: str
    name: str
    question: str
    type: int  # 0: “功能网址”, 1: “第三方应用”
    app_url: str
    param_json: dict
    class_name: str = ""
    url: str


class ActionIntervenor:
    def __init__(
        self,
        config: ActionIntervenorConfig = ACTION_INTERVENOR_CONFIG,
        http_session: aiohttp.ClientSession | None = None,
    ):
        self.config = config
        self.embedding_api_key = config.embedding_api_key
        self.embedding_model_base_url = config.embedding_model_base_url
        self.embedding_model = config.embedding_model
        self.embedding_dim = config.embedding_dim
        self.embedding_batch_size = config.embedding_batch_size
        self.embedding_enable_redis = config.embedding_enable_redis
        self.qdrant_host = config.qdrant_host
        self.qdrant_insert_batch_size = config.qdrant_insert_batch_size
        self.vector_grpc_client = AsyncQdrantClient(
            host=self.qdrant_host, prefer_grpc=True
        )
        self.http_session = http_session
        self.http_session = self._ensure_http_session()
        self.webapp_resource_base_url = config.webapp_resource_base_url
        self.enterprise_level_threshold_url = config.enterprise_level_threshold_url
        self.aos_studio_timeout = config.aos_studio_timeout

    def _ensure_http_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self.http_session:
            self.http_session = http_context.http_session()
        return self.http_session

    async def get_one_embedding(self, query_id: str, text: str) -> list[float]:
        return await async_get_one_embedding(
            query_id=query_id,
            http_session=self.http_session,  # type: ignore
            embedding_api_key=self.embedding_api_key,
            embedding_model_base_url=self.embedding_model_base_url,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
            text=text,
            enable_redis=self.embedding_enable_redis,
        )

    async def get_embeddings(self, batch: list[str]) -> list[list[float]]:
        return await async_get_embeddings(
            http_session=self.http_session,  # type: ignore
            embedding_api_key=self.embedding_api_key,
            embedding_model_base_url=self.embedding_model_base_url,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
            batch=batch,
            enable_redis=self.embedding_enable_redis,
        )

    async def upsert_webapp_list(
        self, webapp_list: list[WebappQdrantPayload]
    ) -> list[WebappQdrantPayload]:
        collection_name_to_webapp_list: dict[str, list[WebappQdrantPayload]] = {}
        for webapp in webapp_list:
            collection_name = get_collection_name_for_intervene(
                enterprise_id=webapp.enterprise_id,
                embedding_model=self.embedding_model,
                embedding_dim=self.embedding_dim,
            )
            collection_name_to_webapp_list.setdefault(collection_name, []).append(
                webapp
            )

        failed_webapp_list: list[WebappQdrantPayload] = []
        for collection_name, _webapp_list in collection_name_to_webapp_list.items():
            if not await self.vector_grpc_client.collection_exists(collection_name):
                ok = await self.vector_grpc_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=self.embedding_dim,
                        distance=qdrant_models.Distance.COSINE,
                        datatype=qdrant_models.Datatype.FLOAT32,
                    ),
                    on_disk_payload=False,  # store payload in memory
                )
                if not ok:
                    logger.error(f"Failed to create collection: {collection_name}")
                    failed_webapp_list.extend(_webapp_list)
                    continue

                logger.info(f"Created collection: {collection_name}")
            batch_size = min(
                self.embedding_batch_size,
                self.qdrant_insert_batch_size,
                len(_webapp_list),
            )
            for webapp_batch in [
                _webapp_list[i : i + batch_size]
                for i in range(0, len(_webapp_list), batch_size)
            ]:
                try:
                    # get embeddings
                    embeddings = await self.get_embeddings(
                        [webapp.question for webapp in webapp_batch]
                    )
                except Exception as e:
                    logger.error(f"Failed to get embeddings: {e}")
                    failed_webapp_list.extend(webapp_batch)
                    continue
                try:
                    # build qdrant points, record-oriented formats
                    points = [
                        qdrant_models.PointStruct(
                            id=get_qdrant_point_id_for_intervene(
                                uuid_from_frontend=webapp.uuid,
                                question=webapp.question,
                            ),
                            vector=embedding,
                            payload=webapp.model_dump(),
                        )
                        for webapp, embedding in zip(webapp_batch, embeddings)
                    ]
                    # insert to qdrant
                    self.vector_grpc_client.upload_points(
                        collection_name=collection_name,
                        points=points,
                        batch_size=self.qdrant_insert_batch_size,
                        wait=True,
                    )
                except Exception as e:
                    logger.error(f"Failed to insert webapp list: {e}")
                    failed_webapp_list.extend(webapp_batch)
                    continue
        return failed_webapp_list

    async def upsert_global_intervene_action_list(
        self, ia_list: list[GlobalInterveneActionQdrantPayload]
    ) -> list[GlobalInterveneActionQdrantPayload]:
        collection_name_to_ia_list: dict[
            str, list[GlobalInterveneActionQdrantPayload]
        ] = {}
        for ia in ia_list:
            collection_name = get_collection_name_for_intervene(
                enterprise_id=ia.enterprise_id,
                embedding_model=self.embedding_model,
                embedding_dim=self.embedding_dim,
            )
            collection_name_to_ia_list.setdefault(collection_name, []).append(ia)

        failed_ia_list: list[GlobalInterveneActionQdrantPayload] = []
        for collection_name, _ia_list in collection_name_to_ia_list.items():
            if not await self.vector_grpc_client.collection_exists(collection_name):
                ok = await self.vector_grpc_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=self.embedding_dim,
                        distance=qdrant_models.Distance.COSINE,
                        datatype=qdrant_models.Datatype.FLOAT32,
                    ),
                    on_disk_payload=False,  # store payload in memory
                )
                if not ok:
                    logger.error(f"Failed to create collection: {collection_name}")
                    failed_ia_list.extend(_ia_list)
                    continue

                logger.info(f"Created collection: {collection_name}")
            batch_size = min(
                self.embedding_batch_size,
                self.qdrant_insert_batch_size,
                len(_ia_list),
            )
            for ia_batch in [
                _ia_list[i : i + batch_size]
                for i in range(0, len(_ia_list), batch_size)
            ]:
                try:
                    # get embeddings
                    embeddings = await self.get_embeddings(
                        [ia.question for ia in ia_batch]
                    )
                except Exception as e:
                    logger.error(f"Failed to get embeddings: {e}")
                    failed_ia_list.extend(ia_batch)
                    continue
                try:
                    # build qdrant points, record-oriented formats
                    points = [
                        qdrant_models.PointStruct(
                            id=get_qdrant_point_id_for_intervene(
                                uuid_from_frontend=ia.uuid,
                                question=ia.question,
                            ),
                            vector=embedding,
                            payload=ia.model_dump(),
                        )
                        for ia, embedding in zip(ia_batch, embeddings)
                    ]
                    # insert to qdrant
                    self.vector_grpc_client.upload_points(
                        collection_name=collection_name,
                        points=points,
                        batch_size=self.qdrant_insert_batch_size,
                        wait=True,
                    )
                except Exception as e:
                    logger.error(f"Failed to insert intervene action list: {e}")
                    failed_ia_list.extend(ia_batch)
                    continue
        return failed_ia_list

    async def delete_global_intervene_action_list(
        self, ia_list: list[GlobalInterveneActionQdrantPayload]
    ) -> list[GlobalInterveneActionQdrantPayload]:
        collection_name_to_ia_list: dict[
            str, list[GlobalInterveneActionQdrantPayload]
        ] = {}
        for ia in ia_list:
            collection_name = get_collection_name_for_intervene(
                enterprise_id=ia.enterprise_id,
                embedding_model=self.embedding_model,
                embedding_dim=self.embedding_dim,
            )
            collection_name_to_ia_list.setdefault(collection_name, []).append(ia)
        failed_ia_list: list[GlobalInterveneActionQdrantPayload] = []
        for collection_name, _ia_list in collection_name_to_ia_list.items():
            if not await self.vector_grpc_client.collection_exists(collection_name):
                logger.info(f"Collection {collection_name} does not exist")
                continue
            update_result: qdrant_models.UpdateResult = (
                await self.vector_grpc_client.delete(
                    collection_name=collection_name,
                    points_selector=qdrant_models.PointIdsList(
                        points=[
                            get_qdrant_point_id_for_intervene(
                                uuid_from_frontend=ia.uuid,
                                question=ia.question,
                            )
                            for ia in _ia_list
                        ]
                    ),
                    wait=True,
                )
            )
            if qdrant_models.UpdateStatus.COMPLETED != update_result.status:
                logger.error(f"Failed to delete intervene action: {_ia_list}")
                failed_ia_list.extend(_ia_list)
        return failed_ia_list

    async def search_global_intervene_action(
        self,
        *,
        query_id: str,
        query_text: str,
        query_embedding: list[float],
        app_id: str = "",
        language_code: str = "",
        candidate_actions_with_fullname: List[str] | None = None,
        threshold: float = 0.95,
        top_k: int = 1,
        exact_search: bool = True,
    ) -> tuple[list[qdrant_models.ScoredPoint], dict]:
        """
        Search global intervene action from qdrant
        """
        start_time = time.time()
        debug_info = {
            "total_tc": 0.0,
            "collection_exists_tc": 0.0,
            "search_tc": 0.0,
        }
        collection_name = get_collection_name_for_intervene(
            enterprise_id="",
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
        )
        _start_time = time.time()
        existed = await self.vector_grpc_client.collection_exists(collection_name)
        debug_info["collection_exists_tc"] = time.time() - _start_time
        if not existed:
            debug_info["total_tc"] = time.time() - start_time
            return [], debug_info
        _start_time = time.time()
        filter = qdrant_models.Filter(
            must=[
                qdrant_models.FieldCondition(
                    key="enterprise_id", match=qdrant_models.MatchValue(value="")
                ),
                qdrant_models.FieldCondition(
                    key="level", match=qdrant_models.MatchValue(value="G")
                ),
                qdrant_models.FieldCondition(
                    key="source", match=qdrant_models.MatchValue(value="action")
                ),
                qdrant_models.FieldCondition(
                    key="resource_type", match=qdrant_models.MatchValue(value="")
                ),
                qdrant_models.FieldCondition(
                    key="have_slots", match=qdrant_models.MatchValue(value="N")
                ),
                qdrant_models.FieldCondition(
                    key="app_id",
                    match=qdrant_models.MatchAny(
                        any=["", app_id]
                    ),  # 同时检索app级别全局作用域和特定app_id下的技能干预
                ),
            ]
        )
        if language_code and language_code in SUPPORTED_LANGUAGE_CODES:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="lang", match=qdrant_models.MatchValue(value=language_code)
                )
            )
        if candidate_actions_with_fullname:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="action_fullname",
                    match=qdrant_models.MatchAny(any=candidate_actions_with_fullname),
                )
            )
        points = await self.vector_grpc_client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=top_k,
            score_threshold=threshold,
            with_payload=True,
            query_filter=filter,
            search_params=qdrant_models.SearchParams(hnsw_ef=128, exact=exact_search),
        )
        if points:
            logger.info(
                f"query_id: {query_id}, query_text: {query_text},  collection: {collection_name}, searched points of global intervene action: {points}"
            )
        else:
            logger.info(
                f"query_id: {query_id}, query_text: {query_text}, collection: {collection_name}, not searched points of global intervene action"
            )
        debug_info["search_tc"] = time.time() - _start_time
        debug_info["total_tc"] = time.time() - start_time
        return points, debug_info

    async def search_enterprise_level_intervene_action(
        self,
        *,
        query_id: str,
        query_text: str,
        query_embedding: list[float],
        enterprise_id: str,
        app_id: str = "",
        language_code: str = "",
        candidate_actions_with_fullname: List[str] | None = None,
        candidate_actions_with_client_alias: List[str] | None = None,
        threshold: float | None = None,
        top_k: int = 1,
        exact_search: bool = True,
    ) -> tuple[list[qdrant_models.ScoredPoint], dict]:
        """
        Search enterprise level intervene action from qdrant
        """
        start_time = time.time()
        debug_info = {
            "total_tc": 0.0,
            "get_threshold_tc": 0.0,
            "threshold": threshold,
            "threshold_from": "argument",
            "collection_exists_tc": 0.0,
            "search_tc": 0.0,
        }
        if threshold is None:
            _start_time = time.time()
            threshold = await self.get_enterprise_level_threshold(
                enterprise_id=enterprise_id
            )
            if threshold is None:
                threshold = get_default_query2query_similarity_threshold(
                    embedding_model=self.embedding_model,
                    embedding_dim=self.embedding_dim,
                )
                debug_info["threshold_from"] = "default"
            else:
                debug_info["threshold_from"] = "aos_studio"
            debug_info["get_threshold_tc"] = time.time() - _start_time
            debug_info["threshold"] = threshold
        collection_name = get_collection_name_for_intervene(
            enterprise_id=enterprise_id,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
        )
        _start_time = time.time()
        existed = await self.vector_grpc_client.collection_exists(collection_name)
        logger.info(
            f"query_id: {query_id}, collection: {collection_name}, is existed: {existed}"
        )
        debug_info["collection_exists_tc"] = time.time() - _start_time
        if not existed:
            debug_info["total_tc"] = time.time() - start_time
            return [], debug_info
        _start_time = time.time()
        filter = qdrant_models.Filter(
            must=[
                qdrant_models.FieldCondition(
                    key="enterprise_id",
                    match=qdrant_models.MatchValue(value=enterprise_id),
                ),
                qdrant_models.FieldCondition(
                    key="level", match=qdrant_models.MatchValue(value="E")
                ),
                qdrant_models.FieldCondition(
                    key="source", match=qdrant_models.MatchValue(value="action")
                ),
                qdrant_models.FieldCondition(
                    key="resource_type", match=qdrant_models.MatchValue(value="")
                ),
                qdrant_models.FieldCondition(
                    key="have_slots", match=qdrant_models.MatchValue(value="N")
                ),
                qdrant_models.FieldCondition(
                    key="app_id",
                    match=qdrant_models.MatchAny(
                        any=["", app_id]
                    ),  # 同时检索app级别全局作用域和特定app_id下的技能干预
                ),
            ]
        )
        if language_code and language_code in SUPPORTED_LANGUAGE_CODES:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="lang", match=qdrant_models.MatchValue(value=language_code)
                )
            )
        if candidate_actions_with_client_alias:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="action_client_alias",
                    match=qdrant_models.MatchAny(
                        any=candidate_actions_with_client_alias
                    ),
                )
            )
        if candidate_actions_with_fullname:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="action_fullname",
                    match=qdrant_models.MatchAny(any=candidate_actions_with_fullname),
                )
            )
        points = await self.vector_grpc_client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=top_k,
            score_threshold=threshold,
            with_payload=True,
            query_filter=filter,
            search_params=qdrant_models.SearchParams(hnsw_ef=128, exact=exact_search),
        )
        if points:
            logger.info(
                f"query_text : {query_text}, collection: {collection_name}, searched points of enterprise level intervene action: {points}"
            )
        else:
            logger.info(
                f"query_text : {query_text}, collection: {collection_name}, not searched points of enterprise level intervene action"
            )
        debug_info["search_tc"] = time.time() - _start_time
        debug_info["total_tc"] = time.time() - start_time
        return points, debug_info

    async def search_webapp(
        self,
        *,
        query_id: str,
        query_text: str,
        query_embedding: list[float],
        enterprise_id: str,
        language_code: str = "",
        candidate_actions_with_fullname: List[str] | None = None,
        threshold: float = 0.95,
        top_k: int = 1,
        exact_search: bool = True,
    ) -> tuple[list[qdrant_models.ScoredPoint], dict]:
        """
        Search webapp from qdrant
        """
        debug_info = {
            "total_tc": 0.0,
            "collection_exists_tc": 0.0,
            "search_tc": 0.0,
        }
        start_time = time.time()
        if enterprise_id == "":
            return [], debug_info
        collection_name = get_collection_name_for_intervene(
            enterprise_id=enterprise_id,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
        )
        _start_time = time.time()
        existed = await self.vector_grpc_client.collection_exists(collection_name)
        debug_info["collection_exists_tc"] = time.time() - _start_time
        if not existed:
            debug_info["total_tc"] = time.time() - start_time
            return [], debug_info
        _start_time = time.time()
        filter = qdrant_models.Filter(
            must=[
                qdrant_models.FieldCondition(
                    key="enterprise_id",
                    match=qdrant_models.MatchValue(value=enterprise_id),
                ),
                qdrant_models.FieldCondition(
                    key="level", match=qdrant_models.MatchValue(value="E")
                ),
                qdrant_models.FieldCondition(
                    key="source", match=qdrant_models.MatchValue(value="action")
                ),
                qdrant_models.FieldCondition(
                    key="resource_type",
                    match=qdrant_models.MatchValue(value="webapp"),
                ),
                qdrant_models.FieldCondition(
                    key="have_slots", match=qdrant_models.MatchValue(value="Y")
                ),
            ]
        )
        if language_code and language_code in SUPPORTED_LANGUAGE_CODES:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="lang", match=qdrant_models.MatchValue(value=language_code)
                )
            )
        if candidate_actions_with_fullname:
            filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="action_fullname",
                    match=qdrant_models.MatchAny(any=candidate_actions_with_fullname),
                )
            )
        points = await self.vector_grpc_client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=top_k,
            score_threshold=threshold,
            with_payload=True,
            query_filter=filter,
            search_params=qdrant_models.SearchParams(hnsw_ef=128, exact=exact_search),
        )
        if points:
            logger.info(
                f"collection: {collection_name}, searched points of webapp: {points}"
            )
        debug_info["search_tc"] = time.time() - _start_time
        debug_info["total_tc"] = time.time() - start_time
        return points, debug_info

    async def search(
        self,
        query_id: str,
        query_text: str,
        enterprise_id: str,
        app_id: str = "",
        language_code: str = "",
        candidate_actions_with_fullname: List[str]
        | None = None,  # 为了兼容旧数据，全局技能干预、轻应用干预仍传入这个参数
        candidate_actions_with_client_alias: List[str]
        | None = None,  # 企业级技能干预传入这个参数
        threshold_user_configed_enterprise_level: float
        | None = None,  # 用户配置的企业级技能干预的阈值
        threshold: float = 0.95,
        top_k: int = 1,
        exact_search: bool = True,
    ) -> tuple[list[qdrant_models.ScoredPoint], dict]:
        """
        Search global intervene action, webapp and enterprise level intervene action from qdrant
        """
        start_time = time.time()
        debug_info = {
            "get_one_embedding_tc": 0.0,
            "search_global_intervene_action_total_tc": 0.0,
            "search_global_intervene_action_search_tc": 0.0,
            "search_global_intervene_action_collection_exists_tc": 0.0,
            "search_webapp_total_tc": 0.0,
            "search_webapp_search_tc": 0.0,
            "search_webapp_collection_exists_tc": 0.0,
            "search_enterprise_level_intervene_action_total_tc": 0.0,
            "search_enterprise_level_intervene_action_get_threshold_tc": 0.0,
            "search_enterprise_level_intervene_action_threshold": threshold_user_configed_enterprise_level,
            "search_enterprise_level_intervene_action_threshold_from": "argument",
            "search_enterprise_level_intervene_action_search_tc": 0.0,
            "search_enterprise_level_intervene_action_collection_exists_tc": 0.0,
            "rerank_tc": 0.0,
            "total_tc": 0.0,
        }
        _start_time = time.time()
        try:
            query_embedding = await self.get_one_embedding(
                query_id=query_id, text=query_text
            )
        except Exception as e:
            logger.error(
                f"query_id: {query_id}, query_text: {query_text}, get_one_embedding error: {e}"
            )
            debug_info["get_one_embedding_tc"] = time.time() - _start_time
            debug_info["total_tc"] = time.time() - start_time
            return [], debug_info
        debug_info["get_one_embedding_tc"] = time.time() - _start_time
        if enterprise_id == "":
            points, _debug_info = await self.search_global_intervene_action(
                query_id=query_id,
                query_text=query_text,
                query_embedding=query_embedding,
                app_id=app_id,
                language_code=language_code,
                candidate_actions_with_fullname=candidate_actions_with_fullname,
                threshold=threshold,
                top_k=top_k,
                exact_search=exact_search,
            )
            debug_info["search_global_intervene_action_total_tc"] = _debug_info[
                "total_tc"
            ]
            debug_info["search_global_intervene_action_search_tc"] = _debug_info[
                "search_tc"
            ]
            debug_info["search_global_intervene_action_collection_exists_tc"] = (
                _debug_info["collection_exists_tc"]
            )
            return points, debug_info
        tasks = [
            asyncio.create_task(
                self.search_global_intervene_action(
                    query_id=query_id,
                    query_text=query_text,
                    query_embedding=query_embedding,
                    app_id=app_id,
                    language_code=language_code,
                    candidate_actions_with_fullname=candidate_actions_with_fullname,
                    threshold=threshold,
                    top_k=top_k,
                    exact_search=exact_search,
                )
            ),
            asyncio.create_task(
                self.search_webapp(
                    query_id=query_id,
                    query_text=query_text,
                    query_embedding=query_embedding,
                    enterprise_id=enterprise_id,
                    language_code=language_code,
                    candidate_actions_with_fullname=candidate_actions_with_fullname,
                    threshold=threshold,
                    top_k=top_k,
                    exact_search=exact_search,
                )
            ),
            asyncio.create_task(
                self.search_enterprise_level_intervene_action(
                    query_id=query_id,
                    query_text=query_text,
                    query_embedding=query_embedding,
                    enterprise_id=enterprise_id,
                    language_code=language_code,
                    candidate_actions_with_fullname=candidate_actions_with_fullname,
                    candidate_actions_with_client_alias=candidate_actions_with_client_alias,
                    threshold=threshold_user_configed_enterprise_level,
                    top_k=top_k,
                    exact_search=exact_search,
                )
            ),
        ]
        (
            (points_global_intervene_action, _debug_info_global_intervene_action),
            (points_webapp, _debug_info_webapp),
            (
                points_enterprise_level_intervene_action,
                _debug_info_enterprise_level_intervene_action,
            ),
        ) = await asyncio.gather(*tasks)
        # rerank
        _start_time = time.time()
        points = (
            points_webapp
            + points_global_intervene_action
            + points_enterprise_level_intervene_action
        )
        if points:
            points.sort(key=lambda x: x.score, reverse=True)
            points = points[:top_k]
        debug_info["rerank_tc"] = time.time() - _start_time
        debug_info["search_global_intervene_action_total_tc"] = (
            _debug_info_global_intervene_action["total_tc"]
        )
        debug_info["search_global_intervene_action_search_tc"] = (
            _debug_info_global_intervene_action["search_tc"]
        )
        debug_info["search_global_intervene_action_collection_exists_tc"] = (
            _debug_info_global_intervene_action["collection_exists_tc"]
        )
        debug_info["search_webapp_total_tc"] = _debug_info_webapp["total_tc"]
        debug_info["search_webapp_search_tc"] = _debug_info_webapp["search_tc"]
        debug_info["search_webapp_collection_exists_tc"] = _debug_info_webapp[
            "collection_exists_tc"
        ]
        debug_info["search_enterprise_level_intervene_action_total_tc"] = (
            _debug_info_enterprise_level_intervene_action["total_tc"]
        )
        debug_info["search_enterprise_level_intervene_action_get_threshold_tc"] = (
            _debug_info_enterprise_level_intervene_action["get_threshold_tc"]
        )
        debug_info["search_enterprise_level_intervene_action_threshold"] = (
            _debug_info_enterprise_level_intervene_action["threshold"]
        )
        debug_info["search_enterprise_level_intervene_action_threshold_from"] = (
            _debug_info_enterprise_level_intervene_action["threshold_from"]
        )
        debug_info["search_enterprise_level_intervene_action_search_tc"] = (
            _debug_info_enterprise_level_intervene_action["search_tc"]
        )
        debug_info["search_enterprise_level_intervene_action_collection_exists_tc"] = (
            _debug_info_enterprise_level_intervene_action["collection_exists_tc"]
        )
        debug_info["total_tc"] = time.time() - start_time
        return points, debug_info

    def convert_qdrant_scored_points_to_action_responses(
        self,
        point_list: list[qdrant_models.ScoredPoint],
    ) -> list[ActionResponse]:
        action_responses: list[ActionResponse] = []
        for point in point_list:
            resource_type = point.payload["resource_type"]  # type: ignore
            resource_id = point.payload["uuid"]  # type: ignore
            if (
                resource_type == ""
            ):  # 全局技能干预、企业级技能干预时，resource_type为空字符串
                resource_id = (
                    ""  # 全局技能干预、企业级技能干预时，resource_id为空字符串
                )
            action_responses.append(
                ActionResponse(
                    enterprise_id=point.payload["enterprise_id"],  # type: ignore
                    resource_type=resource_type,
                    resource_id=resource_id,
                    question=point.payload["question"],  # type: ignore
                    action_fullname=point.payload["action_fullname"],  # type: ignore
                    action_client_alias=point.payload.get("action_client_alias", ""),  # type: ignore
                    app_id=point.payload.get("app_id", ""),  # type: ignore
                    language_code=point.payload["lang"],  # type: ignore
                    source=point.payload["source"],  # type: ignore
                    level=point.payload["level"],  # type: ignore
                    have_slots=point.payload["have_slots"] == "Y",  # type: ignore
                    score=point.score,
                )
            )
        return action_responses

    async def fetch_top_k_actions(
        self,
        *,
        query_id: str,
        query_text: str,
        enterprise_id: str = "",
        app_id: str = "",
        language_code: str = "",
        candidate_actions_with_fullname: List[str]
        | None = None,  # 为了兼容旧数据，全局技能干预、轻应用干预仍传入这个参数
        candidate_actions_with_client_alias: List[str]
        | None = None,  # 企业级技能干预传入这个参数
        threshold_user_configed_enterprise_level: float
        | None = None,  # 用户配置的企业级技能干预的阈值
        threshold: float = -42,
        top_k: int = 1,
    ) -> tuple[List[ActionResponse], dict]:
        start_time = time.time()
        if threshold < -1.0:
            threshold = get_default_query2query_similarity_threshold(
                embedding_model=self.embedding_model,
                embedding_dim=self.embedding_dim,
            )
        d = {
            "intervene_action_query_id": query_id,
            "intervene_action_query2query_similarity_threshold": threshold,
            "intervene_action_total_timecost": 0.0,
            "intervene_action_convert_timecost": 0.0,
            "intervene_action_embedding_timecost": 0.0,
            "intervene_action_global_total_timecost": 0.0,
            "intervene_action_global_search_timecost": 0.0,
            "intervene_action_global_collection_exists_timecost": 0.0,
            "intervene_action_webapp_total_timecost": 0.0,
            "intervene_action_webapp_search_timecost": 0.0,
            "intervene_action_webapp_collection_exists_timecost": 0.0,
            "intervene_action_enterprise_level_total_timecost": 0.0,
            "intervene_action_enterprise_level_get_threshold_tc": 0.0,
            "intervene_action_enterprise_level_threshold": threshold_user_configed_enterprise_level,
            "intervene_action_enterprise_level_threshold_from": "argument",
            "intervene_action_enterprise_level_search_timecost": 0.0,
            "intervene_action_enterprise_level_collection_exists_timecost": 0.0,
            "intervene_action_rerank_timecost": 0.0,
        }
        point_list, _d = await self.search(
            query_id=query_id,
            query_text=query_text,
            enterprise_id=enterprise_id,
            app_id=app_id,
            language_code=language_code,
            candidate_actions_with_fullname=candidate_actions_with_fullname,
            candidate_actions_with_client_alias=candidate_actions_with_client_alias,
            threshold_user_configed_enterprise_level=threshold_user_configed_enterprise_level,
            threshold=threshold,
            top_k=top_k,
        )
        d["intervene_action_embedding_timecost"] = _d["get_one_embedding_tc"]
        d["intervene_action_global_total_timecost"] = _d[
            "search_global_intervene_action_total_tc"
        ]
        d["intervene_action_global_search_timecost"] = _d[
            "search_global_intervene_action_search_tc"
        ]
        d["intervene_action_global_collection_exists_timecost"] = _d[
            "search_global_intervene_action_collection_exists_tc"
        ]
        d["intervene_action_webapp_total_timecost"] = _d["search_webapp_total_tc"]
        d["intervene_action_webapp_search_timecost"] = _d["search_webapp_search_tc"]
        d["intervene_action_webapp_collection_exists_timecost"] = _d[
            "search_webapp_collection_exists_tc"
        ]
        d["intervene_action_enterprise_level_total_timecost"] = _d[
            "search_enterprise_level_intervene_action_total_tc"
        ]
        d["intervene_action_enterprise_level_get_threshold_tc"] = _d[
            "search_enterprise_level_intervene_action_get_threshold_tc"
        ]
        d["intervene_action_enterprise_level_threshold"] = _d[
            "search_enterprise_level_intervene_action_threshold"
        ]
        d["intervene_action_enterprise_level_threshold_from"] = _d[
            "search_enterprise_level_intervene_action_threshold_from"
        ]
        d["intervene_action_enterprise_level_search_timecost"] = _d[
            "search_enterprise_level_intervene_action_search_tc"
        ]
        d["intervene_action_enterprise_level_collection_exists_timecost"] = _d[
            "search_enterprise_level_intervene_action_collection_exists_tc"
        ]
        d["intervene_action_rerank_timecost"] = _d["rerank_tc"]
        action_response_list = []
        if not point_list:
            logger.warning(
                f"query_id: {query_id}, no action found for query: {query_text} enterprise_id: {enterprise_id} language_code: {language_code} app_id: {app_id} candidate_actions_with_client_alias: {candidate_actions_with_client_alias} candidate_actions_with_fullname: {candidate_actions_with_fullname}"
            )
        else:
            logger.info(
                f"query_id: {query_id}, {len(point_list)} actions found for query: {query_text} enterprise_id: {enterprise_id} language_code: {language_code} app_id: {app_id} candidate_actions_with_client_alias: {candidate_actions_with_client_alias} candidate_actions_with_fullname: {candidate_actions_with_fullname}"
            )
            _start_time = time.time()
            action_response_list = (
                self.convert_qdrant_scored_points_to_action_responses(point_list)
            )
            d["intervene_action_convert_timecost"] = time.time() - _start_time
        d["intervene_action_total_timecost"] = time.time() - start_time
        return action_response_list, d

    async def get_webapp_resource(
        self,
        *,
        resource_id: str,
        enterprise_id: str,
    ) -> WebappResource | None:
        try:
            async with self.http_session.get(  # type: ignore
                urljoin(self.webapp_resource_base_url, resource_id),
                headers={"x-origws-c-c-ov-corp-id": enterprise_id},
                timeout=aiohttp.ClientTimeout(total=self.aos_studio_timeout),
            ) as response:
                if response.status != 200:
                    raise RuntimeError(
                        f"Failed to get webapp resource: {response.status} resource_id: {resource_id}, enterprise_id: {enterprise_id}"
                    )
                data = (await response.json())["data"]
                webapp_type = data["type"]
                app_url = data["app_url"]
                if webapp_type == 0:
                    class_name = ""
                    url = app_url
                else:
                    package_name = data["param_json"]["package_name"]
                    class_name = data["param_json"]["class_name"]
                    url = "package_name://" + package_name + "/" + class_name
                return WebappResource(
                    enterprise_id=data["org_id"],
                    resource_id=data["uuid"],
                    lang=data["lang"],
                    org_id=data["org_id"],
                    name=data["name"],
                    question=data["command"],
                    type=webapp_type,
                    app_url=app_url,
                    param_json=data["param_json"],
                    class_name=class_name,
                    url=url,
                )
        except Exception as e:
            logger.error(
                f"Failed to get webapp resource: {e} resource_id: {resource_id}, enterprise_id: {enterprise_id}"
            )
            return None

    @ttl_cache(ttl=60)
    async def get_enterprise_level_threshold(
        self,
        *,
        enterprise_id: str,
    ) -> float | None:
        try:
            async with self.http_session.get(  # type: ignore
                self.enterprise_level_threshold_url,
                headers={"x-origws-c-c-ov-corp-id": enterprise_id},
                timeout=aiohttp.ClientTimeout(total=self.aos_studio_timeout),
            ) as response:
                if response.status != 200:
                    raise RuntimeError(
                        f"Failed to get user configed threshold enterprise level: {response.status} enterprise_id: {enterprise_id}"
                    )
                data = (await response.json())["data"]
                mapped_threshold = data["mapped_threshold"]
                if (
                    isinstance(mapped_threshold, float)
                    and 0.5 <= mapped_threshold <= 1.0
                ):
                    return mapped_threshold
                else:
                    return None
        except Exception as e:
            logger.error(
                f"Failed to get user configed threshold enterprise level: {e} enterprise_id: {enterprise_id}"
            )
            return None

    async def parse_intervene_action_resource(
        self, resource_file_path: Path | str
    ) -> list[GlobalInterveneActionQdrantPayload]:
        with open(resource_file_path, "r") as fin:
            obj = json.load(fin)
        ia_map: dict[str, GlobalInterveneActionQdrantPayload] = {}
        _obj_dict = obj.get("question_to_expected_action", {})
        if isinstance(_obj_dict, dict):
            for question, _obj in _obj_dict.items():
                try:
                    language_code = _obj["language_code"]
                    assert language_code in SUPPORTED_LANGUAGE_CODES, (
                        f"language_code {language_code} is not supported"
                    )
                    expected_action_obj = _obj["expected_action"]
                    action_namespace = expected_action_obj["namespace"]
                    action_name = expected_action_obj["name"]
                    app_id = expected_action_obj.get("app_id", "")
                    action_fullname = f"{action_namespace}.{action_name}"
                    key = f"{question}_{app_id}"
                    if key in ia_map:
                        logger.warning(
                            f"question overwritten: {question} {ia_map[key]} -> {expected_action_obj}"
                        )
                    ia_map[key] = GlobalInterveneActionQdrantPayload(
                        uuid=uuid.uuid4().hex,
                        question=question,
                        action_namespace=action_namespace,
                        action_name=action_name,
                        action_fullname=action_fullname,
                        lang=language_code,
                        app_id=app_id,
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to parse intervene action resource: {e} question: {question}, _obj: {_obj}"
                    )
                    continue
        _obj_list = obj.get("expected_action_and_question_list", [])
        if isinstance(_obj_list, list):
            for _obj in _obj_list:
                try:
                    expected_action_obj = _obj["expected_action"]
                    action_namespace = expected_action_obj["namespace"]
                    action_name = expected_action_obj["name"]
                    app_id = expected_action_obj.get("app_id", "")
                    action_fullname = f"{action_namespace}.{action_name}"
                    question_list = _obj["question_list"]
                    for question_obj in question_list:
                        try:
                            question = question_obj["question"]
                            language_code = question_obj["language_code"]
                            assert language_code in SUPPORTED_LANGUAGE_CODES, (
                                f"language_code {language_code} is not supported"
                            )
                            key = f"{question}_{app_id}"
                            if key in ia_map:
                                logger.warning(
                                    f"question overwritten: {question} {ia_map[key]} -> {expected_action_obj}"
                                )
                            ia_map[key] = GlobalInterveneActionQdrantPayload(
                                uuid=uuid.uuid4().hex,
                                question=question,
                                action_namespace=action_namespace,
                                action_name=action_name,
                                action_fullname=action_fullname,
                                lang=language_code,
                                app_id=app_id,
                            )
                        except Exception as e:
                            logger.error(
                                f"Failed to parse intervene action resource: {e} question_obj: {question_obj}"
                            )
                            continue
                except Exception as e:
                    logger.error(
                        f"Failed to parse intervene action resource: {e} _obj: {_obj}"
                    )
                    continue
        return list(ia_map.values())

    async def reload_intervene_action_collection(self, resource_file_path: Path | str):
        collection_name = get_collection_name_for_intervene(
            enterprise_id="",
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
        )
        if await self.vector_grpc_client.collection_exists(collection_name):
            await self.vector_grpc_client.delete(
                collection_name=collection_name,
                points_selector=qdrant_models.Filter(
                    must=[
                        qdrant_models.FieldCondition(
                            key="enterprise_id",
                            match=qdrant_models.MatchValue(value=""),
                        ),
                        qdrant_models.FieldCondition(
                            key="level",
                            match=qdrant_models.MatchValue(value="G"),
                        ),
                        qdrant_models.FieldCondition(
                            key="source",
                            match=qdrant_models.MatchValue(value="action"),
                        ),
                        qdrant_models.FieldCondition(
                            key="resource_type",
                            match=qdrant_models.MatchValue(value=""),
                        ),
                        qdrant_models.FieldCondition(
                            key="have_slots",
                            match=qdrant_models.MatchValue(value="N"),
                        ),
                    ]
                ),
                wait=True,
            )
            logger.info(
                f"Deleted global intervene action points in collection: {collection_name}"
            )
        ia_list = await self.parse_intervene_action_resource(resource_file_path)
        ia_list_failed = await self.upsert_global_intervene_action_list(ia_list)
        return ia_list, ia_list_failed


if __name__ == "__main__":
    import time

    action_intervenor_config_jsonstr = json.dumps(
        json.loads(ACTION_INTERVENOR_CONFIG.model_dump_json()),
        ensure_ascii=False,
        indent=4,
    )
    logger.info(f"\naction_intervenor_config: {action_intervenor_config_jsonstr}")

    script_dir = Path(__file__).parent
    resource_file_path = script_dir / "resources" / "intervene_action" / "example.json"
    logger.info(f"resource file path: {resource_file_path}")

    testcase_list = [
        {
            "id": 1,
            "query_text": "磕个头",
            # bge_1024: "磕头" 0.9590, "鞠躬" 0.8822, "点头" 0.8733, "放首歌" 0.8053, "来首音乐" 0.7866, "查快递" 0.7748, "明天不上班" 0.7420
            # text-embedding-3-small_1024: "磕头" 0.8764, "点头" 0.5500, "鞠躬" 0.3128, "明天不上班" 0.2171
            "enterprise_id": "",
            "language_code": "zh_CN",
            "candidate_actions_with_fullname": [
                "orion.agent.action.HEAD_NOD",
                "orion.agent.action.OPEN_WEB_URL_DEFINED",
                "orion.agent.action.SAY",
                "orion.agent.action.NOT_MOVE",
                "orion.agent.action.KNOWLEDGE_QA",
            ],
            "threshold": 0.0,
            "top_k": 50,
        },
        {
            "id": 2,
            "query_text": "磕个头",
            "enterprise_id": "",
            "language_code": "zh_CN",
            "candidate_actions_with_fullname": [
                "orion.agent.action.OPEN_WEB_URL_DEFINED",
                "orion.agent.action.SAY",
                "orion.agent.action.NOT_MOVE",
                "orion.agent.action.KNOWLEDGE_QA",
            ],
            "threshold": 0.95,
            "top_k": 1,
        },
        {
            "id": 3,
            "query_text": "摸摸头",
            "enterprise_id": "",
            "language_code": "zh_CN",
            "candidate_actions_with_fullname": [
                "orion.agent.action.HEAD_NOD",
                "orion.agent.action.OPEN_WEB_URL_DEFINED",
            ],
            "threshold": 0.95,
            "top_k": 1,
        },
        {
            "id": 4,
            "query_text": "给个头",
            "enterprise_id": "",
            "language_code": "zh_CN",
            "candidate_actions_with_fullname": [
                "orion.agent.action.HEAD_NOD",
                "orion.agent.action.OPEN_WEB_URL_DEFINED",
                "orion.agent.action.SAY",
            ],
            "threshold": 0.95,
            "top_k": 1,
        },
        {
            "id": 5,
            "query_text": "对个头",
            "enterprise_id": "",
            "language_code": "zh_CN",
            "candidate_actions_with_fullname": [
                "orion.agent.action.HEAD_NOD",
                "orion.agent.action.OPEN_WEB_URL_DEFINED",
                "orion.agent.action.SAY",
                "orion.agent.action.NOT_MOVE",
                "orion.agent.action.KNOWLEDGE_QA",
            ],
            "threshold": 0.95,
            "top_k": 1,
        },
        {
            "id": 6,
            "query_text": "别磕头啦",
            # bge_1024: "磕个头" 0.8872, "磕头" 0.8870, "鞠躬" 0.8200, "点头" 0.7907, "明天不上班" 0.7682, "放首歌" 0.7472
            # text-embedding-3-small_1024: "磕头" 0.8163, "磕个头" 0.7781, "点头" 0.5104, "鞠躬" 0.2942, "明天不上班" 0.2545
            "enterprise_id": "",
            "language_code": "zh_CN",
            "candidate_actions_with_fullname": [
                "orion.agent.action.HEAD_NOD",
                "orion.agent.action.OPEN_WEB_URL_DEFINED",
                "orion.agent.action.SAY",
                "orion.agent.action.NOT_MOVE",
                "orion.agent.action.KNOWLEDGE_QA",
            ],
            "threshold": 0.0,
            "top_k": 50,
        },
        {
            "id": 7,
            "query_text": "明天不上班",
            # bge_1024: "磕个头" 0.7424
            # text-embedding-3-small_1024: "点头" 0.2415, "磕个头" 0.2171
            "enterprise_id": "",
            "language_code": "",
            "candidate_actions_with_fullname": [],
            "threshold": 0.0,
            "top_k": 50,
        },
        {
            "id": 8,
            "query_text": "明天不上班",
            "enterprise_id": "orion.ovs.entprise.9945420568",
            "language_code": "",
            "candidate_actions_with_fullname": [],
            "threshold": 0.0,
            "top_k": 50,
        },
    ]

    async def main():
        async with aiohttp.ClientSession() as session:
            start_at = time.time()
            action_intervenor = ActionIntervenor(http_session=session)
            elapsed = time.time() - start_at
            logger.info(f"init action_intervenor elapsed: {elapsed} seconds")

            # (
            #     ia_list,
            #     ia_list_failed,
            # ) = await action_intervenor.reload_intervene_action_collection(
            #     resource_file_path
            # )
            # if ia_list_failed:
            #     logger.error(
            #         "reload intervene action collection failed, ia_list_failed: %s"
            #         % ia_list_failed
            #     )
            # else:
            #     logger.info(
            #         "reload intervene action collection success, all upserted: %s"
            #         % len(ia_list)
            #     )

            for testcase in testcase_list:
                logger.info(f"testcase id: {testcase['id']}")
                (
                    action_responses,
                    _dbg_info,
                ) = await action_intervenor.fetch_top_k_actions(
                    query_id=testcase["id"],
                    query_text=testcase["query_text"],
                    enterprise_id=testcase["enterprise_id"],
                    language_code=testcase["language_code"],
                    candidate_actions_with_fullname=testcase[
                        "candidate_actions_with_fullname"
                    ],
                    threshold=testcase["threshold"],
                    top_k=testcase["top_k"],
                )
                logger.info(f"action_responses: {action_responses}")
                logger.info(f"_dbg_info: {_dbg_info}")
                if action_responses is None:
                    logger.error(
                        f"action responses is None for testcase id: {testcase['id']}"
                    )
                    continue
                action_responses_jsonstr = json.dumps(
                    [
                        json.loads(action_response.model_dump_json())
                        for action_response in action_responses
                    ],
                    ensure_ascii=False,
                    indent=4,
                )
                logger.info(f"action_responses_jsonstr: {action_responses_jsonstr}")

            # await action_intervenor.delete_intervene_action_list(ia_list)
            # logger.info(f"Deleted query list: {ia_list}")
            # collection_name = get_collection_name_for_intervene(
            #     env=action_intervenor.env,
            #     embedding_model=action_intervenor.embedding_model,
            #     embedding_dim=action_intervenor.embedding_dim,
            # )
            # await action_intervenor.vector_grpc_client.delete_collection(
            #     collection_name
            # )
            # logger.info(f"Deleted collection: {collection_name}")

            start_at = time.time()
            webapp_resource = await action_intervenor.get_webapp_resource(
                resource_id="06464ebaa9f243e986bf8a2f8433e34b",
                enterprise_id="orion.ovs.entprise.8449838034",
            )
            elapsed = time.time() - start_at
            logger.info(f"get_webapp_resource elapsed: {elapsed} seconds")
            if webapp_resource is None:
                logger.error("webapp_resource is None")
            else:
                webapp_resource_jsonstr = json.dumps(
                    json.loads(webapp_resource.model_dump_json()),
                    ensure_ascii=False,
                    indent=4,
                )
                logger.info(f"webapp_resource: {webapp_resource_jsonstr}")

    asyncio.run(main())
