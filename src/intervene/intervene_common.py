import hashlib
from typing import Literal

from src.settings import agent_setting


def get_collection_name_for_intervene(
    *,
    enterprise_id: str,
    embedding_model: str = agent_setting.embedding_model,
    embedding_dim: int = agent_setting.embedding_dim,
) -> str:
    size, num_digits = 100, 2
    index = (
        int.from_bytes(
            hashlib.md5(enterprise_id.encode("utf-8")).digest(), byteorder="big"
        )
        % size
    )
    return f"intervene_query_embeddings_{embedding_model}_{embedding_dim}_{index:0{num_digits}d}"


def get_qdrant_point_id_for_intervene(
    *,
    uuid_from_frontend: str,
    question: str,
    output_length: Literal[32, 36] = 36,
) -> str:
    # NOTE: 在qdrant中，以下两种point_id格式是等价的：
    # (1) 不带横杠的长度为32的uuid字符串: "0d8305fce10dd4eb05a51f96c6e24aee"
    # (2) 带横杠的长度为36的uuid字符串: "0d8305fc-e10d-d4eb-05a5-1f96c6e24aee"
    # qdrant内部会自动转换为带横杠的长度为36的uuid字符串
    # 查询出来的point_id是带横杠的长度为36的uuid字符串
    question_hash = hashlib.md5(question.encode("utf-8")).hexdigest()
    _id = uuid_from_frontend.replace("-", "")[:16] + question_hash[:16]
    if output_length == 32:
        output_id = _id
    elif output_length == 36:
        output_id = f"{_id[:8]}-{_id[8:12]}-{_id[12:16]}-{_id[16:20]}-{_id[20:]}"
    else:
        raise ValueError(f"Invalid output_length: {output_length}")
    return output_id


def get_default_query2query_similarity_threshold(
    embedding_model: str = agent_setting.embedding_model,
    embedding_dim: int = agent_setting.embedding_dim,
) -> float:
    THRESHOLD_MAP = {
        "bge_1024": 0.95,
        "text-embedding-3-small_1024": 0.78,
    }
    threshold = THRESHOLD_MAP.get(f"{embedding_model}_{embedding_dim}", 0.95)
    return threshold
