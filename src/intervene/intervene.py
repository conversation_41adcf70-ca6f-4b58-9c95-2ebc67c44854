import json
import asyncio
import requests
from pydantic import BaseModel, field_validator
from typing import List, Optional, Literal
import aiohttp
from loguru import logger
from qdrant_client import AsyncQdrantClient
from qdrant_client import models as qdrant_models
from livekit.agents.utils import http_context

from src.common.constant import SUPPORTED_LANGUAGE_CODES, LANGUAGE_CODE_TO_ENGLISH_NAME
from src.settings import agent_setting
from src.utils.embedding_utils import async_get_embeddings, async_get_one_embedding
from src.intervene.intervene_common import (
    get_collection_name_for_intervene,
    get_qdrant_point_id_for_intervene,
)


def do_request_qa_pair(*, url: str, pair_id: int, num_retries: int = 3):
    # 根据qa问答对资源id获取问答详情
    headers = {"Content-Type": "application/json"}
    obj = {"pair_id": pair_id}
    last_error = None
    for _ in range(num_retries):
        response = requests.post(url, headers=headers, json=obj)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to request qa pair: {response.json()}")
            last_error = response.json()
    return last_error


def prepare_request_llm_args(
    *,
    base_url: str,
    endpoint: str = "/chat/completions",
    api_key: str = "",
    model_name: str,
    prompt: str | None = None,
    messages: list[dict] | None = None,
    max_completion_tokens: int | None = None,
    temperature: float = 0.0,
    repetition_penalty: float = 1.0,
) -> dict:
    """
    NOTE:
        not support streaming. not support function calling.

        prompt is ignored if messages is not None.

    base_url, e.g.
        "https://prem.dashscope.aliyuncs.com/compatible-mode/v1",
        "https://api.openai.com/v1",
        "http://0.0.0.0:8007/v1"
    api_key, e.g. "sk-xx", "Bearer sk-xx"

    model_name, e.g. "qwen-max", "gpt-4o-2024-11-20"
    """
    if messages is None:
        assert isinstance(prompt, str) and prompt != "", (
            "prompt must be nonempty str when messages is None"
        )
        messages = [{"role": "user", "content": prompt}]
    else:
        assert isinstance(messages, list) and len(messages) > 0, (
            "messages must be nonempty list"
        )
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["Authorization"] = (
            api_key if api_key.startswith("Bearer") else f"Bearer {api_key}"
        )
    req_dict = {
        "temperature": temperature,
        "repetition_penalty": repetition_penalty,
        "model": model_name,
        "stream": False,
        "messages": messages,
        "max_tokens": max_completion_tokens,
    }
    # some special handling for different llm providers
    if base_url in ["https://api.openai.com/v1"]:
        if "repetition_penalty" in req_dict:
            # Unrecognized request argument supplied: repetition_penalty
            del req_dict["repetition_penalty"]
        if "max_tokens" in req_dict:
            # max_tokens is deprecated. Use max_completion_tokens instead.
            req_dict["max_completion_tokens"] = max_completion_tokens
            del req_dict["max_tokens"]
    elif base_url in ["https://api.deepseek.com/v1"]:
        # max_tokens：最终回答的最大长度（不含思维链输出），默认为 4K，最大为 8K
        # 请注意，思维链的输出最多可以达到 32K tokens，控思维链的长度的参数（reasoning_effort）将会在近期上线。
        # "Invalid max_tokens value, the valid range of max_tokens is [1, 8192]"
        if max_completion_tokens is None:
            max_completion_tokens = 4096
        elif max_completion_tokens > 8192:
            max_completion_tokens = 8192
        elif max_completion_tokens < 1:
            max_completion_tokens = 1
        req_dict["max_tokens"] = max_completion_tokens
    return {
        "url": base_url + endpoint,
        "headers": headers,
        "json": req_dict,
    }


def custom_request_llm(
    *,
    base_url: str,
    endpoint: str = "/chat/completions",
    api_key: str = "",
    model_name: str,
    prompt: str | None = None,
    messages: list[dict] | None = None,
    max_completion_tokens: int | None = None,
    temperature: float = 0.0,
    repetition_penalty: float = 1.0,
    max_retries: int = 3,
) -> dict | None:
    request_args = prepare_request_llm_args(
        base_url=base_url,
        endpoint=endpoint,
        api_key=api_key,
        model_name=model_name,
        prompt=prompt,
        messages=messages,
        max_completion_tokens=max_completion_tokens,
        temperature=temperature,
        repetition_penalty=repetition_penalty,
    )
    last_error = None
    for _ in range(max_retries):
        response = requests.post(**request_args)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(
                f"Failed to request llm {base_url + endpoint}: {response.json()}"
            )
            last_error = response.json()
    return last_error


def make_prompt_polish_answer(
    *,
    question: str,
    answer: str,
    language_code: str,
) -> str:
    assert language_code in SUPPORTED_LANGUAGE_CODES, (
        f"Unsupported language code: {language_code}"
    )
    prompt_template_polish_answer = """
Polish the answer based on the question and the given answer.

# Question:
{question}

# Answer:
{answer}

# Polished answer in {language}:
""".lstrip()
    language_name_in_english = LANGUAGE_CODE_TO_ENGLISH_NAME[language_code]
    return prompt_template_polish_answer.format(
        question=question, answer=answer, language=language_name_in_english
    )


class QAManagerConfig(BaseModel):
    embedding_api_key: str
    embedding_model_base_url: str
    embedding_model: str
    embedding_dim: int
    embedding_batch_size: int = 16
    embedding_enable_redis: bool = False

    qdrant_host: str
    qdrant_insert_batch_size: int = 16

    qa_pair_url: str
    num_retries_for_qa_pair: int = 3

    polish_answer_model_base_url: str = ""
    polish_answer_model_api_key: str = ""
    polish_answer_model_name: str = ""
    polish_answer_model_max_completion_tokens: int = 512
    polish_answer_model_temperature: float = 0.0
    polish_answer_model_repetition_penalty: float = 1.0
    num_retries_for_polish_answer: int = 1


_qamanger_config = QAManagerConfig(
    embedding_api_key=agent_setting.embedding_api_key,
    embedding_model_base_url=agent_setting.embedding_model_base_url,
    embedding_model=agent_setting.embedding_model,
    embedding_dim=agent_setting.embedding_dim,
    embedding_batch_size=16,
    embedding_enable_redis=agent_setting.intervene_embedding_enable_redis,
    qdrant_host=agent_setting.qdrant_host,
    qdrant_insert_batch_size=16,
    qa_pair_url=agent_setting.intervene_qa_pair_url,
    num_retries_for_qa_pair=agent_setting.intervene_num_retries_for_qa_pair,
    polish_answer_model_base_url=agent_setting.intervene_polish_answer_model_base_url,
    polish_answer_model_api_key=agent_setting.intervene_polish_answer_model_api_key,
    polish_answer_model_name=agent_setting.intervene_polish_answer_model_name,
    polish_answer_model_max_completion_tokens=agent_setting.intervene_polish_answer_model_max_completion_tokens,
    polish_answer_model_temperature=agent_setting.intervene_polish_answer_model_temperature,
    polish_answer_model_repetition_penalty=agent_setting.intervene_polish_answer_model_repetition_penalty,
    num_retries_for_polish_answer=agent_setting.intervene_num_retries_for_polish_answer,
)


class InterveneQuery(BaseModel):
    enterprise_id: str
    device_id: str  # 空字符串表示企业级配置，支持问答同时生效于多台机器
    level: Literal["D", "E"]  # D: device_level, E: enterprise_level
    uuid: str  # 前端传过来的uuid，一个uuid对应多个问题，所以这个uuid不能作为qdrant_point_id
    qa_id: str
    question: str
    lang: str

    @field_validator("uuid")
    def validate_uuid(cls, v: str) -> str:
        """验证UUID格式"""
        if len(v) != 32:
            raise ValueError("UUID must be 32 characters long")
        if not all(c in "0123456789abcdef" for c in v.lower()):
            raise ValueError("UUID must contain only hexadecimal characters")
        return v


class QAResponse(BaseModel):
    lang: str
    qa_id: str
    score: float
    question: str


class Answer(BaseModel):
    """
    image_info、video_info、radio_info 为json字符串，直接透传
    """

    lang: str
    question: List[str]
    answer: Optional[List[str]] = None
    image_info: str = ""
    video_info: str = ""
    radio_info: str = ""
    uuid: str = ""  # 提供给前端的id
    pair_id: int = 0  # 资源库记录id


class QAManager:
    """
    TODO: <EMAIL>
    """

    def __init__(
        self,
        config: QAManagerConfig = _qamanger_config,
        http_session: aiohttp.ClientSession | None = None,
    ):
        self.config = config
        self.embedding_api_key = config.embedding_api_key
        self.embedding_model_base_url = config.embedding_model_base_url
        self.embedding_model = config.embedding_model
        self.embedding_dim = config.embedding_dim
        self.embedding_batch_size = config.embedding_batch_size
        self.embedding_enable_redis = config.embedding_enable_redis
        self.qdrant_host = config.qdrant_host
        self.qdrant_insert_batch_size = config.qdrant_insert_batch_size
        self.qa_pair_url = config.qa_pair_url
        self.num_retries_for_qa_pair = config.num_retries_for_qa_pair
        self.polish_answer_model_base_url = config.polish_answer_model_base_url
        self.polish_answer_model_api_key = config.polish_answer_model_api_key
        self.polish_answer_model_name = config.polish_answer_model_name
        self.polish_answer_model_max_completion_tokens = (
            config.polish_answer_model_max_completion_tokens
        )
        self.polish_answer_model_temperature = config.polish_answer_model_temperature
        self.polish_answer_model_repetition_penalty = (
            config.polish_answer_model_repetition_penalty
        )
        self.num_retries_for_polish_answer = config.num_retries_for_polish_answer
        self.vector_grpc_client = AsyncQdrantClient(
            host=self.qdrant_host, prefer_grpc=True
        )
        self.http_session = http_session
        self.http_session = self._ensure_http_session()

    def _ensure_http_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self.http_session:
            self.http_session = http_context.http_session()
        return self.http_session

    async def get_one_embedding(self, text: str) -> list[float]:
        return await async_get_one_embedding(
            http_session=self.http_session,  # type: ignore
            embedding_api_key=self.embedding_api_key,
            embedding_model_base_url=self.embedding_model_base_url,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
            text=text,
            enable_redis=self.embedding_enable_redis,
        )

    async def get_embeddings(self, batch: list[str]) -> list[list[float]]:
        return await async_get_embeddings(
            http_session=self.http_session,  # type: ignore
            embedding_api_key=self.embedding_api_key,
            embedding_model_base_url=self.embedding_model_base_url,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
            batch=batch,
            enable_redis=self.embedding_enable_redis,
        )

    async def insert_intervene_query_list(
        self, query_list: list[InterveneQuery]
    ) -> list[InterveneQuery]:
        collection_name_to_query_list: dict[str, list[InterveneQuery]] = {}
        for query in query_list:
            collection_name = get_collection_name_for_intervene(
                enterprise_id=query.enterprise_id,
                embedding_model=self.embedding_model,
                embedding_dim=self.embedding_dim,
            )
            collection_name_to_query_list.setdefault(collection_name, []).append(query)

        failed_query_list: list[InterveneQuery] = []
        for collection_name, _query_list in collection_name_to_query_list.items():
            if not await self.vector_grpc_client.collection_exists(collection_name):
                ok = await self.vector_grpc_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=self.embedding_dim,
                        distance=qdrant_models.Distance.COSINE,
                        datatype=qdrant_models.Datatype.FLOAT32,
                    ),
                    on_disk_payload=False,  # store payload in memory
                )
                if not ok:
                    logger.error(f"Failed to create collection: {collection_name}")
                    failed_query_list.extend(_query_list)
                    continue

                logger.info(f"Created collection: {collection_name}")
            batch_size = min(
                self.embedding_batch_size,
                self.qdrant_insert_batch_size,
                len(_query_list),
            )
            for batch in [
                _query_list[i : i + batch_size]
                for i in range(0, len(_query_list), batch_size)
            ]:
                try:
                    # get embeddings
                    embeddings = await self.get_embeddings(
                        [query.question for query in batch]
                    )
                except Exception as e:
                    logger.error(f"Failed to get embeddings: {e}")
                    failed_query_list.extend(batch)
                    continue
                try:
                    # build qdrant points, record-oriented formats
                    points = [
                        qdrant_models.PointStruct(
                            id=get_qdrant_point_id_for_intervene(
                                uuid_from_frontend=query.uuid,
                                question=query.question,
                            ),
                            vector=embedding,
                            payload={
                                "enterprise_id": query.enterprise_id,
                                "device_id": query.device_id,
                                "level": query.level,
                                "uuid": query.uuid,
                                "qa_id": query.qa_id,
                                "question": query.question,
                                "lang": query.lang,
                            },
                        )
                        for query, embedding in zip(batch, embeddings)
                    ]
                    # insert to qdrant
                    self.vector_grpc_client.upload_points(
                        collection_name=collection_name,
                        points=points,
                        batch_size=self.qdrant_insert_batch_size,
                        wait=True,
                    )
                except Exception as e:
                    logger.error(f"Failed to insert intervene query list: {e}")
                    failed_query_list.extend(batch)
                    continue
        return failed_query_list

    async def delete_intervene_query_list(self, query_list: list[InterveneQuery]):
        for query in query_list:
            collection_name = get_collection_name_for_intervene(
                enterprise_id=query.enterprise_id,
                embedding_model=self.embedding_model,
                embedding_dim=self.embedding_dim,
            )
            if not await self.vector_grpc_client.collection_exists(collection_name):
                logger.info(f"Collection {collection_name} does not exist")
                continue

            update_result: qdrant_models.UpdateResult = (
                await self.vector_grpc_client.delete(
                    collection_name=collection_name,
                    points_selector=qdrant_models.FilterSelector(
                        filter=qdrant_models.Filter(
                            must=[
                                qdrant_models.FieldCondition(
                                    key="uuid",
                                    match=qdrant_models.MatchValue(value=query.uuid),
                                )
                            ]
                        )
                    ),
                    wait=True,
                )
            )
            if qdrant_models.UpdateStatus.COMPLETED != update_result.status:
                logger.error(
                    f"Delete intervene query {query.uuid} from collection {collection_name} failed"
                )

    async def search_intervene_query(
        self,
        query_text: str,
        enterprise_id: str,
        device_id: str = "",  # 如果为空，则不进行device_id过滤
        level: Literal["D", "E"] = "D",  # D: device_level, E: enterprise_level
        language: str = "",  # 如果为空，则不进行language过滤
        threshold: float = 0.9,
        top_k: int = 1,
        exact_match: bool = True,
    ) -> list[qdrant_models.ScoredPoint]:
        """
        Search intervene query from qdrant
        """
        collection_name = get_collection_name_for_intervene(
            enterprise_id=enterprise_id,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
        )
        if not await self.vector_grpc_client.collection_exists(collection_name):
            return []
        query_embedding = await self.get_one_embedding(query_text)
        query_filter = qdrant_models.Filter(
            must=[
                qdrant_models.FieldCondition(
                    key="enterprise_id",
                    match=qdrant_models.MatchValue(value=enterprise_id),
                ),
                qdrant_models.FieldCondition(
                    key="level", match=qdrant_models.MatchValue(value=level)
                ),
            ],
            must_not=[
                qdrant_models.FieldCondition(
                    key="source",
                    match=qdrant_models.MatchAny(any=["action"]),  # NOTE 过滤掉其他来源
                )
            ],
        )
        if level == "D" and device_id != "":
            query_filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="device_id", match=qdrant_models.MatchValue(value=device_id)
                )
            )
        if language != "":
            query_filter.must.append(  # type: ignore
                qdrant_models.FieldCondition(
                    key="lang", match=qdrant_models.MatchValue(value=language)
                )
            )
        search_result = await self.vector_grpc_client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=top_k,
            score_threshold=threshold,
            with_payload=["qa_id", "question", "lang"],
            query_filter=query_filter,
            search_params=qdrant_models.SearchParams(hnsw_ef=128, exact=exact_match),
        )
        return search_result

    async def fetch_top_k_qa(
        self,
        enterprise_id: str,
        device_id: str,
        query_text: str,
        threshold: float = 0.9,
        top_k: int = 1,
        language: str = "",
    ) -> List[QAResponse]:
        """
        Retrieves the top k QA matches for a given query
        """
        # 机器级问答优先级高于企业级，支持问答同时生效于多台机器
        # 机器级问答召回
        point_list: list[qdrant_models.ScoredPoint] = await self.search_intervene_query(
            query_text=query_text,
            enterprise_id=enterprise_id,
            device_id=device_id,
            level="D",
            language=language,
            threshold=threshold,
            top_k=top_k,
        )
        num_recalled_device_level = len(point_list)
        if (
            num_recalled_device_level < top_k
        ):  # 如果机器级问答召回不足，则继续召回企业级问答
            # 企业级问答召回
            point_list.extend(
                await self.search_intervene_query(
                    query_text=query_text,
                    enterprise_id=enterprise_id,
                    device_id="",
                    level="E",
                    language=language,
                    threshold=threshold,
                    top_k=top_k - num_recalled_device_level,
                )
            )
        qa_responses = [
            QAResponse(
                lang=point.payload["lang"],  # type: ignore
                qa_id=point.payload["qa_id"],  # type: ignore
                score=point.score,
                question=point.payload["question"],  # type: ignore
            )
            for point in point_list
        ]

        return qa_responses

    def _convert_qa_pair_response_to_answer(
        self, qa_pair_response: dict | None, language: str
    ) -> Optional[Answer]:
        if qa_pair_response:
            qa_pair_data = qa_pair_response.get("data")
            if qa_pair_data:
                try:
                    return Answer(
                        lang=language,
                        question=qa_pair_data["question"],
                        answer=qa_pair_data["answer"],
                        image_info=qa_pair_data["image_info"] or "",
                        video_info=qa_pair_data["video_info"] or "",
                        radio_info=qa_pair_data["radio_info"] or "",
                        uuid=qa_pair_data["uuid"],
                        pair_id=qa_pair_data["pair_id"],
                    )
                except Exception as e:
                    # TODO: added  query id to log
                    logger.error(f"Failed to convert qa_pair_response to answer: {e}")
                    return None
        return None

    async def _async_request_qa_pair(self, qa_id: str) -> Optional[dict]:
        for _ in range(self.num_retries_for_qa_pair):
            try:
                async with self.http_session.post(  # type: ignore
                    url=self.qa_pair_url,
                    json={"pair_id": int(qa_id)},
                    headers={"Content-Type": "application/json"},
                ) as response:
                    if response.status != 200:
                        error_msg = f"intervene_qa_pair_url调用异常，地址：{self.qa_pair_url}, status: {response.status}"
                        raise RuntimeError(error_msg)
                    return await response.json()
            except Exception as e:
                logger.error(f"Failed to get answer by id: {e}")
                continue
        return None

    async def _async_request_llm(self, prompt: str) -> str:
        req_args = prepare_request_llm_args(
            base_url=self.polish_answer_model_base_url,
            api_key=self.polish_answer_model_api_key,
            model_name=self.polish_answer_model_name,
            prompt=prompt,
            max_completion_tokens=self.polish_answer_model_max_completion_tokens,
            temperature=self.polish_answer_model_temperature,
            repetition_penalty=self.polish_answer_model_repetition_penalty,
        )
        logger.info(f"request llm args: {req_args}")
        for _ in range(self.num_retries_for_polish_answer):
            try:
                async with self.http_session.post(**req_args) as response:  # type: ignore
                    return (await response.json())["choices"][0]["message"]["content"]
            except Exception as e:
                logger.error(f"Failed to request llm: {e}")
                continue
        return ""

    async def _async_polish_answer(self, answer: Answer) -> Answer:
        question_str = answer.question[0]  # 默认使用第一个问题去做润色
        answer_str = answer.answer[0]  # 默认使用第一个答案去做润色
        prompt = make_prompt_polish_answer(
            question=question_str,
            answer=answer_str,
            language_code=answer.lang,
        )
        try:
            polished_answer_str = await self._async_request_llm(prompt)
            if polished_answer_str:
                answer.answer.append(
                    polished_answer_str
                )  # 润色后的答案默认追加到答案列表的末尾
        except Exception as e:
            logger.error(f"Failed to polish answer: {e}")
        return answer

    async def async_get_answer_by_id(
        self, qa_id: str, language: str, need_polish_answer: bool = True
    ) -> Optional[Answer]:
        """
        Returns the answer for a given qa_id
        """
        qa_pair_response = await self._async_request_qa_pair(qa_id)
        answer = self._convert_qa_pair_response_to_answer(qa_pair_response, language)
        if need_polish_answer and answer is not None and answer.answer:
            answer = await self._async_polish_answer(answer)
        return answer


if __name__ == "__main__":
    qamanger_config_jsonstr = json.dumps(
        json.loads(_qamanger_config.model_dump_json()),
        ensure_ascii=False,
        indent=4,
    )
    logger.info(f"\n_qamanger_config: {qamanger_config_jsonstr}")

    query_list = [
        InterveneQuery(
            enterprise_id="dodo_debug_enterprise_id_1",
            device_id="dodo_debug_device_id_1",
            level="D",
            uuid="39089debd4ed4358b4374591225ecd7c",
            qa_id="dodo_debug_qa_id_1",
            question="商事登记咨询去哪里办",  # "三四登记查询去哪个窗口" 0.8797953128814697
            lang="zh_CN",
        ),
        InterveneQuery(
            enterprise_id="dodo_debug_enterprise_id_1",
            device_id="",
            level="E",
            uuid="b682ccae0ece471098ee8268c2b0e876",
            qa_id="dodo_debug_qa_id_2",
            question="商事登记咨询去哪里办",
            lang="zh_CN",
        ),
        InterveneQuery(
            enterprise_id="dodo_debug_enterprise_id_1",
            device_id="",
            level="E",
            uuid="226ca248641e43938a1bdd55c96885b6",
            qa_id="dodo_debug_qa_id_3",
            question="商事登记咨询去321窗口办理，窗口的办理业务是内资企业登记业务咨询。",  # "三四登记查询去哪个窗口" 0.8440409302711487
            lang="zh_CN",
        ),
    ]

    testcase_list = [
        {
            "id": 1,
            "enterprise_id": "dodo_debug_enterprise_id_1",
            "device_id": "dodo_debug_device_id_1",
            "query_text": "三四登记查询去哪个窗口",
            "language": "zh_CN",
            "threshold": 0.87,
            "top_k": 1,
            "need_polish_answer": True,
        },
        {
            "id": 2,
            "enterprise_id": "dodo_debug_enterprise_id_1",
            "device_id": "dodo_debug_device_id_1",
            "query_text": "三四登记查询去哪个窗口",
            "language": "zh_CN",
            "threshold": 0.88,
            "top_k": 1,
            "need_polish_answer": True,
        },
        {
            "id": 3,
            "enterprise_id": "dodo_debug_enterprise_id_1",
            "device_id": "dodo_debug_device_id_3",
            "query_text": "三四登记查询去哪个窗口",
            "language": "zh_CN",
            "threshold": 0.01,
            "top_k": 2,
            "need_polish_answer": True,
        },
    ]

    async def main():
        try:
            session = aiohttp.ClientSession()
            qa_manager = QAManager(http_session=session)
            qa_responses_empty = await qa_manager.fetch_top_k_qa(
                enterprise_id="dodo_debug_enterprise_id_1",
                device_id="dodo_debug_device_id_1",
                query_text="三四登记查询去哪个窗口",
                threshold=0.89,
                top_k=1,
            )
            assert len(qa_responses_empty) == 0
            await qa_manager.insert_intervene_query_list(query_list)

            for testcase in testcase_list:
                logger.info(f"testcase id: {testcase['id']}")
                qa_responses = await qa_manager.fetch_top_k_qa(
                    enterprise_id=testcase["enterprise_id"],
                    device_id=testcase["device_id"],
                    query_text=testcase["query_text"],
                    threshold=testcase["threshold"],
                    top_k=testcase["top_k"],
                )
                qa_responses_jsonstr = json.dumps(
                    [
                        json.loads(qa_response.model_dump_json())
                        for qa_response in qa_responses
                    ],
                    ensure_ascii=False,
                    indent=4,
                )
                print(qa_responses_jsonstr)

            print("async_get_answer_by_id")
            async_answer = await qa_manager.async_get_answer_by_id(
                "1393097718104064",  # 问题："国庆节", 答案："十月一日" => qwen-max润色后的答案："国庆节是十月一日。"
                need_polish_answer=True,
                language="zh_CN",
            )
            if async_answer is not None:
                async_answer_jsonstr = json.dumps(
                    json.loads(async_answer.model_dump_json()),
                    ensure_ascii=False,
                    indent=4,
                )
                print(async_answer_jsonstr)
            else:
                print("No answer found for the given qa_id")

            # await qa_manager.delete_intervene_query_list(query_list)
            # logger.info(f"Deleted query list: {query_list}")
            # embedding_model = _qamanger_config.embedding_model
            # embedding_dim = _qamanger_config.embedding_dim
            # collection_name = get_collection_name_for_intervene(
            #     enterprise_id="dodo_debug_enterprise_id_1",
            #     embedding_model=embedding_model,
            #     embedding_dim=embedding_dim,
            # )
            # await qa_manager.vector_grpc_client.delete_collection(collection_name)
            # logger.info(f"Deleted collection: {collection_name}")
        finally:
            await session.close()

    asyncio.run(main())
