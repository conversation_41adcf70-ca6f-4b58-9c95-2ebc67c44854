import asyncio
import hashlib
import json
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import aiohttp
import redis
from loguru import logger
from openai import AsyncOpenAI

from src.rag.providers.base_provider import BaseRAGProvider, KnowledgeDocument
from src.settings import agent_setting
from src.utils.feishu_alarm import send_feishu_alarm


MAX_NUM_RESULTS = 6


class OpenAIVectorProvider(BaseRAGProvider):
    """OpenAI Vector Store知识库供应商"""

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        session: Optional[aiohttp.ClientSession] = None,
    ):
        """
        初始化OpenAI Vector Store供应商

        Args:
            config: 配置参数，如果为None则使用默认配置
            query_id: 查询ID，用于日志追踪
            session: HTTP会话，用于复用连接
        """
        default_config = {
            "timeout": 10,
            "max_results": 5,
        }

        if config:
            default_config.update(config)

        super().__init__(default_config, session=session)

        # 初始化Redis客户端（用于获取配置）
        self.redis_client = redis.Redis(
            host=agent_setting.assistant_redis_host,
            port=agent_setting.assistant_redis_port,
            db=agent_setting.assistant_redis_db,
            charset="utf-8",
            decode_responses=True,
        )

    async def search_knowledge(
        self,
        query: str,
        query_id: str,
        enterprise_id: str,
    ) -> List[KnowledgeDocument]:
        """
        搜索OpenAI Vector Store知识库

        Args:
            query: 查询文本
            enterprise_id: 企业ID，用于构建Redis key

        Returns:
            List[KnowledgeDocument]: 搜索到的知识文档列表
        """
        start_time = time.time()
        _logger = logger.bind(query_id=query_id)
        try:
            # 1. 从Redis获取配置
            api_key, assistant_id = await self._get_config_from_redis(
                enterprise_id, query_id
            )
            if not api_key or not assistant_id:
                _logger.warning(f"未找到enterprise_id {enterprise_id}的OpenAI配置")
                return []

            # 2. 初始化OpenAI客户端获取Vector Store ID
            openai_client = AsyncOpenAI(api_key=api_key, timeout=self.config["timeout"])

            # 3. 获取Assistant信息和Vector Store ID
            vector_store_id = await self._get_vector_store_id(
                client=openai_client, assistant_id=assistant_id, query_id=query_id
            )
            if not vector_store_id:
                _logger.warning(f"Assistant {assistant_id} 没有配置Vector Store")
                return []

            # 4. 搜索Vector Store - 使用直接的API调用方式
            search_results = await self._search_vector_store(
                api_key, vector_store_id, query, query_id
            )

            request_time = time.time() - start_time
            _logger.info(
                f"OpenAI Vector Store API request time: {request_time:.2f} seconds"
            )

            # 5. 解析并返回结果
            documents = self._parse_search_results(search_results, query_id)
            _logger.info(f"OpenAI Vector Store搜索完成，返回{len(documents)}个文档")

            return documents
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"OpenAI Vector Store知识库搜索失败: {e}")
            return []

    async def _get_config_from_redis(
        self, enterprise_id: str, query_id: str
    ) -> tuple[Optional[str], Optional[str]]:
        """
        从Redis获取OpenAI配置

        Args:
            enterprise_id: 企业ID

        Returns:
            tuple: (api_key, assistant_id)
        """
        _logger = logger.bind(query_id=query_id)
        try:
            # 构建Redis key
            redis_key = f"third_nlp_platform::chatgpt::enterprise::config::{enterprise_id}::agentos"
            _logger.info(f"查询Redis key: {redis_key}")

            # 获取配置
            config_str = self.redis_client.get(redis_key)
            if not config_str:
                # 发送飞书告警
                alarm_msg = f"OpenAI Vector Provider: 未找到enterprise_id {enterprise_id}的配置，Redis key: {redis_key}"
                await send_feishu_alarm(alarm_msg)
                _logger.error(alarm_msg)
                return None, None

            # 解析配置
            config_data = json.loads(config_str)
            api_key = config_data.get("chat_gpt_key")
            assistant_id = config_data.get("assistant_id")

            if not api_key:
                alarm_msg = f"OpenAI Vector Provider: enterprise_id {enterprise_id}的配置中缺少api_key"
                await send_feishu_alarm(alarm_msg)
                _logger.error(alarm_msg)
                return None, None

            if not assistant_id:
                alarm_msg = f"OpenAI Vector Provider: enterprise_id {enterprise_id}的配置中缺少assistant_id"
                await send_feishu_alarm(alarm_msg)
                _logger.error(alarm_msg)
                return None, None

            _logger.info(f"成功获取enterprise_id {enterprise_id}的OpenAI配置")
            return api_key, assistant_id

        except json.JSONDecodeError as e:
            alarm_msg = f"OpenAI Vector Provider: enterprise_id {enterprise_id}的配置JSON解析失败: {e}"
            await send_feishu_alarm(alarm_msg)
            _logger.error(alarm_msg)
            return None, None
        except asyncio.CancelledError:
            raise
        except Exception as e:
            alarm_msg = f"OpenAI Vector Provider: 获取enterprise_id {enterprise_id}的配置时发生错误: {e}"
            await send_feishu_alarm(alarm_msg)
            _logger.error(alarm_msg)
            return None, None

    async def _get_vector_store_id(
        self, client: AsyncOpenAI, assistant_id: str, query_id: str
    ) -> Optional[str]:
        """
        获取Assistant的第一个Vector Store ID

        Args:
            client: OpenAI客户端
            assistant_id: Assistant ID

        Returns:
            Vector Store ID或None
        """
        _logger = logger.bind(query_id=query_id)

        try:
            # 获取Assistant信息
            start_at = time.time()
            assistant = await client.beta.assistants.retrieve(assistant_id)
            _logger.info(
                f"获取到Assistant信息: {assistant.name} Elapsed: {time.time() - start_at}"
            )

            # 获取Vector Store ID - 按照现有代码的方式
            vector_store_ids = assistant.tool_resources.file_search.vector_store_ids
            if not vector_store_ids:
                _logger.warning(f"Assistant {assistant_id} 没有关联的Vector Store")
                return None

            # 返回第一个Vector Store ID
            vector_store_id = vector_store_ids[0]
            _logger.info(f"使用Vector Store ID: {vector_store_id}")
            return vector_store_id
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"获取Vector Store ID失败: {e}")
            return None

    async def _search_vector_store(
        self, api_key: str, vector_store_id: str, query: str, query_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        搜索Vector Store - 按照现有代码的方式实现

        Args:
            api_key: OpenAI API密钥
            vector_store_id: Vector Store ID
            query: 查询文本

        Returns:
            搜索结果或None
        """
        _logger = logger.bind(query_id=query_id)

        try:
            search_url = urljoin(
                agent_setting.vector_api_url, f"{vector_store_id}/search"
            )

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            }

            payload = {
                "query": query,
                "max_num_results": MAX_NUM_RESULTS,
                "ranking_options": {
                    "score_threshold": 0,  # 后续进行阈值过滤，保留足够文档进行后续知识问答
                    "ranker": "auto",
                },
                "rewrite_query": False,
            }

            _logger.debug(f"Vector Store搜索URL: {search_url}")
            _logger.debug(f"搜索payload: {payload}")

            session = self._ensure_session()
            async with session.post(
                url=search_url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=self.config["timeout"]),
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    _logger.info(f"搜索成功，返回{len(result.get('data', []))}个结果")
                    return result
                else:
                    error_text = await response.text()
                    _logger.error(f"搜索失败: HTTP {response.status}, {error_text}")
                    return {"error": f"HTTP {response.status}: {error_text}"}
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"搜索Vector Store失败: {e}")
            return {"error": str(e)}

    def _parse_search_results(
        self, search_results: Optional[Dict[str, Any]], query_id: str
    ) -> List[KnowledgeDocument]:
        """
        解析搜索结果 - 按照Vector Store API的响应格式

        Args:
            search_results: OpenAI Vector Store API返回的搜索结果

        Returns:
            List[KnowledgeDocument]: 解析后的知识文档列表
        """
        documents = []
        _logger = logger.bind(query_id=query_id)

        if not search_results:
            return documents

        # 检查是否有错误
        if "error" in search_results:
            _logger.error(f"搜索结果包含错误: {search_results['error']}")
            return documents

        try:
            # 解析Vector Store搜索结果
            data = search_results.get("data", [])

            for i, result in enumerate(data):
                # 提取文档内容 - content是一个列表，包含多个内容对象
                content_list = result.get("content", [])
                score = result.get("score", 0.0)
                file_id = result.get("file_id", "")
                filename = result.get("filename", "")
                attributes = result.get("attributes", {})

                # 合并所有文本内容
                content_texts = []
                if isinstance(content_list, list):
                    for content_item in content_list:
                        if (
                            isinstance(content_item, dict)
                            and content_item.get("type") == "text"
                        ):
                            text = content_item.get("text", "")
                            if text:
                                content_texts.append(text)
                elif isinstance(content_list, str):
                    # 兼容性处理：如果content是字符串
                    content_texts.append(content_list)

                # 合并所有文本
                content = "\n".join(content_texts).strip()

                if content:
                    # 格式化内容
                    title = filename or f"OpenAI Vector文档{i + 1}"

                    # 去掉文件后缀
                    if "." in filename:
                        filename = ".".join(filename.split(".")[:-1])
                    # 如果没有后缀，保持原文件名不变
                    formatted_content = (
                        f"### {filename} ###\n{content}" if filename else content
                    )

                    doc = KnowledgeDocument(
                        id=f"openai_vector_{i}_{hashlib.md5(content.encode()).hexdigest()[:8]}",
                        title=title,
                        content=formatted_content,
                        score=score,
                        metadata={
                            "type": "vector_search",
                            "file_id": file_id,
                            "filename": filename,
                            "attributes": attributes,
                            "result_index": i,
                            # "raw_content": content,
                        },
                        source="openai_vector",
                    )
                    documents.append(doc)

            # 按评分排序
            documents.sort(key=lambda x: x.score, reverse=True)

            # 限制返回数量
            max_results = self.config.get("max_results", 10)
            documents = documents[:max_results]

            _logger.info(f"解析OpenAI Vector Store响应: 总计{len(documents)}个文档")

        except Exception as e:
            _logger.error(f"解析OpenAI Vector Store响应失败: {e} {search_results}")

        return documents

    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取供应商信息

        Returns:
            Dict[str, Any]: 供应商信息
        """
        return {
            "name": self.name,
            "config": {
                "timeout": self.config.get("timeout", 30),
                "max_results": self.config.get("max_results", 10),
                # 注意：不返回API key等敏感信息
            },
        }


if __name__ == "__main__":
    import asyncio

    async def openai_vector_provider():
        """测试OpenAI Vector Store供应商"""
        session = aiohttp.ClientSession()
        provider = OpenAIVectorProvider(session=session)

        print("测试OpenAI Vector Store知识库搜索...")
        print("查询: 聚言公网SaaS是什么")

        result = await provider.search_knowledge(
            query="聚言公网SaaS是什么",
            enterprise_id="orion.ovs.entprise.**********",
            query_id="test_query_001",
        )

        print(f"搜索结果: {len(result)} 个文档")
        for i, doc in enumerate(result[:3]):  # 只显示前3个
            print(f"文档{i + 1}: {doc.title} (评分: {doc.score})")
            print(f"内容: {doc.content[:100]}...")
            print()
        await session.close()

    asyncio.run(openai_vector_provider())
