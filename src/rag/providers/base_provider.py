from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import aiohttp
from livekit.agents.utils import http_context


class KnowledgeDocument(BaseModel):
    """知识文档片段"""

    id: str
    title: str = ""
    content: str
    score: float = 0.0
    metadata: Dict[str, Any] = {}
    source: str = ""


class BaseRAGProvider(ABC):
    """RAG供应商基础抽象类"""

    def __init__(
        self,
        config: Dict[str, Any],
        session: Optional[aiohttp.ClientSession] = None,
    ):
        """
        初始化RAG供应商

        Args:
            config: 供应商配置参数
        """
        self.config = config
        self.name = self.__class__.__name__
        self._session = session

    def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self._session:
            self._session = http_context.http_session()
        return self._session

    @abstractmethod
    async def search_knowledge(
        self,
        query: str,
        query_id: str,
        enterprise_id: str,
    ) -> List[KnowledgeDocument]:
        """
        搜索知识文档片段

        Args:
            query: 查询文本
            top_k: 返回的文档数量
            filters: 搜索过滤条件

        Returns:
            List[KnowledgeDocument]: 搜索到的知识文档列表
        """
        pass

    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取供应商信息

        Returns:
            Dict[str, Any]: 供应商信息
        """
        return {
            "name": self.name,
            "config": {k: v for k, v in self.config.items() if "key" not in k.lower()},
        }
