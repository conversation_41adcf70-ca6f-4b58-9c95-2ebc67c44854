import asyncio
import hashlib
import time
import urllib.parse
from typing import Any, Dict, List, Optional

import aiohttp
from loguru import logger

from src.rag.providers.base_provider import BaseRAGProvider, KnowledgeDocument
from src.settings import agent_setting


class ChatMaxProvider(BaseRAGProvider):
    """ChatMax知识库供应商（使用机器人开放API）"""

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        session: Optional[aiohttp.ClientSession] = None,
    ):
        """
        初始化ChatMax供应商

        Args:
            config: 配置参数，如果为None则使用默认配置
        """
        default_config = {
            "robot_openapi_host": agent_setting.robot_openapi_host,
            "robot_openapi_key": agent_setting.robot_openapi_key,
            "knowledge_content_path": agent_setting.knowledge_content_path,
            "timeout": 10,
        }

        if config:
            default_config.update(config)

        super().__init__(default_config, session=session)

    async def search_knowledge(
        self,
        query: str,
        query_id: str,
        enterprise_id: str,
    ) -> List[KnowledgeDocument]:
        """
        搜索ChatMax知识库

        Args:
            query: 查询文本

        Returns:
            List[KnowledgeDocument]: 搜索到的知识文档列表
        """
        start_time = time.time()

        _logger = logger.bind(query_id=query_id)
        try:
            knowledge_content_url = (
                urllib.parse.urljoin(
                    self.config["robot_openapi_host"],
                    self.config["knowledge_content_path"],
                )
                + f"?chatmax_api=ctai_search_ctai_doc&ov_corp_id={enterprise_id}"
            )

            # 构建请求参数 - 完全按照用户的实现
            request_payload = {
                "query_text": query,
                "retrieval_strategy": "v2",
                "pad_ctai_doc": 0,
            }

            headers = {
                "orionstar-api-key": self.config["robot_openapi_key"],
                "Content-Type": "application/json",
            }

            _logger.debug(f"ChatMax API URL: {knowledge_content_url}")
            _logger.debug(f"ChatMax API payload: {request_payload}")

            session = self._ensure_session()
            async with session.post(
                url=knowledge_content_url,
                headers=headers,
                json=request_payload,
                timeout=aiohttp.ClientTimeout(total=self.config["timeout"]),
            ) as response:
                request_time = time.time() - start_time
                _logger.info(f"ChatMax API request time: {request_time:.2f} seconds")

                if response.status != 200:
                    error_msg = f"ChatMax API调用失败，状态码: {response.status}"
                    logger.error(error_msg)
                    return []

                result = await response.json()
                _logger.info(f"ChatMax API response: {result}")

                return self._parse_response(result, query_id)
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"ChatMax知识库搜索失败: {e}")
            return []

    def _parse_response(
        self, response: Dict[str, Any], query_id: str
    ) -> List[KnowledgeDocument]:
        """
        解析ChatMax API响应

        Args:
            response: API响应数据

        Returns:
            List[KnowledgeDocument]: 解析后的知识文档列表

            {
                "id":"section_2_e1ba05d0",
                "title":"猎豹移动介绍",
                "content":"### 猎豹移动介绍 ###\n技术合作：与小米、喜马拉雅等企业深度合作，提供语音交互及AIoT解决方案。商业模式创新广告生态：与Google、Facebook等全球头部平台建立长期合作，精准匹配用户与广告主需求。企业服务升级：通过AI机器人、智慧营销系统等B端产品，助力零售、医疗、政务等领域数字化转型。技术实力与行业认可研发投入：每年将营收的20%以上投入AI核心技术研发，专利储备超千项。国际奖项：多次获评Google Play“顶尖开发者”（Top Developer）、CES创新奖等荣誉。权威背书：入选《麻省理工科技评论》“全球50家聪明公司”榜单（2020）。未来愿景猎豹移动以“用科技让世界更聪明”为使命，持续探索AI、机器人技术与场景化应用的深度融合，目标成为全球智能服务机器人领域的标杆企业，推动人类生活与商业效率的革新。",
                "score":0.8178803356987177,
                "metadata":
                {
                    "type":"section",  # section or summary
                    "doc_name":"猎豹移动介绍",
                    "section_index":2,
                    "raw_content":"技术合作：与小米、喜马拉雅等企业深度合作，提供语音交互及AIoT解决方案。商业模式创新广告生态：与Google、Facebook等全球头部平台建立长期合作，精准匹配用户与广告主需求。企业服务升级：通过AI机器人、智慧营销系统等B端产品，助力零售、医疗、政务等领域数字化转型。技术实力与行业认可研发投入：每年将营收的20%以上投入AI核心技术研发，专利储备超千项。国际奖项：多次获评Google Play“顶尖开发者”（Top Developer）、CES创新奖等荣誉。权威背书：入选《麻省理工科技评论》“全球50家聪明公司”榜单（2020）。未来愿景猎豹移动以“用科技让世界更聪明”为使命，持续探索AI、机器人技术与场景化应用的深度融合，目标成为全球智能服务机器人领域的标杆企业，推动人类生活与商业效率的革新。"},
                    "source":"chatmax_section"
                }
            }
        """
        documents = []
        summary_documents = []
        _logger = logger.bind(query_id=query_id)

        try:
            # 根据真实的ChatMax API响应格式解析
            data = response.get("data", {})
            if not data:
                logger.warning(f"ChatMax API响应中没有data字段: {response}")
                return documents

            # 处理section_list - 文档片段
            sections = data.get("section_list", [])
            for i, section in enumerate(sections):
                content = section.get("content", "")
                doc_name = section.get("doc_name", f"文档片段{i + 1}")

                if content:
                    # 如果有文档名称，格式化为 ### 文档名称 ###\n内容
                    if doc_name and doc_name != f"文档片段{i + 1}":
                        formatted_content = f"### {doc_name} ###\n{content}"
                        title = doc_name
                    else:
                        formatted_content = content
                        title = f"文档片段{i + 1}"

                    doc = KnowledgeDocument(
                        id=f"section_{i}_{hashlib.md5(content.encode()).hexdigest()[:8]}",
                        title=title,
                        content=formatted_content,
                        score=section.get("score"),  # 默认评分
                        metadata={
                            "type": "section",
                            "doc_name": doc_name,
                            "section_index": i,
                            # "raw_content": content,
                        },
                        source="chatmax_section",
                    )
                    documents.append(doc)

            # 处理summary_list - 摘要
            summaries = data.get("summary_list", [])

            if summaries:
                for summary in summaries:
                    summary_content = summary.get("content", "")
                    content = f"### 总结摘要 ###\n{summary_content}"
                    score = summary.get("score", 0.0)
                    doc = KnowledgeDocument(
                        id=f"summary_{hashlib.md5(content.encode()).hexdigest()[:8]}",
                        title="总结摘要",
                        content=content,
                        score=score,
                        metadata={
                            "type": "summary",
                            "summary_count": len(summary_content),
                            "raw_summaries": summary_content,
                        },
                        source="chatmax_summary",
                    )
                    summary_documents.append(doc)

            # sort by score
            documents.sort(key=lambda x: x.score, reverse=True)
            summary_documents.sort(key=lambda x: x.score, reverse=True)

            # limit to top 3, 先和原qa数量一致
            documents = documents[:3]
            summary_documents = summary_documents[:3]

            final_documents = documents + summary_documents
            _logger.info(
                f"解析ChatMax响应: {len(sections)}个文档片段, {len(summaries)}个摘要, 总计{len(documents)}个文档"
            )

        except Exception as e:
            _logger.error(f"解析ChatMax API响应失败: {e}")
            final_documents = []

        return final_documents

    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取供应商信息

        Returns:
            Dict[str, Any]: 供应商信息
        """
        return {
            "name": self.name,
            "config": {
                "robot_openapi_host": self.config.get("robot_openapi_host", ""),
                "knowledge_content_path": self.config.get("knowledge_content_path", ""),
                "timeout": self.config.get("timeout", 10),
                # 注意：不返回API key等敏感信息
            },
        }


if __name__ == "__main__":
    import asyncio

    async def test_chatmax_provider():
        """测试ChatMax供应商"""
        provider = ChatMaxProvider()

        print("测试ChatMax知识库搜索...")
        print("查询: 公司里谁最能喝酒")

        result = await provider.search_knowledge(
            query="公司里谁最能喝酒", enterprise_id="orion.ovs.entprise.**********"
        )

        print(f"搜索结果: {len(result)} 个文档")
        for i, doc in enumerate(result[:3]):  # 只显示前3个
            print(doc.model_dump_json())
            print(f"文档{i + 1}: {doc.title} (评分: {doc.score})")
            print(f"内容: {doc.content[:100]}...")
            print()

    asyncio.run(test_chatmax_provider())
