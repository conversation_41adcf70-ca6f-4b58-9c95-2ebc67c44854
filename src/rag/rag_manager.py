import asyncio
import time
import traceback
from enum import Enum
from typing import Any, Dict, List, Optional

import aiohttp
from livekit.agents.utils import http_context
from loguru import logger
from pydantic import BaseModel

from src.rag.providers import OpenAIVectorProvider
from src.rag.providers.base_provider import BaseRAGProvider, KnowledgeDocument
from src.rag.providers.chatmax_provider import ChatMaxProvider
from src.rag.query_rewriter import QueryRewriter, QueryRewriteResult
from src.utils.llm import LLMConfig
from src.intervene.intervene import QAManager
from src.common.constant import QA_THRESHOLD
from src.settings import agent_setting


class InterventionDocument(BaseModel):
    """干预文档模型，包含QA对和答案信息"""

    content: str
    score: float


class RAGDebugInfo(BaseModel):
    """RAG调试信息模型，包含所有调试信息和耗时"""

    # 基础信息
    original_query: str
    rewritten_query: str
    provider: str
    total_count: int

    # 耗时信息
    query_rewrite_time_ms: float = 0
    knowledge_search_time_ms: float = 0
    intervention_search_time_ms: float = 0
    intervention_answer_time_ms: float = 0
    total_time_ms: float = 0

    # 查询改写信息
    enable_query_rewrite: bool
    requires_decontextualization: Optional[str] = None
    query_rewrite_debug: Optional[Dict[str, Any]] = None
    knowledge_score_threshold: float = 0.9
    intervention_threshold: float = 0.9

    # 错误信息
    error: Optional[str] = None


class KnowledgeSearchResult(BaseModel):
    """知识检索结果模型，只保留必要的结果和调试信息"""

    total_documents: List[KnowledgeDocument]  # 用于后续回答用户问题
    high_score_documents: List[KnowledgeDocument]  # 用于判定是否需要执行工具
    total_intervention_documents: List[InterventionDocument]
    high_score_intervention_documents: List[InterventionDocument]
    debug_info: RAGDebugInfo


class RAGProviderType(Enum):
    """RAG供应商类型"""

    CHATMAX = "chatmax"
    OPENAI_VECTOR = "openai_vector"
    # TODO: 可以在这里添加更多供应商类型


class RAGManager:
    """RAG管理器，整合query改写和知识检索功能"""

    def __init__(
        self,
        provider_type: RAGProviderType = RAGProviderType.CHATMAX,
        provider_config: Optional[Dict[str, Any]] = None,
        rewriter_config: Optional[LLMConfig] = None,
        session: Optional[aiohttp.ClientSession] = None,
    ):
        """
        初始化RAG管理器

        Args:
            provider_type: RAG供应商类型
            provider_config: 供应商配置
            rewriter_config: Query改写器配置
        """
        self.provider_type = provider_type

        self.session = session
        self.provider = self._create_provider(provider_type, provider_config, session)
        self.query_rewriter = QueryRewriter(rewriter_config)
        self.qa_manager = QAManager(http_session=session)

    def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self.session:
            self.session = http_context.http_session()
        return self.session

    def _create_provider(
        self,
        provider_type: RAGProviderType,
        config: Optional[Dict[str, Any]],
        session: Optional[aiohttp.ClientSession] = None,
    ) -> BaseRAGProvider:
        """
        Create provider instance

        Args:
            provider_type: 供应商类型
            config: 供应商配置

        Returns:
            BaseRAGProvider: 供应商实例
        """
        if provider_type == RAGProviderType.CHATMAX:
            return ChatMaxProvider(config, session=session)
        elif provider_type == RAGProviderType.OPENAI_VECTOR:
            return OpenAIVectorProvider(config, session=session)
        else:
            raise ValueError(f"不支持的RAG供应商类型: {provider_type}")

    async def fetch_intervention(
        self,
        query_id: str,
        query: str,
        enterprise_id: str,
        device_id: str,
        language: str = "",
        intervention_threshold: float = 0.9,
        top_k: int = 3,
    ) -> List[InterventionDocument]:
        """
        获取干预内容（QA对和答案）

        Args:
            query: 原始查询
            enterprise_id: 企业ID
            device_id: 设备ID
            language: 语言
            intervention_threshold: 干预阈值
            top_k: 返回的最大数量

        Returns:
            List[InterventionDocument]: 干预文档列表
        """
        _logger = logger.bind(query_id=query_id)

        try:
            _logger.info(f"开始获取干预内容，查询: '{query}'")

            # 获取QA匹配
            qa_responses = await self.qa_manager.fetch_top_k_qa(
                enterprise_id=enterprise_id,
                device_id=device_id,
                query_text=query,
                threshold=intervention_threshold,
                top_k=top_k,
                language=language,
            )

            intervention_documents = []

            if qa_responses:
                # 获取所有QA的答案
                tasks = []
                for qa_response in qa_responses:
                    tasks.append(
                        asyncio.create_task(
                            self.qa_manager.async_get_answer_by_id(
                                qa_response.qa_id,
                                qa_response.lang,
                                need_polish_answer=False,
                            )
                        )
                    )

                answers = await asyncio.gather(*tasks, return_exceptions=True)

                # 构建干预文档
                for qa_response, answer in zip(qa_responses, answers):
                    intervention_content = ""
                    # 获取问题和答案
                    question = qa_response.question or ""
                    # answer.answer是一个列表，取最后一个（润色后的答案）
                    answer = answer.answer[-1] if answer.answer else ""
                    intervention_content += (
                        f"\nQuestion: {question}  Answer: {answer}\n"
                    )

                    intervention_doc = InterventionDocument(
                        content=intervention_content,
                        score=qa_response.score,
                    )
                    intervention_documents.append(intervention_doc)

            _logger.info(f"获取到 {len(intervention_documents)} 个干预文档")
            _logger.info(f"干预文档内容: {intervention_documents}")
            return intervention_documents
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"获取干预内容失败: {e}")
            return []

    async def fetch_knowledge_content(
        self,
        query: str,
        query_id: str,
        enterprise_id: str,
        device_id: str = "",
        language: str = "",
        chat_history: Optional[List[Dict[str, str]]] = None,
        enable_query_rewrite: bool = True,
        knowledge_score_threshold: float = 0.9,
        intervention_threshold: float = 0.9,
        enable_intervention: bool = True,
        intervention_top_k: int = 3,
    ) -> KnowledgeSearchResult:
        """
        获取知识内容（核心接口）

        Args:
            query: 原始查询
            enterprise_id: 企业ID
            device_id: 设备ID
            language: 语言
            chat_history: 聊天历史记录
            enable_query_rewrite: 是否启用查询改写
            knowledge_score_threshold: 知识文档分数阈值
            intervention_threshold: 干预分数阈值
            enable_intervention: 是否启用干预
            intervention_top_k: 干预文档最大数量

        Returns:
            KnowledgeSearchResult: 包含检索到的知识文档和调试信息的结构化结果
        """
        total_start_time = time.perf_counter()
        _logger = logger.bind(query_id=query_id)

        try:
            _logger.info(f"开始知识检索，原始查询: '{query}'")

            # step 1: query rewrite
            rewritten_query = query
            query_rewrite_time_ms = 0
            rewrite_debug_info = None
            requires_decontextualization = None

            if enable_query_rewrite and chat_history:
                rewrite_start_time = time.perf_counter()
                try:
                    rewrite_result = await self.query_rewriter.decontextualize_query(
                        current_query=query,
                        chat_history=chat_history or [],
                        query_id=query_id,
                    )
                    rewritten_query = rewrite_result.decontextualized_query
                    requires_decontextualization = (
                        rewrite_result.requires_decontextualization
                    )
                    rewrite_debug_info = rewrite_result.debug_info.model_dump()
                except Exception as e:
                    _logger.error(f"查询改写失败: {e}")

                rewrite_end_time = time.perf_counter()
                query_rewrite_time_ms = round(
                    (rewrite_end_time - rewrite_start_time) * 1000, 3
                )

            _logger.info(
                f"改写后查询: '{rewritten_query}', 改写耗时: {query_rewrite_time_ms}ms"
            )

            # step 2: 并行执行知识检索和干预检索
            knowledge_task = asyncio.create_task(
                self._search_knowledge(
                    query_id=query_id,
                    query=rewritten_query,
                    enterprise_id=enterprise_id,
                )
            )

            intervention_task = None
            if enable_intervention:
                intervention_task = asyncio.create_task(
                    self.fetch_intervention(
                        query=rewritten_query,
                        query_id=query_id,
                        enterprise_id=enterprise_id,
                        device_id=device_id,
                        language=language,
                        intervention_threshold=QA_THRESHOLD[
                            agent_setting.embedding_model
                        ],  # 低阈值获取所有QA对
                        top_k=intervention_top_k,
                    )
                )

            # 等待任务完成
            if intervention_task:
                (
                    (search_start_time, documents),
                    intervention_documents,
                ) = await asyncio.gather(knowledge_task, intervention_task)
            else:
                (search_start_time, documents) = await knowledge_task
                intervention_documents = []

            search_end_time = time.perf_counter()
            knowledge_search_time_ms = round(
                (search_end_time - search_start_time) * 1000, 3
            )

            # 计算干预相关的耗时
            intervention_search_time_ms = 0
            intervention_answer_time_ms = 0
            if intervention_documents:
                # 这里简化处理，实际干预耗时已经包含在total_time中
                intervention_search_time_ms = 50  # 估算值
                intervention_answer_time_ms = 50  # 估算值

            total_end_time = time.perf_counter()
            total_time_ms = round((total_end_time - total_start_time) * 1000, 3)

            _logger.info(
                f"检索到 {len(documents)} 个知识文档, {len(intervention_documents)} 个干预文档, "
                f"知识检索耗时: {knowledge_search_time_ms}ms, 总耗时: {total_time_ms}ms"
            )

            debug_info = RAGDebugInfo(
                original_query=query,
                rewritten_query=rewritten_query,
                provider=self.provider.name,
                total_count=len(documents),
                query_rewrite_time_ms=query_rewrite_time_ms,
                knowledge_search_time_ms=knowledge_search_time_ms,
                intervention_search_time_ms=intervention_search_time_ms,
                intervention_answer_time_ms=intervention_answer_time_ms,
                total_time_ms=total_time_ms,
                enable_query_rewrite=enable_query_rewrite,
                requires_decontextualization=requires_decontextualization,
                query_rewrite_debug=rewrite_debug_info,
                knowledge_score_threshold=knowledge_score_threshold,
                intervention_threshold=intervention_threshold,
            )

            return KnowledgeSearchResult(
                total_documents=documents,
                high_score_documents=[
                    doc for doc in documents if doc.score >= knowledge_score_threshold
                ],
                total_intervention_documents=intervention_documents,
                high_score_intervention_documents=[
                    doc
                    for doc in intervention_documents
                    if doc.score >= intervention_threshold
                ]
                if enable_intervention
                else [],
                debug_info=debug_info,
            )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.critical(f"Error Reason: {traceback.format_exc()}")
            total_end_time = time.perf_counter()
            total_time_ms = round((total_end_time - total_start_time) * 1000, 3)
            _logger.error(f"知识检索失败: {e}, 总耗时: {total_time_ms}ms")

            debug_info = RAGDebugInfo(
                original_query=query,
                rewritten_query=query,
                provider=self.provider.name,
                total_count=0,
                total_time_ms=total_time_ms,
                enable_query_rewrite=enable_query_rewrite,
                intervention_threshold=intervention_threshold,
                error=str(e),
            )

            return KnowledgeSearchResult(
                total_documents=[],
                high_score_documents=[],
                total_intervention_documents=[],
                high_score_intervention_documents=[],
                debug_info=debug_info,
            )

    async def _search_knowledge(self, query: str, query_id: str, enterprise_id: str):
        """内部方法：搜索知识文档"""
        search_start_time = time.perf_counter()
        documents = await self.provider.search_knowledge(
            query=query,
            query_id=query_id,
            enterprise_id=enterprise_id,
        )
        return search_start_time, documents

    async def decontextualize_query(
        self,
        query: str,
        query_id: str,
        chat_history: List[Dict[str, str]],
        history_count: int = 1,
    ) -> QueryRewriteResult:
        """
        查询改写接口

        Args:
            query: 原始查询
            chat_history: 聊天历史记录
            max_history_turns: 考虑的最大历史轮数

        Returns:
            Dict[str, Any]: 包含改写结果的字典
        """
        return await self.query_rewriter.decontextualize_query(
            current_query=query,
            chat_history=chat_history,
            history_count=history_count,
            only_user=True,
            query_id=query_id,
        )


if __name__ == "__main__":

    async def rag_manager():
        session = aiohttp.ClientSession()
        rag_manager = RAGManager(session=session)
        result = await rag_manager.fetch_knowledge_content(
            query="刘关张说的是谁你知道吗？他们出自那本书",
            query_id="test_query_123",
            enterprise_id="orion.ovs.entprise.9945420568",
            device_id="test_device_123",
            language="zh_CN",
            chat_history=[],
            enable_query_rewrite=True,
            knowledge_score_threshold=0.8,
            intervention_threshold=0.8,
            enable_intervention=True,
            intervention_top_k=3,
        )
        print("Knowledge documents:", len(result.total_documents))
        print("High score knowledge documents:", len(result.high_score_documents))
        print("Intervention documents:", len(result.total_intervention_documents))
        print(
            "High score intervention documents:",
            len(result.high_score_intervention_documents),
        )

        print("Debug info:", result.debug_info)
        await session.close()

    asyncio.run(rag_manager())
