import asyncio
import json
import time
from typing import List, Dict, Optional
from loguru import logger
from pydantic import BaseModel

from src.utils.llm import LLMManager, LLMConfig, parse_output_to_dict
from src.settings import agent_setting
from .prompts import get_decontextualization_prompt


class DebugInfo(BaseModel):
    """调试信息模型"""

    original_query: str
    history_count: int
    only_user: bool
    previous_queries: List[str]
    total_history_messages: int
    used_history_messages: int
    generated_prompt: Optional[str] = None
    llm_raw_response: Optional[str] = None
    json_parse_error: Optional[str] = None
    exception_error: Optional[str] = None
    messages: List[Dict[str, str]] = []


class QueryRewriteResult(BaseModel):
    """查询改写结果模型"""

    requires_decontextualization: str
    decontextualized_query: str
    decontextualization_time_ms: float
    debug_info: DebugInfo

    @property
    def requires_decontextualization_bool(self) -> bool:
        """将字符串形式的requires_decontextualization转换为布尔值"""
        return self.requires_decontextualization.lower() == "true"


class QueryRewriter:
    """查询改写器"""

    def __init__(self, llm_config: Optional[LLMConfig] = None):
        """
        初始化查询改写器

        Args:
            llm_config: LLM配置，如果为None则使用默认配置
        """
        if llm_config is None:  # 先使用plan model，使用text model需要重新调整prompt
            self.llm_config = LLMConfig(
                base_url=agent_setting.plan_model_base_url,
                llm_model_name=agent_setting.plan_model,
                api_key=agent_setting.plan_model_api_key,
                temperature=0.0,
                max_tokens=500,
                timeout=10,
            )
        else:
            self.llm_config = llm_config

    async def decontextualize_query(
        self,
        current_query: str,
        chat_history: List[Dict[str, str]],
        history_count: int = 1,
        only_user: bool = True,
        query_id: Optional[str] = None,
    ) -> QueryRewriteResult:
        """
        根据历史记录改写查询

        Args:
            current_query: 当前查询
            chat_history: 聊天历史记录，格式为[{"role": "user/assistant", "content": "..."}]
            history_count: 考虑的历史记录数量
            only_user: 是否只考虑用户消息

        Returns:
            QueryRewriteResult: 包含改写结果的Pydantic模型
        """
        start_time = time.perf_counter()
        _logger = logger.bind(query_id=query_id)

        # 转换历史记录格式为简单的查询列表
        previous_queries = []

        for msg in chat_history:
            if only_user:
                if msg.get("role") == "user":
                    previous_queries.append(msg.get("content", ""))
            else:
                previous_queries.append(msg.get("content", ""))

        previous_queries = previous_queries[-history_count:]

        # 准备debug信息
        debug_info = DebugInfo(
            original_query=current_query,
            history_count=history_count,
            only_user=only_user,
            previous_queries=previous_queries,
            total_history_messages=len(chat_history),
            used_history_messages=len(previous_queries),
        )

        messages = []  # 初始化messages变量

        try:
            # 使用prompt模板构建prompt
            prompt = get_decontextualization_prompt(current_query, previous_queries)
            debug_info.generated_prompt = prompt

            messages = [{"role": "user", "content": prompt}]
            debug_info.messages = messages

            # 调用LLM进行改写
            result = await LLMManager.invoke_generate_text_model(
                messages=messages, llm_config=self.llm_config
            )

            debug_info.llm_raw_response = result.content
            _logger.info(f"LLM原始响应: {debug_info.llm_raw_response}")

            # 解析返回结果
            try:
                response_data, err_msg = parse_output_to_dict(result.content)
                if err_msg:
                    raise json.JSONDecodeError(err_msg, "", 0)

                end_time = time.perf_counter()
                decontext_time = round((end_time - start_time) * 1000, 3)  # 转换为毫秒

                query_result = QueryRewriteResult(
                    requires_decontextualization=response_data.get(
                        "requires_decontextualization", "False"
                    ),
                    decontextualized_query=response_data.get(
                        "decontextualized_query", current_query
                    ),
                    decontextualization_time_ms=decontext_time,
                    debug_info=debug_info,
                )

                _logger.info(
                    f"去上下文化结果: {query_result.model_dump()}, 耗时: {decontext_time}ms"
                )
                return query_result

            except json.JSONDecodeError as e:
                end_time = time.perf_counter()
                decontext_time = round((end_time - start_time) * 1000, 3)
                debug_info.json_parse_error = str(e)
                debug_info.messages = messages
                logger.error(f"解析去上下文化结果失败: {e}, 原始内容: {result.content}")
                # 如果解析失败，返回原始查询
                return QueryRewriteResult(
                    requires_decontextualization="False",
                    decontextualized_query=current_query,
                    decontextualization_time_ms=decontext_time,
                    debug_info=debug_info,
                )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            end_time = time.perf_counter()
            decontext_time = round((end_time - start_time) * 1000, 3)
            debug_info.exception_error = str(e)
            debug_info.messages = messages
            _logger.error(f"去上下文化过程中出错: {str(e)}")
            # 如果出错，返回原始查询
            return QueryRewriteResult(
                requires_decontextualization="False",
                decontextualized_query=current_query,
                decontextualization_time_ms=decontext_time,
                debug_info=debug_info,
            )
