"""
Rewrite Query Prompt Template
"""


def get_decontextualization_prompt(current_query: str, previous_queries: list) -> str:
    """
    Get the prompt for decontextualization

    Args:
        current_query: 当前查询
        previous_queries: 历史查询列表

    Returns:
        str: 构建好的prompt
    """
    return f"""Analyze whether the query contains ambiguous references that require conversation history to be understood.

STRICT CRITERIA - Mark "True" ONLY if the query contains:
1. Ambiguous pronouns that refer to something from history ("it", "that", "this", "they", "which one")
2. Unclear demonstratives without clear context ("that", "this", "here", "over there")
3. Incomplete comparative or continuation references ("what about...", "and also...", "in addition...")
4. Missing essential subjects that are only identifiable from conversation history

DO NOT mark "True" for:
- Complete action commands and control instructions
- Complete statements with clear standalone meaning
- Emotional expressions and exclamations
- Self-contained questions or requests
- Commands that include all necessary context
- Status descriptions or observations

When decontextualizing (if True):
- Use MINIMAL necessary information from history
- Preserve original tone, style, and intent exactly
- Do NOT add explanatory words or extra descriptions
- Do NOT answer the current query
- Only fill in the missing essential information

Query: "{current_query}"
History: {previous_queries}

Return JSON only (ensure both fields are always present and non-empty):
{{
    "requires_decontextualization": "True or False",
    "decontextualized_query": "Complete standalone query with original meaning"
}}"""
