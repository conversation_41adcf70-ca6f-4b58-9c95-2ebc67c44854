from lingua import Language, LanguageDetectorBuilder

from src.common.constant import LanguageEnum, LANGUAGE_NAME_TO_LANGUAGE_ENUM, Area
from src.settings import agent_setting
from src.utils.detect_cantonse import judge, LanguageType

languages = [
    Language.ENGLISH,
    Language.FRENCH,
    Language.GERMAN,
    Language.SPANISH,
    Language.JAPANESE,
    Language.CHINESE,
    Language.KOREAN,
    Language.ITALIAN,
]


detector = LanguageDetectorBuilder.from_languages(*languages).build()

zh_en_detector = LanguageDetectorBuilder.from_languages(
    Language.ENGLISH, Language.CHINESE
).build()


def detect_language(text: str, default_language: str, is_multilingual: int = 0) -> str:
    """
    Detect the language of the text based on word count.
    Returns the language code (zh or en) depending on which has more words.
    """
    if not text:
        return default_language

    if agent_setting.region_version == Area.overseas:
        return default_language

    if not is_multilingual:  # 系统中文, 未开启多语言，默认支持中英多语言
        if default_language != LanguageEnum.zh:
            return default_language

        language_name = zh_en_detector.detect_language_of(text)
        language_code = LANGUAGE_NAME_TO_LANGUAGE_ENUM.get(language_name)
        return language_code if language_code else default_language

    language_name = detector.detect_language_of(text)
    # logger.debug(f"Language name: {language_name}")
    language_code = LANGUAGE_NAME_TO_LANGUAGE_ENUM.get(language_name)
    if not language_code:
        return default_language

    if language_code and language_code != LanguageEnum.zh:
        return language_code

    # 先尝试识别粤语, 粤语存在很大概率的识别问题
    if judge(text) in [
        LanguageType.CANTONESE,
        LanguageType.MIXED,
    ]:
        return LanguageEnum.zh_gd

    return LanguageEnum.zh


if __name__ == "__main__":
    # 测试文本列表，包含九种不同语言的文本，每种语言各三句
    test_text_list = [
        # 简体中文
        ("你好，很高兴认识你。", LanguageEnum.zh),
        ("今天天气真不错，我想去公园散步。", LanguageEnum.zh),
        ("中国有着悠久的历史和灿烂的文化。", LanguageEnum.zh),
        ("今天天气怎么样", LanguageEnum.zh),
        ("北京市", LanguageEnum.zh),
        # 英文
        ("Hello, nice to meet you.", LanguageEnum.en),
        (
            "The weather is lovely today, I'd like to go for a walk in the park.",
            LanguageEnum.en,
        ),
        (
            "The United States is a diverse country with many different cultures.",
            LanguageEnum.en,
        ),
        ("What's the weather like today?", LanguageEnum.en),
        # 意大利语
        ("Ciao, piacere di conoscerti.", LanguageEnum.it_it),
        (
            "Oggi il tempo è bellissimo, vorrei fare una passeggiata nel parco.",
            LanguageEnum.it_it,
        ),
        ("L'Italia è famosa per la sua cucina, arte e storia.", LanguageEnum.it_it),
        # 法语
        ("Bonjour, enchanté de faire votre connaissance.", LanguageEnum.fr_fr),
        (
            "Il fait beau aujourd'hui, j'aimerais me promener dans le parc.",
            LanguageEnum.fr_fr,
        ),
        (
            "La France est connue pour sa cuisine, ses vins et sa culture.",
            LanguageEnum.fr_fr,
        ),
        # 德语
        ("Hallo, schön dich kennenzulernen.", LanguageEnum.de),
        (
            "Das Wetter ist heute schön, ich möchte im Park spazieren gehen.",
            LanguageEnum.de,
        ),
        (
            "Deutschland ist bekannt für sein Bier, seine Autos und seine Effizienz.",
            LanguageEnum.de,
        ),
        # 韩语
        ("안녕하세요, 만나서 반갑습니다.", LanguageEnum.ko_kr),
        ("오늘 날씨가 정말 좋네요, 공원에서 산책하고 싶어요.", LanguageEnum.ko_kr),
        ("한국은 역동적인 문화와 맛있는 음식으로 유명합니다.", LanguageEnum.ko_kr),
        # 日语
        ("こんにちは、はじめまして。", LanguageEnum.ja_jp),
        ("今日の天気はとても良いです、公園を散歩したいです。", LanguageEnum.ja_jp),
        ("日本は美しい自然と伝統文化で知られています。", LanguageEnum.ja_jp),
        # 西班牙语
        ("Hola, mucho gusto en conocerte.", LanguageEnum.es_es),
        (
            "El clima está muy agradable hoy, me gustaría dar un paseo por el parque.",
            LanguageEnum.es_es,
        ),
        (
            "España es conocida por su cultura vibrante y su deliciosa comida.",
            LanguageEnum.es_es,
        ),
        # 粤语
        ("你好，好高興認識你。", LanguageEnum.zh_gd),
        ("今日天氣好靚，我想去公園行下。", LanguageEnum.zh_gd),
        ("香港係一個好繁忙嘅城市，有好多美食。", LanguageEnum.zh_gd),
    ]
    for text, expected_language in test_text_list:
        result = detect_language(text, "zh", 1)
        print(f"Test text: {text}")
        print(f"Expected language: {expected_language}")
        print(f"Detected language: {result}")
        print(f"Match: {result == expected_language}")
        print("-" * 40)
        assert result == expected_language
    # print(f"Test text: {test_text}")
    # print(f"English words: {count_english_words(test_text)}")
    # print(f"Chinese words: {count_chinese_words(test_text)}")
    # print(f"Detected language: {calculate_language_ratio(test_text, LanguageEnum.en)}")
    #
    # # Test with other languages
    # test_fr = "Bonjour le monde! C'est un texte français avec des accents."
    # print(f"\nFrench test: {test_fr}")
    # print(f"Detected language: {calculate_language_ratio(test_fr, LanguageEnum.en)}")
    #
    # test_es = "이것은 한글이다."
    # print(f"\nSpanish test: {test_es}")
    # print(f"Detected language: {calculate_language_ratio(test_es, LanguageEnum.en)}")
