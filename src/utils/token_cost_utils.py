from loguru import logger
from copy import deepcopy


_TOKEN_COST_EXAMPLE = {
    "prompt_tokens": 1117,
    "completion_tokens": 46,
    "total_tokens": 1163,
    "prompt_tokens_details": {
        "cached_tokens": 0,
    },
    "___details": {
        "prompt_tokens": [],
        "completion_tokens": [],
        "total_tokens": [],
        "prompt_tokens_details": {
            "cached_tokens": [],
        },
    },
}


class TokenCostUtils:
    @staticmethod
    def add(d1: dict, d2: dict) -> dict:
        if not d1 and not d2:
            return {}
        if not d1:
            return TokenCostUtils.fill_details(deepcopy(d2))
        if not d2:
            return TokenCostUtils.fill_details(deepcopy(d1))
        try:
            d1 = TokenCostUtils.fill_details(deepcopy(d1))
            d2 = TokenCostUtils.fill_details(deepcopy(d2))
            d = {
                "prompt_tokens": d1.get("prompt_tokens", 0)
                + d2.get("prompt_tokens", 0),
                "completion_tokens": d1.get("completion_tokens", 0)
                + d2.get("completion_tokens", 0),
                "total_tokens": d1.get("total_tokens", 0) + d2.get("total_tokens", 0),
                "prompt_tokens_details": {
                    "cached_tokens": d1.get("prompt_tokens_details", {}).get(
                        "cached_tokens", 0
                    )
                    + d2.get("prompt_tokens_details", {}).get("cached_tokens", 0),
                },
                "___details": {
                    "prompt_tokens": d1.get("___details", {}).get("prompt_tokens", [])
                    + d2.get("___details", {}).get("prompt_tokens", []),
                    "completion_tokens": d1.get("___details", {}).get(
                        "completion_tokens", []
                    )
                    + d2.get("___details", {}).get("completion_tokens", []),
                    "total_tokens": d1.get("___details", {}).get("total_tokens", [])
                    + d2.get("___details", {}).get("total_tokens", []),
                    "prompt_tokens_details": {
                        "cached_tokens": d1.get("___details", {})
                        .get("prompt_tokens_details", {})
                        .get("cached_tokens", [])
                        + d2.get("___details", {})
                        .get("prompt_tokens_details", {})
                        .get("cached_tokens", []),
                    },
                },
            }
            return d
        except Exception as e:
            logger.error(f"add error: d1={d1}, d2={d2}, error={e}")
            return deepcopy(d2)

    @staticmethod
    def fill_details(d: dict) -> dict:
        prompt_tokens = d.get("prompt_tokens", 0)
        completion_tokens = d.get("completion_tokens", 0)
        total_tokens = d.get("total_tokens", 0)
        prompt_tokens_details = d.get("prompt_tokens_details", {})
        cached_tokens = prompt_tokens_details.get("cached_tokens", 0)

        ___details = d.get("___details", {})
        ___prompt_tokens = ___details.get("prompt_tokens", [])
        ___completion_tokens = ___details.get("completion_tokens", [])
        ___total_tokens = ___details.get("total_tokens", [])
        ___prompt_tokens_details = ___details.get("prompt_tokens_details", {})
        ___cached_tokens = ___prompt_tokens_details.get("cached_tokens", [])

        if not ___prompt_tokens:
            ___prompt_tokens = [prompt_tokens]
        if not ___completion_tokens:
            ___completion_tokens = [completion_tokens]
        if not ___total_tokens:
            ___total_tokens = [total_tokens]
        if not ___cached_tokens:
            ___cached_tokens = [cached_tokens]

        d["___details"] = {
            "prompt_tokens": ___prompt_tokens,
            "completion_tokens": ___completion_tokens,
            "total_tokens": ___total_tokens,
            "prompt_tokens_details": {
                "cached_tokens": ___cached_tokens,
            },
        }
        return d
