from datetime import datetime, timedelta, time


class Rotator:

    def __init__(self, size, time):
        self._size = size
        self._time = datetime.now().replace(
            hour=time.hour, minute=time.minute, second=time.second
        )

    def should_rotate(self, message, file):
        file.seek(0, 2)
        if file.tell() + len(message) > self._size:
            return True
        if message.record["time"].timestamp() > self._time.timestamp():
            self._time += timedelta(days=1)
            return True
        return False


rotator = Rotator(1_000_000_000, time(1, 0, 0))
