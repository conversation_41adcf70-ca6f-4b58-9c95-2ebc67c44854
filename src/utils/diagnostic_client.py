"""
Diagnostic tracking client using aiohttp
"""

import json
from typing import List, Optional

import aiohttp
from loguru import logger
from src.utils.feishu_alarm import send_feishu_alarm
from src.settings import agent_setting
from src.common.constant import Area


class DiagnosticClient:
    def __init__(
        self, diagnostic_host: str, diagnostic_path: str, diagnostic_secret_key: str
    ):
        self.host = diagnostic_host
        self.path = diagnostic_path
        self.secret_key = diagnostic_secret_key
        self.url = f"{self.host}{self.path}"
        self._session = None

    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession()
        return self._session

    async def send_diagnostic(
        self,
        sid: str,
        client_id: str,
        enterprise_id: str,
        device_id: str,
        group_id: str,
        query: str,
        answer: str,
        model: str,
        lang_str: str,
        speaker_id: int = 9527,
        extra_info: dict = None,
        debug_info: list = None,
        plan_info: str = "",
        audio_path_list: Optional[List[str]] = None,
    ) -> bool:
        """
        Send diagnostic information to the server

        Args:
            sid: Session ID
            client_id: Client ID
            enterprise_id: Enterprise ID
            device_id: Device ID
            group_id: Group ID
            query: User query
            answer: System response
            model: Device model
            lang_str: Language string
            speaker_id: Speaker ID
            extra_info: Extra information dict
            debug_info: Debug information list
            plan_info: Plan information string
            audio_path_list: List of audio file paths

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            headers = {
                "speech-secret-key": self.secret_key,
                "Content-Type": "application/json",
            }

            payload = {
                "sid": sid,
                "client_id": client_id,
                "enterprise_id": enterprise_id,
                "device_id": device_id,
                "group_id": group_id,
                "lang_str": lang_str,
                "query": query or "-",
                "answer": answer,
                "model": model,
                "speaker_id": speaker_id,
                "extra_info": json.dumps(extra_info or {}),
                "debug_info": json.dumps(debug_info or []),
                "plan_info": plan_info,
                "audio_path_list": audio_path_list or [],
                "is_oversea": True
                if agent_setting.region_version == Area.overseas
                else False,
            }

            logger.bind(query_id=sid).info(
                f"[BO] Diagnostic payload faceid {speaker_id}"
            )

            session = await self.get_session()
            async with session.post(
                self.url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                response.raise_for_status()
                logger.info(
                    f"[BO] Diagnostic sent successfully {response.status}"
                )  # {payload}
                logger.bind(query_id=sid).info(
                    f"[BO] Diagnostic sent result {await response.text()}"
                )
                return True

        except Exception as e:
            await send_feishu_alarm(
                f"BO上报 api调用异常，地址：{self.url}, error: {str(e)} payload: {payload}"
            )
            logger.error(f"[BO] Failed to send diagnostic info: {str(e)}")
            return False

    async def close(self):
        """Close the client session"""
        if self._session and not self._session.closed:
            await self._session.close()


async def main():
    client = DiagnosticClient(
        agent_setting.diagnostic_host,
        agent_setting.diagnostic_path,
        agent_setting.diagnostic_secret_key,
    )

    try:
        success = await client.send_diagnostic(
            sid="21814bc9-1502-4d20-aae8-83jxah2dodjau",
            client_id="orion.ovs.client.1597807388298",
            enterprise_id="orion.ovs.entprise.3456747594",
            device_id="MC1BCNC0EA021319N305",
            group_id="ovs.group.156024545289012",
            query="this is test",
            answer="ok",
            audio_path_list=[
                "https://nlp-test-1256573505.cos.ap-beijing.myqcloud.com/agentos/test/M03SCN1A14024430N038/2024-10-14/Gr9x8cDj_%E8%BF%99%E4%BD%A0%E8%A7%89%E5%BE%97%E5%88%AB%E4%BA%BA%E6%80%8E%E6%A0%B7.wav"
            ],
        )
        print(f"Diagnostic sent: {success}")
    finally:
        await client.close()


# Usage example:
if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
