import json
import os
import time
from threading import Thread

import shortuuid
from loguru import logger
import requests

from src.utils.cos_client import CosClient
from src.settings import agent_setting
from src.common.constant import Area


MAX_QUERY_ID_LENGTH = 150


def _save_diagnostic_info(
    cos_client: "CosClient",
    query_id: str,
    device_id: str,
    type_: str,
    diagnostic_info: dict,
) -> None:
    logger.debug(f"start save diagnotic info to cos")
    env = os.getenv("ENV", "dev")
    today = time.strftime("%Y-%m-%d", time.localtime())
    query_id = query_id[:MAX_QUERY_ID_LENGTH]
    file_path_prefix = f"{env}/{device_id}/{today}/{query_id}"
    file_path = (
        f"{file_path_prefix}/{type_}_{shortuuid.ShortUUID().random(length=4)}.txt"
    )
    # covert diagnostic_info to bytes
    text = json.dumps(diagnostic_info, ensure_ascii=False, indent=4)
    text_bytes = text.encode("utf-8")
    cos_client.upload_file(text_bytes, file_path)


async def save_diagnostic_info(
    cos_client: "CosClient",
    query_id: str,
    device_id: str,
    type_: str,
    diagnostic_info: dict,
) -> None:
    """
    Save diagnostic info to S3

    Format:
        A.wav
        B.wav
        C.wav
        A_B_C/type_xxxx.txt
    """
    thread = Thread(
        target=_save_diagnostic_info,
        args=(
            cos_client,
            query_id,
            device_id,
            type_,
            diagnostic_info,
        ),
    )
    thread.start()


def report_wakeup_result(audio_id: str, result: bool):
    if agent_setting.region_version == Area.domestic:
        return

    is_online = False
    start_time = time.time()
    vad_status = "use" if result else "no_use"
    logger.info(
        f"report wakeup result to asr server: {audio_id} {vad_status} {is_online}"
    )
    try:
        response = requests.post(
            agent_setting.speech_wakeup_result_report_url,
            json={
                "sid": audio_id,
                "key": "Ks9AW1Jcnqb3glLNro4aXBFDTiZCOkUG",
                "vad_status": vad_status,
                "is_online": is_online,
            },
            timeout=5,
        )
        response.raise_for_status()
        result = response.json()
        logger.info(
            f"[Report Wakeup Result] Audio_id: {audio_id} report wakeup result to asr server success: {result} Elapsed: {time.time() - start_time}"
        )
        return result
    except Exception as e:
        logger.error(
            f"[Report Wakeup Result] Audio_id: {audio_id} report wakeup result to asr server error: {e} {e.__class__.__name__}  Elapsed: {time.time() - start_time}"
        )
        return None
