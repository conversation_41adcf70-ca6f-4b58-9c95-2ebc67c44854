import os
import gettext
import builtins
from typing import Optional, Callable

from loguru import logger
from babel.support import Translations


# 语言包目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(CURRENT_DIR)
LOCALE_DIR = os.path.join(PROJECT_ROOT, "locale")

_translations = {}
_current_translation: Optional[Translations] = None


def get_current_translation_func():
    """获取当前的翻译函数"""
    return _current_translation.gettext if _current_translation else gettext.gettext


class TranslationProxy:
    """翻译函数的动态代理"""

    def __call__(self, message):
        return get_current_translation_func()(message)


_ = TranslationProxy()
builtins._ = _


def load_languages():
    """加载所有可用语言"""
    global _translations
    _translations = {}

    # 遍历 locale 目录下的所有语言包
    for lang in os.listdir(LOCALE_DIR):
        lang_path = os.path.join(LOCALE_DIR, lang, "LC_MESSAGES")
        if os.path.exists(lang_path):
            _translations[lang] = Translations.load(LOCALE_DIR, [lang])


def set_language(lang_code):
    """全局切换语言"""
    global _current_translation

    try:
        translation = _translations.get(lang_code)
        if translation:
            _current_translation = translation
    except Exception as e:
        logger.error(e)
        lang_code = "en_US"
        translation = _translations.get(lang_code)
        if translation:
            _current_translation = translation

    logger.info(f"Changed language to {lang_code}")


def get_available_languages():
    """获取所有可用语言"""
    return list(_translations.keys())


# 预加载所有语言
load_languages()
