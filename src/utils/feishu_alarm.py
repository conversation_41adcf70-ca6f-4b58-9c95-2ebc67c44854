from socket import gethostname

import aiohttp
import requests
from loguru import logger

from src.settings import agent_setting


async def send_feishu_alarm(text: str):
    """发送飞书报警

    Args:
        text (str): 报警内容
    
curl -X POST -H "Content-Type: application/json" \
    -d '{"msg_type":"text","content":{"text":"request example"}}' \
    https://open.feishu.cn/open-apis/bot/v2/hook/****
    """
    headers = {"Content-Type": "application/json"}
    host_name = gethostname().strip()
    data = {
        "msg_type": "text",
        "content": {
            "text": f"环境：{agent_setting.env} | 主机：{host_name} | 内容：{text}"
        },
    }
    try:
        if host_name.startswith("easy-nlp"):
            async with aiohttp.ClientSession() as session:
                async with session.post(
                        agent_setting.feishu_alarm_url, headers=headers, json=data
                ) as response:
                    response.raise_for_status()
    except Exception as e:
        logger.error(f"发送飞书报警失败: {e}")


def send_feishu_alarm_sync(text: str):
    """发送飞书报警

    Args:
        text (str): 报警内容

curl -X POST -H "Content-Type: application/json" \
    -d '{"msg_type":"text","content":{"text":"request example"}}' \
    https://open.feishu.cn/open-apis/bot/v2/hook/****
    """
    url = agent_setting.feishu_alarm_url
    headers = {"Content-Type": "application/json"}
    # data = {"msg_type": "text", "content": {"text": text}}
    try:
        host_name = gethostname().strip()
        data = {
            "msg_type": "text",
            "content": {
                "text": f"环境：{agent_setting.env} | 主机：{host_name} | 内容：{text}"
            },
        }
        if host_name.startswith("easy-nlp"):
            requests.post(url, headers=headers, json=data)
    except Exception as e:
        logger.error(f"发送飞书报警失败: {e}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(send_feishu_alarm("测试飞书报警"))
