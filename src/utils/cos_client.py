import os
import time

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client, CosServiceError, CosClientError
from loguru import logger


class CosClient:
    def __init__(
        self,
        secret_id: str,
        secret_key: str,
        bucket: str,
        region: str,
        prefix: str = "",
    ) -> None:
        self.config = CosConfig(
            Region=region,
            SecretId=secret_id,
            SecretKey=secret_key,
            Scheme="https",
        )
        self.client = CosS3Client(self.config)
        self.bucket = bucket
        self.prefix = prefix

    def upload_file(self, data: bytes, file_path: str) -> None:
        assert isinstance(data, bytes), "data must be bytes"
        start = time.time()
        object_key = os.path.join(self.prefix, file_path)
        try:
            response = self.client.put_object(
                Bucket=self.bucket,  # Bucket 由 BucketName-APPID 组成
                Body=data,
                Key=object_key,
            )
            logger.info(f"upload file to cos: {object_key} {response['ETag']}")
        except (CosClientError, CosServiceError) as e:
            logger.error(f"upload file to cos failed: {object_key} {e}")

        logger.info(f"upload file to cos elapsed {time.time() - start}")