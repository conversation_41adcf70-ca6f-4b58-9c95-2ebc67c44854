from collections import OrderedDict


class LimitedSizeDict(OrderedDict):
    """
    A dictionary that limits the number of items it can contain.
    """
    def __init__(self, *args, **kwds):
        self.size_limit = kwds.pop("size_limit", None)
        OrderedDict.__init__(self, *args, **kwds)
        self._check_size_limit()

    def __setitem__(self, key, value):
        OrderedDict.__setitem__(self, key, value)
        self._check_size_limit()

    def _check_size_limit(self):
        if self.size_limit is not None:
            while len(self) > self.size_limit:
                self.popitem(last=False)


if __name__ == "__main__":
    d = LimitedSizeDict(size_limit=3)
    d["a"] = 1
    d["b"] = 2
    d["c"] = 3
    d["d"] = 4
    print(d)
