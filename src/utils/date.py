import datetime
import json
import time

from loguru import logger


def get_current_date_str(timezone: str = None) -> str:
    if timezone:
        import pytz

        tz = pytz.timezone(timezone)
        current_time = datetime.datetime.now(tz)
    else:
        current_time = datetime.datetime.now()
    return current_time.strftime("%Y-%m-%d %H:%M:%S")


def stats_async_func_cost(func):
    async def wrapper(*args, **kwargs):
        start = time.time()
        result = await func(*args, **kwargs)
        cost = time.time() - start
        if cost > 5:
            logger.error(f"{func.__name__} cost: {cost}s > 5s")
        else:
            logger.info(f"{func.__name__} cost: {time.time() - start}s")
        return result

    return wrapper


def safe_loads(json_str: str) -> dict:
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.warning(f"safe_loads error: {e}. json_str:{json_str}")
        return {}
