import asyncio
import time
from dataclasses import dataclass
from typing import Callable, Optional

from loguru import logger

from src.action.skill_center.skill_tools import SkillCenter
from src.common.constant import Area
from src.settings import agent_setting


@dataclass
class WeatherInfo:
    temperature: float  # 温度
    weather: str  # 天气类型：sunny, rainy, snowy 等
    wind_speed: Optional[float] = None  # 风速，单位 m/s
    aqi: Optional[int] = None  # 空气质量指数


@dataclass
class CalendarInfo:
    festival: str
    date: str
    week: str
    lunar: str
    solarterm: str
    motto: str


def cached_async(maxsize: int = 50):
    cache = {}

    def decorator(func: Callable):
        async def wrapper(*args):
            if args not in cache:
                cache[args] = await func(*args)
            return cache[args]

        wrapper.cache_clear = lambda: cache.clear()
        return wrapper

    return decorator


def get_wind_level(wind_speed_kph):
    """
    根据风速(km/h)转换为蒲福风级
    返回: (风力等级, 风力等级描述)
    """
    # 蒲福风级对照表 (km/h)
    beaufort_scale = [
        (0, "Calm", 1),  # 无风，烟直上
        (1, "Light Air", 5),  # 软风，烟示风向
        (2, "Light Breeze", 11),  # 轻风，感觉有风
        (3, "Gentle Breeze", 19),  # 微风，旗展开
        (4, "Moderate", 28),  # 和风，吹起灰尘
        (5, "Fresh", 38),  # 清风，小树摇摆
        (6, "Strong", 49),  # 强风，电线有声
        (7, "Near Gale", 61),  # 疾风，步行困难
        (8, "Gale", 74),  # 大风，折毁树枝
        (9, "Strong Gale", 88),  # 烈风，小损房屋
        (10, "Storm", 102),  # 狂风，拔树倒屋
        (11, "Violent Storm", 117),  # 暴风，损毁严重
        (12, "Hurricane", 999),  # 飓风，破坏性极强
    ]

    for level, desc, max_speed in beaufort_scale:
        if wind_speed_kph < max_speed:
            return level
    return 12


@cached_async(maxsize=50)
async def _get_weather_with_timestamp(
    city: str, timestamp_2h: int, language: str
) -> Optional[WeatherInfo]:
    """
    Internal cached weather fetching function.
    Results are cached for 2 hours with maximum 50 cities.
    国内环境: 返回高德地图天气API的响应，结构如下：
                {
                    "province": "省份",
                    "city": "城市",
                    "adcode": "区域编码",
                    "weather": "天气状况",
                    "temperature": "温度",
                    "winddirection": "风向",
                    "windpower": "风力",
                    "humidity": "湿度",
                    "reporttime": "数据发布时间"
                }

    海外环境: 返回WeatherAPI的current数据，结构如下：
                {
                    "last_updated_epoch": 更新时间戳,
                    "last_updated": "最后更新时间",
                    "temp_c": 摄氏温度,
                    "temp_f": 华氏温度,
                    "is_day": 是否白天,
                    "condition": {
                        "text": "天气状况描述",
                        "icon": "天气图标URL",
                        "code": 天气代码
                    },
                    "wind_mph": 风速(英里/小时),
                    "wind_kph": 风速(公里/小时),
                    "wind_degree": 风向角度,
                    "wind_dir": "风向",
                    "pressure_mb": 气压(百帕),
                    "pressure_in": 气压(英寸),
                    "precip_mm": 降水量(毫米),
                    "precip_in": 降水量(英寸),
                    "humidity": 湿度,
                    "cloud": 云量,
                    "feelslike_c": 体感温度(摄氏),
                    "feelslike_f": 体感温度(华氏),
                    "vis_km": 能见度(公里),
                    "vis_miles": 能见度(英里),
                    "uv": 紫外线指数,
                    "gust_mph": 阵风(英里/小时),
                    "gust_kph": 阵风(公里/小时)
                }
    """
    logger.info(f"Stared fetching weather for {city}")
    if city == "0.0,0.0":  # 经纬度为0.0,0.0时，表示未初始化
        return None

    skill_center = SkillCenter()
    try:
        weather_info = await skill_center.get_realtime_weather(
            city,
            env=agent_setting.region_version,
            robot_language=language,
        )
        logger.info(f"weather_info: {weather_info}")
        if agent_setting.region_version == Area.domestic:
            return WeatherInfo(
                temperature=int(weather_info["temperature"]),
                weather=weather_info["weather"],
                wind_speed=0,  # 国内使用weather描述判定风力，弃用此字段
                aqi=None,
            )
        else:
            return WeatherInfo(
                temperature=int(weather_info["feelslike_c"]),
                weather=weather_info["condition"]["text"],
                wind_speed=get_wind_level(weather_info["wind_kph"]),
                aqi=None,
            )
    except Exception as e:
        logger.error(f"Error fetching weather data: {e}")
        return None


async def get_weather(city: str, language: str) -> Optional[WeatherInfo]:
    """
    Get real-time weather information for a specified city using AMap API.
    Results are cached for 2 hours.

    Args:
        city (str): City name or code

    Returns:
        WeatherInfo: Weather information object, or None if data cannot be retrieved.
    """
    logger.info(f"Started getting weather for {city}")
    timestamp_4h = int(time.time()) // (4 * 3600)
    return await _get_weather_with_timestamp(city, timestamp_4h, language)


def clear_weather_cache():
    _get_weather_with_timestamp.cache_clear()


@cached_async(maxsize=50)
async def _calendar_with_timestamp(timestamp_d: int, timezone: str, language: str):
    query = "今天是几号?"
    sc = SkillCenter()

    try:
        result = await sc.get_calendar(
            user_question=query,
            env=agent_setting.region_version,
            timezone=timezone,
            language=language,
        )
        result = result.get("response", {}).get("display", {}).get("data", {})
        return CalendarInfo(
            festival=result.get("festival"),
            date=result.get("target"),
            week=result.get("week"),
            lunar=result.get("date", {}).get("lunar"),
            solarterm=result.get("solarterm"),
            motto=result.get("motto"),
        )
    except Exception as e:
        logger.error(f"Error fetching calendar data: {e}")
        return None


async def calendar(timezone: str, language: str) -> Optional[CalendarInfo]:
    """
    Get calendar information for today.
    Results are cached for 1 day.

    Returns:
        WeatherInfo: Weather information object, or None if data cannot be retrieved.
    """
    logger.info("Started getting calendar")
    timestamp_1d = int(time.time()) // (24 * 3600)
    return await _calendar_with_timestamp(timestamp_1d, timezone, language)


def clear_calendar_cache():
    _calendar_with_timestamp.cache_clear()


if __name__ == "__main__":
    # weather_data = asyncio.run(get_weather("北京"))
    # print(weather_data)
    #
    agent_setting.region_version = Area.overseas
    calendar_data = asyncio.run(calendar("Asia/Shanghai", "zh_CN"))
    print(calendar_data)

    # calendar_data = get_calendar_sync()
    # print(calendar_data)
