import json
from typing import Dict, List, Optional

from openai import OpenAI
from loguru import logger
from mem0.configs.llms.base import BaseLlmConfig
from mem0.llms.base import LLMBase

from src.settings import agent_setting


class OrionOpenAILLM(LLMBase):
    def __init__(self, config: Optional[BaseLlmConfig] = None):
        super().__init__(config)

        if not self.config.model:
            self.config.model = "gpt-4o-mini"

        self.client = OpenAI(
            api_key=agent_setting.plan_model_api_key,
            base_url=agent_setting.plan_model_base_url,
        )

    def _parse_response(self, response, tools):
        """
        Process the response based on whether tools are used or not.

        Args:
            response: The raw response from API.
            tools: The list of tools provided in the request.

        Returns:
            str or dict: The processed response.
        """
        if tools:
            processed_response = {
                "content": response.choices[0].message.content,
                "tool_calls": [],
            }

            if response.choices[0].message.tool_calls:
                for tool_call in response.choices[0].message.tool_calls:
                    processed_response["tool_calls"].append(
                        {
                            "name": tool_call.function.name,
                            "arguments": json.loads(tool_call.function.arguments),
                        }
                    )

            return processed_response
        else:
            content = response.choices[0].message.content
            # Compatible with other llms
            if "```json" in content:
                content = content.replace("```json", "")
            if "```" in content:
                content = content.replace("```", "")
            logger.info(f"mem0 modify memory content: {content}")
            fallback_content = json.dumps({"memory": []})
            try:
                content = json.loads(content)
            except Exception as e:
                logger.error(f"Error validating response: {e} with content: {content}")
                return fallback_content

            event_alias = ["class", "value"]
            try:
                if "memory" in content:
                    memories = content.get("memory", [])
                    logger.info(f"[Mem0] Raw memories: {memories}")
                    new_memories = []
                    for memory in memories:
                        for alias in event_alias:
                            if alias in memory:
                                memory["event"] = memory[alias]
                                memory.pop(alias)
                        if "event" not in memory:
                            continue
                        new_memories.append(memory)
                    content["memory"] = new_memories
                return json.dumps(content)
            except Exception as e:
                logger.error(f"Error validating response: {e}")
                return fallback_content

    def generate_response(
        self,
        messages: List[Dict[str, str]],
        response_format=None,
        tools: Optional[List[Dict]] = None,
        tool_choice: str = "auto",
    ):
        """
        Generate a response based on the given messages using OpenAI.

        Args:
            messages (list): List of message dicts containing 'role' and 'content'.
            response_format (str or object, optional): Format of the response. Defaults to "text".
            tools (list, optional): List of tools that the model can call. Defaults to None.
            tool_choice (str, optional): Tool choice method. Defaults to "auto".

        Returns:
            str: The generated response.
        """
        params = {
            "model": agent_setting.plan_model,
            "messages": messages,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
            "top_p": self.config.top_p,
        }

        if response_format:
            params["response_format"] = response_format
        if (
            tools
        ):  # TODO: Remove tools if no issues found with new memory addition logic
            params["tools"] = tools
            params["tool_choice"] = tool_choice

        # Update base_url and api_key real time
        self.client.base_url = agent_setting.plan_model_base_url
        self.client.api_key = agent_setting.plan_model_api_key
        response = self.client.chat.completions.create(**params)
        logger.info(
            f"client: {self.client} {self.client.base_url} {self.client.api_key}"
        )
        logger.debug(f"params: {params}")
        logger.debug(f"response: {response}")
        return self._parse_response(response, tools)
