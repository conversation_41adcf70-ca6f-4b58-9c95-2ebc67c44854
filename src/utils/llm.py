import asyncio
import json
import re
import time
import traceback
from typing import AsyncIterable, Callable, Optional

import aiohttp
from livekit.agents.utils import http_context
from loguru import logger
from openai import AsyncAzureOpenAI, AsyncClient
from openai.types.chat import ChatCompletionStreamOptionsParam
from pydantic import BaseModel, field_serializer
from google import genai
from google.genai import types

from src.action.model import RequestLLMConfig

# from vertexai.generative_models import GenerativeModel
from src.settings import (
    agent_setting,
    gemini_client,
    # boto3_client,
)
from src.utils.feishu_alarm import send_feishu_alarm

async_client = AsyncClient(
    max_retries=1,
    api_key=agent_setting.generate_text_model_api_key,
)
async_azure_client = AsyncAzureOpenAI(
    api_key=agent_setting.azure_subscription_key,
    azure_endpoint=agent_setting.azure_endpoint,
    api_version="2024-05-01-preview",
)


class LLMConfig(BaseModel):
    base_url: str = ""
    llm_model_name: str = ""
    api_key: str = ""
    temperature: float = 0.0
    max_tokens: int | None = None
    timeout: int = 6
    repetition_penalty: float | None = None

    @field_serializer("api_key")
    def serialize_api_key(self, api_key: str | None, _info):
        """序列化api_key字段时隐藏敏感信息"""
        if not api_key:
            return "****"
        if len(api_key) > 6:
            return f"{api_key[:2]}****{api_key[-3:]}"
        return "****"


class ModelResult(BaseModel):
    content: str | None
    token_cost: dict = {}
    llm_config: LLMConfig
    messages: list[dict] = []
    elapsed_time: float = 0
    return_message: dict = None
    error: str = None


class LLMManager:
    @classmethod
    async def _a_invoke_openai_llm_stream(
        cls,
        model: LLMConfig,
        messages: list[dict],
        system_prompt: str = None,
    ) -> AsyncIterable[str]:
        """Stream response from LLM"""
        start_at = time.time()
        async_client.base_url = model.base_url
        async_client.api_key = model.api_key
        async_client.timeout = model.timeout
        if system_prompt:
            messages.insert(
                0,
                {
                    "role": "system",
                    "content": system_prompt,
                },
            )
        try:
            response = await async_client.chat.completions.create(
                model=model.llm_model_name,
                messages=messages,
                temperature=model.temperature,
                max_tokens=model.max_tokens,
                stream=True,
                stream_options=ChatCompletionStreamOptionsParam(include_usage=True),
                timeout=10,
            )

            count = 0
            async for chunk in response:
                if count == 0:
                    logger.info(f"First chunk time: {time.time() - start_at}s")
                count += 1
                if chunk.choices:
                    # logger.info(f"Chunk: {chunk.choices[0].delta.content}")
                    content = chunk.choices[0].delta.content
                    if content:
                        yield content

        except asyncio.CancelledError:
            raise
        except Exception as e:
            await send_feishu_alarm(
                f"{model.llm_model_name} 流式请求异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息： {e}"
            )
            logger.error(f"Failed to invoke LLM stream: {e}, messages: {messages}")
            yield ""

    @classmethod
    async def _a_invoke_gemini_llm_stream(
        cls,
        model: LLMConfig,
        messages: list[dict],
        system_prompt: str = None,
    ) -> AsyncIterable[str]:
        """Stream response from LLM"""
        if system_prompt:
            messages.insert(
                0,
                {
                    "role": "system",
                    "content": system_prompt,
                },
            )

        contents = []
        for message in messages:
            content = message["content"]
            if message["role"] == "user":
                contents.append(types.Content(role="user", parts=[{"text": content}]))
            elif message["role"] == "system":  # system role is not supported
                contents.append(types.Content(role="user", parts=[{"text": content}]))
            elif message["role"] == "assistant":
                contents.append(types.Content(role="model", parts=[{"text": content}]))
            else:
                contents.append(types.Content(role="model", parts=[{"text": content}]))

        try:
            start_time = time.time()

            generate_content_config = types.GenerateContentConfig(
                temperature=model.temperature,
                max_output_tokens=model.max_tokens,
                safety_settings=[
                    types.SafetySetting(
                        category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"
                    ),
                    types.SafetySetting(
                        category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"
                    ),
                    types.SafetySetting(
                        category="HARM_CATEGORY_HARASSMENT", threshold="OFF"
                    ),
                ],
                http_options=types.HttpOptions(timeout=model.timeout * 1000),  # unit: ms, 流式调用gemini如果超时，会报出无法解析json的异常
            )

            count = 0
            async for chunk in await asyncio.wait_for(
                gemini_client.aio.models.generate_content_stream(
                    model=model.llm_model_name,
                    contents=contents,
                    config=generate_content_config,
                ),
                timeout=model.timeout,
            ):
                if count == 0:
                    logger.info(
                        f"First chunk time: {time.time() - start_time}s, {chunk.text}"
                    )
                count += 1

                yield chunk.text

        except asyncio.TimeoutError:
            await send_feishu_alarm(
                f"gemini 流式请求超时，模型名称：{model.llm_model_name}， 超时时间：{model.timeout}秒"
            )
            logger.error(
                f"Gemini stream timeout after {model.timeout} seconds, messages: {messages}"
            )
            yield ""
        except Exception as e:
            await send_feishu_alarm(
                f"gemini 流式请求异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息： {e} {traceback.format_exc()}"
            )
            logger.error(
                f"Failed to invoke LLM stream: {e}, messages: {messages} {traceback.format_exc()}"
            )
            yield ""

    @classmethod
    async def _a_invoke_azure_llm_stream(
        cls,
        model: LLMConfig,
        messages: list[dict],
        system_prompt: str = None,
    ) -> AsyncIterable[str]:
        """Stream response from LLM"""
        start_at = time.time()
        async_client.base_url = model.base_url
        if system_prompt:
            messages.insert(
                0,
                {
                    "role": "system",
                    "content": system_prompt,
                },
            )

        model_name = model.llm_model_name.replace(
            "azure_", ""
        )  #  azure_gpt-4o-2024-11-20, we use a tricky way to tell the difference between azure and openai
        try:
            response = await async_azure_client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=model.temperature,
                max_tokens=model.max_tokens,
                stream=True,
                stream_options=ChatCompletionStreamOptionsParam(include_usage=True),
                timeout=10,
            )

            count = 0
            async for chunk in response:
                if count == 0:
                    logger.info(f"First chunk time: {time.time() - start_at}s")
                count += 1
                if chunk.choices:
                    # logger.info(f"Chunk: {chunk.choices[0].delta.content}")
                    content = chunk.choices[0].delta.content
                    if content:
                        yield content

        except asyncio.CancelledError:
            raise
        except Exception as e:
            await send_feishu_alarm(
                f"azure 流式请求异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息： {e}"
            )
            logger.error(
                f"Failed to invoke Azure LLM stream: {e}, messages: {messages}"
            )
            yield ""

    @classmethod
    async def _a_invoke_llm_stream(
        cls,
        model: LLMConfig,
        messages: list[dict],
        system_prompt: str = None,
    ) -> AsyncIterable[str]:
        """Stream response from LLM"""
        if "gemini" in model.llm_model_name:
            async for chunk in cls._a_invoke_gemini_llm_stream(
                model, messages, system_prompt
            ):
                yield chunk
        elif "azure" in model.llm_model_name:
            async for chunk in cls._a_invoke_azure_llm_stream(
                model, messages, system_prompt
            ):
                yield chunk
        else:
            async for chunk in cls._a_invoke_openai_llm_stream(
                model, messages, system_prompt
            ):
                yield chunk

    @classmethod
    async def _a_call_openai_api(
        cls,
        model: LLMConfig,
        messages: list[dict],
        _logger=logger,
        session: Optional[aiohttp.ClientSession] = None,
        tools: Optional[list[dict]] = None,
        tool_choice: Optional[str] = None,
    ):
        """适用OpenAI的调用方式"""
        start_at = time.time()
        if not session:
            session = cls._load_session_context()

        try:
            request_body = {
                "model": model.llm_model_name,
                "messages": messages,
                "temperature": model.temperature,
                "max_tokens": model.max_tokens,
            }
            if model.repetition_penalty:
                request_body["repetition_penalty"] = model.repetition_penalty
            if tools:
                request_body["tools"] = tools
            if tool_choice:
                request_body["tool_choice"] = tool_choice
            async with session.post(
                f"{model.base_url}/chat/completions",
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {model.api_key}",
                },
                timeout=aiohttp.ClientTimeout(total=model.timeout),
            ) as response:
                _logger.info(
                    f"[LLM {model.llm_model_name}] Response status: {response.status}. response:{await response.text()}\nrequest_body:{request_body} Elapsed time: {time.time() - start_at}s"
                )
                if response.status != 200:
                    _logger.error(
                        f"[LLM {model.llm_model_name}] Response status: {response.status}. response:{await response.text()}\nrequest_body:{request_body} Elapsed time: {time.time() - start_at}s"
                    )
                    await send_feishu_alarm(
                        f"{model.llm_model_name} api调用异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, status: {response.status}. response:{await response.text()}\nrequest_body:{request_body}"
                    )
                    return ModelResult(
                        content="",
                        token_cost={},
                        llm_config=model,
                        messages=messages,
                        error=f"Request LLM API Failed: {await response.text()}",
                    )

                data = await response.json()
                _logger.info(
                    f"[LLM {model.llm_model_name}] Elapsed time: {time.time() - start_at}s"
                )
                return ModelResult(
                    content=data["choices"][0]["message"]["content"],
                    token_cost=data["usage"],
                    llm_config=model,
                    messages=messages,
                    return_message=data["choices"][0]["message"],
                )

        except asyncio.TimeoutError as e:
            _logger.warning(
                f"Request to {model.llm_model_name} API Failed, error: {e.__class__.__name__}"
            )
            return ModelResult(
                content="",
                token_cost={},
                llm_config=model,
                messages=messages,
                error="Request LLM API Timeout",
            )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"Error when call {model.llm_model_name} API: {str(e)}")
            print(traceback.format_exc())
            await send_feishu_alarm(
                f"{model.llm_model_name} api调用异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息：{str(e)}"
            )
            return ModelResult(
                content="",
                token_cost={},
                llm_config=model,
                messages=messages,
                error="Internal Error",
            )

    @classmethod
    async def _a_call_gemini_api(
        cls,
        model: LLMConfig,
        messages: list[dict],
        _logger=logger,
        tools: Optional[list[dict]] = None,
        tool_choice: Optional[str] = None,
        session: Optional[aiohttp.ClientSession] = None,  # noqa
    ):
        start_time = time.time()
        config = types.GenerateContentConfig(
            temperature=model.temperature,
            max_output_tokens=model.max_tokens,
            safety_settings=[
                types.SafetySetting(
                    category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"
                ),
                types.SafetySetting(
                    category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"
                ),
                types.SafetySetting(
                    category="HARM_CATEGORY_HARASSMENT", threshold="OFF"
                ),
            ],
            http_options=types.HttpOptions(timeout=model.timeout * 1000),  # unit: ms, 如果未通过wait catch住，非流式超时可能报出429错误
        )

        try:
            contents = []
            for message in messages:
                content = message["content"]
                if message["role"] == "user":
                    contents.append(
                        types.Content(role="user", parts=[{"text": content}])
                    )
                elif message["role"] == "system":  # system role is not supported
                    contents.append(
                        types.Content(role="assistant", parts=[{"text": content}])
                    )
                elif message["role"] == "assistant":
                    contents.append(
                        types.Content(role="assistant", parts=[{"text": content}])
                    )
                else:
                    contents.append(
                        types.Content(role="assistant", parts=[{"text": content}])
                    )

            result = await asyncio.wait_for(
                gemini_client.aio.models.generate_content(
                    model=model.llm_model_name,
                    contents=contents,
                    config=config,
                ),
                timeout=model.timeout,
            )

            _logger.info(f"[LLM Gemini] Elapsed time: {time.time() - start_time}s")
            return ModelResult(
                content=result.text,
                token_cost={
                    "prompt_tokens": result.usage_metadata.prompt_token_count,
                    "completion_tokens": result.usage_metadata.candidates_token_count,
                    "total_tokens": result.usage_metadata.total_token_count,
                    "prompt_tokens_details": {
                        "cached_tokens": result.usage_metadata.cached_content_token_count,
                    },
                },
                llm_config=model,
                messages=messages,
            )
        except asyncio.TimeoutError:
            await send_feishu_alarm(
                f"gemini api调用超时，模型名称：{model.llm_model_name}， 超时时间：{model.timeout}秒"
            )
            _logger.error(
                f"Gemini API timeout after {model.timeout} seconds. Elapsed time: {time.time() - start_time}s"
            )
            return ModelResult(
                content="",
                token_cost={},
                llm_config=model,
                messages=messages,
                error="Gemini API Timeout",
            )
        except Exception as e:
            await send_feishu_alarm(
                f"gemini api调用异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息： {e}"
            )
            _logger.error(
                f"Error when call Gemini API: {e} Elapsed time: {time.time() - start_time}s"
            )
            _logger.error(traceback.format_exc())
            return ModelResult(
                content="",
                token_cost={},
                llm_config=model,
                messages=messages,
            )

    # @classmethod
    # async def _a_call_bedrock_api(
    #     cls, model: LLMConfig, messages: list[dict], _logger=logger
    # ):
    #     start_time = time.time()
    #     try:
    #         new_messages = deepcopy(messages)
    #         for msg in new_messages:
    #             if msg["role"] == "system":
    #                 msg["role"] = "user"
    #             msg["content"] = [{"text": msg["content"]}]
    #
    #         inf_params = {
    #             # "maxTokens": model.max_tokens,
    #             "temperature": model.temperature,
    #         }
    #
    #         additionalModelRequestFields = {"inferenceConfig": {"topK": 20}}
    #
    #         model_response = boto3_client.converse(
    #             modelId=model.llm_model_name,
    #             messages=new_messages,
    #             inferenceConfig=inf_params,
    #             additionalModelRequestFields=additionalModelRequestFields,
    #         )
    #         _logger.info(f"[LLM Bedrock] Elapsed time: {time.time() - start_time}s")
    #         return ModelResult(
    #             content=model_response["output"]["message"]["content"][0]["text"],
    #             token_cost={
    #                 "prompt_tokens": model_response["usage"]["inputTokens"],
    #                 "completion_tokens": model_response["usage"]["outputTokens"],
    #             },
    #             llm_config=model,
    #             messages=messages,
    #         )
    #     except Exception as e:
    #         await send_feishu_alarm(
    #             f"Nova api调用异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息： {e}"
    #         )
    #         _logger.error(
    #             f"Error when call Nova API: {e} Elapsed time: {time.time() - start_time}s"
    #         )
    #         return ModelResult(
    #             content="",
    #             token_cost={},
    #             llm_config=model,
    #             messages=messages,
    #         )

    @classmethod
    async def _a_call_azure_api(
        cls,
        model: LLMConfig,
        messages: list[dict],
        _logger=logger,
        tools: Optional[list[dict]] = None,
        tool_choice: Optional[str] = None,
    ):
        start_at = time.time()
        model_name = model.llm_model_name.replace(
            "azure_", ""
        )  #  azure_gpt-4o-2024-11-20, we use a tricky way to tell the difference between azure and openai

        session = cls._load_session_context()
        try:
            request_body = {
                "model": model_name,
                "messages": messages,
                "temperature": model.temperature,
                "max_tokens": model.max_tokens,
            }
            if tools:
                request_body["tools"] = tools
            if tool_choice:
                request_body["tool_choice"] = tool_choice
            async with session.post(
                agent_setting.azure_endpoint,
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "api-key": agent_setting.azure_subscription_key,
                },
                timeout=aiohttp.ClientTimeout(total=model.timeout),
            ) as response:
                if response.status != 200:
                    _logger.error(
                        f"[LLM Azure] Response status: {response.status}. response:{await response.text()}\nrequest_body:{request_body} Elapsed time: {time.time() - start_at}s"
                    )
                    await send_feishu_alarm(
                        f"azure openai api调用异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, status: {response.status}. response:{await response.text()}\nrequest_body:{request_body}"
                    )

                    return ModelResult(
                        content="",
                        token_cost={},
                        llm_config=model,
                        messages=messages,
                    )

                data = await response.json()
                _logger.info(f"[LLM Azure] Elapsed time: {time.time() - start_at}s")
                return ModelResult(
                    content=data["choices"][0]["message"]["content"],
                    token_cost=data["usage"],
                    llm_config=model,
                    messages=messages,
                    return_message=data["choices"][0]["message"],
                )
        except asyncio.TimeoutError as e:
            _logger.error(f"Request to Azure OpenAI API Failed, error: {e}")
            return ModelResult(
                content="",
                token_cost={},
                llm_config=model,
                messages=messages,
            )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"Error when call Azure OpenAI API: {str(e)}")
            print(traceback.format_exc())
            await send_feishu_alarm(
                f"azure openai api调用异常，模型名称：{model.llm_model_name}， 地址：{model.base_url}, 异常信息：{str(e)}"
            )
            return ModelResult(
                content="",
                token_cost={},
                llm_config=model,
                messages=messages,
            )

    @classmethod
    async def _invoke_model_api(
        cls,
        model_config: LLMConfig,
        messages: list[dict],
        call_llm_api_func: Optional[Callable],
        diff_provider: bool = False,
        parallel_count: int = 1,
        _logger=logger,
        session: Optional[aiohttp.ClientSession] = None,
        tools: Optional[list[dict]] = None,
        tool_choice: Optional[str] = None,
    ) -> ModelResult:
        async def call_api():
            return await call_llm_api_func(
                model_config,
                messages,
                _logger,
                session=session,
                tools=tools,
                tool_choice=tool_choice,
            )

        async def _handle_parallel_tasks(tasks, _logger):
            """Handle parallel API tasks and return the first valid result"""
            done, pending = await asyncio.wait(
                tasks, return_when=asyncio.FIRST_COMPLETED
            )

            first_task = done.pop()
            first_result = first_task.result()

            if pending:
                if (
                    first_result.content or first_result.messages
                ):  # 如果done的result不为空，则直接返回
                    for task in pending:
                        task.cancel()
                    return first_result

                try:  # 如果done的result为空，则等待所有pending的result，返回第一个不为空的result
                    results = await asyncio.gather(*pending)
                    for result in results:
                        if result.content or result.messages:
                            return result
                except asyncio.CancelledError:
                    raise
                except Exception as e:
                    _logger.error(f"Error when gather pending tasks: {e}")
                    return first_result

            return first_result

        if diff_provider:
            _logger.info(
                "[LLM Parallel] Start to invoke agent with two different provider."
            )

            # for openai backup
            async def call_api_backup():
                return await cls._a_call_azure_api(
                    LLMConfig(
                        base_url=agent_setting.plan_model_base_url,  # 不影响，函数内部没用到base_url
                        llm_model_name=agent_setting.plan_model,
                    ),
                    messages,
                    _logger,
                    tools=tools,
                    tool_choice=tool_choice,
                )

            return await _handle_parallel_tasks(
                [asyncio.Task(call_api()), asyncio.Task(call_api_backup())], _logger
            )

        elif parallel_count > 1:
            _logger.info(
                f"[LLM Parallel] Start to invoke agent with same provider. parallel_count: {parallel_count}"
            )
            return await _handle_parallel_tasks(
                [asyncio.Task(call_api()) for _ in range(parallel_count)], _logger
            )

        else:
            return await call_api()

    @classmethod
    def _load_session_context(cls) -> aiohttp.ClientSession:
        try:
            return http_context.http_session()
        except Exception:
            logger.warning("Session context not found, creating new one.")
            http_context._new_session_ctx()()
            return http_context.http_session()

    @classmethod
    async def invoke_plan_model(
        cls,
        messages: list[dict],
        _logger=logger,
        tools: Optional[list[dict]] = None,
        tool_choice: Optional[str] = None,
    ) -> ModelResult:
        """just for call plan model"""
        diff_provider = False
        llm_config = LLMConfig(
            base_url=agent_setting.plan_model_base_url,
            llm_model_name=agent_setting.plan_model,
            api_key=agent_setting.plan_model_api_key,
            timeout=10,
        )
        if "gemini" in agent_setting.plan_model:
            call_llm_api = cls._a_call_gemini_api
        # elif "nova" in agent_setting.plan_model:
        #     call_llm_api = cls._a_call_bedrock_api
        if "azure" in agent_setting.plan_model:
            call_llm_api = cls._a_call_azure_api
        elif "qwen" in agent_setting.plan_model:
            call_llm_api = cls._a_call_openai_api
            llm_config.repetition_penalty = 1.0
        else:  # default to OpenAI GPT models (e.g. gpt-4)
            call_llm_api = cls._a_call_openai_api
            if agent_setting.dual_model_planning:
                diff_provider = True

        return await cls._invoke_model_api(
            llm_config,
            messages,
            parallel_count=1,
            diff_provider=diff_provider,
            call_llm_api_func=call_llm_api,
            _logger=_logger,
            tools=tools,
            tool_choice=tool_choice,
        )

    @classmethod
    def _get_call_llm_api_func(cls, model_name: str, llm_config: LLMConfig) -> Callable:
        if "gemini" in model_name:
            return cls._a_call_gemini_api
        elif "azure" in model_name:
            return cls._a_call_azure_api
        elif "qwen" in model_name:
            # patch the llm_config
            llm_config.repetition_penalty = 1.0
            return cls._a_call_openai_api
        else:
            return cls._a_call_openai_api

    @classmethod
    async def invoke_generate_text_model(
        cls,
        messages: list[dict],
        _logger=logger,
        llm_config: Optional[LLMConfig] = None,
    ) -> ModelResult:
        """just for call server function model"""
        if not llm_config:
            llm_config = LLMConfig(
                base_url=agent_setting.generate_text_model_base_url,
                llm_model_name=agent_setting.generate_text_model,
                api_key=agent_setting.generate_text_model_api_key,
                timeout=10,
            )

        call_llm_api_func = cls._get_call_llm_api_func(
            llm_config.llm_model_name, llm_config
        )
        return await cls._invoke_model_api(
            llm_config,
            messages,
            parallel_count=1,
            diff_provider=False,
            call_llm_api_func=call_llm_api_func,
            _logger=_logger,
        )

    @classmethod
    async def invoke_model_from_llm_config(
        cls,
        messages: list[dict],
        llm_config: LLMConfig,
        _logger=logger,
    ) -> ModelResult:
        call_llm_api_func = cls._get_call_llm_api_func(
            llm_config.llm_model_name, llm_config
        )
        return await cls._invoke_model_api(
            llm_config,
            messages,
            parallel_count=1,
            diff_provider=False,
            call_llm_api_func=call_llm_api_func,
            _logger=_logger,
        )

    @classmethod
    async def invoke_summary_model(
        cls,
        messages: list[dict],
        _logger=logger,
        session: Optional[aiohttp.ClientSession] = None,
    ) -> ModelResult:
        """just for call summary model"""
        llm_config = LLMConfig(
            base_url=agent_setting.summary_model_base_url,
            llm_model_name=agent_setting.summary_model,
            api_key=agent_setting.summary_model_api_key,
            timeout=10,
        )
        call_llm_api_func = cls._get_call_llm_api_func(
            agent_setting.summary_model,
            llm_config,
        )
        return await cls._invoke_model_api(
            messages=messages,
            model_config=llm_config,
            parallel_count=1,
            diff_provider=False,
            call_llm_api_func=call_llm_api_func,
            _logger=_logger,
            session=session,
        )


def parse_output_to_dict(output: str) -> tuple[dict, str]:
    try:
        pattern = re.compile(r"```json\n({.*})\n```", re.DOTALL)
        match = pattern.search(output)
        if match:
            return json.loads(match.group(1)), ""

        pattern = re.compile(r"\{.*}", re.DOTALL)
        match = pattern.search(output)
        if match:
            return json.loads(match.group(0)), ""

        return json.loads(output), ""
    except Exception as e:
        return {}, f"Invalid format. error message: {e}"


async def async_stream_generator(
    request_url: str,  # noqa: F821
    api_key: str,  # noqa: F821
    model_name: str,  # noqa: F821
    messages: list,
    llm_stream_config: Optional[RequestLLMConfig] = None,
) -> AsyncIterable[bytes]:
    """
    TODO(<EMAIL>): remove useless params
    """
    # Use LLMManager's stream method
    count = 0
    start_time = time.time()
    if not llm_stream_config:
        llm_stream_config = RequestLLMConfig(
            temperature=0.7,
            max_tokens=500,
        )

    # Support LLM mode
    if llm_stream_config.mode == "turbo":
        llm_config = LLMConfig(
            temperature=llm_stream_config.temperature,
            max_tokens=llm_stream_config.max_tokens,
            timeout=llm_stream_config.timeout,
            base_url=agent_setting.guest_text_model_base_url,
            api_key=agent_setting.guest_text_model_api_key,
            llm_model_name=agent_setting.guest_text_model,
        )
    else:
        llm_config = LLMConfig(
            temperature=llm_stream_config.temperature,
            max_tokens=llm_stream_config.max_tokens,
            timeout=llm_stream_config.timeout,
            base_url=agent_setting.generate_text_model_base_url,
            api_key=agent_setting.generate_text_model_api_key,
            llm_model_name=agent_setting.generate_text_model,
        )

    async for chunk in LLMManager._a_invoke_llm_stream(
        model=llm_config,
        messages=messages,
    ):
        if count == 0:
            logger.info(
                f"First chunk elapsed time: {time.time() - start_time:.2f} seconds"
            )
        count += 1

        if chunk:
            yield chunk.encode("utf-8")


if __name__ == "__main__":
    agent_setting.plan_model = "gpt-4o-2024-11-20"
    agent_setting.plan_model_base_url = "http://proxy-ai.smartsales.vip/v1"
    agent_setting.plan_model_api_key = (
        "***************************************************"
    )

    async def test_invoke_plan_model():
        messages = [
            {"role": "user", "content": "你好，我是小明，我是一个学生。"},
        ]
        start_at = time.time()
        result = await LLMManager.invoke_plan_model(messages)
        print(result)
        print(f"Time taken: {time.time() - start_at:.2f} seconds")

    asyncio.run(test_invoke_plan_model())
