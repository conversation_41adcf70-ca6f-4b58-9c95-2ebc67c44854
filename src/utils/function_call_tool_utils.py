from copy import deepcopy

from loguru import logger


_FUNCTION_CALL_TOOL_EXAMPLE = {
    "type": "function",
    "function": {
        "name": "case_introduction",
        "description": "案例介绍。播放案例视频。",
        "parameters": {
            "type": "object",
            "properties": {
                "case": {
                    "type": "string",
                    "description": "案例场景",
                    "enum": [
                        "小秘2-助残功能场景",
                        "小秘2-博物馆场景",
                        "小秘2-企业展厅场景",
                        "小秘2-图书馆场景",
                        "小秘2-政务大厅场景",
                        "小秘2-医疗场景",
                        "小秘2-教育场景",
                        "豹厂通-工厂场景",
                    ],
                }
            },
            "required": ["case"],
        },
    },
}


class FunctionCallToolUtils:
    @staticmethod
    def get_enum_values_from_tools(
        tools: list[dict], target_tool_name: str, target_param_name: str
    ) -> list:
        try:
            enum_kv = FunctionCallToolUtils.get_enum_kv_from_tools(
                tools, target_tool_name
            )
            return deepcopy(enum_kv.get(target_param_name, []))
        except Exception as e:
            logger.error(
                f"Error when get enum values from tools: {tools}, target_tool_name: {target_tool_name}, target_param_name: {target_param_name}, error: {e}"
            )
        return []

    @staticmethod
    def get_enum_kv_from_one_tool(tool: dict) -> dict[str, list]:
        enum_kv = {}
        try:
            if function := tool.get("function", {}):
                if parameters := function.get("parameters", {}):
                    if properties := parameters.get("properties", {}):
                        for param_name, param_info in properties.items():
                            if enum_values := param_info.get("enum", []):
                                enum_kv[param_name] = deepcopy(enum_values)
        except Exception as e:
            logger.error(f"Error when get enum kv from one tool: {tool}, error: {e}")
        return enum_kv

    @staticmethod
    def get_enum_kv_from_tools(
        tools: list[dict], target_tool_name: str
    ) -> dict[str, list]:
        try:
            for tool in tools:
                if function := tool.get("function", {}):
                    if tool_name := function.get("name", ""):
                        if tool_name == target_tool_name:
                            return FunctionCallToolUtils.get_enum_kv_from_one_tool(tool)
        except Exception as e:
            logger.error(
                f"Error when get enum kv from tools: {tools}, target_tool_name: {target_tool_name}, error: {e}"
            )
        return {}

    @staticmethod
    def get_multi_enum_kv_from_tools(tools: list[dict]) -> dict[str, dict[str, list]]:
        multi_enum_kv = {}
        try:
            for tool in tools:
                if function := tool.get("function", {}):
                    if tool_name := function.get("name", ""):
                        if enum_kv := FunctionCallToolUtils.get_enum_kv_from_one_tool(
                            tool
                        ):
                            multi_enum_kv[tool_name] = enum_kv
        except Exception as e:
            logger.error(
                f"Error when get multi enum kv from tools: {tools}, error: {e}"
            )
        return multi_enum_kv


if __name__ == "__main__":
    from rich import print

    tools = [
        {
            "function": {
                "name": "case_introduction",
                "parameters": {
                    "properties": {
                        "case": {
                            "enum": [
                                "小秘2-助残功能场景",
                                "小秘2-博物馆场景",
                                "小秘2-企业展厅场景",
                                "小秘2-图书馆场景",
                                "小秘2-政务大厅场景",
                                "小秘2-医疗场景",
                                "小秘2-教育场景",
                                "豹厂通-工厂场景",
                            ]
                        }
                    }
                },
            }
        }
    ]
    print(FunctionCallToolUtils.get_multi_enum_kv_from_tools(tools))
    print(FunctionCallToolUtils.get_enum_kv_from_tools(tools, "case_introduction"))
    print(
        FunctionCallToolUtils.get_enum_values_from_tools(
            tools, "case_introduction", "case"
        )
    )
