import json
import time
from typing import Any, Callable, Union

import redis.asyncio as redis
from loguru import logger

from src.settings import agent_setting


class RedisPool:
    def __init__(self):
        self.async_redis_pool = redis.ConnectionPool(
            host=agent_setting.redis_host,
            port=agent_setting.redis_port,
            db=agent_setting.redis_db,
            decode_responses=True,
            max_connections=20,
        )

    def client(self) -> redis.Redis:
        return redis.Redis.from_pool(self.async_redis_pool)


redis_pool = RedisPool()


# 不带参数的函数缓存装饰器, 只根据函数名和前缀生成key
def cache_func_decorator_without_parameters(
    func_or_seconds: Union[Callable, int] = 600,
) -> Callable:
    def decorator(func: Callable, seconds: int) -> Callable:
        async def wrapper(*args, **kwargs) -> Any:
            # 生成唯一键
            prefix = ""
            from src.agent_core.models.model import AgentParameter

            if len(args) == 1 and isinstance(args[0], AgentParameter):
                p: AgentParameter = args[0]
                prefix = (
                    f"{p.robot.device_id}_{p.robot.enterprise_id}_{p.robot.language}"
                )

            key = f"{func.__module__}.{func.__name__}_{prefix}"

            redis_client = await redis_pool.client()

            try:
                start_at = time.time()
                # 尝试从缓存中获取结果
                if cached_result := await redis_client.get(key):
                    r = json.loads(cached_result)
                    logger.info(
                        f"Cache hit: {key} -> {r} cost_time: {time.time() - start_at}"
                    )
                    return r

                start_at = time.time()
                # 调用原始函数并缓存结果
                result = await func(*args, **kwargs)
                await redis_client.set(
                    key, json.dumps(result, ensure_ascii=False), ex=seconds
                )
                logger.info(
                    f"Cache miss: {key} -> {result} cost_time: {time.time() - start_at}"
                )
                return result

            finally:
                # 确保关闭 Redis 客户端连接
                await redis_client.aclose()

        return wrapper

    # 如果直接传入的是函数，使用默认过期时间
    if callable(func_or_seconds):
        return decorator(func_or_seconds, 600)  # 默认10分钟

    # 如果传入的是过期时间
    return lambda func: decorator(func, func_or_seconds)
