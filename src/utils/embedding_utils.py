from loguru import logger
from typing import Any
import aiohttp
import hashlib
import redis.asyncio as redis
import numpy as np
import uuid

from src.settings import agent_setting


class RedisPoolForEmbedding:
    def __init__(self):
        self.async_redis_pool = redis.ConnectionPool(
            host=agent_setting.redis_host,
            port=agent_setting.redis_port,
            db=agent_setting.redis_db,
            decode_responses=False,  # NOTE embedding以numpy.float32的bytes形式存储，比json形式存储更节省redis内存
            max_connections=20,
        )

    def client(self) -> redis.Redis:
        return redis.Redis.from_pool(self.async_redis_pool)


REDIS_POOL_FOR_EMBEDDING = RedisPoolForEmbedding()


def is_embeddings_base_url_special(base_url: str) -> bool:
    """
    base_url, e.g.
        "https://api.openai.com/v1",
        "http://*************:8080/embed"
    """
    return base_url.endswith("/embed")


def prepare_request_embeddings_args(
    *,
    base_url: str,
    endpoint: str = "/embeddings",
    api_key: str = "",
    model_name: str,
    input: str | list[str],
    dimensions: int = 1024,
) -> dict:
    """
    base_url, e.g.
        "https://api.openai.com/v1",
        "http://*************:8080/embed"

    api_key, e.g. "sk-xx", "Bearer sk-xx"

    model_name, e.g. "text-embedding-3-small", "bge"
    """
    if isinstance(input, str):
        input = [input]
    assert (
        isinstance(input, list)
        and len(input) > 0
        and all(isinstance(item, str) and item != "" for item in input)
    ), "input must be nonempty list of str"
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["Authorization"] = (
            api_key if api_key.startswith("Bearer") else f"Bearer {api_key}"
        )
    req_dict = {"model": model_name, "dimensions": dimensions}
    # special handling for embeddings base url which is not openai compatible
    if is_embeddings_base_url_special(base_url):
        req_dict["inputs"] = input
        url = base_url
    else:
        req_dict["input"] = input
        url = base_url + endpoint
    return {"url": url, "headers": headers, "json": req_dict}


def postprocess_embeddings_response_json(
    response_json: Any, *, base_url: str
) -> list[list[float]]:
    try:
        if is_embeddings_base_url_special(base_url):
            return response_json
        else:
            return [
                item["embedding"]
                for item in sorted(response_json["data"], key=lambda x: x["index"])
            ]
    except Exception as e:
        logger.error(f"Failed to postprocess embeddings response json: {e}")
        return []


def get_redis_key_for_embedding(
    *,
    text: str,
    embedding_model: str,
    embedding_dim: int,
) -> str:
    text_hash = hashlib.md5(text.encode()).hexdigest()
    return f"embeddings:{embedding_model}:{embedding_dim}:{text_hash}"


async def async_get_one_embedding(
    *,
    query_id: str = uuid.uuid4().hex,
    text: str,
    http_session: aiohttp.ClientSession | None = None,
    embedding_api_key: str = agent_setting.embedding_api_key,
    embedding_model_base_url: str = agent_setting.embedding_model_base_url,
    embedding_model: str = agent_setting.embedding_model,
    embedding_dim: int = agent_setting.embedding_dim,
    enable_redis: bool = False,
    redis_client: redis.Redis | None = None,
    redis_ttl: int = 31 * 24 * 60 * 60,
    embedding_timeout: int = agent_setting.embedding_timeout,
) -> list[float]:
    if http_session is None:
        http_session = aiohttp.ClientSession()
    if not enable_redis:
        req_args = prepare_request_embeddings_args(
            base_url=embedding_model_base_url,
            api_key=embedding_api_key,
            model_name=embedding_model,
            input=text,
            dimensions=embedding_dim,
        )
        logger.info(f"query_id: {query_id}, request embeddings args: {req_args}")
        async with http_session.post(
            url=req_args["url"],
            headers=req_args["headers"],
            json=req_args["json"],
            timeout=aiohttp.ClientTimeout(total=embedding_timeout),
        ) as response:  # type: ignore
            if response.status != 200:
                error_msg = f"query_id: {query_id}, embedding调用异常，地址：{embedding_model_base_url}, status: {response.status}"
                raise RuntimeError(error_msg)
            response_json = await response.json()  # NOTE 这一步办公网耗时：bge:1024大约20ms, text-embedding-3-small:1024大约600到2300ms
            embedding = postprocess_embeddings_response_json(
                response_json, base_url=embedding_model_base_url
            )[0]
            return embedding
    else:
        if redis_client is None:
            redis_client = (
                REDIS_POOL_FOR_EMBEDDING.client()
            )  # NOTE 这一步耗时大约0.34ms，可以忽略不计
        key = get_redis_key_for_embedding(
            embedding_model=embedding_model,
            embedding_dim=embedding_dim,
            text=text,
        )
        async with redis_client:
            result = await redis_client.get(
                key
            )  # NOTE 这一步耗时大约80ms，大于bge:1024的20ms，小于text-embedding-3-small:1024的600ms
            if result is not None:
                logger.info(f"query_id: {query_id}, found embedding in redis: {key}")
                embedding = np.frombuffer(result, dtype=np.float32).tolist()
                return embedding
            else:
                embedding = await async_get_one_embedding(
                    query_id=query_id,
                    http_session=http_session,
                    embedding_api_key=embedding_api_key,
                    embedding_model_base_url=embedding_model_base_url,
                    embedding_model=embedding_model,
                    embedding_dim=embedding_dim,
                    text=text,
                    enable_redis=False,
                    embedding_timeout=embedding_timeout,
                )
                embedding_bytes = np.array(embedding, dtype=np.float32).tobytes()
                await redis_client.set(
                    key, embedding_bytes, ex=redis_ttl
                )  # NOTE 这一步耗时大约25ms
                return embedding


async def async_get_embeddings(
    *,
    query_id: str = uuid.uuid4().hex,
    batch: list[str],
    http_session: aiohttp.ClientSession | None = None,
    embedding_api_key: str = agent_setting.embedding_api_key,
    embedding_model_base_url: str = agent_setting.embedding_model_base_url,
    embedding_model: str = agent_setting.embedding_model,
    embedding_dim: int = agent_setting.embedding_dim,
    enable_redis: bool = False,
    redis_client: redis.Redis | None = None,
    redis_ttl: int = 31 * 24 * 60 * 60,
    embedding_timeout: int = agent_setting.embedding_timeout,
) -> list[list[float]]:
    if http_session is None:
        http_session = aiohttp.ClientSession()
    if not enable_redis:
        req_args = prepare_request_embeddings_args(
            base_url=embedding_model_base_url,
            api_key=embedding_api_key,
            model_name=embedding_model,
            input=batch,
            dimensions=embedding_dim,
        )
        logger.info(f"query_id: {query_id}, request embeddings args: {req_args}")
        async with http_session.post(
            url=req_args["url"],
            headers=req_args["headers"],
            json=req_args["json"],
            timeout=aiohttp.ClientTimeout(total=embedding_timeout),
        ) as response:  # type: ignore
            if response.status != 200:
                error_msg = f"query_id: {query_id}, embedding调用异常，地址：{embedding_model_base_url}, status: {response.status}"
                raise RuntimeError(error_msg)
            response_json = await response.json()
            return postprocess_embeddings_response_json(
                response_json, base_url=embedding_model_base_url
            )
    else:
        if redis_client is None:
            redis_client = REDIS_POOL_FOR_EMBEDDING.client()
        key_to_text = {}
        all_key_list = []
        for text in batch:
            key = get_redis_key_for_embedding(
                embedding_model=embedding_model,
                embedding_dim=embedding_dim,
                text=text,
            )
            key_to_text[key] = text
            all_key_list.append(key)
        deduplicated_key_list = list(set(all_key_list))
        keys_found_in_redis = []
        key_to_embedding = {}
        async with redis_client:
            pipe = redis_client.pipeline()
            for key in deduplicated_key_list:
                pipe.get(key)
            results = await pipe.execute()
            for key, result in zip(deduplicated_key_list, results):
                if result is not None:
                    keys_found_in_redis.append(key)
                    key_to_embedding[key] = np.frombuffer(
                        result, dtype=np.float32
                    ).tolist()
            keys_not_found_in_redis = [
                key for key in deduplicated_key_list if key not in keys_found_in_redis
            ]
            logger.info(
                f"query_id: {query_id}, keys_found_in_redis: {keys_found_in_redis}"
            )
            logger.info(
                f"query_id: {query_id}, keys_not_found_in_redis: {keys_not_found_in_redis}"
            )
            if keys_not_found_in_redis:
                embeddings = await async_get_embeddings(
                    query_id=query_id,
                    http_session=http_session,
                    embedding_api_key=embedding_api_key,
                    embedding_model_base_url=embedding_model_base_url,
                    embedding_model=embedding_model,
                    embedding_dim=embedding_dim,
                    batch=[key_to_text[key] for key in keys_not_found_in_redis],
                    enable_redis=False,
                    embedding_timeout=embedding_timeout,
                )
                for key, embedding in zip(keys_not_found_in_redis, embeddings):
                    key_to_embedding[key] = embedding
                    await pipe.set(
                        key,
                        np.array(embedding, dtype=np.float32).tobytes(),
                        ex=redis_ttl,
                    )
                await pipe.execute()
        return [key_to_embedding[key] for key in all_key_list]


if __name__ == "__main__":
    import asyncio
    import time

    test_batch = [
        "磕个头",
        "假设我让你向右转整整三百六十度然后你觉得这个不用转跟没转一样的话你就不转了如果你觉得这个事情是需要转弯你再转好不好",
        "明天不上班",
        "nod your head",
        "明天不上班",
        "nod your head",
    ]

    async def main():
        elapsed_info = {}
        async with aiohttp.ClientSession() as session:
            for test_enable_redis in [True, False]:
                exp_name = (
                    f"test_enable_redis: {test_enable_redis} for async_get_embeddings"
                )
                logger.info(exp_name)
                start_at = time.time()
                embeddings = await async_get_embeddings(
                    http_session=session,
                    embedding_api_key=agent_setting.embedding_api_key,
                    embedding_model_base_url=agent_setting.embedding_model_base_url,
                    embedding_model=agent_setting.embedding_model,
                    embedding_dim=agent_setting.embedding_dim,
                    batch=test_batch,
                    enable_redis=test_enable_redis,
                    redis_client=None,
                )
                elapsed_info[exp_name] = time.time() - start_at
                num_embeddings = len(embeddings)
                embedding_dim = len(embeddings[0])
                logger.info(
                    f"num_embeddings: {num_embeddings}, embedding_dim: {embedding_dim}"
                )

            for test_enable_redis in [True, False]:
                exp_name = f"test_enable_redis: {test_enable_redis} for async_get_one_embedding"
                logger.info(exp_name)
                start_at = time.time()
                for text in test_batch:
                    embedding = await async_get_one_embedding(
                        http_session=session,
                        embedding_api_key=agent_setting.embedding_api_key,
                        embedding_model_base_url=agent_setting.embedding_model_base_url,
                        embedding_model=agent_setting.embedding_model,
                        embedding_dim=agent_setting.embedding_dim,
                        text=text,
                        enable_redis=test_enable_redis,
                        redis_client=None,
                    )
                    embedding_dim = len(embedding)
                    logger.info(
                        f"test_enable_redis: {test_enable_redis}, text: {text}, embedding_dim: {embedding_dim}"
                    )
                elapsed_info[exp_name] = time.time() - start_at

        for exp_name, elapsed_time in elapsed_info.items():
            logger.info(f"{exp_name}: {elapsed_time} seconds")

    asyncio.run(main())
