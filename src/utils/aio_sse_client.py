import asyncio
from typing import List, Optional, AsyncGenerator, Dict, Any

import aiohttp
from loguru import logger


# Good parts of the below class is adopted from:
#   https://github.com/btubbs/sseclient/blob/db38dc6/sseclient.py
class Event:
    """The object created as the result of received events"""

    data: str
    event: str
    id: Optional[str]
    retry: Optional[bool]

    def __init__(
        self,
        data: str = "",
        event: str = "message",
        id: Optional[str] = None,
        retry: Optional[bool] = None,
    ):
        self.data = data
        self.event = event
        self.id = id
        self.retry = retry

    def dump(self) -> str:
        """Serialize the event object to a string"""
        lines = []
        if self.id:
            lines.append(f"id: {self.id}")

        # Only include an event line if it's not the default already.
        if self.event != "message":
            lines.append(f"event: {self.event}")

        if self.retry:
            lines.append(f"retry: {self.retry}")

        lines.extend(f"data: {d}" for d in self.data.split("\n"))
        return "\n".join(lines) + "\n\n"

    def encode(self) -> bytes:
        """Serialize the event object to a bytes object"""
        return self.dump().encode("utf-8")

    @classmethod
    def parse(cls, raw: str) -> "Event":
        """
        Given a possibly-multiline string representing an SSE message, parse it
        and return an Event object.
        """
        msg = cls()
        for line in raw.splitlines():
            parts = line.split(":", 1)
            if len(parts) != 2:
                # Malformed line.  Discard but warn.
                logger.warning("Invalid SSE line: %s", line)
                continue

            name, value = parts
            if value.startswith(" "):
                value = value[1:]

            if name == "data":
                # If we already have some data, then join to it with a newline.
                # Else this is it.
                if msg.data:
                    msg.data = f"{msg.data}\n{value}"
                else:
                    msg.data = value
            elif name == "event":
                msg.event = value
            elif name == "id":
                msg.id = value
            elif name == "retry":
                msg.retry = bool(value)

        return msg

    def __str__(self) -> str:
        return self.data


async def aiosseclient(
    url: str,
    session: aiohttp.ClientSession,
    data: Optional[Dict[str, Any]],
    headers: Optional[Dict[str, str]] = None,
    raise_for_status: bool = False,
    total_timeout: int = 30,
) -> AsyncGenerator[Event, None]:
    """Canonical API of the library"""
    if headers is None:
        headers = {}

    headers["Content-Type"] = "application/json"
    headers["Accept"] = "text/event-stream"  # 添加 SSE 请求头

    try:
        response = await session.get(
            url,
            headers=headers,
            json=data,
            raise_for_status=raise_for_status,
            timeout=aiohttp.ClientTimeout(total=total_timeout),  # 添加超时控制
        )

        logger.debug(f"SSE connection established. Status: {response.status}")

        lines = []
        async for line in response.content:
            line = line.decode("utf8")
            if line == "\n" or line == "\r" or line == "\r\n":
                if not lines:
                    continue
                current_event = Event.parse("".join(lines))
                yield current_event
                lines = []
            elif line.startswith(":"):
                # Lines start with a ':' are comment, ignore them
                continue
            else:
                lines.append(line)

    except asyncio.TimeoutError as e:
        logger.error(f"SSE connection timed out: {e}")
        raise
    except Exception as e:
        logger.error(f"SSE connection error: {e}")
        raise
