"""
Double cache decorator
双缓存机制，包含内存和redis的cache装饰器

此装饰器实现了一个双层缓存机制：
第一层是内存缓存，用于快速访问最近使用过的数据；可配置，默认10分钟。**建议比Redis缓存时间小**，才有意义。
第二层是Redis缓存，用于持久化存储和跨进程共享数据。不可配置，10分钟

Args:
    memory_cache (dict): 内存缓存对象，用于存储短期的键值对。
    redis_client: Redis客户端实例，用于与Redis服务器交互。
    ttl (int): 缓存过期时间（单位：秒），默认为600秒。

Returns:
    function: 装饰后的函数，具备双层缓存功能。

注意：如果是对象实例会根据对象ID缓存， 如果用了成员函数，缓存范围是当前进程内生效
"""

import asyncio
import collections
import functools
import json
import time
import traceback

from cachetools import Cache, TTLCache, keys
from loguru import logger

from src.agent_core.models.model import AgentParameter
from src.utils.cache import redis_pool
from src.session_manager.robot import Robot

_CacheInfo = collections.namedtuple(
    "CacheInfo", ["hits", "misses", "maxsize", "currsize"]
)


def _cache(cache, maxsize, typed):
    def decorator(func):
        key = keys.typedkey if typed else keys.hashkey
        hits = misses = 0

        if isinstance(cache, Cache):

            def getinfo():
                nonlocal hits, misses
                return _CacheInfo(hits, misses, cache.maxsize, cache.currsize)

        elif isinstance(cache, collections.abc.Mapping):

            def getinfo():
                nonlocal hits, misses
                return _CacheInfo(hits, misses, None, len(cache))

        else:

            def getinfo():
                nonlocal hits, misses
                return _CacheInfo(hits, misses, 0, 0)

        def generate_key(*args, **kwargs):
            p_hash = "default"
            try:
                if len(args) == 1 and isinstance(args[0], AgentParameter):
                    p: AgentParameter = args[0]
                    p_hash = f"{p.robot.device_id}_{p.robot.enterprise_id}_{p.robot.language}"
                elif len(args) == 1 and isinstance(args[0], Robot):
                    robot: Robot = args[0]
                    p_hash = f"{robot.device_id}_{robot.enterprise_id}_{robot.language}"
                else:
                    p_hash = key(*args, **kwargs)
            except Exception as _:
                logger.error(f"args {args} kwargs: {kwargs} 不可hash")
            k = f"{func.__module__}.{func.__name__}_{p_hash}"
            return k

        async def wrapper(*args, **kwargs):
            nonlocal hits, misses
            k = generate_key(*args, **kwargs)
            start_at = time.time()
            try:
                result = cache[k]
                hits += 1
                logger.debug(
                    f"double memory Cache hit: {k} -> {result} cost_time: {time.time() - start_at}"
                )
                return result
            except KeyError:
                misses += 1

            redis_client = await redis_pool.client()
            try:
                start_at = time.time()
                try:
                    # 尝试从缓存中获取结果
                    if cached_result := await redis_client.get(k):
                        result = json.loads(cached_result)
                        logger.debug(
                            f"double redis Cache hit: {k} -> {result} cost_time: {time.time() - start_at}"
                        )
                        try:
                            cache[k] = result
                        except ValueError:
                            pass  # value too large
                        return result
                except Exception as _:
                    logger.error(traceback.format_exc())

                start_at = time.time()
                # 调用原始函数并缓存结果
                try:
                    result = await func(*args, **kwargs)
                except asyncio.CancelledError:
                    raise
                except Exception as e:
                    logger.error(
                        f"Failed to call function: {e} {traceback.format_exc()}"
                    )
                    raise e

                try:
                    cache[k] = result
                except ValueError:
                    pass  # value too large

                try:
                    await redis_client.set(
                        k, json.dumps(result, ensure_ascii=False), ex=60 * 10
                    )
                    logger.debug(
                        f"double Cache miss: {k} -> {result} cost_time: {time.time() - start_at}"
                    )
                except Exception as _:
                    logger.error(
                        f"result is {result}, error is {traceback.format_exc()}"
                    )
                return result

            finally:
                # 确保关闭 Redis 客户端连接（如果需要）
                await redis_client.aclose()

        def cache_clear():
            nonlocal hits, misses
            cache.clear()
            hits = misses = 0

        async def cache_delete(*args, **kwargs):
            k = generate_key(*args, **kwargs)
            # 清除内存缓存
            cache.pop(k, None)
            logger.info(f"{k} 删除内存数据")
            # 清除Redis缓存
            redis_client = await redis_pool.client()
            try:
                await redis_client.delete(k)
                logger.info(f"{k} 删除redis数据")
            finally:
                await redis_client.aclose()

        cache_info = getinfo

        wrapper.cache = cache
        wrapper.cache_key = key
        wrapper.cache_lock = None
        wrapper.cache_clear = cache_clear
        wrapper.cache_delete = cache_delete
        wrapper.cache_info = cache_info
        wrapper.cache_parameters = lambda: {"maxsize": maxsize, "typed": typed}
        wrapper = functools.update_wrapper(wrapper, func)
        return wrapper

    return decorator


def ttl_cache(maxsize=128, ttl=600, timer=time.monotonic, typed=False):
    """Decorator to wrap a function with a memoizing callable that saves
    up to `maxsize` results based on a Least Recently Used (LRU)
    algorithm with a per-item time-to-live (TTL) value.
    """
    return _cache(TTLCache(maxsize, ttl, timer), maxsize, typed)
