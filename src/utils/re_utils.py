"""
实现正则相关的工具类
"""

import re
import ast
from typing import Any, List, Optional


class ReUtils:
    @staticmethod
    def extract_list_from_text(text: str) -> Optional[List[Any]]:
        """
        从文本中提取第一个形如 [1, 2, 3] 或 ["a", "b"] 的Python风格列表
        :param text: 包含列表的文本
        :return: 解析出来的列表或None
        """
        match = re.search(r'\[.*?\]', text, re.DOTALL)
        if match:
            list_str = match.group(0)
            try:
                result_list = ast.literal_eval(list_str)
                if isinstance(result_list, list):
                    return result_list
            except Exception as e:
                print("解析列表失败:", e)
        return []
