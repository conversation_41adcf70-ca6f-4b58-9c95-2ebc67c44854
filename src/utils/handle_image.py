import base64
from typing import Dict

import aiohttp
from loguru import logger

from src.settings import agent_setting


async def get_image_content(image_url: str) -> str:
    """Get image content based on environment setting.

    Args:
        image_url: The URL of the image to process

    Returns:
        str: Image content in either URL or base64 format depending on environment
    """
    if agent_setting.env == "oversea_test":
        # 获取图片数据并转换为 base64
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    image_data = await response.read()
            base64_image = base64.b64encode(image_data).decode("utf-8")
            return f"data:image/jpeg;base64,{base64_image}"
        except Exception as e:
            logger.error(f"Error getting image content: {e}")
            return image_url

    return image_url
