from src.action.retrieve import _MaterialRetriever
from src.settings import agent_setting
from src.utils.feishu_alarm import send_feishu_alarm


def _mem0_embedding_adapter():
    """Support `embed` method for mem0 by using MaterialRetriever"""

    retriever = _MaterialRetriever(
        version="draft"
    )

    class ModelConfig:
        embedding_dims = retriever.embedding_dim
        model_name = retriever.embedding_model
        print(f"ModelConfig: {retriever.embedding_dim}, {model_name}")

    class EmbeddingModel:
        config = ModelConfig()

        def embed(self, texts: list[str]) -> list[float]:
            if len(texts) > agent_setting.max_batch_size:
                send_feishu_alarm(
                    f"mem0 embedding 批量大小超过限制，请检查 texts 数量：{len(texts)} texts: {texts}"
                )
            return retriever._embedding_sync(texts)[0].embedding

    return EmbeddingModel()
