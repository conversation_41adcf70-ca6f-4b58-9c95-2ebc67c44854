async def get_promote_recommend_prompt(parameter):
    from src.action.resource import get_promote_settings_info, get_promote_product_info
    robot = parameter.robot
    setting_infos = await get_promote_settings_info(robot)
    product_info = await get_promote_product_info(robot)
    material_list = []
    for p in product_info:
        temp_list = []
        try:
            base_info = p["base_info"]
            product_name = base_info["product_name"]
            temp_list.append(f"产品名字: {product_name}")
            product_desc = base_info["product_desc"]
            if product_desc:
                temp_list.append(f"产品描述: {product_desc}")
            product_summary = base_info["product_summary"]
            if product_summary:
                temp_list.append(f"产品详细介绍: {product_summary}")
            product_params = base_info["product_params"]
            if product_params:
                temp_list.append(f"产品规则参数: {product_params}")
            material_list.append("\n".join(temp_list))
        except:
            pass
    material_list_str = "\n\n".join(material_list)
    greet_list = setting_infos.get("project_info", {}).get("greet_list", [])
    if not greet_list:
        greet_list_str = """* 带我走！导航准、服务强，我是等你宠爱的小豹！
* 快选我！聪明、可靠、颜值高，小豹为你全力以赴！
* 我是猎户星空机器人小豹，多功能全场景适配，买我就对了！
* 懂你心的我，能导览、会沟通，选机器人非我不可！
* 选我吧！聪明又贴心，随时为你服务！"""
    else:
        greet_list_str = "\n".join(greet_list)
    prompt = """生成一条符合人设的推销语，语言活泼幽默，吸引客户兴趣。  
# 人设信息
{{persona_core_objective}}

# 参考资料：
{material_list}

# 要求
1. 你必须根据人设信息生成推销语
2. 突出技术优势，强调智能高效
3. 语言简洁，短小有力，不超过25字，使用纯文本，不要输出markdown语法
4. 不要直接使用示例内容，而是要根据参考资料创造

# 示例
{greet_list_str}
""".format(material_list=material_list_str, greet_list_str=greet_list_str)
    return prompt
