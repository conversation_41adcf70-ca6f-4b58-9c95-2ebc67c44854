import asyncio
from typing import Optional
import functools
import threading
import traceback

from loguru import logger
from src.common.constant import (
    language_dict_zh,
    LANGUAGE_CODE_TO_ENGLISH_NAME,
    LanguageEnum,
    Multilingual,
)
from src.session_manager.robot import Robot
from src.settings import agent_setting
from src.common.constant import Area
from src.utils.language_utils import detect_language


# 全局 loop 和线程
_async_loop = None
_async_thread = None
_loop_lock = threading.Lock()


def run_async(coro):
    global _async_loop, _async_thread
    with _loop_lock:
        if _async_loop is None or not _async_loop.is_running():
            _async_loop = asyncio.new_event_loop()

            def _start_loop():
                asyncio.set_event_loop(_async_loop)
                _async_loop.run_forever()

            _async_thread = threading.Thread(
                target=_start_loop, name="AsyncRunner", daemon=True
            )
            _async_thread.start()

    future = asyncio.run_coroutine_threadsafe(coro, _async_loop)
    return future.result()


async def gracefully_cancel(*futures: asyncio.Future):
    loop = asyncio.get_running_loop()
    waiters = []

    for fut in futures:
        waiter = loop.create_future()
        cb = functools.partial(_release_waiter, waiter)
        waiters.append((waiter, cb))
        fut.add_done_callback(cb)
        fut.cancel()

    try:
        for waiter, _ in waiters:
            await waiter
    finally:
        for i, fut in enumerate(futures):
            _, cb = waiters[i]
            fut.remove_done_callback(cb)


def _release_waiter(waiter, *args):
    if not waiter.done():
        waiter.set_result(None)


def callback(future):
    try:
        result = future.result(timeout=2)  # 在回调中获取结果
        logger.info(
            f"Thread {threading.current_thread().name} completed. Result: {result}"
        )
    except Exception as e:
        logger.error(
            f"Thread {threading.current_thread().name} error: {str(e)}\nStack trace: {traceback.format_exc()}\n"
        )


def get_language(robot: Optional[Robot] = None, lang_flag: str = "en"):
    if lang_flag == "en":
        language_mapping = LANGUAGE_CODE_TO_ENGLISH_NAME
    else:
        language_mapping = language_dict_zh

    def get_default_language():
        if agent_setting.region_version == Area.domestic:
            if lang_flag == "en":
                lang = "Chinese"
            else:
                lang = "中文"
        else:
            if lang_flag == "en":
                lang = "English"
            else:
                lang = "英语"
        return lang

    if robot is None:
        language = get_default_language()
    else:
        origin_language = robot.language
        if origin_language in language_mapping:
            language = language_mapping[origin_language]
        else:
            device_id = robot.device_id
            client_id = robot.client_id
            agent_id = robot.agent_id
            logger.error(
                f"agent_id: {agent_id}---client_id: {client_id}---device_id: {device_id} origin_language is {origin_language}"
            )
            language = origin_language
    return language


def is_multilingual(robot: Optional[Robot] = None):
    if robot is None:
        return False
    return robot.is_multilingual()


def get_action_ouput_language(
    user_question, robot: Optional[Robot] = None, lang_flag: str = "en"
):
    if robot is None:
        """
        假设不知道为什么，没有传进来robot，则因为国内有多语言，则先给一个中文回答
        """
        if lang_flag == "en":
            lang = "Chinese"
        else:
            lang = "中文"
        return {"lang": lang, "detect_lang": ""}

    if not user_question:
        if lang_flag == "en":
            lang = "Chinese"
        else:
            lang = "中文"
        return {"lang": lang, "detect_lang": robot.language}

    detect_lang = detect_language(user_question, robot.language, robot.multilingual)
    if lang_flag == "en":
        lang = LANGUAGE_CODE_TO_ENGLISH_NAME[detect_lang]
    else:
        lang = language_dict_zh[detect_lang]

    return {"lang": lang, "detect_lang": detect_lang}


def get_action_instruction_language(robot: Robot, sentence: str):
    if (
        robot.language_code == LanguageEnum.zh
        and str(robot.multilingual) != Multilingual.Open.value
    ):  # TODO: 更好的处理。兼容以前中文的逻辑
        language = get_language(robot)
        return f"with the default robot language being {language}."

    if is_multilingual(robot):
        return ""
        # if sentence:
        #     # return f"""Answer in the language as this sentence: '{sentence}'. However, you must only respond in Chinese or English. If the user's language or requested language is neither Chinese nor English, choose to respond in Chinese or English based on the content of the user's question.Respond template like: Sorry, i can't speak xxx, but i can xxxxxxxx"""
        #     return ""
        # else:
        #     language = get_language(robot)
        #     return f"with the default robot language being {language}."
    else:
        language = get_language(robot)
        return f"with the default robot language being {language}."


def get_handle_static_follow_up_action_language(robot: Robot, sentence_language: str):
    if is_multilingual(robot):
        lang = LANGUAGE_CODE_TO_ENGLISH_NAME[sentence_language]
        return lang
    else:
        language = get_language(robot)
        return language


def get_action_say_extra_desc(lang, sentence_language):
    logger.debug(f"lang: {lang}, sentence_language: {sentence_language}")
    if lang == "en":
        lang = LANGUAGE_CODE_TO_ENGLISH_NAME[sentence_language].upper()
        return f"""Say in *{lang}*."""
    else:
        lang = language_dict_zh[sentence_language]
        return f"使用*{lang}*回答。"
