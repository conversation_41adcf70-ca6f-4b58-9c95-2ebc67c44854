from loguru import logger


def clean_text(text):
    """
    清洗文本，保留关键信息和基本标签
    """

    # 定义需要替换的标签
    replace_tags = {
        "<b>": "\n<b>",
        "</b>": "</b>\n",
        "<d>": " ",
        "</d>": " ",
        "<s>": " ",
        "</s>": " ",
        "<l>": " ",
        "</l>": " ",
        "<u>": " ",
        "</u>": " ",
        "<a>": " ",
        "</a>": " ",
        "<f>": " ",
        "</f>": " ",
        "<h>": "\n<h>",
        "</h>": "</h>\n",
        "<e>": " ",
        "</e>": " ",
    }

    # 替换标签
    cleaned_text = text
    for old, new in replace_tags.items():
        cleaned_text = cleaned_text.replace(old, new)

    # 移除多余的空格
    cleaned_text = " ".join(cleaned_text.split())

    # 移除空行
    cleaned_text = "\n".join(line for line in cleaned_text.split("\n") if line.strip())
    logger.info(
        f"[clean_text] origin text length: {len(text)}, cleaned text length: {len(cleaned_text)}"
    )
    return cleaned_text
