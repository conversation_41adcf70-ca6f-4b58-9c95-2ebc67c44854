from typing import Dict, Any, List, Optional
import json

import aiohttp
from livekit.agents.utils.http_context import http_session
from urllib.parse import urljoin
from loguru import logger
import uuid

from src.agent_core.models.agent_core import (
    ConfirmationAction,
    Context,
    MultilingualInfo,
    RunAgentResponse,
    Record,
    RunAgentRequest,
    Action,
    LLMConfig,
    RunAction,
)
from src.settings import agent_setting


async def call_agent_core_wrapper(
    user_question: str,
    query_id: str,
    robot_base_info: Dict[str, Any],
    robot_real_time_info: Dict[str, Any],
    screen_info: str,
    user_memory: str,
    language: str,
    context_messages: List[Dict[str, Any]],
    robot_personality_info: str,
    actions: List[Action],
    retry_count: int,
    llm_config: LLMConfig,
    session: Optional[aiohttp.ClientSession] = None,
    fewshot: dict = {},
    previous_confirmed_action: Optional[RunAction] = None,
    allow_clarify: bool = True,
    allow_confirm: bool = True,
    confirmation_tools: List[ConfirmationAction] = [],
    multilingual_info: Optional[MultilingualInfo] = None,
):
    try:
        return await call_agent_core(
            user_question=user_question,
            query_id=query_id,
            robot_base_info=robot_base_info,
            robot_real_time_info=robot_real_time_info,
            screen_info=screen_info,
            user_memory=user_memory,
            language=language,
            context_messages=context_messages,
            robot_personality_info=robot_personality_info,
            actions=actions,
            retry_count=retry_count,
            llm_config=llm_config,
            session=session,
            fewshot=fewshot,
            previous_confirmed_action=previous_confirmed_action,
            allow_clarify=allow_clarify,
            allow_confirm=allow_confirm,
            confirmation_tools=confirmation_tools,
            multilingual_info=multilingual_info,
        )
    except Exception as e:
        logger.error(f"call_agent_core error: {e}")
        return RunAgentResponse(
            error=f"{str(e)}-{e.__class__.__name__}",
            success=False,
            run_id=str(uuid.uuid4()),
        )


async def call_agent_core(
    user_question: str,
    query_id: str,
    robot_base_info: Dict[str, Any],
    robot_real_time_info: Dict[str, Any],
    screen_info: str,
    user_memory: str,
    language: str,
    context_messages: List[Dict[str, Any]],
    robot_personality_info: str,
    actions: List[Action],
    retry_count: int,
    llm_config: LLMConfig,
    session: Optional[aiohttp.ClientSession] = None,
    fewshot: dict = {},
    previous_confirmed_action: Optional[RunAction] = None,
    allow_clarify: bool = True,
    allow_confirm: bool = True,
    confirmation_tools: List[ConfirmationAction] = [],
    multilingual_info: Optional[MultilingualInfo] = None,
) -> RunAgentResponse:
    if not session:
        session = http_session()

    history = []
    for message in context_messages:
        if message["role"].lower() == "user":
            history.append(
                Record(
                    role="user",
                    content=message["content"],
                )
            )
        else:
            history.append(
                Record(
                    role="assistant",
                    content=message["content"],
                )
            )
    if user_question:
        history.append(  # add user question
            Record(
                role="user",
                content=user_question,
            )
        )

    ctx = Context(
        user_memory=user_memory,
        page_info=screen_info,
        persona=robot_personality_info,
        static_running_info=json.dumps(robot_base_info, ensure_ascii=False),
        dynamic_running_info=json.dumps(robot_real_time_info, ensure_ascii=False),
        history=history,
        fewshot=fewshot,
    )

    run_agent_request = RunAgentRequest(
        ctx=ctx,
        llm_config=llm_config,
        q=user_question,
        choice_actions=actions,
        lang=language,
        retry_count=retry_count,
        instruction=None,
        allow_clarify=allow_clarify,
        previous_confirmed_action=previous_confirmed_action,
        allow_confirm=allow_confirm,
        confirmation_tools=confirmation_tools,
        multilingual_info=multilingual_info,
    )

    agent_core_url = urljoin(
        agent_setting.agent_core_base_url, agent_setting.agent_core_path
    )

    json_data = run_agent_request.model_dump()
    request_id = str(uuid.uuid4())
    logger.info(
        f"[{query_id}] {request_id} Call agent_core_base_url：{agent_core_url} request: {json_data}"
    )
    async with session.post(
        agent_core_url,
        json=json_data,
        headers={
            "Content-Type": "application/json",
            "X-Request-ID": request_id,
            "X-Trace-ID": query_id,
        },
        timeout=agent_setting.agent_core_timeout,
    ) as response:
        response.raise_for_status()
        ret = await response.text()
        logger.info(
            f"[{query_id}] {request_id} {user_question} Get Agent Core response: {ret}"
        )
        if response.status != 200:
            logger.error(
                f"agent_core_base_url调用异常，地址：{agent_core_url}, status: {response.status}"
            )
            raise RuntimeError(
                f"agent_core_base_url调用异常，地址：{agent_core_url}, status: {response.status}"
            )
        return RunAgentResponse.model_validate_json(ret)


if __name__ == "__main__":
    import asyncio

    async def main():
        async with aiohttp.ClientSession() as session:
            ret = await call_agent_core(
                user_question="你好",
                query_id="1234567890",
                robot_base_info={},
                robot_real_time_info={},
                screen_info="",
                user_memory="",
                language="zh",
                context_messages=[],
                robot_personality_info="",
                actions=[],
                retry_count=0,
                session=session,
                fewshot={},
                llm_config=LLMConfig(),
            )
            print(ret)

    asyncio.run(main())
