import time

from loguru import logger

from src.agent_core.base_agent import BaseAgent
from src.agent_core.models.model import (
    InterventionAgentParameter,
    AgentResult,
)
from src.common.model import Debug, RunStep
from src.intervene.intervene_action import ActionIntervenor
from src.settings import agent_setting
from src.intervene.intervene_action import WebappResource


class InterventionAgent(BaseAgent):
    @classmethod
    async def a_invoke(
        cls,
        parameter: InterventionAgentParameter,
        _logger=logger,
    ) -> AgentResult:
        start_fetch_intervention_action_time = time.time()

        await parameter.run_step_queue.put(
            RunStep(
                step_name="thinking",
            )
        )

        actions = parameter.candidate_actions
        intervention_action = actions[0]

        logger.info(
            f"{parameter.query}开始返回干预action {intervention_action.full_name}"
        )
        debug_info = {}

        final_action_parameters = {}
        # 修改当前robot参数
        if intervention_action.parameters:
            # 请求接口获取param信息
            resource_type = parameter.resource_type
            resource_id = parameter.resource_id
            _logger.info(f"resource_type: {resource_type}, resource_id: {resource_id}")
            if resource_type == "webapp":
                try:
                    start_get_webapp_resource_time = time.time()
                    webapp_resource: "WebappResource" = (
                        await ActionIntervenor().get_webapp_resource(
                            resource_id=resource_id,
                            enterprise_id=parameter.robot.enterprise_id,
                        )
                    )
                    if not webapp_resource:
                        raise ValueError("Failed to get webapp resource")

                    final_action_parameters["url"] = webapp_resource.url
                    debug_info["webapp_resource"] = webapp_resource.model_dump()
                    debug_info["get_webapp_resource_time"] = (
                        time.time() - start_get_webapp_resource_time
                    )
                except Exception as e:
                    logger.error(f"Failed to get webapp resource: {e}")
                    debug_info["error"] = str(e)

        action_dict = {
            "ACTION": intervention_action.full_name,
            intervention_action.full_name: final_action_parameters,
            "PARAMETERS": final_action_parameters,
        }

        from src.utils.llm import LLMConfig

        start_action_post_process_time = time.time()
        action = await cls.action_post_process(
            action_dict,
            parameter,
            parameter.query,
        )
        action_post_process_cost_time = time.time() - start_action_post_process_time
        debug = Debug(
            retry=0,
            agent_messages=[],
            llm_config=LLMConfig(
                model_config={},
                base_url="",
                llm_model_name="",
                temperature=0.0,
                max_tokens=0,
                timeout=0,
            ),
            few_shot=[],
            summary="",
            summary_messages=[],
            agent_token_cost={},
            error_msg="",
            summary_token_cost={},
            select_few_shot_cost_time=0,
            summary_cost_time=0,
            agent_call_cost_time=0,
            embedded_cost_time=0,
            retrieval_cost_time=0,
            user_profile_cost_time=0,
            action_flag="intervention",
            agent_mode=parameter.robot.agent_mode,
            summary_mode=agent_setting.summary_mode.value,
            action_post_process_cost_time=action_post_process_cost_time,
            action_intervention_info=debug_info,
            fetch_intervention_action_cost_time=time.time()
            - start_fetch_intervention_action_time,
        )

        start_action_to_plan_time = time.time()
        plan = cls.action_to_plan(action)
        convert_action_to_plan_cost_time = time.time() - start_action_to_plan_time

        debug.convert_action_to_plan_cost_time = convert_action_to_plan_cost_time
        debug.end_timestamp = time.time()
        debug.final_plan = plan.content

        if plan:
            agent_result = AgentResult(
                return_type="plan",
                plan=plan,
                opening_remarks="",
                debug=debug,
                pre_execute_actions=cls.extra_pre_execute_actions(
                    plan.original_xml_root
                ),
                action=action,
            )
        else:
            agent_result = AgentResult(
                return_type="failed",
                debug=debug,
            )
        _logger.info(
            f"[InterventionAgent] Return Type:{agent_result.return_type}\nFinal Plan:{agent_result.plan.content}"
        )

        return agent_result
