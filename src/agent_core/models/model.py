import asyncio
from typing import Any, Literal, Optional, Union, Dict

from pydantic import BaseModel

from src.action.model import Action
from src.agent_core.models.agent_core import RunAction
from src.common.constant import SynthesizeType
from src.common.model import Debug
from src.session_manager.robot import Robot


class Plan(BaseModel):
    id: str
    content: str
    created_at: float
    original_xml_root: Any


class AgentParameter(BaseModel):
    memory: Any
    query: str
    rag_manager: Any
    query_id: str = ""
    run_step_queue: "asyncio.Queue"
    robot: Robot = Robot()
    max_history_turns: int = 6
    candidate_actions: list[
        Union[Action, Dict]
    ] = []  # TODO(<EMAIL>): load_support_action 不再依赖 agent parameter
    use_candidate_actions_directly: bool = False
    use_user_mem: bool = True
    face_id: str = ""
    synthesize_type: SynthesizeType = SynthesizeType.USER_QUERY
    extract_slots_only: bool = False

    class Config:
        arbitrary_types_allowed = True


class PreExecuteAction(BaseModel):
    action_id: str
    action_name: str
    execute_timeout_limit: float
    parameters: dict[str, Any]


class AgentResult(BaseModel):
    return_type: Literal["plan", "ask", "failed"]

    # for return_type == "plan"
    plan: Optional[Plan] = None
    # for return_type == "failed" or "plan"
    opening_remarks: Optional[str] = ""
    pre_execute_actions: list[PreExecuteAction] = []

    debug: Debug

    elapsed_time: float = 0
    select_few_shot_cost_time: float = 0
    summary_cost_time: float = 0

    confirmed_action: Optional[RunAction] = None
    action: Optional[Dict] = None


class PlanClassifyParameter(BaseModel):
    memory: Any
    query: str


class PlanClassifyResult(BaseModel):
    return_type: Literal["plan", "exit", "run"]

    debug: Debug


class InterventionAgentParameter(BaseModel):
    memory: Any
    query: str
    query_id: str = ""
    run_step_queue: "asyncio.Queue"
    robot: Robot = Robot()
    max_history_turns: int = 6
    candidate_actions: list[Action] = []
    use_candidate_actions_directly: bool = False
    use_user_mem: bool = True
    face_id: str = ""
    synthesize_type: SynthesizeType = SynthesizeType.USER_QUERY
    resource_id: str = ""
    resource_type: str = ""

    class Config:
        arbitrary_types_allowed = True
