from typing import Any, Literal, Optional, Dict, List
import uuid

from pydantic import BaseModel, Field

type_literal = Literal["string", "number", "boolean", "array", "object"]


class Parameter(BaseModel):
    name: str
    description: str
    required: bool
    type: type_literal
    item_type: Optional[type_literal] = None
    enum: Optional[list[str]] = None
    min: Optional[int] = None
    max: Optional[int] = None


class Action(BaseModel):
    name: str
    description: str
    parameters: list[Parameter]


class LLMInfo(BaseModel):
    model: Optional[str] = None
    token_cost: Optional[dict] = None
    elapsed_time: Optional[float] = None
    error: Optional[str] = None
    messages: Optional[list[dict[str, Any]]] = None


class Debug(BaseModel):
    llm_info: LLMInfo
    total_elapsed_time: Optional[float] = None
    retry_count: int = 0
    tools: List[Dict] = []


class LLMConfig(BaseModel):
    base_url: str = ""
    llm_model_name: str = ""
    api_key: str = ""
    temperature: float = 0.0
    max_tokens: int | None = None
    timeout: int = 8
    repetition_penalty: float | None = None
    dual_model_planning: bool = False
    region_version: Literal["domestic", "oversea"] = "domestic"


class Record(BaseModel):
    role: Literal["user", "assistant"] = Field(..., description="角色")
    content: str = Field(..., description="内容")


class Context(BaseModel):
    history: Optional[list[Record]] = Field(None, description="历史对话记录")
    user_memory: Optional[str] = Field(None, description="当前用户私有记忆")
    page_info: Optional[str] = Field(None, description="当前页面信息")
    persona: Optional[str] = Field(None, description="当前人物画像")
    static_running_info: Optional[str] = Field(
        None,
        description="静态执行信息，点位等，ip等",
    )
    dynamic_running_info: Optional[str] = Field(
        None, description="动态执行信息，时间、速度等。"
    )
    fewshot: dict = {}


class RunAction(BaseModel):
    name: str
    parameters: dict[str, Any]


class ConfirmationAction(BaseModel):
    """需要二次确认的Action"""

    name: str = Field(..., description="Action名称")
    no_need_confirm_qwen: str = Field(..., description="qwen什么情况下不需要确认")
    need_confirm_qwen: str = Field(..., description="qwen什么情况下需要确认")
    confirm_text_qwen: str = Field(..., description="qwen请求模型确认的文本")
    no_need_confirm_gpt: str = Field(..., description="gpt什么情况下不需要确认")
    need_confirm_gpt: str = Field(..., description="gpt什么情况下需要确认")
    confirm_text_gpt: str = Field(..., description="gpt请求模型确认的文本")
    action_area: list[Literal["domestic", "oversea"]] = Field(
        ["domestic", "oversea"], description="标识作用地区，国内，海外还是都适用"
    )


class MultilingualInfo(BaseModel):
    language: str = Field(description="机器人语言")
    multilingual: bool = Field(description="是否多语言")
    query_language: Optional[str] = Field(None, description="用户输入的问题的语言")
    extra_multilingual_desc: Optional[str] = Field(
        None, description="多语言描述，用于action的参数描述"
    )


class RunAgentRequest(BaseModel):
    ctx: Context = Field(..., description="上下文")
    llm_config: LLMConfig = Field(..., description="LLM配置")
    instruction: Optional[str] = Field(None, description="指令")
    q: Optional[str] = Field(None, description="用户输入的问题")
    choice_actions: list[Action] = Field(..., description="可选动作")
    run_id: str = Field(
        ..., default_factory=lambda: uuid.uuid4().hex, description="请求的唯一标识"
    )
    lang: str = Field(description="语言", default="zh")
    retry_count: int = Field(description="重试次数", default=3, ge=0, le=10)
    allow_clarify: bool = Field(description="是否允许澄清", default=False)
    allow_confirm: bool = Field(description="是否需要二次确认再执行", default=False)
    previous_confirmed_action: Optional[RunAction] = Field(
        None, description="已经被确认的动作"
    )
    confirmation_tools: Optional[List[ConfirmationAction]] = Field(
        None, description="需要二次确认的Action"
    )
    multilingual_info: Optional[MultilingualInfo] = Field(
        None, description="多语言信息"
    )


class RunAgentResponse(BaseModel):
    run_id: str = Field(..., description="请求的唯一标识")
    run_action: Optional[RunAction] = Field(None, description="调用的动作")
    confirmed_action: Optional[RunAction] = Field(None, description="已经被确认的动作")
    success: bool = Field(True, description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    debug: Optional[Debug] = Field(None, description="调试信息")
    content: Optional[str] = Field(None, description="内容")
