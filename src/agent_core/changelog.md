# Summary Prompt Change Log

## (2024-11-27) 总结的方向强调趋于「用户最新的发言」,对于过去已经发生并且完成的事件，不做总结。

 - 典型的case如下：
 
之前：

```{"content":"用户想了解中国面积最大的省、市、县，并计划去这些地方旅行。","elapsed_time":1.649315595626831,"token_cost":{"completion_tokens":17,"prompt_tokens":474,"total_tokens":491,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}},"messages":[{"role":"system","content":"You are an expert in summarization"},{"role":"user","content":"Summarize the user's latest intent in the chat conversation, keeping the output within 20 words, without any explanations, and use Chinese\nChat Conversation: ['user:你能够告诉我中国面积最大的省是哪里吗？...然后我想去那里转一转，然后。...那你再告诉我这个省里面面积最大的市是哪里？...我也想去那里转一转。...然你再告诉我那个。...最大的线是哪里？然后在我也想去转转。/[恩你能够告诉我中国面积最大的省是哪里吗...然后我想去那里转转...你再告诉我那个就是这个省里面面积最大的市是哪里...我也想去那里转转...那你再告诉我那个...这断线是哪里然后再我也想去转转]', 'assistant:这个问题我懂！中国面积最大的省是新疆，最大的地级市是咸阳市，最大的县是若羌县，都在新疆。希望这些地方能让你有个美好的旅行体验！', 'user:唧唧唧唧唧，木兰当户织。不闻机杼声，惟闻女叹息。女欲何所思，你欲何所忆何。昨日见军帖，贺汗大点兵。...卷，军书十二卷，卷卷有爷名，阿爷无大儿，木兰无爪兄不问。/[欺负鸡鸡木兰当户织不闻机杼声惟闻女爱情体育和思域和索一盒昨日建军节和盘大点兵...简直君书十二卷卷卷有爷名阿爷无大儿木兰无长兄]', 'assistant:听起来你在背诵《木兰辞》呢！这首诗歌讲述了花木兰替父从军的故事，非常动人。', 'user:阿爷无大儿，木兰无长兄。军书十二卷，卷卷有爷名。/[那也不大木兰无长兄均属实案卷卷券有缘民]']\nStart.\n"}]} ```

之后： **不应该再考虑用户想去哪个地方旅游**

```{"content":"用户在背诵《木兰辞》，关注花木兰替父从军的故事。","elapsed_time":0.992426872253418,"token_cost":{"completion_tokens":21,"prompt_tokens":476,"total_tokens":497,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}},"messages":[{"role":"system","content":"You are an expert in summarization"},{"role":"user","content":"Summarize the user's latest intent in the chat conversation, keeping the output within 20 words, without any explanations, and use Chinese\nChat Conversation: ['user:你能够告诉我中国面积最大的省是哪里吗？...然后我想去那里转一转，然后。...那你再告诉我这个省里面面积最大的市是哪里？...我也想去那里转一转。...然你再告诉我那个。...最大的线是哪里？然后在我也想去转转。/[恩你能够告诉我中国面积最大的省是哪里吗...然后我想去那里转转...你再告诉我那个就是这个省里面面积最大的市是哪里...我也想去那里转转...那你再告诉我那个...这断线是哪里然后再我也想去转转]', 'assistant:这个问题我懂！中国面积最大的省是新疆，最大的地级市是咸阳市，最大的县是若羌县，都在新疆。希望这些地方能让你有个美好的旅行体验！', 'user:唧唧唧唧唧，木兰当户织。不闻机杼声，惟闻女叹息。女欲何所思，你欲何所忆何。昨日见军帖，贺汗大点兵。...卷，军书十二卷，卷卷有爷名，阿爷无大儿，木兰无爪兄不问。/[欺负鸡鸡木兰当户织不闻机杼声惟闻女爱情体育和思域和索一盒昨日建军节和盘大点兵...简直君书十二卷卷卷有爷名阿爷无大儿木兰无长兄]', 'assistant:听起来你在背诵《木兰辞》呢！这首诗歌讲述了花木兰替父从军的故事，非常动人。']\nLatest User Input: user:阿爷无大儿，木兰无长兄。军书十二卷，卷卷有爷名。/[那也不大木兰无长兄均属实案卷卷券有缘民]\nStart.\n"}]}```

## (2024-11-27) 保留上文关键信息。

- 典型case

之前：

```{"content":"用户想查询从北京到山东的航班信息。","elapsed_time":1.6022679805755615,"token_cost":{"completion_tokens":12,"prompt_tokens":152,"total_tokens":164,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}},"messages":[{"role":"system","content":"You are an expert in summarization"},{"role":"user","content":"Summarize the user's latest intent in the chat conversation, keeping the output within 20 words, without any explanations, and use Chinese\nChat Conversation: ['user:还有多久个？/[还有多久]', 'assistant:春节是1月29日，今天距离2025年春节还有63天', 'user:还有多久过年？/[还有多久过年]', 'assistant:春节是1月29日，今天距离2025年春节还有63天']\nLatest User Input: 你帮我查一下那一天从北京到山东的航班。/[你帮我查一下那一天从北京到山东的航班]\nStart.\n"}]}```

之后：**保留了关键的「1月29日」信息**

```{"content":"用户想查询1月29日从北京到山东的航班信息。","elapsed_time":0.8594739437103271,"token_cost":{"completion_tokens":16,"prompt_tokens":169,"total_tokens":185,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}},"messages":[{"role":"system","content":"You are an expert in summarization"},{"role":"user","content":"Summarize the user's latest intent in the chat conversation, Pay attention to retain the key information above, such as time, location, etc. keeping the output within 30 words, without any explanations, and use Chinese\nChat Conversation: ['user:还有多久个？/[还有多久]', 'assistant:春节是1月29日，今天距离2025年春节还有63天', 'user:还有多久过年？/[还有多久过年]', 'assistant:春节是1月29日，今天距离2025年春节还有63天']\nLatest User Input: 你帮我查一下那一天从北京到山东的航班。/[你帮我查一下那一天从北京到山东的航班]\nStart.\n"}]}```

## (2024-11-28) 摘要总结的上下文中添加当前app页面信息 & Summaries 使用 Discover 替换

## (2024-12-03) 去掉输出节后中的ASK字段，使用SAY代替

## (2024-12-04) 去掉解释聊天记录有两份的说明

# Single Action Prompt Change Log

## (2024-11-27) 调整「聊天摘要」在推理prompt中的位置，重点使用原始聊天记录的丰富信息

## (2024-11-28) 强调在选 Action 时要考虑其限制条件；强调使用 SAY 时，回答要自圆其说，不能捏造事实；对于超出Action使用条件的输出，予以重新考虑或礼貌拒绝的建议

## (2024-12-04) query 不超过10个字，不做Summary

## (2024-12-10) system prompt 根据 app_id 设定并且模板化，强调 Conversation Progress 的地位；优化 Summary 和 user mem 的展示 ；调整Action的格式，减少token,提高可读性
## (2024-12-16) few shot 添加置信度（即 Qdrant 的Score），加强置信度最高的 example 对结果的影响

## (2024-12-23) 去掉「何种场景要使用 SAY 」的说明

## (2025-02-06) 为利用Cache，Prompt 做结构调整，将「聊天摘要」和「用户记忆」「当前页面信息」等的Prompt 放在一起

## (2025-02-07) Summary 环节开关控制，默认不开启


## (2025-03-13) 增加ASR纠错指令，4o模型可以纠错以前纠错不准确的以下case，qwenmax模型可以解决其1、3、4
```
"top out如何防止模型过年",
"任务调度中一步和同步有什么区别",
"分部时系统中的异步通信和刘诗处理如何协同工作",
"rest net的残差结构有什么劣势",
```