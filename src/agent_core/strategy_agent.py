import csv
import json
import re
from copy import deepcopy
from pathlib import Path
from typing import Dict, List, Union

from src.agent_core.models.model import AgentParameter


class StrategyAgent:
    """
    执行一些策略
    """

    def __init__(self):
        self.data_path = Path(__file__).parent / "key_words.csv"
        self.keyword_item_mapping = self.read_csv()

    def read_csv(self) -> Dict[str, List[Dict[str, Union[str, List[str]]]]]:
        with open(self.data_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)
            data_list = list(reader)
        for item in data_list:
            keywords = item["key_words"].strip()
            if keywords:
                item["key_words"] = [
                    k.strip() for k in keywords.split("|") if k.strip()
                ]
            else:
                item["key_words"] = []

            keywords = item["key_words_en"].strip()
            if keywords:
                item["key_words_en"] = [
                    k.strip() for k in keywords.split("|") if k.strip()
                ]
            else:
                item["key_words_en"] = []

            keywords = item["key_words_de"].strip()
            if keywords:
                item["key_words_de"] = [
                    k.strip() for k in keywords.split("|") if k.strip()
                ]
            else:
                item["key_words_de"] = []

            param_str = item["param"].strip()
            if param_str:
                item["param"] = json.loads(param_str)
            else:
                item["param"] = ""

            realtime_robot_fields_str = item["realtime_robot_fields"].strip()
            if realtime_robot_fields_str:
                item["realtime_robot_fields"] = json.loads(realtime_robot_fields_str)
            else:
                item["realtime_robot_fields"] = ""

            app_ids = item["app_id"].strip()
            if app_ids:
                item["app_id"] = [k.strip() for k in app_ids.split("|") if k.strip()]
            else:
                item["app_id"] = []

        keyword_item_mapping = {}
        for data_item in data_list:
            if data_item["key_words"]:
                item = deepcopy(data_item)
                for key_words_item in item["key_words"]:
                    if key_words_item not in keyword_item_mapping:
                        keyword_item_mapping[key_words_item] = []
                    keyword_item_mapping[key_words_item].append(item)
            if data_item["key_words_en"]:
                item = deepcopy(data_item)
                item["query_len"] = item["query_len_en"]
                for key_words_item in item["key_words_en"]:
                    if key_words_item not in keyword_item_mapping:
                        keyword_item_mapping[key_words_item] = []
                    keyword_item_mapping[key_words_item].append(item)
            if data_item["key_words_de"]:
                item = deepcopy(data_item)
                item["query_len"] = item["query_len_de"]
                for key_words_item in item["key_words_de"]:
                    if key_words_item not in keyword_item_mapping:
                        keyword_item_mapping[key_words_item] = []
                    keyword_item_mapping[key_words_item].append(item)
        return keyword_item_mapping

    def strategy_check(
        self, parameter: AgentParameter, actions: List[dict]
    ) -> Dict[str, Union[str, bool, dict]]:
        action_name = ""
        param = ""
        agent_id = ""
        realtime_robot_fields = {}
        supported_actions_name = {action["client_alias"]: action for action in actions}
        app_id = ""
        query = parameter.query
        query = re.sub(
            r"[\s.!\\,$%*+—！，。？、~@#￥…&（）：；《》“”()»<>]+", "", query
        )
        query = query.lower()
        select_actions = []
        for keyword, word_dict_list in self.keyword_item_mapping.items():
            # 关键字在query中，并且长度小于等于设定的长度
            for word_dict in word_dict_list:
                if keyword and keyword.lower().replace(" ", "") == query:
                    action_name = word_dict["action"]
                    param = word_dict["param"]
                    agent_id = word_dict["agent_id"]
                    app_id = word_dict["app_id"]
                    scene = word_dict["scene"]
                    realtime_robot_fields = word_dict["realtime_robot_fields"]

                    if action_name not in supported_actions_name:
                        continue

                    select_actions.append(
                        {
                            "action": action_name,
                            "param": param,
                            "agent_id": agent_id,
                            "app_id": app_id,
                            "realtime_robot_fields": realtime_robot_fields,
                            "scene": scene,
                            "status": True,
                        }
                    )
        result = {"status": False}
        if select_actions:
            for data in select_actions:
                if data["app_id"]:
                    if parameter.robot.APP_ID in data["app_id"]:
                        result = data
                        return result
                    else:
                        continue
                else:
                    if data["scene"] == "global":
                        result = data
            return result
        else:
            return dict(
                action=action_name,
                param=param,
                agent_id=agent_id,
                app_id=app_id,
                realtime_robot_fields=realtime_robot_fields,
                status=False,
            )


strategy_agent = StrategyAgent()
