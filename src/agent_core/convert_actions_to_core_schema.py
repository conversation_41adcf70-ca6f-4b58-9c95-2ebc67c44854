from loguru import logger

from src.common.constant import (
    CLARIFY_ACTION_NAME,
    MCP_PREFIX,
    REPORT_PREFIX,
    BUILTIN_PREFIX,
    OTHER_PREFIX,
)
from src.agent_core.models.agent_core import Action, Parameter


def convert_action_to_core_schema(action: dict, action_name_maps: dict) -> Action:
    """
    将Action对象转换为JSON schema格式

    Args:
        action:
        language: 语言选择，"zh"表示中文，"en"表示英文，默认为"zh"

    Returns:
        Action: 转换后的AgentCore Action对象
    """

    # 函数名使用小写
    function_name = action["name_for_llm"].lower()
    real_name = action["name"].lower()
    prefix = ""
    if action["source"] == "mcp":
        prefix = MCP_PREFIX
    elif action["source"] == "report":
        prefix = REPORT_PREFIX
    elif action["source"] == "builtin":
        prefix = BUILTIN_PREFIX
    else:
        logger.warning(f"Unknown action source: {action['source']}")
        prefix = OTHER_PREFIX

    full_function_name = f"{prefix}{real_name}"
    # 极端情况，在mcp tool前缀会有report_的情况
    max_iteration = 5
    while function_name in action_name_maps and max_iteration > 0:
        max_iteration -= 1
        function_name = f"{prefix}{function_name}"
    action_name_maps[function_name] = full_function_name

    function_desc = action["purpose"]

    # 处理参数
    parameters = []
    if "parameters" in action:
        for param in action["parameters"]:
            param_name = param["name"]
            # 参数描述暂时不区分中英文，因为参数描述通常只有一种语言
            param_desc = param["desc"]
            param_type = param["type"]
            is_required = param["is_required"]

            # 确定参数类型
            schema_type = "string"  # 默认为字符串类型
            item_type = None
            if param_type in ["int"]:
                schema_type = "number"
            elif param_type == "float":
                schema_type = "number"
            elif param_type in ["bool"]:
                schema_type = "boolean"
            elif param_type in ["Integer array"]:
                schema_type = "array"
                item_type = "number"
            elif param_type in ["String array", "list"]:
                schema_type = "array"
                item_type = "string"
            elif param_type in ["dict"]:
                schema_type = "object"

            parameter = Parameter(
                name=param_name,
                description=param_desc,
                required=is_required,
                type=schema_type,
                item_type=item_type,
            )

            # 处理枚举类型
            if param_type in ["enum", "String array"]:
                parameter.enum = param["enum"] if "enum" in param else []
            elif "enum" in param:
                parameter.enum = param["enum"]

            # 处理最大值和最小值限制
            if "max" in param:
                parameter.max = param["max"]
            if "min" in param:
                parameter.min = param["min"]

            parameters.append(parameter)

    # 构建完整的action
    action = Action(
        name=function_name,
        description=function_desc,
        parameters=parameters,
    )

    return action


def batch_convert_action_to_core_schema(
    actions: list[dict],
) -> tuple[list[Action], dict]:
    """
    将所有Action对象转换为JSON schema列表

    Args:
        language: 语言选择，"zh"表示中文，"en"表示英文，默认为"zh"

    Returns:
        tuple[list[Action], dict]: 转换后的AgentCore Action列表和action_name_maps
    """
    core_actions = []

    # 对actions进行排序，把source=builtin的放在最前面，其它的按照source值排序
    def sort_key(action):
        if action.get("source") == "builtin":
            return (0, action.get("source", ""))
        return (1, action.get("source", ""))

    actions = sorted(actions, key=sort_key)
    action_name_maps = {}

    for action in actions:
        core_action = convert_action_to_core_schema(action, action_name_maps)
        core_actions.append(core_action)
    action_validation(core_actions)
    return core_actions, action_name_maps


def action_validation(core_actions: list[Action]):
    # 判断是否有澄清action
    all_action_names = [core_action.name for core_action in core_actions]
    if CLARIFY_ACTION_NAME.lower() not in all_action_names:
        logger.error(f"tool:{CLARIFY_ACTION_NAME} not found in tools for function call")

    if len(set(all_action_names)) != len(all_action_names):
        logger.error("function tools has duplicate name")
