from typing import List
from loguru import logger
from src.agent_core.models.agent_core import ConfirmationAction


DEFAULT_CONFIRMATION_ACTIONS = [
    ConfirmationAction(
        name="start_guide_tour",
        no_need_confirm_qwen="用户明确提到需要导览、或者参观，或者需要提供总体导览介绍",
        need_confirm_qwen="用户没有明确提到需要导览、或者参观、或者提供总体导览介绍",
        confirm_text_qwen="询问用户是否需要提供导览参观服务。",
        no_need_confirm_gpt="User mentions any one of the following: needing a guided tour, needing to visit,show around, or providing an overall tour introduction",
        need_confirm_gpt="User does not explicitly mention needing a guided tour, visiting, show around, or providing an overall tour introduction, but based on reasoning, the user may need guided tour services",
        confirm_text_gpt="Ask the user if they need guided tour services.",
        action_area=["domestic", "oversea"],
    ),
    ConfirmationAction(
        name="recommend_guide_routes",
        no_need_confirm_qwen="用户明确提到需要导览、或者参观，或者需要推荐参观路线",
        need_confirm_qwen="用户没有明确提到需要导览、或者参观、或者推荐参观路线",
        confirm_text_qwen="询问用户是否需要推荐参观路线服务。",
        no_need_confirm_gpt="User mentions any one of the following: needing a guided tour, needing to visit, show around, or recommending a tour route",
        need_confirm_gpt="User does not explicitly mention needing a guided tour, visiting, show around, or recommending a tour route, but based on reasoning, the user may need recommended tour guide services",
        confirm_text_gpt="Ask the user if they need recommended tour route services.",
        action_area=["domestic", "oversea"],
    ),
    ConfirmationAction(
        name="sing_and_dance",
        no_need_confirm_qwen="通过对话历史，用户query符合如下任一条件：1、用户明确要求机器人唱歌，2、用户明确要求机器人跳舞",
        need_confirm_qwen="通过对话历史，用户query满足如下条件：用户没有明确提到要求机器人唱歌，也没有明确提到要求机器人跳舞",
        confirm_text_qwen="首先描述一下你对用户意图的理解，然后按如下回答用户‘需要我表演唱歌跳舞吗’，注意：唱歌和跳舞对机器人来说是一个功能，所以不要分开询问。",
        no_need_confirm_gpt="Through conversation history, the user query meets any of the following conditions: 1. User directly asks the robot to sing, 2. User directly asks the robot to dance",
        need_confirm_gpt="Through conversation history, user query meets the following conditions: User does not require the robot to sing, nor does he require the robot to dance, but based on reasoning, the user may need singing or dancing services",
        confirm_text_gpt="First, briefly describe to the user your understanding of their intention in one sentence, then ask: 'Do you need me to perform singing and dancing?' Note: For the robot, singing and dancing are one function, so do not ask about them separately.",
        action_area=["domestic", "oversea"],
    ),
    ConfirmationAction(
        name="start_visitor_reception",
        no_need_confirm_qwen="通过对话历史，用户query符合如下任一条件：1、用户要求机器人打开登记接待功能，2、用户是过来面试、应聘、或者参加会议",
        need_confirm_qwen="通过对话历史，不太确定是否需要给用户打开登记接待功能来接待用户",
        confirm_text_qwen="首先用一句话简短的描述一下用户是过来做什么的，然后按如下回答用户‘需要我帮您开启访客接待登记吗’。",
        no_need_confirm_gpt="Through conversation history, the user query meets any of the following conditions: 1. The user explicitly requests the robot to open the visitor reception function; 2. The user only expresses and does not raise other questions: I am here for an interview; I am here to apply for a job; I am here to attend a meeting",
        need_confirm_gpt="Through conversation history, 1. The user may need the robot to open the visitor reception function, but the user does not express it clearly, 2. The user expresses that they are here to interview, apply, or visit a customer, and also raises other questions, need to confirm",
        confirm_text_gpt="First, briefly describe to the user what they are here for in one sentence, then ask: 'Do you need me to help you start the visitor reception registration?'",
        action_area=["domestic", "oversea"],
    ),
    # ConfirmationAction(
    #     name="guide_indoor_navigation",
    #     no_need_confirm_qwen="通过对话历史,用户query符合如下任一条件 1、用户提到要求机器人带他去某个地点，2、或者用户要求机器人给他找个地方",
    #     need_confirm_qwen="通过对话历史,用户query满足如下条件：用户没有提到要求机器人带他去某个地点，也没有要求机器人给他找个地方，用户只是询问某个地点在哪里",
    #     confirm_text_qwen="不需要回答用户问题，直接询问用户是否需要带他去他所询问的地方。",
    #     no_need_confirm_gpt="Through conversation history, the user query meets any of the following conditions: 1. take user or other people to a certain place, 2. Or the user request the robot to find a place for him",
    #     need_confirm_gpt="Through conversation history, the user may need the robot to take him to a certain place, but the user does not express the need for the robot to take him and does not request the robot to find a place for him, need to confirm, for example, the user asks where a certain place is, or the way to the place",
    #     confirm_text_gpt="Do not answer the user's question, directly ask the user if they need to take them to the place they asked about.",
    #     action_area=["domestic", "oversea"],
    # ),
]

# DEFAULT_CONFIRMATION_ACTIONS = [
#     ConfirmationAction(
#         name="start_guide_tour",
#         no_need_confirm_zh="用户明确提到需要导览、或者参观，或者需要提供总体导览介绍",
#         need_confirm_zh="用户没有明确提到需要导览、或者参观、或者提供总体导览介绍",
#         confirm_text_zh="询问用户是否需要提供导览参观服务。",
#         no_need_confirm="User explicitly mentions any one of the following: needing a guided tour, needing to visit, or providing an overall tour introduction",
#         need_confirm="User does not mention needing a guided tour, visiting, or providing an overall tour introduction",
#         confirm_text="Ask the user if they need guided tour services.",
#         action_area="domestic",
#     ),
#     ConfirmationAction(
#         name="recommend_guide_routes",
#         no_need_confirm_zh="用户明确提到需要导览、或者参观，或者需要推荐参观路线",
#         need_confirm_zh="用户没有明确提到需要导览、或者参观、或者推荐参观路线",
#         confirm_text_zh="询问用户是否需要推荐参观路线服务。",
#         no_need_confirm="User explicitly mentions any one of the following: needing a guided tour, needing to visit, or recommending a tour route",
#         need_confirm="User does not mention needing a guided tour, visiting, or recommending a tour route",
#         confirm_text="Ask the user if they need recommended tour route services.",
#         action_area="domestic",
#     ),
#     ConfirmationAction(
#         name="sing_and_dance",
#         no_need_confirm_zh="通过对话历史，用户query符合如下任一条件：1、用户明确要求机器人唱歌，2、用户明确要求机器人跳舞",
#         need_confirm_zh="通过对话历史，用户query满足如下条件：用户没有明确提到要求机器人唱歌，也没有明确提到要求机器人跳舞",
#         confirm_text_zh="如果有必要首先用一句话向用户描述一下你对用户意图的理解，然后按如下回答用户‘需要我表演唱歌跳舞吗’，注意：唱歌和跳舞对机器人来说是一个功能，所以不要分开询问。",
#         no_need_confirm="Through conversation history, the user query meets any of the following conditions: 1. User requires the robot to sing, 2. User requires the robot to dance",
#         need_confirm="Through conversation history, user query meets the following conditions: User does not explicitly mention requiring the robot to sing, nor does he explicitly mention requiring the robot to dance",
#         confirm_text="First, briefly describe to the user your understanding of their intention in one sentence, then ask: 'Do you need me to perform singing and dancing?' Note: For the robot, singing and dancing are one function, so do not ask about them separately.",
#         action_area="domestic",
#     ),
#     ConfirmationAction(
#         name="start_visitor_reception",
#         no_need_confirm_zh="通过对话历史，用户query符合如下任一条件：1、用户明确要求机器人打开登记接待功能，2、用户如下表达：我是过来面试的；我是过来应聘的；我是过来参加会议的。",
#         need_confirm_zh="通过对话历史，1、用户可能需要机器人打开登记接待功能，但是用户没有明确表达，2、用户表达是过来面试、应聘、或者访问客户，同时用户还提出其它疑问，需要确认一下",
#         confirm_text_zh="首先用一句话向用户描述一下是过来做什么的，然后按如下回答用户‘需要我帮您开启访客接待登记吗’。",
#         no_need_confirm="Through conversation history, the user query meets any of the following conditions: 1. The user explicitly requests the robot to open the visitor reception function; 2. The user only expresses and does not raise other questions: I am here for an interview; I am here to apply for a job; I am here to attend a meeting",
#         need_confirm="Through conversation history, 1. The user may need the robot to open the visitor reception function, but the user does not express it clearly, 2. The user expresses that they are here to interview, apply, or visit a customer, and also raises other questions, need to confirm",
#         confirm_text="First, briefly describe to the user what they are here for in one sentence, then ask: 'Do you need me to help you start the visitor reception registration?'",
#         action_area="domestic",
#     ),
#     ConfirmationAction(
#         name="guide_indoor_navigation",
#         no_need_confirm_zh="通过对话历史,用户query符合如下任一条件： 1、用户提到要求机器人带他去某个地点，2、或者用户明确表述让机器人给他找个地方",
#         need_confirm_zh="通过对话历史推断，用户可能需要机器人带他去某个地点，但是用户没有直接表达需要机器人带他去，需要确认一下，例如用户问某个地点在哪里",
#         confirm_text_zh="不需要回答用户问题，直接询问用户是否需要带他去他所询问的地方。",
#         no_need_confirm="Through conversation history, the user query meets any of the following conditions: 1. User mentions requiring the robot to take him to a certain place, 2. Or the user explicitly expresses asking the robot to find a place for him",
#         need_confirm="Through conversation history, the user may need the robot to take him to a certain place, but the user does not directly express the need for the robot to take him, need to confirm, for example, the user asks where a certain place is",
#         confirm_text="Do not answer the user's question, directly ask the user if they need to take them to the place they asked about.",
#         action_area="domestic",
#     ),
# ]


class ConfirmationManager:
    """需要二次确认的Action管理器"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.area_to_actions = {}
        for action in DEFAULT_CONFIRMATION_ACTIONS:
            for area in action.action_area:
                if area not in self.area_to_actions:
                    self.area_to_actions[area] = []
                self.area_to_actions[area].append(action)
        logger.info(f"初始化需要确认的Action: {self.area_to_actions}")

    def get_confirmation_actions_by_area(self, area: str) -> List[ConfirmationAction]:
        """根据地区获取需要确认的Action"""
        return self.area_to_actions.get(area, [])


# 创建全局单例实例
confirmation_manager = ConfirmationManager()
