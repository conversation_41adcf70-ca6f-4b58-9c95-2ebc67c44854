import json
import re
import time
import uuid
import traceback
from copy import deepcopy
from typing import Any, Optional
from xml.etree import ElementTree as ET

import aiohttp
from loguru import logger

from src.action.actions import ActionLib
from src.action.model import Action
from src.agent_core.models.model import AgentParameter, Plan, PreExecuteAction
from src.common.constant import SynthesizeType
from src.common.constant import (
    MCP_PREFIX,
    REPORT_PREFIX,
    BUILTIN_PREFIX,
    OTHER_PREFIX,
    CLARIFY_ACTION_NAME,
)
from src.common.agent_config import (
    Opk_Guide_App_Id,
    OverSea_Opk_Guide_App_Id,
    PROMOTE_AGENT_ID,
)
from src.utils.function_call_tool_utils import FunctionCallToolUtils


DEFAULT_FALLBACK_ACTION = "orion.agent.action.say"


class BaseAgent:
    @classmethod
    def format_few_shot(cls, few_shot: list[dict]) -> str:
        return "\n".join(
            (
                f"Score: {shot['score']}, Input: {shot['Input']}\n"
                + json.dumps(
                    {
                        shot["Output"]["action"].lower(): shot["Output"].get(
                            "params", {}
                        ),
                    },
                    ensure_ascii=False,
                )
            )
            for shot in few_shot
        )

    @classmethod
    def parse_output_to_xml(cls, output: str) -> str:
        pattern = re.compile(r"```xml\n({.*})\n```", re.DOTALL)
        match = pattern.search(output)
        if match:
            return match.group(1)

        pattern = re.compile(r"<.*>", re.DOTALL)
        match = pattern.search(output)
        if match:
            return match.group(0)

        return output

    @classmethod
    def extra_pre_execute_actions(cls, root: ET.Element) -> list[PreExecuteAction]:
        pre_execute_actions = []
        for idx, action in enumerate(root.findall(".//Action")):
            action_definition = ActionLib().get_one_action(
                full_name=action.attrib["name"]
            )

            # Skip if any parameter needs to be generated by robot
            if any(
                param.generate_side == "robot" for param in action_definition.parameters
            ):
                continue

            if action_definition.pre_execute:
                pre_execute_actions.append(
                    PreExecuteAction(
                        action_id=f"act_{idx}",
                        action_name=action_definition.full_name,
                        execute_timeout_limit=action_definition.execute_timeout_limit,
                        parameters=cls.extra_parameter_from_action_node(action),
                    )
                )
        return pre_execute_actions

    @classmethod
    def extra_parameter_from_action_node(cls, action_node: ET.Element) -> dict:
        parameters = {}
        for attr, value in action_node.attrib.items():
            if attr in [
                "name",
                "ID",
                "execute_timeout_limit",
                "execute_side",
                "display_name",
                "original_action_name",
            ]:
                continue
            parameters[attr] = value

        return parameters

    @classmethod
    def plan_checker(cls, output: str) -> tuple[Optional[ET.Element], str]:
        try:
            root = ET.fromstring(cls.parse_output_to_xml(output))
        except Exception as e:
            return None, f"Parse output to xml failed: {e}"

        if root.tag != "root":
            return None, "Root tag not found"

        action_nodes = root.findall(".//Action")
        if not action_nodes:
            return None, "Action node not found"

        error_msg = ""
        for action_node in action_nodes:
            for attr in ["name", "ID"]:
                if attr not in action_node.attrib.keys():
                    error_msg += f"Action node attr **{attr}** not exists.\n"

        if error_msg:
            return None, error_msg

        for action_node in action_nodes:
            if not ActionLib().get_one_action(full_name=action_node.attrib["name"]):
                error_msg += f"Action **{action_node.attrib['name']}** not supported"
        if error_msg:
            return None, error_msg

        for action_node in action_nodes:
            action = ActionLib().get_one_action(full_name=action_node.attrib["name"])
            require_params = [
                param.name for param in action.parameters if param.is_required
            ]
            for attr in cls.extra_parameter_from_action_node(action_node).keys():
                if attr not in require_params:
                    error_msg += f"Action **{action_node.attrib['name']}** attr **{attr}** not exists.\n"

            action_node.attrib["execute_timeout_limit"] = str(
                action.execute_timeout_limit
            )
            action_node.attrib["execute_side"] = action.execute_side

        if error_msg:
            return None, error_msg

        return root, ""

    @classmethod
    def postprocess_for_agent_core_action_name(
        cls,
        action_name: str,
        action_name_maps: dict,
    ) -> str:
        # 逆向转换action name
        action_name = action_name_maps.get(action_name, action_name).lower()
        source = ""
        if action_name.startswith(MCP_PREFIX):
            action_name = action_name[len(MCP_PREFIX) :]
            source = "mcp"
        elif action_name.startswith(REPORT_PREFIX):
            action_name = action_name[len(REPORT_PREFIX) :]
            source = "report"
        elif action_name.startswith(BUILTIN_PREFIX):
            action_name = action_name[len(BUILTIN_PREFIX) :]
            source = "builtin"
        elif action_name.startswith(OTHER_PREFIX):
            action_name = action_name[len(OTHER_PREFIX) :]

        action: "Action" = ActionLib().get_one_action(
            name=action_name,
            source=source,
        )
        return action.full_name

    @classmethod
    async def output_check_and_feedback_for_function_call(
        cls,
        return_message: dict,
        supported_actions: dict,
        agent_parameters: AgentParameter,
        action_name_maps: dict,
        tools: list[dict],
    ) -> tuple[dict, str]:
        try:
            if not return_message or (
                not return_message.get("content")
                and not return_message.get("tool_calls")
            ):
                return {}, "Model output is empty."

            if not return_message.get("tool_calls") and return_message.get("content"):
                content = return_message["content"]
                for tool in tools:
                    if f"{tool['function']['name']}:" in content:
                        return (
                            {},
                            "model output is not in the correct format, please output in format of function call",
                        )

                if content.startswith("Robot:"):
                    content = content[len("Robot:") :].strip()

                action = {
                    "ACTION": f"orion.agent.action.{CLARIFY_ACTION_NAME.lower()}",
                    "PARAMETERS": {
                        "request_clarify_text": return_message["content"],
                    },
                }
                action = cls._convert_clarify_action(action, agent_parameters)
                return action, ""

            tool_calls = return_message["tool_calls"]
            last_tool_call = tool_calls[-1]

            # 逆向转换action name
            tool_name = last_tool_call["function"]["name"]
            action_name = last_tool_call["function"]["name"]
            action_name = action_name_maps.get(action_name, action_name).lower()
            source = ""
            if action_name.startswith(MCP_PREFIX):
                action_name = action_name[len(MCP_PREFIX) :]
                source = "mcp"
            elif action_name.startswith(REPORT_PREFIX):
                action_name = action_name[len(REPORT_PREFIX) :]
                source = "report"
            elif action_name.startswith(BUILTIN_PREFIX):
                action_name = action_name[len(BUILTIN_PREFIX) :]
                source = "builtin"
            elif action_name.startswith(OTHER_PREFIX):
                action_name = action_name[len(OTHER_PREFIX) :]

            action: "Action" = ActionLib().get_one_action(
                name=action_name,
                source=source,
            )

            action_dict = {
                action.full_name: json.loads(last_tool_call["function"]["arguments"])
            }
            logger.debug(f"action_dict: {action_dict}")

            supported_actions_full_name = {
                action["fullname"]: action for action in supported_actions
            }
            logger.debug(
                f"supported_actions_name: {supported_actions_full_name.keys()}"
            )

            if action.full_name not in supported_actions_full_name:
                return (
                    {},
                    f"tool `{tool_name}` is not supported, must choose from tool defined in the tools",
                )
            action_dict["ACTION"] = action.full_name

            action_require_parameter = [p for p in action.parameters if p.is_required]

            action_parameters = action_dict.get(action.full_name, {})
            action_dict["PARAMETERS"] = action_parameters

            if action_parameters:
                if not isinstance(action_parameters, dict):
                    return {}, "<params> must be a dict type"
            else:
                if action_require_parameter:
                    return (
                        {},
                        f"This tool `{tool_name}` has required parameters: {[p.name for p in action_require_parameter]}",
                    )
                else:
                    return action_dict, ""

            parameters_error = ""
            allowed_enum_kv = FunctionCallToolUtils.get_enum_kv_from_tools(
                tools, tool_name
            )
            for p in action.parameters:
                if p.type == "enum":
                    if val := action_parameters.get(p.name):
                        allowed_enum_values = allowed_enum_kv.get(p.name, [])
                        if val not in allowed_enum_values:
                            parameters_error = f"Parameter {p.name} 's value is not in enum {allowed_enum_values}, please check"
                            break

                value_is_null = action_parameters.get(p.name) in [
                    None,
                    "",
                    [],
                    {},
                    (),
                    "null",
                ]

                if not p.is_required and value_is_null:
                    continue

                if p.is_required and value_is_null:
                    parameters_error = f"Missing required parameter: {p.name}"
                    break

                pp_value = action_parameters[p.name]
                if p.type == "Integer array":
                    if error := cls._validate_array(pp_value, p, int):
                        parameters_error = error
                elif p.type == "String array":
                    if error := cls._validate_array(pp_value, p, str):
                        parameters_error = error
                        break
                    if enum_value := allowed_enum_kv.get(p.name, []):
                        for v in pp_value:
                            if v not in enum_value:
                                parameters_error = f"Parameter {p.name}'s value `{v}` is not in enum choices, you can find the best similar options from the following list: `{enum_value}`, please be aware of possible errors caused by speech transcription"
                    else:
                        parameters_error = f"Parameter `{p.name}` has no enumerated values to choose from, You can politely explain the reasons why it cannot be done."
                    if parameters_error:
                        return (
                            {},
                            parameters_error,
                        )
                elif p.type == "HttpUrl":
                    continue
                # TODO(<EMAIL>): “String array”与“enum”可以合并吗？
                elif p.type == "enum":
                    enum_value = allowed_enum_kv.get(p.name, [])
                    if not enum_value and pp_value:
                        parameters_error = f"Parameter `{p.name}` has no enumerated values to choose from, You can politely explain the reasons why it cannot be done."
                    if enum_value and pp_value not in enum_value:
                        parameters_error = f"Parameter {p.name}'s value `{pp_value}` is not in enum `{enum_value}`."
                elif p.type in ["int", "float"]:
                    val = int(pp_value) if p.type == "int" else float(pp_value)
                    if p.max and val > p.max:
                        parameters_error = f"Parameter {p.name}'s value `{val}` is greater than `{p.max}`."
                    if p.min and val < p.min:
                        parameters_error = f"Parameter {p.name}'s value `{val}` is less than `{p.min}`."

                    action_dict["PARAMETERS"][p.name] = val

                if parameters_error:
                    return (
                        {},
                        parameters_error,
                    )
            return action_dict, parameters_error
        except Exception as e:
            return {}, f"parse tool_calls error: {e} {traceback.format_exc()}"

    @classmethod
    def _validate_array(
        cls, pp_value: Any, p_define: Action.Parameter, _check_func: type
    ) -> str:
        if isinstance(pp_value, str):
            try:
                pp_value = json.loads(pp_value)
            except Exception:
                return f"Parameter {p_define.name}'s value `{pp_value}` is not valid array object"

        if not isinstance(pp_value, list):
            return f"Parameter {p_define.name} 's value is not array"

        if p_define.length and len(pp_value) > p_define.length:
            return f"Parameter `{p_define.name}`'s value [{pp_value}] length is greater than `{p_define.length}`"

        # check integer
        for i in pp_value:
            try:
                _check_func(i)
            except Exception:
                return f"unexpected value type `{type(i)}` in `{p_define.name}` array, should be `{str(_check_func)}`"

        return ""

    @classmethod
    async def _validate_http_url(cls, http_url: str) -> str:
        # 如果是谷歌的网站，则不需要校验
        if "google.com" in http_url:
            return ""

        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=2)
        ) as session:
            start_time = time.time()
            try:
                async with session.get(http_url) as resp:
                    if resp.status != 200:
                        return f"{http_url} is not available, status code is {resp.status}, You can use Google search engine directly according to user intent"
            except Exception as e:
                logger.error("validate http url error: {}", e)
                return ""
            finally:
                logger.info(
                    "validate http url {} cost: {}", http_url, time.time() - start_time
                )
        return ""

    @classmethod
    def action_to_plan(
        cls,
        action: dict,
        version: str = "1.0",
    ) -> Plan:
        root = ET.Element("root", version=version)
        plan = ET.SubElement(root, "Plan", ID=cls.generate_plan_id())
        sequence = ET.SubElement(plan, "Sequence", ID="seq_1", name="root_sequence")

        for idx, action in enumerate([action]):
            parameters = action.get("PARAMETERS", {})
            # convert value to string
            for k, v in parameters.items():
                parameters[k] = str(v)

            ET.SubElement(
                sequence,
                "Action",
                ID=f"act_{idx}",
                name=action["ACTION"],
                original_action_name=action["_ORIGINAL_ACTION_NAME"],
                execute_timeout_limit=action["execute_timeout_limit"],
                execute_side=action["execute_side"],
                display_name=action["display_name"],
                **parameters,
            )

        ET.indent(root)

        return Plan(
            id=root.find("Plan").attrib["ID"],
            content=ET.tostring(root, encoding="unicode"),
            created_at=time.time(),
            original_xml_root=root,
        )

    @classmethod
    def generate_plan_id(cls) -> str:
        return uuid.uuid4().hex

    @classmethod
    def _get_fallback_action_full_name(cls, agent_parameters: AgentParameter) -> str:
        if agent_parameters.robot.fallback_action:
            return agent_parameters.robot.fallback_action
        else:  # TODO: 兼容以前逻辑，后续移除
            if agent_parameters.robot.agent_id == PROMOTE_AGENT_ID:
                if agent_parameters.synthesize_type not in [SynthesizeType.USER_QUERY]:
                    if (
                        agent_parameters.robot.action_version
                        in ["draft", "oversea_draft"]
                        or agent_parameters.robot.action_version >= "v1.0.5"
                    ):
                        return "orion.app.promote.GENERAL_SALES_SERVICE".lower()
                    else:
                        return "orion.app.promote.SALES_PITCH".lower()
            else:
                if agent_parameters.robot.APP_ID in [
                    Opk_Guide_App_Id,
                    OverSea_Opk_Guide_App_Id,
                ]:
                    if not agent_parameters.robot.enable_qa:
                        return "orion.agent.action.SILENT".lower()
        return DEFAULT_FALLBACK_ACTION.lower()

    @classmethod
    def _convert_clarify_action(
        cls, action_dict: dict, agent_parameters: AgentParameter
    ) -> dict:
        to_replace_action_full_name = cls._get_fallback_action_full_name(
            agent_parameters
        )
        logger.info(
            "Fallback action: {}",
            to_replace_action_full_name,
        )

        to_replace_action = ActionLib().get_one_action(
            full_name=to_replace_action_full_name,
            agent_id=agent_parameters.robot.agent_id,
        )

        original_action_name = action_dict["ACTION"]
        action_dict["ACTION"] = to_replace_action.full_name
        if not to_replace_action.parameters:
            action_dict["PARAMETERS"] = {}
        elif (
            to_replace_action.full_name.lower() == "orion.agent.action.SAY".lower()
            and "arm" in agent_parameters.robot.action_version
        ):
            for _, v in action_dict["PARAMETERS"].items():
                break

            action_dict["PARAMETERS"] = {
                "text": v,
                # "arm_gesture": "open-arms",  # 挥手表示搞不明白
            }
        else:
            for _, v in action_dict["PARAMETERS"].items():
                break

            if len(to_replace_action.parameters) != 1:  # rollback
                logger.error(
                    "Fallback action {} has {} parameters, only one parameter is supported",
                    to_replace_action.full_name,
                    len(to_replace_action.parameters),
                )
                action_dict["ACTION"] = original_action_name
            else:
                action_dict["PARAMETERS"] = {
                    p.name: v for p in to_replace_action.parameters
                }

        logger.info(
            f"Fallback action name: {action_dict['ACTION']} and parameters: {action_dict['PARAMETERS']}"
        )
        return action_dict

    @classmethod
    async def action_post_process(
        cls, action_dict: dict, agent_parameters: AgentParameter, summary: str
    ) -> dict:
        if not action_dict.get("PARAMETERS"):
            action_dict["PARAMETERS"] = {}

        action_dict["_ORIGINAL_ACTION_NAME"] = action_dict["ACTION"]
        action_dict["_ORIGINAL_PARAMETERS"] = deepcopy(
            action_dict["PARAMETERS"]
        )  # TODO：模型效果评测时，哪些case需要deepcopy，哪些case不能deepcopy？
        if CLARIFY_ACTION_NAME.lower() == action_dict["ACTION"].lower().split(".")[-1]:
            action_dict = cls._convert_clarify_action(action_dict, agent_parameters)

        action = ActionLib().get_one_action(
            full_name=action_dict["ACTION"], agent_id=agent_parameters.robot.agent_id
        )

        for p_name, p_val in action_dict["PARAMETERS"].items():
            if p_val in ["null", None, "None"]:
                action_dict["PARAMETERS"][p_name] = ""
                logger.warning(
                    "Parameter {} 's value is null, set to empty string", p_name
                )

        if action.client_alias:
            action_dict["ACTION"] = action.client_alias
        else:
            action_dict["ACTION"] = f"{action.namespace}.{action.name}"

        action_dict["display_name"] = action.get_display_name(
            agent_parameters.robot.language
        )
        action_dict["execute_side"] = action.execute_side
        action_dict["execute_timeout_limit"] = str(action.execute_timeout_limit)

        if action.post_processing:
            action_dict["PARAMETERS"] = await action.post_processing(
                action_dict["PARAMETERS"], agent_parameters
            )

        for p in action.parameters:
            if p.need_summary:
                action_dict["PARAMETERS"][p.name] = summary

        _user_trigger_query = ""
        if agent_parameters.synthesize_type == SynthesizeType.EVENT:
            _user_trigger_query = "用户无交互"
        elif agent_parameters.synthesize_type == SynthesizeType.USER_QUERY:
            _user_trigger_query = agent_parameters.query

        action_dict["PARAMETERS"]["_USER_QUERY"] = _user_trigger_query
        action_dict["PARAMETERS"]["_CURRENT_SUMMARY"] = summary
        action_dict["PARAMETERS"]["SYNTHESIZE_TYPE"] = (
            agent_parameters.synthesize_type.value
        )
        return action_dict
