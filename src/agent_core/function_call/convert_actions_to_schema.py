from loguru import logger

from src.common.constant import (
    CLARIFY_ACTION_NAME,
    MCP_PREFIX,
    REPORT_PREFIX,
    BUILTIN_PREFIX,
    OTHER_PREFIX,
    Area,
)
from src.settings import agent_setting


def convert_action_to_schema(action: dict, action_name_maps: dict):
    """
    将Action对象转换为JSON schema格式

    Args:
        action:
        language: 语言选择，"zh"表示中文，"en"表示英文，默认为"zh"

    Returns:
        dict: 转换后的JSON schema
    """

    # 函数名使用小写
    function_name = action["name"].lower()
    prefix = ""
    if action["source"] == "mcp":
        prefix = MCP_PREFIX
    elif action["source"] == "report":
        prefix = REPORT_PREFIX
    elif action["source"] == "builtin":
        prefix = BUILTIN_PREFIX
    else:
        logger.warning(f"Unknown action source: {action['source']}")
        prefix = OTHER_PREFIX

    full_function_name = f"{prefix}{function_name}"
    # 极端情况，在mcp tool前缀会有report_的情况
    max_iteration = 5
    while function_name in action_name_maps and max_iteration > 0:
        max_iteration -= 1
        function_name = f"{prefix}{function_name}"
    action_name_maps[function_name] = full_function_name

    function_desc = action["purpose"]

    # 构建参数对象
    parameters = {
        "type": "object",
        "properties": {},
    }

    # 记录必需参数
    required_params = []

    # 处理参数
    if "parameters" in action:
        for param in action["parameters"]:
            param_name = param["name"]
            # 参数描述暂时不区分中英文，因为参数描述通常只有一种语言
            param_desc = param["desc"]
            param_type = param["type"]
            is_required = param["is_required"]

            # 确定参数类型
            schema_type = "string"  # 默认为字符串类型
            if param_type in ["int"]:
                schema_type = "integer"
            elif param_type == "float":
                schema_type = "number"
            elif param_type in ["bool"]:
                schema_type = "boolean"
            elif param_type in ["Integer array"]:
                schema_type = "array"
                item_type = "integer"
            elif param_type in ["String array", "list"]:
                schema_type = "array"
                item_type = "string"
            elif param_type in ["dict"]:
                schema_type = "object"

            # 创建参数属性
            param_property = {"type": schema_type, "description": param_desc}

            # 处理数组类型
            if schema_type == "array":
                param_property["items"] = {"type": item_type}

            # 处理枚举（枚举是值域，不是一种类型，参考 https://json-schema.org/understanding-json-schema/reference/enum#enumerated-values）
            if param_type in ["enum", "String array"]:
                enum_values = param.get("enum", [])
                if schema_type == "array":
                    # 参考 https://tour.json-schema.org/content/04-Arrays/05-Enumerated-Array-Items
                    param_property["items"]["enum"] = enum_values
                else:
                    param_property["enum"] = enum_values
            elif "enum" in param:
                param_property["enum"] = param["enum"]

            # 处理最大值和最小值限制
            if "max" in param:
                param_property["maximum"] = param["max"]
            if "min" in param:
                param_property["minimum"] = param["min"]

            # 添加到properties
            parameters["properties"][param_name] = param_property

            # 如果是必需参数，添加到required列表
            if is_required:
                required_params.append(param_name)

    # 如果有必需参数，添加到parameters对象中
    if required_params:
        parameters["required"] = required_params

    # 构建完整的schema
    schema = {
        "type": "function",
        "function": {
            "name": function_name,
            "description": function_desc,
            "parameters": parameters,
        },
    }

    return schema


def batch_convert_action_to_schema(actions: list[dict]):
    """
    将所有Action对象转换为JSON schema列表

    Args:
        language: 语言选择，"zh"表示中文，"en"表示英文，默认为"zh"

    Returns:
        list: 转换后的JSON schema列表
    """
    schemas = []

    # 对actions进行排序，把source=builtin的放在最前面，其它的按照source值排序
    def sort_key(action):
        if action.get("source") == "builtin":
            return (0, action.get("source", ""))
        return (1, action.get("source", ""))

    actions = sorted(actions, key=sort_key)
    action_name_maps = {}

    for action in actions:
        schema = convert_action_to_schema(action, action_name_maps)
        schemas.append(schema)
    action_validation(schemas)
    return schemas, action_name_maps


def action_validation(schemas: list[dict]):
    # 判断是否有澄清action
    all_action_names = [schema["function"]["name"] for schema in schemas]
    if (
        agent_setting.region_version == Area.domestic
    ):  # TODO(<EMAIL>): 海外版本暂时不支持澄清action
        if CLARIFY_ACTION_NAME.lower() not in all_action_names:
            logger.error(
                f"tool:{CLARIFY_ACTION_NAME} not found in tools for function call"
            )

    if len(set(all_action_names)) != len(all_action_names):
        logger.error("function tools has duplicate name")
