from loguru import logger

from src.agent_core.function_call.convert_actions_to_schema import (
    batch_convert_action_to_schema,
)
from src.agent_core.function_call.prompts.prompt_v2 import (
    system_prompt as system_prompt_v2,
    user_prompt as user_prompt_v2,
)
from src.agent_core.function_call.prompts.prompt_v1 import (
    system_prompt as system_prompt_v1,
    user_prompt as user_prompt_v1,
)
from src.common.constant import Area
from src.settings import agent_setting


def assemble_function_call_messages_and_tools(
    function_call_params1, function_call_params2
):
    robot_actions = function_call_params1["actions"]
    tools_json_schema, action_name_maps = batch_convert_action_to_schema(robot_actions)
    robot_basic_info = function_call_params1["robot_base_info"]
    robot_personality_info = function_call_params1["robot_personality_info"]

    robot_realtime_info = function_call_params2["robot_real_time_info"]
    screen_info = function_call_params2["screen_info"].strip("\n")
    # user_info = function_call_params2["user_memory"].strip("\n")
    user_info = ""
    prompt_language = function_call_params2["language"]
    chat_conversation = function_call_params2["conversation_progress"].strip()

    system_prompt = system_prompt_v2
    user_prompt = user_prompt_v2
    if agent_setting.region_version == Area.overseas:  # TODO(<EMAIL>): 海外版本暂时不支持澄清action， remove this after v1.3
        system_prompt = system_prompt_v1
        user_prompt = user_prompt_v1
        logger.info("Use v1 prompt for overseas")

    system_message = system_prompt.format(
        robot_basic_info=robot_basic_info,
        robot_personality_info=robot_personality_info,
        language=prompt_language,
    )
    user_message = user_prompt.format(
        robot_realtime_info=robot_realtime_info,
        screen_info=screen_info,
        user_info=user_info,
        chat_conversation=chat_conversation,
    )

    messages = {
        "system": system_message,
        "user": user_message,
        "functions_json_schema": tools_json_schema,
        "action_name_maps": action_name_maps,
    }
    return messages
