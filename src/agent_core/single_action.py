import asyncio
import json
import time
import traceback
from typing import Any, Dict, List, Optional, Union

from loguru import logger

from src.action.single_action_fewshotor import SingleActionFewshotor
from src.agent_core.base_agent import BaseAgent
from src.agent_core.call_agent_core import call_agent_core_wrapper
from src.agent_core.convert_actions_to_core_schema import (
    batch_convert_action_to_core_schema,
)
from src.agent_core.models.agent_core import (
    LLMConfig as AgentCoreLLMConfig,
    MultilingualInfo,
)
from src.agent_core.models.model import (
    AgentParameter,
    AgentResult,
)
from src.agent_core.strategy_agent import strategy_agent
from src.common.agent_config import Opk_Promote_App_Id
from src.common.constant import (
    CLARIFY_ACTION_NAME,
    KNOWLEDGE_QA_ACTION_NAME,
    LANGUAGE_CODE_TO_ENGLISH_NAME,
    PROMOTE_USER_TEXT,
    Area,
    LanguageEnum,
    SynthesizeType,
    KNOWLEDGE_FIRST_ACTIONS,
)
from src.common.model import Debug, RunStep
from src.common.toolkit import LLMToolKit
from src.rag.rag_manager import (
    KnowledgeSearchResult,
)
from src.session_manager.chat_context import ChatMessage
from src.session_manager.memory import Memory
from src.settings import agent_setting
from src.utils.async_utils import (
    get_action_instruction_language,
    get_action_say_extra_desc,
)
from src.utils.date import safe_loads
from src.utils.i18n import _
from src.agent_core.confirmation import confirmation_manager
from src.utils.language_utils import detect_language


class SingleActionAgent(BaseAgent):
    @classmethod
    async def load_user_mem(
        cls, search_query: str, parameter: AgentParameter, _logger=logger
    ) -> tuple[Optional[list[str]], float]:
        try:
            start_mem0_time = time.time()
            mems = await parameter.memory.get_memories(
                search_query,
                user_id=parameter.face_id,
                language=parameter.robot.language,
            )
            cost_time = time.time() - start_mem0_time
            _logger.info(
                f"face_id:{parameter.face_id} Mem0 search time: {cost_time}.\nresults: {mems}"
            )
            return mems, cost_time
        except Exception as e:
            _logger.error(f"Failed to search mem0: {e}")
            return None, 0

    @classmethod
    async def load_context(
        cls, parameter: AgentParameter, _logger=logger, limit: int = 20
    ) -> list[ChatMessage]:
        """异步加载聊天上下文，避免阻塞主流程"""
        try:
            context_messages: List[ChatMessage] = (
                await parameter.memory.get_chat_context(max_chat_history=limit)
            ).messages
        except Exception as e:
            print(traceback.format_exc())
            _logger.error(f"Failed to get chat context: {e}")
            context_messages = []

        # skip say, say_for_clarification, knowledge_qa
        filtered_messages = []
        filtered_action_names = [
            "orion.agent.action.say",
            "orion.agent.action.knowledge_qa",
            f"orion.agent.action.{CLARIFY_ACTION_NAME}",
            f"orion.agent.action.{KNOWLEDGE_QA_ACTION_NAME.lower()}",
            "say",
            "knowledge_qa",
            f"{KNOWLEDGE_QA_ACTION_NAME.lower()}",
            f"{CLARIFY_ACTION_NAME.lower()}",
        ]
        for message in context_messages:
            if message.action:
                if message.action.name.lower() in filtered_action_names:
                    continue
            filtered_messages.append(message)

        return filtered_messages

    @classmethod
    def build_search_query(
        cls, context_messages: list[ChatMessage], query: str = "", limit: int = 2
    ) -> str:
        search_query = []
        for msg in context_messages[::-1]:
            if msg.content:
                search_query.append(msg.content)

            if len(search_query) >= limit:
                break

        search_query.reverse()
        if query:
            search_query.append(query)

        return "|".join(search_query)

    @classmethod
    async def a_invoke(cls, parameter: AgentParameter, _logger=logger) -> AgentResult:
        first_start_time = time.time()

        load_context_start_time = time.time()
        context_messages = await cls.load_context(parameter, _logger, limit=8)
        load_context_elapsed_time = time.time() - load_context_start_time

        actions = parameter.candidate_actions
        start_strategy_check_time = time.time()

        if not parameter.robot.app_config.enable_action_command:
            strategy_res = {"status": False}
        else:
            strategy_res = strategy_agent.strategy_check(parameter, actions)

        start_strategy_check_time = time.time() - start_strategy_check_time

        if strategy_res["status"] is True:
            action, debug = await cls.select_strategy_action(
                parameter, strategy_res, actions, context_messages, _logger
            )
        else:
            language = parameter.robot.language
            # TODO(<EMAIL>): remove this
            # action, debug = await cls.select_action_by_llm(
            #     parameter, actions, context_messages, _logger, language
            # )
            action, debug = await cls.select_action_by_agent_core(
                parameter, actions, context_messages, _logger, language
            )
        start_action_to_plan_time = time.time()
        plan = cls.action_to_plan(action)
        convert_action_to_plan_cost_time = time.time() - start_action_to_plan_time

        debug.load_context_cost_time = load_context_elapsed_time
        debug.strategy_check_cost_time = start_strategy_check_time
        debug.convert_action_to_plan_cost_time = convert_action_to_plan_cost_time
        debug.total_elapsed_time = time.time() - first_start_time
        debug.end_timestamp = time.time()
        debug.final_plan = plan.content

        if plan:
            if action["ACTION"].lower() not in ["orion.agent.action.say"]:
                await parameter.run_step_queue.put(
                    RunStep(
                        step_scope="action_executor",
                        step_name=action["display_name"],
                        step_result={"result": plan},
                    )
                )
            _logger.info(
                f"[SingleActionAgent] Final confirmed action: {debug.confirmed_action}"
            )
            agent_result = AgentResult(
                return_type="plan",
                plan=plan,
                opening_remarks="",
                debug=debug,
                pre_execute_actions=cls.extra_pre_execute_actions(
                    plan.original_xml_root
                ),
                confirmed_action=debug.confirmed_action,
                action=action,
            )
        else:
            agent_result = AgentResult(
                return_type="failed",
                debug=debug,
            )
        _logger.info(
            f"[SingleActionAgent] Return Type:{agent_result.return_type}\nFinal Plan:{agent_result.plan.content}"
        )

        return agent_result

    @classmethod
    async def search_knowledge(
        cls,
        user_query: str,
        parameter: AgentParameter,
        context_messages: list[ChatMessage],
    ) -> Optional[KnowledgeSearchResult]:
        _logger = logger.bind(query_id=parameter.query_id)
        rag_manager = parameter.rag_manager

        if context_messages:
            chat_history = Memory.chat_context_to_llm_messages(context_messages)
        else:
            chat_history = []

        try:
            result: "KnowledgeSearchResult" = await rag_manager.fetch_knowledge_content(
                query=user_query,
                query_id=parameter.query_id,
                chat_history=chat_history,
                enable_query_rewrite=True,
                enterprise_id=parameter.robot.enterprise_id,
                knowledge_score_threshold=agent_setting.knowledge_score_threshold,
                intervention_threshold=agent_setting.intervention_threshold,
                enable_intervention=agent_setting.region_version
                == Area.domestic,  # 仅海外版本支持QA intervention
            )
            return result
        except asyncio.CancelledError:
            raise
        except Exception as e:
            _logger.error(f"Failed to search knowledge: {e}")
            return None

    @classmethod
    def match_knowledge_qa(
        cls,
        query_id,
        high_score_knowledge_documents: List,
        high_score_intervention_documents: List,
        action: Dict,
        confirmed_action: Any,
    ):
        _logger = logger.bind(query_id=query_id)

        if not high_score_knowledge_documents and not high_score_intervention_documents:
            _logger.info("No knowledge or intervention found")
            return False

        if confirmed_action:  # already has confirmed action
            _logger.info(f"Already confirmed action {confirmed_action}")
            return False

        if action["ACTION"].lower() not in KNOWLEDGE_FIRST_ACTIONS:
            _logger.info(f"It's not a knowledge first action {action}")
            return False

        return True

    @classmethod
    def multilingual_info_in_request(cls, agent_parameter: AgentParameter):
        result = MultilingualInfo(
            language=agent_parameter.robot.language,
            multilingual=agent_parameter.robot.multilingual,
        )
        detect_lang = detect_language(
            agent_parameter.query,
            agent_parameter.robot.language,
            agent_parameter.robot.multilingual,
        )
        logger.critical(
            f"multi detect_lang: {detect_lang} {agent_parameter.query} {agent_parameter.robot.language} {agent_parameter.robot.multilingual}"
        )
        extra_desc_chinese = get_action_say_extra_desc(
            lang="en", sentence_language=detect_lang
        )
        result.query_language = LANGUAGE_CODE_TO_ENGLISH_NAME[detect_lang]
        result.extra_multilingual_desc = extra_desc_chinese
        return result

    @classmethod
    async def select_action_by_agent_core(
        cls,
        parameter: AgentParameter,
        actions: list[dict],
        context_messages: list[ChatMessage],
        _logger=logger,
        language: str = LanguageEnum.zh,
        short_circuit_data_for_test: dict | None = None,
    ) -> tuple[dict, Debug]:
        current_session = parameter.memory.get_aos_session(parameter.robot)
        confirmed_action = current_session.confirmed_action

        if short_circuit_data_for_test:  # for test
            confirmed_action = short_circuit_data_for_test.get(
                "previous_confirmed_action"
            )

        # remove knowledge_qa
        core_actions = []

        parallelize_knowledge_qa = False
        if parameter.synthesize_type in [SynthesizeType.USER_QUERY]:
            for action in actions:
                if action["name"].lower() not in [
                    KNOWLEDGE_QA_ACTION_NAME.lower(),
                    "knowledge_qa",
                ]:
                    core_actions.append(action)
                else:
                    parallelize_knowledge_qa = True
        else:
            core_actions = actions

        _logger.info(f"parallelize_knowledge_qa: {parallelize_knowledge_qa}")

        # core actions
        core_actions, action_name_maps = batch_convert_action_to_core_schema(
            core_actions
        )
        logger.debug(f"core_actions: {core_actions}")

        # screen info
        screen_info = ""
        if origin_screen_info := parameter.robot.interface_state.clean_interface_info():
            screen_info = safe_loads(origin_screen_info)
            if not isinstance(screen_info, dict) or (
                isinstance(screen_info, dict) and not screen_info.get("status")
            ):
                screen_info = {
                    "status": "文本展示窗",
                    "content": origin_screen_info,
                }
            else:
                screen_info = origin_screen_info

        if isinstance(screen_info, dict):
            screen_info = json.dumps(screen_info, ensure_ascii=False)

        # conversation progress
        if short_circuit_data_for_test:  # for benchmark
            context_messages = short_circuit_data_for_test.pop("context_messages", [])
            if not context_messages:
                logger.warning(
                    f"Failed to get context_messages from short_circuit_data_for_test: {short_circuit_data_for_test}"
                )

        conversation_progress: List[Dict[str, Any]] = (
            LLMToolKit.build_conversation_progress_for_agent_core(context_messages)
        )

        # language
        language = get_action_instruction_language(parameter.robot, parameter.query)

        # user question
        _logger.debug(f"query: {parameter.query}")
        user_question = parameter.query
        if parameter.synthesize_type in [SynthesizeType.EVENT, SynthesizeType.ACTION]:
            user_question = PROMOTE_USER_TEXT

        # user memory
        user_profile_cost_time = 0
        user_memory = ""
        if parameter.face_id and parameter.use_user_mem:
            # get user mem
            mems, user_profile_cost_time = await cls.load_user_mem(
                parameter.query, parameter
            )
            if mems:
                user_memory = "\n".join(mems)

        # robot personality info
        robot_personality_info = LLMToolKit.build_system_prompt(
            parameter.robot.agent_id,
            parameter.robot.APP_ID,
            persona=parameter.robot.PERSONA,
            language_style=parameter.robot.LANGUAGE_STYLE,
            language=parameter.robot.language,
            objective=parameter.robot.OBJECTIVE,
        )

        if agent_setting.region_version == Area.domestic:
            llm_config = AgentCoreLLMConfig(
                base_url=agent_setting.plan_model_base_url,
                llm_model_name=agent_setting.plan_model,
                api_key=agent_setting.plan_model_api_key,
                temperature=0.0,
                max_tokens=256,
                region_version="domestic",
            )
        else:
            # TODO: 海外配置双模型
            llm_config = AgentCoreLLMConfig(
                base_url=agent_setting.plan_model_base_url,
                llm_model_name=agent_setting.plan_model,
                api_key=agent_setting.plan_model_api_key,
                temperature=0.0,
                max_tokens=256,
                dual_model_planning=True,
                region_version="oversea",
            )

        start_at = time.time()

        fewshot: dict = {}
        if agent_setting.enable_single_action_fewshot:
            fewshot, fewshot_debug_info = await SingleActionFewshotor().fetch_fewshot(
                query_text=user_question,
                language_code=language,
                candidate_actions_with_fullname=[],
            )

        # use short_circuit_data_for_test
        kwargs = {
            "user_question": user_question,
            "query_id": parameter.query_id,
            "robot_base_info": parameter.robot.robot_base_info,
            "robot_real_time_info": parameter.robot.robot_real_time_info,
            "screen_info": screen_info,
            "user_memory": user_memory,
            "language": language,
            "context_messages": conversation_progress,
            "robot_personality_info": robot_personality_info,
            "actions": core_actions,
            "retry_count": agent_setting.agent_retry_times,
            "llm_config": llm_config,
            "fewshot": fewshot,
            "previous_confirmed_action": confirmed_action,
            "allow_clarify": parameter.robot.is_builtin_app
            and not parameter.extract_slots_only
            and parameter.robot.APP_ID != Opk_Promote_App_Id
            and parameter.robot.turn_on_clarify,  # 仅内置app支持clarify, 二开注册app不支持clarify
            "allow_confirm": parameter.robot.turn_on_confirm
            and not parameter.extract_slots_only,
            "confirmation_tools": confirmation_manager.get_confirmation_actions_by_area(
                agent_setting.region_version
            ),
            "multilingual_info": cls.multilingual_info_in_request(parameter),
        }
        if short_circuit_data_for_test:
            for k, v in short_circuit_data_for_test.items():
                if k in kwargs:
                    kwargs[k] = v

        tasks = []
        high_score_knowledge_documents = []
        total_knowledge_documents = []
        total_intervention_documents = []
        high_score_intervention_documents = []
        knowledge_search_result = None
        knowledge_task = None
        knowledge_debug_info = {}

        if parallelize_knowledge_qa:
            # search knowledge
            knowledge_task = asyncio.create_task(
                cls.search_knowledge(user_question, parameter, context_messages)
            )
            tasks.append(knowledge_task)

        # call agent core
        agent_core_task = asyncio.create_task(call_agent_core_wrapper(**kwargs))
        tasks.append(agent_core_task)

        try:
            # 先等待agent_core_ret结果
            agent_core_ret = await agent_core_task
            _logger.info(f"[SingleActionAgent] AgentCore response: {agent_core_ret}")

            if parallelize_knowledge_qa:
                # 检查是否需要等待知识搜索
                need_knowledge = False
                if agent_core_ret and agent_core_ret.success:
                    if agent_core_ret.run_action and agent_core_ret.run_action.name:
                        # 判断action是否命中KNOWLEDGE_FIRST_ACTIONS
                        action_name = cls.postprocess_for_agent_core_action_name(
                            action_name=agent_core_ret.run_action.name,
                            action_name_maps=action_name_maps,
                        )
                        if action_name.lower() in KNOWLEDGE_FIRST_ACTIONS:
                            need_knowledge = True

                    if not agent_core_ret.run_action and agent_core_ret.content:
                        need_knowledge = True

                if need_knowledge:
                    # 需要知识搜索，等待knowledge_task完成
                    _logger.info(
                        "Agent core result needs knowledge, waiting for knowledge search"
                    )
                    knowledge_search_result = await knowledge_task
                    if knowledge_search_result:
                        high_score_knowledge_documents = (
                            knowledge_search_result.high_score_documents
                        )
                        knowledge_debug_info = (
                            knowledge_search_result.debug_info.model_dump()
                        )
                        high_score_intervention_documents = (
                            knowledge_search_result.high_score_intervention_documents
                        )
                        total_knowledge_documents = (
                            knowledge_search_result.total_documents
                        )
                        total_intervention_documents = (
                            knowledge_search_result.total_intervention_documents
                        )
                    _logger.info(
                        f"Find {len(high_score_knowledge_documents)} knowledge documents, {len(high_score_intervention_documents)} intervention documents"
                    )
                else:
                    # 不需要知识搜索，取消知识搜索任务
                    if not knowledge_task.done():
                        knowledge_task.cancel()
                        _logger.info(
                            "Agent core result doesn't need knowledge, cancelled knowledge search task"
                        )
        except Exception as e:
            _logger.error(f"Failed to call agent core: {e} {e.__class__.__name__}")
            agent_core_ret = None

        _logger.info(f"[SingleActionAgent] AgentCore response: {agent_core_ret}")
        call_agent_core_cost_time = time.time() - start_at

        action = {}
        confirmed_action = None
        if not agent_core_ret or not agent_core_ret.success:
            logger.error(
                "[SingleActionAgent] Failed to call AgentCore, use the fallback logic. "
            )
            if agent_core_ret and agent_core_ret.error:
                _logger.error(f"AgentCore error: {agent_core_ret.error}")
            action = {
                "ACTION": f"orion.agent.action.{CLARIFY_ACTION_NAME.lower()}",
                "PARAMETERS": {
                    "request_clarify_text": _(
                        "Sorry, I didn't catch that. Could you please repeat?"
                    ),
                },
            }
        else:
            confirmed_action = agent_core_ret.confirmed_action
            if agent_core_ret.content and not agent_core_ret.run_action:  # 无工具兜底
                content = agent_core_ret.content
                try:
                    if content.startswith(
                        CLARIFY_ACTION_NAME.lower()
                    ):  # clarify为常驻工具，可能存在无tools但开启澄清的情况，先进行简单兜底
                        content = content[len(CLARIFY_ACTION_NAME.lower()) + 1 :]

                        if content.startswith(":"):  # remove :
                            content = content[1:]
                        elif content.startswith("："):
                            content = content[1:]

                        content = content.strip()
                except Exception as e:
                    _logger.error(f"Failed to get content: {e}")

                action = {
                    "ACTION": f"orion.agent.action.{CLARIFY_ACTION_NAME.lower()}",
                    "PARAMETERS": {
                        "request_clarify_text": content,
                    },
                }
            else:
                _logger.debug(
                    f"[SingleActionAgent] original action name: {agent_core_ret.run_action.name}"
                )
                _logger.debug(
                    f"[SingleActionAgent] action_name_maps: {action_name_maps}"
                )

                action_name = cls.postprocess_for_agent_core_action_name(
                    action_name=agent_core_ret.run_action.name,
                    action_name_maps=action_name_maps,
                )
                action = {
                    "ACTION": action_name,
                    "PARAMETERS": agent_core_ret.run_action.parameters,
                }
                logger.debug(f"[SingleActionAgent] final action: {action}")

        if cls.match_knowledge_qa(
            parameter.query_id,
            high_score_knowledge_documents,
            high_score_intervention_documents,
            action,
            confirmed_action,
        ):
            _logger.info(
                f"[SingleActionAgent] exist high score documents, original action: {action}"
            )

            knowledge_prompt = "# Knowledge\n"
            docs = [doc.model_dump() for doc in knowledge_search_result.total_documents]
            qa_docs = [
                doc.model_dump()
                for doc in knowledge_search_result.total_intervention_documents
            ]

            # sort by score
            if qa_docs:
                intervene_queries = sorted(
                    qa_docs, key=lambda x: x.get("score"), reverse=True
                )
                if intervene_queries:
                    knowledge_prompt += "\n# QA Pairs"
                    for doc in intervene_queries:
                        knowledge_prompt += doc.get("content")

            if agent_setting.region_version == Area.domestic:
                max_chars = 3000
            else:
                max_chars = 30000

            knowledge_content = []
            current_doc_length = 0
            if docs:
                # sort by score
                documents = sorted(docs, key=lambda x: x.get("score"), reverse=True)

                for doc in documents:
                    doc_content = doc.get("content")
                    doc_content_length = len(doc_content)
                    if current_doc_length + doc_content_length <= max_chars:
                        knowledge_content.append(doc_content)
                        current_doc_length += doc_content_length
                    else:
                        break

            knowledge_content_str = "\n".join(knowledge_content)
            if knowledge_content_str:
                knowledge_prompt += (
                    f"\n# Retrieved Documents:\n{knowledge_content_str}\n"
                )

            _logger.info(f"[SingleActionAgent] knowledge_prompt: {knowledge_prompt}")

            action = {
                "ACTION": f"orion.agent.action.{KNOWLEDGE_QA_ACTION_NAME.lower()}",
                "PARAMETERS": {
                    "question": knowledge_search_result.debug_info.rewritten_query,
                    "_KNOWLEDGE_PROMPT": knowledge_prompt,
                },
            }

        _logger.debug(f"[SingleActionAgent] action: {action}")
        start_action_post_process_time = time.time()
        action = await cls.action_post_process(
            action,
            parameter,
            parameter.query,
        )
        _logger.debug(f"[SingleActionAgent] after post process action: {action}")
        action_post_process_cost_time = time.time() - start_action_post_process_time

        tools = [item.model_dump() for item in core_actions]
        agent_messages = []
        agent_token_cost = {}
        error = ""
        retry = 0
        confirmed_action = None
        inner_agent_core_cost_time = 0.0
        try:
            if agent_core_ret:
                _logger.debug(
                    f"[SingleActionAgent] agent_core_ret: {agent_core_ret.confirmed_action}"
                )
                tools = agent_core_ret.debug.tools or []
                agent_messages = agent_core_ret.debug.llm_info.messages or []
                agent_token_cost = agent_core_ret.debug.llm_info.token_cost or {}
                error = agent_core_ret.error or ""
                retry = agent_core_ret.debug.retry_count or 0
                confirmed_action = agent_core_ret.confirmed_action
                inner_agent_core_cost_time = agent_core_ret.debug.total_elapsed_time
        except Exception as e:
            _logger.error(f"Failed to get tools: {e}")

        debug = Debug(
            retry=retry,
            agent_messages=agent_messages,
            tools=tools,
            tool_short_name_map=action_name_maps,
            llm_config=llm_config,
            few_shot=[],
            summary_messages=[],
            agent_token_cost=agent_token_cost,
            error_msg=error,
            summary_token_cost={},
            select_few_shot_cost_time=0,
            summary_cost_time=0,
            agent_call_cost_time=0,
            embedded_cost_time=0,
            retrieval_cost_time=0,
            user_profile_cost_time=user_profile_cost_time,
            action_flag="llm",
            agent_mode=parameter.robot.agent_mode,
            summary_mode=agent_setting.summary_mode.value,
            search_query="",
            output_check_cost_time=0,
            action_post_process_cost_time=action_post_process_cost_time,
            call_agent_core_cost_time=call_agent_core_cost_time,
            context_messages=[cm.model_dump() for cm in context_messages],
            run_agent_response=agent_core_ret,
            parallelize_knowledge_qa=parallelize_knowledge_qa,
            knowledge_documents=[
                doc.model_dump() for doc in high_score_knowledge_documents
            ],
            intervention_documents=[
                doc.model_dump() for doc in high_score_intervention_documents
            ],
            total_knowledge_documents=[
                doc.model_dump() for doc in total_knowledge_documents
            ],
            total_intervention_documents=[
                doc.model_dump() for doc in total_intervention_documents
            ],
            knowledge_debug_info=knowledge_debug_info,
            confirmed_action=confirmed_action,
            inner_agent_core_cost_time=inner_agent_core_cost_time,
        )

        return action, debug

    @classmethod
    def set_short_circuit_data_for_test(
        cls, param_var1: dict, param_var2: dict, short_circuit_data_for_test: dict
    ):
        if not short_circuit_data_for_test:
            return
        param_var1.update(short_circuit_data_for_test)
        param_var2.update(short_circuit_data_for_test)

    @classmethod
    async def select_strategy_action(
        cls,
        parameter: AgentParameter,
        strategy_res: Dict[str, Union[str, bool, Dict[str, Any]]],
        actions: list[dict],
        context_messages: list[ChatMessage],
        _logger=logger,
    ) -> tuple[dict, Debug]:
        """
        通过关键字来判断是否需要exit，不通过大模型判断
        """
        start_strategy_action_time = time.time()
        _logger.info(f"{parameter.query}进入快指令过程")
        robot_real_time_info = parameter.robot.robot_real_time_info
        strategy_param = strategy_res["param"]
        strategy_action = strategy_res["action"]
        if not strategy_action.startswith("orion.agent.action."):  # 兼容旧的action
            strategy_action = "orion.agent.action." + strategy_action

        realtime_robot_fields = strategy_res["realtime_robot_fields"]
        final_action_parameters = {}
        # 修改当前robot参数
        if strategy_param:
            for param_name, param_set in strategy_param.items():
                param_set_action = param_set["action"]
                if param_set_action in ["minus", "add"]:
                    if param_name in realtime_robot_fields:
                        robot_info_key = realtime_robot_fields[param_name]
                        if robot_info_key in robot_real_time_info:
                            current_value = robot_real_time_info[robot_info_key]
                        else:
                            current_value = 0
                    else:
                        current_value = 0
                    param_set_num = param_set["num"]
                    if param_set_action == "minus":
                        current_value = current_value - param_set_num
                    else:
                        current_value = current_value + param_set_num
                    if current_value < param_set["range"][0]:
                        current_value = param_set["range"][0]
                    if current_value > param_set["range"][1]:
                        current_value = param_set["range"][1]
                    final_action_parameters[param_name] = current_value

        action = {
            "ACTION": strategy_action,
            strategy_action: final_action_parameters,
            "PARAMETERS": final_action_parameters,
        }
        _logger.info(f"{parameter.query}选择的action为：{action}")
        from src.utils.llm import LLMConfig

        start_action_post_process_time = time.time()
        action = await cls.action_post_process(
            action,
            parameter,
            parameter.query,
        )
        action_post_process_cost_time = time.time() - start_action_post_process_time
        debug = Debug(
            retry=0,
            agent_messages=[],
            llm_config=LLMConfig(
                model_config={},
                base_url="",
                llm_model_name="",
                temperature=0.0,
                max_tokens=0,
                timeout=0,
            ),
            few_shot=[],
            summary="",
            summary_messages=[],
            agent_token_cost={},
            error_msg="",
            summary_token_cost={},
            select_few_shot_cost_time=0,
            summary_cost_time=0,
            agent_call_cost_time=0,
            embedded_cost_time=0,
            retrieval_cost_time=0,
            user_profile_cost_time=0,
            action_flag="strategy",
            agent_mode=parameter.robot.agent_mode,
            summary_mode=agent_setting.summary_mode.value,
            strategy_action_cost_time=time.time() - start_strategy_action_time,
            action_post_process_cost_time=action_post_process_cost_time,
        )

        return action, debug
