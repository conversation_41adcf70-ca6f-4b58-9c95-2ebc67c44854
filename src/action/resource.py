import asyncio
import json
import re
import time
import traceback
from copy import deepcopy
from typing import Any, Dict, List
from urllib.parse import urljoin

import aiohttp
from loguru import logger

from src.action.function_register import action_function_register
from src.common.enums import LightAppType
from src.settings import agent_setting
from src.utils.double_cache import ttl_cache
from src.utils.feishu_alarm import send_feishu_alarm


@action_function_register
@ttl_cache(ttl=60)
async def load_robot_support_map_points(agent_parameters) -> list[str]:
    device_id = agent_parameters.robot.device_id
    enterprise_id = agent_parameters.robot.enterprise_id
    language = agent_parameters.robot.language
    map_id = agent_parameters.robot.map_id
    map_name = agent_parameters.robot.map_name
    logger.info(
        f"load_robot_support_map_points device_id:{device_id}, enterprise_id:{enterprise_id}, language: {language}"
    )
    start = time.time()
    async with aiohttp.ClientSession() as session:
        url = urljoin(
            agent_setting.robot_openapi_host, agent_setting.support_map_points_path
        )
        request_body = {
            "ov_corp_id": enterprise_id,
            "robot_sn": device_id,
            "map_id": map_id,
            "map_name": map_name,
            "lang": language
        }
        async with session.post(
            url=url,
            json=request_body,
            headers={
                "orionstar-api-key": agent_setting.robot_openapi_key,
                "Content-Type": "application/json",
            },
        ) as response:
            if response.status != 200:
                await send_feishu_alarm(
                    f"获取地图点位 api调用异常，地址：{url}, status: {response.status}"
                )
                return []

            data = await response.json()
            logger.info(
                f"[load_robot_support_map_points] device_id:{device_id},enterprise_id:{enterprise_id}. get map data: {data} elapsed {time.time() - start}"
            )
            try:
                points = []
                position_list = data.get("data", {}).get("position_list", [])
                allowed_types = {"0", "3", "7", "8"}  # 只保留这些类型的点位
                for position in position_list:
                    pos_type_id = position.get("pos_type_id", "")
                    name = position.get("pos_name", "")
                    if name and pos_type_id in allowed_types:
                        points.append(name)
                return sorted(points)
            except Exception as e:
                logger.error(f"load_robot_support_map_points error: {e}")
                return []


@action_function_register
@ttl_cache(ttl=60)
async def load_guide_whole_point(agent_parameters) -> list[str]:
    device_id = agent_parameters.robot.device_id
    enterprise_id = agent_parameters.robot.enterprise_id
    start = time.time()
    async with aiohttp.ClientSession() as session:
        request_body = {
            "get_action": "guide_whole_point",
            "ov_corp_id": enterprise_id,
            "robot_sn": device_id,
        }

        url = urljoin(
            agent_setting.robot_openapi_host, agent_setting.guide_whole_point_path
        )
        async with session.post(
            url=url,
            json=request_body,
            headers={
                "orionstar-api-key": agent_setting.robot_openapi_key,
                "Content-Type": "application/json",
            },
        ) as response:
            if response.status != 200:
                logger.error(
                    f"[load_guide_whole_point] device_id:{device_id}, enterprise_id:{enterprise_id}, url:{url}. get map data failed."
                )
                await send_feishu_alarm(
                    f"load_guide_whole_point调用异常，地址：{url}, device_id:{device_id}, enterprise_id:{enterprise_id}, status: {response.status}"
                )
                return []

            data = await response.json()
            logger.info(
                f"[load_guide_whole_point] device_id:{device_id},enterprise_id:{enterprise_id}. get map data: {data} elapsed {time.time() - start}"
            )

            obj_list = data["data"].get("obj_list", [])
            if not obj_list:
                return []

            return obj_list[0]["obj"]["guide_point_list"]


@action_function_register
async def load_clickable_elements_value(agent_parameters) -> list[str]:
    """
    Get clickable elements id
    :param agent_parameters:
    :return:
    """
    clickable_elements = agent_parameters.robot.interface_state.clickable_elements
    if not clickable_elements:
        return []

    clickable_tags = []
    if clickable_elements:
        """<a id="1">酒店</a>
        <a id="2">机票</a>
        <a id="3">火车票</a>
        """
        element_tag_pattern = re.compile(r'<a id="(\d+)">(.*?)</a>')
        # regex match id number
        for element in clickable_elements:
            tag_match = re.search(element_tag_pattern, str(element))
            if tag_match:
                # ['机票', '火车票', '酒店']
                clickable_tags.append(tag_match.group(2))
            else:
                logger.warning(f"Failed to extract id from element: {element}")

        logger.debug(f"[load_clickable_elements_value]clickable_tags:{clickable_tags}")
    return clickable_tags


@action_function_register
async def load_product_list_v2(parameter) -> list[str]:
    product_infos = await get_promote_product_info(parameter.robot)
    product_list = [
        p.get("base_info", {}).get("product_name", "") for p in product_infos
    ]
    return product_list


@action_function_register
async def load_product_functions_v2(parameter) -> list[str]:
    product_infos = await get_promote_product_info(parameter.robot)
    product_functions = set()
    for p in product_infos:
        product_name = p.get("base_info", {}).get("product_name", "")
        # 详情配置信息
        detail_config = p.get("detail_config", {})
        # 功能介绍
        feature_list = detail_config.get("feature_list", [])
        for temp_data in feature_list:
            name = temp_data.get("name", "")
            product_functions.add(f"{product_name}-{name}")

        home_config = p.get("home_config", {})
        feat_list = home_config.get("feat_list", [])
        for temp_data in feat_list:
            jump = temp_data.get("jump", {})
            name = temp_data.get("name", "")
            if jump:
                product_functions.add(f"{product_name}-{name}")

    return list(product_functions)


@action_function_register
async def load_product_parameters(parameter) -> list[str]:
    product_infos = await get_promote_product_info(parameter.robot)
    product_functions = set()
    for p in product_infos:
        product_name = p.get("base_info", {}).get("product_name", "")
        # 产品规格图
        product_functions.add(f"{product_name}-产品参数规格")

    return list(product_functions)


@action_function_register
async def load_case_name_v2(parameter) -> list[str]:
    product_list = []
    product_infos = await get_promote_product_info(parameter.robot)
    for p in product_infos:
        product_name = p.get("base_info", {}).get("product_name", "")
        case_list = p.get("detail_config", {}).get("case_list", [])
        for q in case_list:
            name = q.get("name", "")
            product_list.append(f"{product_name}-案例-{name}")
    return product_list


@action_function_register
@ttl_cache(ttl=300)
async def get_light_app_info(agent_parameters) -> Dict[str, Dict[str, str]]:
    enterprise_id = agent_parameters.robot.enterprise_id
    start = time.time()
    total_num = 0
    page = 1
    page_rows = 1000
    result = {}
    async with aiohttp.ClientSession() as session:
        url = urljoin(agent_setting.robot_openapi_host, agent_setting.light_app_path)
        while True:
            request_body = {
                "ov_corp_id": enterprise_id,
                "page": page,
                "page_rows": page_rows,
            }
            async with session.post(
                url=url,
                json=request_body,
                headers={
                    "orionstar-api-key": agent_setting.robot_openapi_key,
                    "Content-Type": "application/json",
                },
            ) as response:
                if response.status != 200:
                    await send_feishu_alarm(
                        f"获取企业：{enterprise_id} 轻应用信息api调用异常，地址：{url}, status: {response.status}"
                    )
                    return {}

                data = await response.json()
                logger.info(
                    f"[get_light_app_info] enterprise_id:{enterprise_id}. data: {data} elapsed {time.time() - start}"
                )
                corp_total_count = int(data.get("data", {}).get("total_count", "0"))
                webapp_list = data.get("data", {}).get("webapp_list", [])
                for item in webapp_list:
                    try:
                        webapp = item["webapp"]
                        norm_word = webapp.get("norm_word", "")
                        similar_words = webapp.get("similar_words", [])
                        similar_words.append(norm_word)
                        all_words = list(set(similar_words))
                        app_url = webapp["app_url"]
                        app_type = str(webapp["type"])
                        if app_type == LightAppType.APP.value:
                            try:
                                param_dict = json.loads(webapp["param_json"])
                            except json.JSONDecodeError:
                                logger.error(traceback.format_exc())
                                param_dict = {"class_name": ""}
                            class_name = param_dict.get("class_name", "")
                            if class_name:
                                app_url = f"{app_url}/{class_name}"
                        for words in all_words:
                            # 如果后面的问题和前面的问题一样，但配置的地址不一样，直接使用后面
                            result[words.lower()] = {
                                "app_url": app_url,
                                "type": app_type,
                            }
                        total_num += 1
                    except:
                        logger.error(traceback.format_exc())
                if total_num >= corp_total_count:
                    break
                page += 1
    return result


@action_function_register
async def load_light_app_enum(agent_parameters) -> List[str]:
    light_app_dict = await get_light_app_info(agent_parameters)
    return [words for words in light_app_dict.keys()]


@ttl_cache(ttl=300)
async def get_promote_project_origin_product_info(robot) -> Dict[str, Any]:
    """
        推销模式产品信息格式：
        {
            "base_info": {
                    "product_name": "产品名字",
                    "product_desc": "产品描述",
                    "product_summary": "产品详细介绍",
                    "product_params": "产品规则参数"
            },
            "file_data": {//资料库
                    "list": [{
                            "name": "",
                            "file_id": "",
                            "file_url": "",
                            "type": "文件类型", // image/video
                            "tag": "分类", //  "feature"/"case"/"params" , 产品功能/产品案例/产品规格
                            "cover_url": "", // 视频类型，可能会有此值
                    }]
            },
            "peer_info": { //竞品信息
                    "list": [{
                            "name": "", //竞品名称
                            "corp_name": "", //竞品公司
                            "price": "", //价格,单位分
                            "desc": "", //产品描述
                            "bad_tag": "", //劣势标签,逗号分隔
                    }]
            },
            "home_config": { //首页配置
                    "title": "",
                    "desc": "",
                    "contact_btn_text": "留资按钮文案",
                    "detail_btn_text": "详情按钮文案",
                    "bg_img": {
                            "file_url": ""
                            "file_id":""
                    },
                    "product_img": {
                            "file_id":""
                            "file_url": "",
                    },
                    "feat_list": [{
                            "img": {
                                    "file_url": "",
                                    "file_id": "",
                            },
                            "name": "",
                            "jump": {
                                    "type": "video",
                                    "file_id": "",
                                    "file_url": ""
                            }
                    }]
            },
            "detail_config": { //详情配置
                    "product_img": {
                            "file_url": ""
                    },
                    "title": "",
                    "desc": "",
                    "contact_btn_text": "留资按钮文案",
                    "tag_list": [ //最多3个
                            {
                                    text: "",
                                    color: "#ffffff", //16进制，#开头颜色
                            }
                    ],
                    "feature_list": [{ //产品功能
                            "file": {
                                    "file_url": "",
                                    "file_id": "",
                                    "type": "",
                                    "cover_url": "",
                            },
                            "name": "",
                    }],
                    "case_list": [ //案例列表同feature_list
                    ],
                    "params_list": [{
                            "file": {
                                    "file_url": "",
                                    "file_id": "",
                                    "type": "",
                                    "cover_url": "",
                            }
                    }]
            }
    }
    """
    enterprise_id = robot.enterprise_id
    device_id = robot.device_id
    start = time.time()
    total_num = 0
    page = 1
    page_rows = 100
    result = {}
    try:
        async with aiohttp.ClientSession() as session:
            url = urljoin(
                agent_setting.robot_openapi_host, agent_setting.guide_whole_point_path
            )
            while True:
                request_body = {
                    "get_action": "promotion_corp_product",
                    "ov_corp_id": enterprise_id,
                    "robot_sn": device_id,
                    "page": page,
                    "page_rows": page_rows,
                }
                async with session.post(
                    url=url,
                    json=request_body,
                    headers={
                        "orionstar-api-key": agent_setting.robot_openapi_key,
                        "Content-Type": "application/json",
                    },
                ) as response:
                    if response.status != 200:
                        await send_feishu_alarm(
                            f"获取企业：{enterprise_id} 推销模式产品配置信息api调用异常，地址：{url}, status: {response.status}"
                        )
                        return {}

                    data = await response.json()
                    # logger.info(
                    #     f"[get_promote_product_info] enterprise_id:{enterprise_id}. data: {data} elapsed {time.time() - start}"
                    # )
                    corp_total_count = int(data.get("data", {}).get("total_count", "0"))
                    obj_list = data.get("data", {}).get("obj_list", [])
                    for item in obj_list:
                        obj = item.get("obj", {})
                        if obj:
                            config_id = obj.get("config_id", "")
                            config_json_info = obj.get("config_json_info", {})
                            if config_json_info and config_id:
                                result[config_id] = config_json_info
                                total_num += 1
                    if total_num >= corp_total_count:
                        break
                    page += 1
        return result
    except:
        return {}


@ttl_cache(ttl=300)
async def get_promote_settings_info(robot) -> Dict[str, Any]:
    """
    推销模式应用配置信息格式：
    {
        "corp_name":"公司名称",
        "character_info":"人设信息",
        "greet_list":[
                "你好",
                "我是打招呼列表"
        ],
        "enable_chatup":false, // 是否开启主动搭话
        "min_chatup_interval": 5 //默认：5， 2次搭话最小间隔时间，单位：秒
    }
    """
    enterprise_id = robot.enterprise_id
    device_id = robot.device_id
    start = time.time()
    total_num = 0
    page = 1
    page_rows = 10
    if robot.action_version in ["draft", "oversea_draft"] or robot.action_version >= "v1.0.5":
        get_action = "promotion_robot_project"
    else:
        get_action = "promotion_corp_settings"
    try:
        async with aiohttp.ClientSession() as session:
            url = urljoin(
                agent_setting.robot_openapi_host, agent_setting.guide_whole_point_path
            )
            while True:
                request_body = {
                    "get_action": get_action,
                    "ov_corp_id": enterprise_id,
                    "robot_sn": device_id,
                    "page": page,
                    "page_rows": page_rows,
                }
                async with session.post(
                    url=url,
                    json=request_body,
                    headers={
                        "orionstar-api-key": agent_setting.robot_openapi_key,
                        "Content-Type": "application/json",
                    },
                ) as response:
                    if response.status != 200:
                        await send_feishu_alarm(
                            f"获取企业：{enterprise_id} 推销模式应用配置信息api调用异常，地址：{url}, status: {response.status}"
                        )
                        return {}

                    data = await response.json()
                    # logger.info(
                    #     f"[get_promote_settings_info] enterprise_id:{enterprise_id}. data: {data} elapsed {time.time() - start}"
                    # )
                    corp_total_count = int(data.get("data", {}).get("total_count", "0"))
                    obj_list = data.get("data", {}).get("obj_list", [])
                    for item in obj_list:
                        obj = item.get("obj", {})
                        if obj:
                            config_id = obj.get("config_id", "")
                            config_json_info = obj.get("config_json_info", {})
                            if config_json_info and config_id:
                                config_json_info["config_id"] = config_id
                                if "project_info" not in config_json_info:
                                    config_json_info["project_info"] = deepcopy(config_json_info)
                                return config_json_info
                    total_num += len(obj_list)
                    if total_num >= corp_total_count:
                        break
                    page += 1
        return {}
    except:
        return {}


async def get_promote_product_info(robot) -> List[dict]:
    """
    获取推销模式中一个设备下的已发布的有序产品信息列表
    """
    if robot.action_version in ["draft", "oversea_draft"] or robot.action_version >= "v1.0.5":
        tasks = []
        tasks.append(asyncio.create_task(get_promote_project_origin_product_info(robot)))
        tasks.append(asyncio.create_task(get_promote_settings_info(robot)))
        project_origin_product_infos, project_origin_setting_info = await asyncio.gather(*tasks)
        # 当前项目下引用的产品cinfig_id
        reference_project_config_ids = project_origin_setting_info.get("product_list", [])
        result = []
        for ref_config_id in reference_project_config_ids:
            if ref_config_id in project_origin_product_infos:
                result.append(project_origin_product_infos[ref_config_id])
    else:
        data = await get_promote_project_origin_product_info(robot)
        result = []
        for config_id, item in data.items():
            result.append(item)
    return result


async def get_promote_brief_product_info(robot) -> List[dict]:
    """
    获取推销模式下产品的关键信息，包含
    产品名称
    产品描述
    产品规格
    产品特点
    成功案例名称
    产品功能名称
    """
    data = await get_promote_product_info(robot)
    result = []
    for p in data:
        product_name = p.get("base_info", {}).get("product_name", "")
        product_desc = p.get("base_info", {}).get("product_desc", "")
        product_params = p.get("base_info", {}).get("product_params", "")
        product_features = p.get("detail_config", {}).get("tag_list", [])

        # 功能介绍
        text_content_list = []
        for feature_list_item in p.get("detail_config", {}).get("feature_list", []):
            name = feature_list_item.get("name", "")
            file_desc = feature_list_item.get("file", {}).get("file_desc", "")
            text_content_list.append(
                f"产品 {product_name} 功能：{name}，描述： {file_desc}"
            )

        text_content_list = []
        for case_list_item in p.get("detail_config", {}).get("case_list", []):
            name = case_list_item.get("name", "")
            file_desc = case_list_item.get("file", {}).get("file_desc", "")
            text_content_list.append(
                f"产品 {product_name} 成功案例名称：{name}，描述： {file_desc}"
            )
        result.append({
            "产品名称": product_name,
            "产品描述": product_desc,
            "产品规格参数": product_params,
            "产品特点": product_features,
            "产品功能": text_content_list,
            "产品成功案例": text_content_list,
        })
    return result


@action_function_register
async def load_competitor_production_name(parameter) -> list[str]:
    product_infos = await get_promote_product_info(parameter.robot)
    product_list = []
    for p in product_infos:
        for item in p.get("peer_info", {}).get("list", []):
            if item.get("name"):
                product_list.append(item["name"])
    return product_list


@action_function_register
@ttl_cache(ttl=60)
async def load_route_list(parameter) -> list[str]:
    from src.action.skill_center.router import get_route_info

    routes_data = await get_route_info(parameter.robot)
    route_names = [route.get("name") for route in routes_data if route.get("name")]
    return route_names


@action_function_register
async def load_userinfo_collect(parameter) -> list[str]:
    """
    获取用户信息收集的键值列表
    :param parameter: 参数对象
    :return: 用户信息收集键值列表
    """
    from src.session_manager.session import get_setting_user_preferences
    user_info = await get_setting_user_preferences(parameter.robot)
    user_info_fields = [item["name"] for item in user_info]
    return user_info_fields
