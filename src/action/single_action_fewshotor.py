import asyncio
import json
import time
import uuid
from pathlib import Path

import aiohttp
from livekit.agents.utils import http_context
from loguru import logger
from pydantic import BaseModel
from qdrant_client import AsyncQdrantClient
from qdrant_client import models as qdrant_models

from src.common.constant import SUPPORTED_LANGUAGE_CODES
from src.settings import agent_setting
from src.utils.embedding_utils import async_get_embeddings, async_get_one_embedding


class SingleActionFewshotQdrantPayload(BaseModel):
    user_query: str  # 用于embedding
    action_name: str  # prompt中使用action_name而不是action_fullname
    action_namespace: str
    action_fullname: str  # 全小写，用于条件过滤
    language_code: str  # 用于条件过滤，e.g. "zh_CN", "en_US", "ja_JP", "de_DE"
    singleshot_jsonstr: (
        str  # 用于存储数据 {user_query: str, context: dict, preferred_response: dict}
    )


class SingleActionFewshotor:
    def __init__(
        self,
        *,
        embedding_api_key: str = agent_setting.single_action_fewshot_embedding_api_key,
        embedding_model_base_url: str = agent_setting.single_action_fewshot_embedding_model_base_url,
        embedding_model: str = agent_setting.single_action_fewshot_embedding_model,
        embedding_dim: int = agent_setting.single_action_fewshot_embedding_dim,
        embedding_batch_size: int = agent_setting.single_action_fewshot_embedding_batch_size,
        embedding_enable_redis: bool = agent_setting.single_action_fewshot_embedding_enable_redis,
        qdrant_host: str = agent_setting.single_action_fewshot_qdrant_host,
        qdrant_insert_batch_size: int = agent_setting.single_action_fewshot_qdrant_insert_batch_size,
        qdrant_collection_name: str = "",
        http_session: aiohttp.ClientSession | None = None,
    ):
        self.embedding_api_key = embedding_api_key
        self.embedding_model_base_url = embedding_model_base_url
        self.embedding_model = embedding_model
        self.embedding_dim = embedding_dim
        self.embedding_batch_size = embedding_batch_size
        self.embedding_enable_redis = embedding_enable_redis

        self.qdrant_host = qdrant_host
        self.qdrant_insert_batch_size = qdrant_insert_batch_size
        self.vector_grpc_client = AsyncQdrantClient(
            host=self.qdrant_host, prefer_grpc=True
        )
        self.qdrant_collection_name = (
            qdrant_collection_name
            if qdrant_collection_name
            else f"single_action_fewshot_query_embeddings_{self.embedding_model}_{self.embedding_dim}"
        )

        self.http_session = http_session
        self.http_session = self._ensure_http_session()

    def _ensure_http_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self.http_session:
            self.http_session = http_context.http_session()
        return self.http_session

    async def get_one_embedding(self, text: str) -> list[float]:
        return await async_get_one_embedding(
            http_session=self.http_session,  # type: ignore
            embedding_api_key=self.embedding_api_key,
            embedding_model_base_url=self.embedding_model_base_url,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
            text=text,
            enable_redis=self.embedding_enable_redis,
        )

    async def get_embeddings(self, batch: list[str]) -> list[list[float]]:
        return await async_get_embeddings(
            http_session=self.http_session,  # type: ignore
            embedding_api_key=self.embedding_api_key,
            embedding_model_base_url=self.embedding_model_base_url,
            embedding_model=self.embedding_model,
            embedding_dim=self.embedding_dim,
            batch=batch,
            enable_redis=self.embedding_enable_redis,
        )

    async def upsert_single_action_fewshot_list(
        self, saf_list: list[SingleActionFewshotQdrantPayload]
    ) -> list[SingleActionFewshotQdrantPayload]:
        if len(saf_list) == 0:
            return []
        batch_size = min(
            self.embedding_batch_size, self.qdrant_insert_batch_size, len(saf_list)
        )
        failed_saf_list: list[SingleActionFewshotQdrantPayload] = []
        for saf_batch in [
            saf_list[i : i + batch_size] for i in range(0, len(saf_list), batch_size)
        ]:
            try:
                # get embeddings
                embeddings = await self.get_embeddings(
                    [saf.user_query for saf in saf_batch]
                )
            except Exception as e:
                logger.error(f"Failed to get embeddings: {e}")
                failed_saf_list.extend(saf_batch)
                continue
            try:
                # build qdrant points, record-oriented formats
                points = [
                    qdrant_models.PointStruct(
                        id=uuid.uuid4().hex,
                        vector=embedding,
                        payload=saf.model_dump(),
                    )
                    for saf, embedding in zip(saf_batch, embeddings)
                ]
                # insert to qdrant
                await self.vector_grpc_client.upsert(
                    collection_name=self.qdrant_collection_name,
                    points=points,
                )
            except Exception as e:
                logger.error(f"Failed to upsert single action fewshot list: {e}")
                failed_saf_list.extend(saf_batch)
                continue
        return failed_saf_list

    def parse_single_action_fewshot_resource(
        self, resource_file_path: Path | str
    ) -> list[SingleActionFewshotQdrantPayload]:
        with open(resource_file_path, "r") as fin:
            obj = json.load(fin)
        data = obj.get("fewshot", [])
        if not isinstance(data, list) or len(data) == 0:
            return []
        saf_list: list[SingleActionFewshotQdrantPayload] = []
        for d in data:
            singleshot = d.get("singleshot", {})
            if not isinstance(singleshot, dict):
                logger.warning(f"Invalid singleshot: {singleshot}")
                continue
            user_query = singleshot.get("user_query", "")
            if not user_query:
                continue
            action_name = d.get("action_name", "").lower()
            action_namespace = d.get("action_namespace", "").lower()
            action_fullname = d.get("action_fullname", "").lower()
            if (
                not action_name
                or not action_namespace
                or not action_fullname
                or f"{action_namespace}.{action_name}" != action_fullname
            ):
                logger.warning(
                    f"Invalid action name or namespace or fullname: {action_name}, {action_namespace}, {action_fullname}"
                )
                continue
            language_code = d.get("language_code", "")
            if language_code not in SUPPORTED_LANGUAGE_CODES:
                logger.warning(f"Unsupported language code: {language_code}")
                continue
            context = singleshot.get("context", {})
            if not isinstance(context, dict):
                logger.warning(f"Invalid context: {context}")
                continue
            preferred_response = singleshot.get("preferred_response", {})
            if not isinstance(preferred_response, dict):
                logger.warning(f"Invalid preferred response: {preferred_response}")
                continue
            keys_in_preferred_response = list(preferred_response.keys())
            if len(keys_in_preferred_response) != 1:
                logger.warning(f"Invalid preferred response: {preferred_response}")
                continue
            preferred_response_key = keys_in_preferred_response[0]
            if preferred_response_key != action_fullname:
                logger.warning(
                    f"Invalid preferred response key: {preferred_response_key}, expected: {action_fullname}"
                )
                continue
            preferred_response_value = preferred_response[preferred_response_key]
            if not isinstance(preferred_response_value, dict):
                logger.warning(
                    f"Invalid preferred response value: {preferred_response_value}"
                )
                continue
            saf_list.append(
                SingleActionFewshotQdrantPayload(
                    user_query=user_query,
                    action_name=action_name,
                    action_namespace=action_namespace,
                    action_fullname=action_fullname,
                    language_code=language_code,
                    singleshot_jsonstr=json.dumps(
                        singleshot,
                        ensure_ascii=False,
                        indent=None,
                        separators=(",", ":"),
                    ),
                )
            )
        return saf_list

    async def reload_single_action_fewshot_collection(
        self, resource_file_path: Path | str
    ) -> tuple[
        list[SingleActionFewshotQdrantPayload], list[SingleActionFewshotQdrantPayload]
    ]:
        if await self.vector_grpc_client.collection_exists(self.qdrant_collection_name):
            await self.vector_grpc_client.delete_collection(self.qdrant_collection_name)
            logger.info(f"Deleted collection: {self.qdrant_collection_name}")
        await self.vector_grpc_client.create_collection(
            collection_name=self.qdrant_collection_name,
            vectors_config=qdrant_models.VectorParams(
                size=self.embedding_dim,
                distance=qdrant_models.Distance.COSINE,
                datatype=qdrant_models.Datatype.FLOAT32,
            ),
            on_disk_payload=False,  # store payload in memory
        )
        logger.info(f"Created collection: {self.qdrant_collection_name}")
        saf_list = self.parse_single_action_fewshot_resource(resource_file_path)
        if len(saf_list) == 0:
            return [], []
        failed_saf_list = await self.upsert_single_action_fewshot_list(saf_list)
        return saf_list, failed_saf_list

    async def fetch_fewshot(
        self,
        *,
        query_text: str,
        language_code: str = "",
        candidate_actions_with_fullname: list[str] = [],
        top_k: int = 3,
        threshold: float = 0.9,
        replace_with_action_name: bool = True,  # 如果为True，则prompt中使用action_name而不是action_fullname
    ) -> tuple[dict, dict]:
        start_time = time.time()
        d = {
            "single_action_fewshot_total_timecost": 0.0,
            "single_action_fewshot_embedding_timecost": 0.0,
            "single_action_fewshot_search_timecost": 0.0,
            "single_action_fewshot_point_list": [],
        }
        embedding_start_time = time.time()
        try:
            embedding = await self.get_one_embedding(query_text)
        except Exception as e:
            logger.error(f"Failed to get embedding: {e}")
            return {}, d
        d["single_action_fewshot_embedding_timecost"] = (
            time.time() - embedding_start_time
        )
        search_start_time = time.time()
        try:
            filter = qdrant_models.Filter()
            if language_code and language_code in SUPPORTED_LANGUAGE_CODES:
                filter.must.append(  # type: ignore
                    qdrant_models.FieldCondition(
                        key="language_code",
                        match=qdrant_models.MatchValue(value=language_code),
                    )
                )
            if candidate_actions_with_fullname:
                filter.must.append(  # type: ignore
                    qdrant_models.FieldCondition(
                        key="action_fullname",
                        match=qdrant_models.MatchAny(
                            any=candidate_actions_with_fullname
                        ),
                    )
                )
            point_list = await self.vector_grpc_client.search(
                collection_name=self.qdrant_collection_name,
                query_vector=embedding,
                limit=top_k,
                score_threshold=threshold,
                query_filter=filter,
            )
        except Exception as e:
            logger.error(f"Failed to search: {e}")
            return {}, d
        d["single_action_fewshot_search_timecost"] = time.time() - search_start_time
        if len(point_list) == 0:
            return {}, d
        d["single_action_fewshot_point_list"] = [
            point.model_dump() for point in point_list
        ]
        fewshot = []
        try:
            for point in point_list:
                singleshot = json.loads(point.payload["singleshot_jsonstr"])  # type: ignore
                if replace_with_action_name:
                    action_name = point.payload["action_name"]  # type: ignore
                    action_fullname = point.payload["action_fullname"]  # type: ignore
                    user_query = singleshot["user_query"]
                    context = singleshot["context"]
                    preferred_response = {
                        action_name: singleshot["preferred_response"][action_fullname],
                    }
                    singleshot = {
                        "user_query": user_query,
                        "context": context,
                        "preferred_response": preferred_response,
                    }
                fewshot.append(singleshot)
        except Exception as e:
            logger.error(f"Failed to parse fewshot: {e}")
            return {}, d
        d["single_action_fewshot_total_timecost"] = time.time() - start_time
        return {"fewshot": fewshot}, d


if __name__ == "__main__":
    import argparse

    def is_file_type(arg):
        if Path(arg).is_file():
            return arg
        else:
            raise FileNotFoundError(f"{arg}")

    def str2bool(v):
        if isinstance(v, bool):
            return v
        if v.lower() in ("yes", "true", "t", "y", "1"):
            return True
        elif v.lower() in ("no", "false", "f", "n", "0"):
            return False
        else:
            raise argparse.ArgumentTypeError("Boolean value expected.")

    parser = argparse.ArgumentParser(
        description="reload single action fewshot and fetch fewshot by query",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--resource_file_path",
        type=is_file_type,
        required=True,
        help="resource file path, e.g. src/action/few_shot_data/example_single_action_fewshot_data_domestic.json",
    )
    parser.add_argument(
        "--embedding_api_key",
        type=str,
        default=agent_setting.single_action_fewshot_embedding_api_key,
        help="embedding api key, e.g. ***************************************************",
    )
    parser.add_argument(
        "--embedding_model_base_url",
        type=str,
        default=agent_setting.single_action_fewshot_embedding_model_base_url,
        help="embedding model base url, e.g. http://*************:8080/embed",
    )
    parser.add_argument(
        "--embedding_model",
        type=str,
        default=agent_setting.single_action_fewshot_embedding_model,
        help="embedding model, e.g. bge",
    )
    parser.add_argument(
        "--embedding_dim",
        type=int,
        default=agent_setting.single_action_fewshot_embedding_dim,
        help="embedding dim, e.g. 1024",
    )
    parser.add_argument(
        "--embedding_batch_size",
        type=int,
        default=agent_setting.single_action_fewshot_embedding_batch_size,
        help="embedding batch size, e.g. 16",
    )
    parser.add_argument(
        "--embedding_enable_redis",
        type=str2bool,
        default=agent_setting.single_action_fewshot_embedding_enable_redis,
        help="embedding enable redis, e.g. True/False",
    )
    parser.add_argument(
        "--qdrant_collection_name",
        type=str,
        default="",
        help="qdrant collection name, e.g. single_action_fewshot_query_embeddings_bge_1024, if not provided, will be generated automatically",
    )
    parser.add_argument(
        "--qdrant_host",
        type=str,
        default=agent_setting.single_action_fewshot_qdrant_host,
        help="qdrant host, e.g. *************",
    )
    parser.add_argument(
        "--qdrant_insert_batch_size",
        type=int,
        default=agent_setting.single_action_fewshot_qdrant_insert_batch_size,
        help="qdrant insert batch size, e.g. 16",
    )
    parser.add_argument(
        "--fetch_fewshot_by_this_query",
        type=str,
        default="",
        help="fetch fewshot by this query",
    )
    parser.add_argument(
        "--language_code",
        type=str,
        default="",
        help="language code, e.g. zh_CN, en_US, ja_JP, de_DE",
    )
    parser.add_argument(
        "--candidate_actions_with_fullname",
        type=str,
        default="",
        help="candidate actions with fullname, e.g. orion.agent.action.set_volume, orion.agent.action.say",
        nargs="+",
    )
    parser.add_argument(
        "--top_k",
        type=int,
        default=3,
        help="top k, only used when fetch_fewshot_by_this_query is not empty",
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.90,
        help="threshold, only used when fetch_fewshot_by_this_query is not empty",
    )
    parser.add_argument(
        "--skip_reload_collection",
        type=str2bool,
        default=False,
        help="skip reload collection, e.g. True/False",
    )
    parser.add_argument(
        "--replace_with_action_name",
        type=str2bool,
        default=False,
        help="replace with action name, e.g. True/False",
    )

    args = parser.parse_args()
    logger.info(f"{args}")

    resource_file_path = args.resource_file_path
    embedding_api_key = args.embedding_api_key
    embedding_model_base_url = args.embedding_model_base_url
    embedding_model = args.embedding_model
    embedding_dim = args.embedding_dim
    embedding_batch_size = args.embedding_batch_size
    embedding_enable_redis = args.embedding_enable_redis
    qdrant_collection_name = args.qdrant_collection_name
    qdrant_host = args.qdrant_host
    qdrant_insert_batch_size = args.qdrant_insert_batch_size
    fetch_fewshot_by_this_query = args.fetch_fewshot_by_this_query
    top_k = args.top_k
    threshold = args.threshold
    language_code = args.language_code
    candidate_actions_with_fullname = args.candidate_actions_with_fullname
    skip_reload_collection = args.skip_reload_collection
    replace_with_action_name = args.replace_with_action_name

    async def main():
        async with aiohttp.ClientSession() as session:
            single_action_fewshotor = SingleActionFewshotor(
                embedding_api_key=embedding_api_key,
                embedding_model_base_url=embedding_model_base_url,
                embedding_model=embedding_model,
                embedding_dim=embedding_dim,
                embedding_batch_size=embedding_batch_size,
                embedding_enable_redis=embedding_enable_redis,
                qdrant_host=qdrant_host,
                qdrant_insert_batch_size=qdrant_insert_batch_size,
                qdrant_collection_name=qdrant_collection_name,
                http_session=session,
            )
            if not skip_reload_collection:
                (
                    saf_list,
                    failed_saf_list,
                ) = await single_action_fewshotor.reload_single_action_fewshot_collection(
                    resource_file_path
                )
                logger.info(f"saf_list: {len(saf_list)}")
                logger.info(f"failed_saf_list: {len(failed_saf_list)}")
            if fetch_fewshot_by_this_query:
                fewshot, debug_info = await single_action_fewshotor.fetch_fewshot(
                    query_text=fetch_fewshot_by_this_query,
                    language_code=language_code,
                    candidate_actions_with_fullname=candidate_actions_with_fullname,
                    top_k=top_k,
                    threshold=threshold,
                    replace_with_action_name=replace_with_action_name,
                )
                logger.info(f"fewshot: {fewshot}")
                logger.info(f"debug_info: {debug_info}")

    asyncio.run(main())
