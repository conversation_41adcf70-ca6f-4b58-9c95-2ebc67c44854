"""
This module provides the ActionVersionManager class, which is responsible for managing the versions of actions.
"""

import datetime
import json
from pathlib import Path

from loguru import logger

from src.action.model import Action
from src.common.constant import Area


class ActionVersionManager:
    def __init__(self):
        pass

    def fetch_actions_by_version(self, version: str):
        """
        Fetch all actions by version.
        :param version: The version of the action.
        :return:
        """
        # register ACTIONS

        version_path = Path(__file__).parent / "resources" / f"{version}.json"
        if not version_path.exists():
            logger.error(f"Version {version} does not exist.")
            return None

        # Fetch all actions by version.
        with open(version_path, "r", encoding="utf-8") as f:
            action_config = json.load(f)
            actions = action_config.get("actions", [])
            logger.info(
                f"Load {version} actions from {version_path}, count: {len(actions)}"
            )

        # filter by version. TODO: We will only use version to fetch actions in the database in the future.
        actions = [
            Action(**action) for action in actions if action["version"] == version
        ]
        return actions

    def _snapshot_actions(self, actions: list[Action], version: str):
        """
        Snapshot the actions to the specified version.
        :param actions: The actions to be snapshot.
        :param version: The version to be snapshot.
        :return:
        """
        version_path = Path(__file__).parent / "resources" / f"{version}.json"
        if version_path.exists():
            logger.error(f"Version {version} already exists.")
            return

        for action in actions:
            action.version = version

        # Snapshot the actions to the specified version.
        with open(version_path, "w") as f:
            serialized_actions = []
            for action in actions:
                action_dict = action.model_dump(
                    exclude={"execute_function", "post_processing"}
                )
                if action.execute_function:
                    action_dict["execute_function"] = action.execute_function.__name__
                if action.post_processing:
                    action_dict["post_processing"] = action.post_processing.__name__
                for i, parameter in enumerate(action.parameters):
                    if parameter.enum_func:
                        action_dict["parameters"][i]["enum_func"] = (
                            parameter.enum_func.__name__
                        )
                serialized_actions.append(action_dict)

            action_config = {
                "version": version,
                "created_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "actions": serialized_actions,
            }
            json.dump(
                action_config,
                f,
                indent=4,
                ensure_ascii=False,
            )
            logger.info(
                f"Snapshot {version} actions to {version_path}, count: {len(actions)}"
            )

    def load_to_draft(self, release_version: str, draft_version: str):
        """
        Load the release version to the draft version for editing.
        :param release_version:
        :param draft_version:
        :return:
        """
        assert draft_version.startswith("draft") or draft_version.startswith(
            "oversea_draft"
        )
        release_actions = self.fetch_actions_by_version(release_version)
        if not release_actions:
            return

        # load to draft version
        self._snapshot_actions(release_actions, draft_version)

    def publish_action_version(
        self,
        draft_version: str,
        release_version: str,
        regional_version: str,
        exclude_actions: list[str] = [],
    ):
        """
        Publish the draft version to the release version.

        :param draft_version: The draft version to be published.
        :param release_version: The release version to be published.
        :return:
        """
        # assert draft_version.startswith("draft")
        draft_actions = self.fetch_actions_by_version(draft_version)
        if not draft_actions:
            return

        # filter the actions by the regional version
        filtered_actions = []
        for action in draft_actions:
            if action.full_name in exclude_actions:
                print(f"Exclude action: {action.full_name}")
                continue

            if action.action_area == "all" or action.action_area == regional_version:
                filtered_actions.append(action)

        # publish the draft version to the release version
        self._snapshot_actions(filtered_actions, release_version)


if __name__ == "__main__":
    from rich import print

    # register funtcitons
    from src.action.server_function import *  # noqa
    from src.action.post_processing import *  # noqa
    from src.action.resource import *  # noqa

    # 测试脚本
    # old_actions = ActionVersionManager().fetch_actions_by_version("v1.0.2")
    # print(f"old_actions: {len(old_actions)}")
    # for action in old_actions:
    #     if "OUTDOOR_NAVIGATE_START" in action.full_name:
    #         print(
    #             f"version: {action.version} {action.full_name} -> {action.post_processing.__name__}"
    #         )
    #
    # new_actions = ActionVersionManager().fetch_actions_by_version("draft")
    # for action in new_actions:
    #     if "OUTDOOR_NAVIGATE_START" in action.full_name:
    #         print(
    #             f"version: {action.version} {action.full_name} -> {action.post_processing.__name__}"
    #         )

    ActionVersionManager().publish_action_version(
        "draft",
        "v1.0.6",
        regional_version=Area.domestic,
        exclude_actions=[
            # 演示action
            # "orion.agent.action.SEND_MESSAGE".lower(),
        ],
    )
