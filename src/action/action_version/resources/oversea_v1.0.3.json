{"version": "oversea_v1.0.3", "created_at": "2025-04-23 20:33:34", "actions": [{"namespace": "orion.agent.action", "level": "opk", "name": "CRUISE", "display_name": "巡航", "en_display_name": "Cruise", "desc": "cruise", "desc_chinese": "巡航，巡逻。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "admin", "name": "EXIT_CRUISE", "display_name": "退出巡逻", "en_display_name": "Exit Cruise", "desc": "stop cruise", "desc_chinese": "退出巡逻、巡航模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "NOT_MOVE", "display_name": "停止移动", "en_display_name": "Stop Moving", "desc": "not move.", "desc_chinese": "停止走路。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "COME_FAR", "display_name": "让路", "en_display_name": "Give Way", "desc": "Make way.", "desc_chinese": "让路", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "TURN_DIRECTION", "display_name": "转圈", "en_display_name": "Turn Circle", "desc": "The body can only turn left and right 10 times at most. If it exceeds 10 times, use `SAY` to reject the user, and then choose the number of circles or angle according to the user's requirements.", "desc_chinese": "身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to turn, default is left", "is_required": true, "length": null, "enum_constant": ["left", "right"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "angle", "type": "int", "desc": "The value of the rotation angle", "is_required": false, "length": null, "enum_constant": null, "max": 3600, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "turns", "type": "float", "desc": "Number of turns.", "is_required": false, "length": null, "enum_constant": null, "max": 10, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "HEAD_NOD", "display_name": "点头", "en_display_name": "Nod Head", "desc": "Nod and bow.", "desc_chinese": "点头、鞠躬", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "START_DANCE", "display_name": "唱歌跳舞", "en_display_name": "Sing and Dance", "desc": "Dancing, playing music.", "desc_chinese": "跳舞、播放音乐。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "MOVE_DIRECTION", "display_name": "移动", "en_display_name": "Move", "desc": "Move forward or backward, closer or farther away from the user. If the movement distance exceeds the limit, you can use `SAY` to reject the user.", "desc_chinese": "前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to move in, select from enumerated values", "is_required": true, "length": null, "enum_constant": ["forward", "backward"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "moving_distance", "type": "float", "desc": "单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "WEATHER_GET", "display_name": "查询未来天气", "en_display_name": "Query Future Weather", "desc": "Answers based on user questions combined with weather information. Get weather information for a region or city over a period of time or at a point in time.", "desc_chinese": "未来天气查询。查询「明天及未来10天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！", "parameters": [{"name": "area_level", "type": "enum", "desc": "city 对应的区域等级", "is_required": true, "length": null, "enum_constant": ["province", "city", "area"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "city", "type": "string", "desc": "行政区域名称，默认查询<city>。注意：如果用户的机器语言（robot语言）不是中文（非zh_CN），则提取的城市名称**必须**为英文。例如，用户询问 '내일 시안 날씨 어때요?' 时，应提取 'Xi'an' 而非 '西安'。如果用户没有指明城市，则设置值为'-1'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "user_question", "type": "string", "desc": "用户的问题", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": true, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The weather of the city", "type": "string", "enum_constant": null}], "post_processing": null, "app_ids": ["system_a8c9098ad7a91a66287b25d6befef6ec", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CALENDAR", "display_name": "查询日历", "en_display_name": "Query Calendar", "desc": "Supports time and date calculations and date query function, such as 'How many days until Christmas?'", "desc_chinese": "日历功能，包含日期或节假日的查询，注意：无法查询天气", "parameters": [{"name": "user_question", "type": "string", "desc": "question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "post_processing": null, "app_ids": ["system_17bf9cfc230d17c94a19a0dc4faa6569", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "GUIDE_INTRODUCTION", "display_name": "导览", "en_display_name": "Guide Introduction", "desc": "The navigation function guides users on a tour without a specific visiting purpose.", "desc_chinese": "导览功能，带领用户参观，在没有明确的参观目的下使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 1800, "result_schema": [], "post_processing": null, "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "OPEN_WEB_URL", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Web Search.Search for information that users want to know through google or official website.", "desc_chinese": "网络搜索，例如股票、门票、新闻等。推荐使用「百度」搜索引擎；公司官网等特定网站直接通过对应网址打开。", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "Must be a legitimate https or http link.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "post_processing": null, "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "SET_VOLUME", "display_name": "调整音量", "en_display_name": "Adjust Volume", "desc": "Adjust the volume. The default increase or decrease range is between 10 and 30.", "desc_chinese": "调整音量。调整的幅度是10或30，根据用户语气选择", "parameters": [{"name": "volume_level", "type": "int", "desc": "The volume level to be set.", "is_required": true, "length": null, "enum_constant": null, "max": 100, "min": 0, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "SAY", "display_name": "说", "en_display_name": "Say", "desc": "Say. Basic communication with the user.", "desc_chinese": "说话，与用户的基础交流。", "parameters": [{"name": "text", "type": "string", "desc": "Speak in the first person, using pure text without emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": true, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "CANCEL", "display_name": "取消", "en_display_name": "Cancel", "desc": "cancel the current action. for example, dancing, nodding, navigation, etc.", "desc_chinese": "取消当前动作。例如跳舞、点头、导航等", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "EXIT", "display_name": "退出", "en_display_name": "Exit", "desc": "exit current application", "desc_chinese": "退出当前应用", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "BACK", "display_name": "返回上一级", "en_display_name": "Back", "desc": "Back to the previous level.", "desc_chinese": "返回上一级", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "NEXT", "display_name": "下一步", "en_display_name": "Next", "desc": "Next step.", "desc_chinese": "下一步", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "CONFIRM", "display_name": "确认", "en_display_name": "Confirm", "desc": "Confirm the operation.", "desc_chinese": "用户确认操作", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "MULTIMEDIA_PLAY", "display_name": "播放", "en_display_name": "Play", "desc": "Triggered when there is a video to be played on the screen information.", "desc_chinese": "Triggered when there is a video to be played on the screen information.", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "COMMON_PAUSE", "display_name": "暂停", "en_display_name": "Pause", "desc": "Pause the guide explanation process; pause the media resource playback.", "desc_chinese": "暂停导览讲解过程；媒体资源播放时，暂停播放。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "COMMON_REPEAT", "display_name": "重新讲解", "en_display_name": "Repeat", "desc": "After a round of explanation ends, it enters the scoring session, and the user wants to re-explain.", "desc_chinese": "当一轮讲解结束后，进入打分环节，用户想重新进行讲解。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "ROUTES_OTHERS", "display_name": "选择其他导览路线", "en_display_name": "Choose Another Route", "desc": "Choose another route", "desc_chinese": "选择其他导览路线。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "SCORE", "display_name": "打分", "en_display_name": "Score", "desc": "Score.", "desc_chinese": "评分。", "parameters": [{"name": "score", "type": "int", "desc": "score 1-5 代表五颗星", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "ADJUST_SPEED", "display_name": "调整当前移动速度", "en_display_name": "Adjust Walking Speed", "desc": "Adjust the <current walking speed>.", "desc_chinese": "调整<当前移动速度>。", "parameters": [{"name": "adjusted_speed", "type": "float", "desc": "The robot's walking speed.", "is_required": true, "length": null, "enum_constant": null, "max": 1.2, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "GENERATE_MESSAGE", "display_name": "生成", "en_display_name": "Generate", "desc": "Text generation. Only for 'Welcome Someone', '<PERSON><PERSON><PERSON> Someone', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'. Cannot generate 'Suggestion', 'Recommendation', 'Introduction', etc.", "desc_chinese": "文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等", "parameters": [{"name": "goal", "type": "string", "desc": "user instructions", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 90, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "GO_CHARGING", "display_name": "去充电", "en_display_name": "Go to Charging", "desc": "Go to the charging station", "desc_chinese": "自动去充电桩充电", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "apk", "name": "SET_LOCATION", "display_name": "设置位置", "en_display_name": "Set Location", "desc": "Set the location on the map.", "desc_chinese": "在地图上设置点位。", "parameters": [{"name": "location", "type": "string", "desc": "The location to set, a valid location name", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.maptool"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "KNOWLEDGE_QA", "display_name": "找答案", "en_display_name": "Find Answer", "desc": "Query knowledge to answer user questions, including 'encyclopedic knowledge,' 'company products,' 'employee information,' 'city and scenic spot introductions,' and 'travel planning.'", "desc_chinese": "查询知识回答用户的问题，包含「百科知识」、「公司产品」、「员工信息」、「城市、景点介绍」、「旅游计划规划」等", "parameters": [{"name": "question", "type": "string", "desc": "The user's question has been summarized based on the context of the conversation.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 600, "result_schema": [{"name": "knowledge_content", "desc": "The knowledge content.", "type": "string", "enum_constant": null}], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CHANGE_FACE", "display_name": "换脸", "en_display_name": "Change Face", "desc": "Change face", "desc_chinese": "切换表情", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": ["system_01917709e2d711f26014d0f1e78295fb", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "WEATHER_GET_REALTIME", "display_name": "查询当天天气", "en_display_name": "Query Today Weather", "desc": "Get today's weather.", "desc_chinese": "当天天气查询。查询「当天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！不支持日历功能！！！", "parameters": [{"name": "city", "type": "string", "desc": "行政区域名称，默认查询`<所在城市>`。当用户没有指明城市，则设置值为'-1'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "user_question", "type": "string", "desc": "user's question", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": true, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CLICK", "display_name": "点击", "en_display_name": "Click", "desc": "Simulate web page click.", "desc_chinese": "模拟网页点击，根据“应用页面HTML”信息，理解用户需求，帮助用户点击网页上的元素。如果存在可点击元素，则优先考虑使用，而不是生成 URL。", "parameters": [{"name": "element_tag", "type": "enum", "desc": "可点击元素的标签", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "post_processing": null, "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "ANSWER_QUESTION_FROM_VISION", "display_name": "根据视觉回答", "en_display_name": "Answer from Vision", "desc": "In front of the robot, only answer questions related to 'clothing, expression, gender,' 'surrounding environment,' and 'object recognition.'", "desc_chinese": "在机器人面前，仅回答有关「穿着、表情、性别」、「周边环境」、「物体识别」问题。", "parameters": [{"name": "image_url", "type": "string", "desc": "视觉采集的图片url", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "question", "type": "string", "desc": "用户的问题，必须总结为第一人称的问题，长度限制在15字以内。", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The answer of the question", "type": "string", "enum_constant": null}], "post_processing": null, "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "NAVIGATE_REC_START", "display_name": "室内导航推荐", "en_display_name": "Indoor Navigation Recommendation", "desc": "Point navigation intent. Users can only be taken to the locations provided below, and outdoor locations are not supported.If the user does not explicitly specify the destination, a list of up to 4 available navigation points is returned and sorted by similarity, with the closest point at the front.", "desc_chinese": "室内导航意图。不支持去室外位置，选择你认为最符合用户意图的4个地点。", "parameters": [{"name": "destinations", "type": "String array", "desc": "Navigation points that match user intent. You can choose multiple.", "is_required": false, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "guide_text", "type": "string", "desc": "A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 300, "result_schema": [], "post_processing": null, "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "OUTDOOR_NAVIGATE", "display_name": "室外导航", "en_display_name": "Outdoor Navigation", "desc": "Provide outdoor navigation capability. Select this action when the user wants to know how to reach a place.", "desc_chinese": "", "parameters": [{"name": "origin", "type": "string", "desc": "Route origin, provided by the user. Set the value to \"-1\" if indeterminate.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "destination", "type": "string", "desc": "Route destination. Provided by the user", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "travelmode", "type": "enum", "desc": "Default to driving if not provided by the user.", "is_required": true, "length": null, "enum_constant": ["driving", "walking", "bicycling", "transit"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.3", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "oversea"}, {"namespace": "orion.agent.action", "level": "opk", "name": "RECOMMEND_PLACES", "display_name": "推荐", "en_display_name": "Recommend", "desc": "Recommendations for various places, such as restaurants, attractions, shopping areas, bars, KTVs, subways, etc", "desc_chinese": "", "parameters": [{"name": "target", "type": "string", "desc": "The target location that the user wants to go to", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "ref_location", "type": "string", "desc": "The starting point. Search based on this location. Don't use <Current Indoor Point>. If not specificed by user, set ref_location <Current Latitude and Longitude>)", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.3", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "oversea"}, {"namespace": "orion.agent.action", "level": "opk", "name": "OPEN_WEB_URL_DEFINED", "display_name": "打开轻应用", "en_display_name": "Open Light App", "desc": "Use a browser or app to browse information. Trigger this action if there's a semantically identical question in the predefined list, as it allows you to directly open the URL or app.", "desc_chinese": "使用浏览器或者APP浏览信息。*只要*预定义问题列表中存在与用户当前问题语义相同的问题，则选择此action。", "parameters": [{"name": "predefined_question", "type": "enum", "desc": "list of predefined question.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.3", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REALTIME_SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Think about it.", "desc_chinese": "思考说", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": true, "page_id": "", "execute_function": "stream_say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY_FOR_CLARIFICATION", "category": "interaction", "display_name": "澄清用户问题", "en_display_name": "Clarify User Question", "desc": "Clarify the user's question. When you need to clarify the user's question, please use this tool.", "desc_chinese": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具", "parameters": [{"name": "request_clarify_text", "type": "string", "desc": "Ask user to clarify the question", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.3", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "clarify"}]}