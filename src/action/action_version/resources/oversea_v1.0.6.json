{"version": "oversea_v1.0.6", "created_at": "2025-07-28 20:44:51", "actions": [{"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_CRUISE_MODE", "name_for_llm": "START_CRUISE_MODE", "category": "basic_movement", "display_name": "巡航", "en_display_name": "Cruise", "desc": "Activate autonomous cruise, patrol, or inspection mode.", "desc_chinese": "启动自主巡航/巡逻/巡视模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CRUISE", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "巡航", "zh_TW": "巡航", "zh_GD": "巡航", "en_US": "Cruise", "ja_JP": "巡航", "th_TH": "ลาดตระเวน", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "ko_KR": "순찰", "da_DK": "<PERSON><PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "Partio", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Croisière", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Патруль", "pl_PL": "Patrol", "pt_PT": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Pattuglia", "ro_RO": "<PERSON><PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "Tu<PERSON>n tra", "id_ID": "Patroli", "fil_PH": "Patrol", "cs_CZ": "Hlídka", "el_GR": "Περιπολία", "pt_BR": "<PERSON><PERSON><PERSON><PERSON>", "hu_HU": "Járőrözés", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Hliadka", "ar_SA": "التجوال"}}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "EXIT_CRUISE_MODE", "name_for_llm": "EXIT_CRUISE_MODE", "category": "basic_movement", "display_name": "退出巡逻", "en_display_name": "Exit Cruise", "desc": "Exit or stop the autonomous patrol mode. ", "desc_chinese": "退出或停止巡逻/巡航模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.EXIT_CRUISE", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出巡逻", "zh_TW": "退出巡邏", "zh_GD": "退出巡邏", "en_US": "Exit Cruise", "ja_JP": "巡航終了", "th_TH": "ออกจากการลาดตระเวน", "de_DE": "<PERSON><PERSON><PERSON>den", "es_ES": "<PERSON><PERSON>", "ko_KR": "순찰 종료", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Lopeta partio", "nb_NO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON>rouille", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Выйти из патруля", "pl_PL": "Zakończ patrol", "pt_PT": "<PERSON><PERSON> <PERSON>", "it_IT": "<PERSON><PERSON><PERSON> dalla pattuglia", "ro_RO": "Ieși din patrulare", "ms_MY": "<PERSON><PERSON><PERSON> dari rondaan", "vi_VN": "<PERSON><PERSON><PERSON>t khỏi tuần tra", "id_ID": "<PERSON><PERSON><PERSON> dari <PERSON>i", "fil_PH": "Umalis sa patrol", "cs_CZ": "Ukončit hlídku", "el_GR": "Έξοδος από περιπολία", "pt_BR": "<PERSON><PERSON> <PERSON>", "hu_HU": "Járőrözés befejezése", "tr_TR": "<PERSON><PERSON><PERSON><PERSON> ç<PERSON>", "sk_SK": "Ukončiť hliadku", "ar_SA": "الخروج من التجوال"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GIVE_WAY", "name_for_llm": "GIVE_WAY", "category": "basic_movement", "display_name": "让路", "en_display_name": "Give Way", "desc": "Robot will move aside to clear the path for people.", "desc_chinese": "机器人会主动让路，方便他人通行。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COME_FAR", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "让路", "zh_TW": "讓路", "zh_GD": "讓路", "en_US": "Give Way", "ja_JP": "道を譲る", "th_TH": "หลีกทาง", "de_DE": "Platz machen", "es_ES": "Ceder el paso", "ko_KR": "길을 비키다", "da_DK": "Giv plads", "sv_SE": "Ge plats", "fi_FI": "<PERSON>", "nb_NO": "Gi plass", "fr_FR": "<PERSON><PERSON><PERSON> le passage", "nl_NL": "Plaats maken", "ru_RU": "Уступить дорогу", "pl_PL": "Ustąp mi<PERSON>", "pt_PT": "Dar <PERSON>", "it_IT": "Dare la precedenza", "ro_RO": "<PERSON><PERSON> drumul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>hư<PERSON><PERSON> đường", "id_ID": "Memberi jalan", "fil_PH": "Magbigay ng daan", "cs_CZ": "Uhnout z cesty", "el_GR": "Δώσε δρόμο", "pt_BR": "Dar <PERSON>", "hu_HU": "Utat enged", "tr_TR": "Yol ver", "sk_SK": "Ustúpiť z cesty", "ar_SA": "<PERSON><PERSON><PERSON><PERSON><PERSON> الطريق"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ROTATE", "name_for_llm": "ROTATE", "category": "basic_movement", "display_name": "转圈", "en_display_name": "Turn Circle", "desc": "The robot can rotate left or right by a specified angle or number of turns, but not both at the same time. Maximum rotation of 10 turns, exceeding 10 turns should be rejected using the `say_for_clarification` tool.", "desc_chinese": "机器人可向左或向右旋转指定角度或圈数，二者不可同时设置。最多旋转10圈，超出请用`say_for_clarification`工具拒绝。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to turn, default is left", "is_required": true, "length": null, "enum_constant": ["left", "right"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "angle", "type": "int", "desc": "The value of the rotation angle", "is_required": false, "length": null, "enum_constant": null, "max": 3600, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "turns", "type": "float", "desc": "Number of turns.", "is_required": false, "length": null, "enum_constant": null, "max": 10, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.TURN_DIRECTION", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "转圈", "zh_TW": "轉圈", "zh_GD": "轉圈", "en_US": "Turn Circle", "ja_JP": "回転", "ko_KR": "회전", "th_TH": "หมุน", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON>", "da_DK": "<PERSON><PERSON>", "sv_SE": "Vänd", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Поворот", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "G<PERSON><PERSON>", "ro_RO": "Rotește", "ms_MY": "Putar", "vi_VN": "Xoay", "id_ID": "Putar", "fil_PH": "Ikot", "cs_CZ": "O<PERSON>č<PERSON>", "el_GR": "Στροφή", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Fordulás", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ar_SA": "دوران"}, "post_processing": "convert_turns_to_degree"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "HEAD_NOD_OR_BOW", "name_for_llm": "HEAD_NOD_OR_BOW", "category": "basic_movement", "display_name": "点头", "en_display_name": "Nod Head", "desc": "<PERSON> performs a head nodding or bowing gesture.", "desc_chinese": "机器人执行点头或鞠躬动作。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.HEAD_NOD", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "点头", "zh_TW": "點頭", "zh_GD": "點頭", "en_US": "Nod Head", "ja_JP": "うなずく", "ko_KR": "고개 끄덕이기", "th_TH": "พยักหน้า", "de_DE": "<PERSON><PERSON> nicken", "es_ES": "Asentir con la cabeza", "fr_FR": "Hocher la tête", "da_DK": "Nikke med hovedet", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Nyökkää päätä", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Knikken", "ru_RU": "Кивнуть головой", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> głową", "pt_PT": "Acenar com a cabeça", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Dă din cap", "ms_MY": "Angguk kepala", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON><PERSON>", "fil_PH": "Tumango", "cs_CZ": "Pokývat hlavou", "el_GR": "Νεύμα κεφαλιού", "pt_BR": "Acenar com a cabeça", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Başını salla", "sk_SK": "Prikývnuť", "ar_SA": "إيماءة الرأس"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SING_AND_DANCE", "name_for_llm": "SING_AND_DANCE", "category": "entertainment", "display_name": "唱歌跳舞", "en_display_name": "Sing and Dance", "desc": "<PERSON> performs singing and dancing entertainment routines. Use for entertainment, demonstrations, or to create an engaging atmosphere.", "desc_chinese": "机器人表演唱歌和跳舞。用于音乐播放、娱乐、演示或创造活跃氛围。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.START_DANCE", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "唱歌跳舞", "zh_TW": "唱歌跳舞", "zh_GD": "唱歌跳舞", "en_US": "Sing and Dance", "ja_JP": "歌って踊る", "ko_KR": "노래하고 춤추기", "th_TH": "ร้องเพลงและเต้นรำ", "de_DE": "Singen und tanzen", "es_ES": "Cantar y bailar", "fr_FR": "<PERSON><PERSON> et danser", "da_DK": "Synge og danse", "sv_SE": "Sjunga och dansa", "fi_FI": "<PERSON><PERSON><PERSON> ja tanssia", "nb_NO": "Synge og danse", "nl_NL": "Zingen en dansen", "ru_RU": "Петь и танцевать", "pl_PL": "Śpiewać i tańczyć", "pt_PT": "Cantar e dançar", "it_IT": "Cantare e ballare", "ro_RO": "Cântă și dansează", "ms_MY": "<PERSON><PERSON><PERSON> dan menari", "vi_VN": "H<PERSON><PERSON> v<PERSON> n<PERSON>ả<PERSON>", "id_ID": "<PERSON><PERSON><PERSON> dan menari", "fil_PH": "Kumanta at sumayaw", "cs_CZ": "Zpívat a tančit", "el_GR": "Τραγούδι και χορός", "pt_BR": "Cantar e dançar", "hu_HU": "Énekelni és táncolni", "tr_TR": "Şarkı söyle ve dans et", "sk_SK": "Spievať a tancovať", "ar_SA": "الغناء والرقص"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "MOVE_FORWARD_OR_BACKWARD", "name_for_llm": "MOVE_FORWARD_OR_BACKWARD", "category": "basic_movement", "display_name": "移动", "en_display_name": "Move", "desc": "Move forward or backward, closer or farther away from the user. Note:If forward exceeds 5m or backward exceeds 1m, you can use `say_for_clarification` tool to reject the user.", "desc_chinese": "让机器人向前或向后移动指定距离。注意：若前进超5米或后退超1米时，用`say_for_clarification`工具拒绝用户。", "parameters": [{"name": "direction", "type": "enum", "desc": "Movement direction: choose 'forward' or 'backward'.", "is_required": true, "length": null, "enum_constant": ["forward", "backward"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "moving_distance", "type": "float", "desc": "Movement distance in meters (default 0.1).", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MOVE_DIRECTION", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "移动", "zh_TW": "移動", "zh_GD": "移動", "en_US": "Move", "ja_JP": "移動", "ko_KR": "이동", "th_TH": "เคลื่อนที่", "de_DE": "Bewegen", "es_ES": "Mover", "fr_FR": "<PERSON><PERSON><PERSON>", "da_DK": "<PERSON><PERSON><PERSON><PERSON>", "sv_SE": "Flytta", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Beveg", "nl_NL": "Verplaatsen", "ru_RU": "Двигаться", "pl_PL": "P<PERSON><PERSON><PERSON>ś", "pt_PT": "Mover", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Mi<PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>", "id_ID": "<PERSON><PERSON>", "fil_PH": "Ilipat", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Κίνηση", "pt_BR": "Mover", "hu_HU": "Mozgatás", "tr_TR": "Hareket et", "sk_SK": "Presunúť", "ar_SA": "تحرك"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "QUERY_CALENDAR", "name_for_llm": "QUERY_CALENDAR", "category": "information", "display_name": "查询日历", "en_display_name": "Query Calendar", "desc": "Calendar function, provides date, holiday or weekday queries, not support query weather.", "desc_chinese": "日历功能，包含日期、节假日、星期的查询，不支持查询天气。", "parameters": [{"name": "user_question", "type": "string", "desc": "The complete calendar or date-related question. Format should specify the target date/event and reference time frame (e.g., 'When is [holiday] in [year]').", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_17bf9cfc230d17c94a19a0dc4faa6569", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CALENDAR", "version": "oversea_v1.0.6", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "查询日历", "zh_TW": "查詢日曆", "zh_GD": "查詢日曆", "en_US": "Query Calendar", "ja_JP": "カレンダーを照会", "ko_KR": "캘린더 조회", "th_TH": "สอบถามปฏิทิน", "vi_VN": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> l<PERSON>ch", "id_ID": "<PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON> kalen<PERSON>", "fil_PH": "Tanong sa kalendaryo", "de_DE": "<PERSON><PERSON><PERSON> ab<PERSON>", "es_ES": "Consultar calendario", "fr_FR": "<PERSON><PERSON><PERSON> le calendrier", "it_IT": "Interroga calendario", "pt_PT": "Consultar calendário", "pt_BR": "Consultar calendário", "nl_NL": "<PERSON><PERSON><PERSON>v<PERSON>n", "ru_RU": "Запросить календарь", "pl_PL": "Zapytaj o kalendarz", "sv_SE": "<PERSON><PERSON><PERSON> kalender", "da_DK": "<PERSON><PERSON><PERSON><PERSON><PERSON> kalender", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Kysy ka<PERSON>", "cs_CZ": "Dotaz na kalendář", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "sk_SK": "Dotaz na kalendár", "ro_RO": "Interogare calendar", "el_GR": "Ερώτημα ημερολογίου", "tr_TR": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>", "ar_SA": "استعلام عن التقويم"}, "execute_function": "calendar"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_GUIDE_TOUR", "name_for_llm": "START_GUIDE_TOUR", "category": "guide", "display_name": "导览", "en_display_name": "Guide Introduction", "desc": "Tour guide function, triggered when the user requests a tour without specifying a location or route, providing a general tour introduction.", "desc_chinese": "导览功能，用户未指定具体地点或路线，仅提出参观请求时触发，带领用户参观，提供总体导览介绍。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original request expressing general interest in a tour without specifying a route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GUIDE_INTRODUCTION", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "导览", "zh_TW": "導覽", "zh_GD": "導覽", "en_US": "Guide Introduction", "ja_JP": "ガイド紹介", "ko_KR": "가이드 소개", "th_TH": "แนะนำไกด์", "de_DE": "Führung", "es_ES": "Introducción guía", "fr_FR": "Introduction guide", "da_DK": "Guide introduktion", "sv_SE": "Guide introduktion", "fi_FI": "Opas esittely", "nb_NO": "Guide introduksjon", "nl_NL": "Gids introductie", "ru_RU": "Представление гида", "pl_PL": "Wprowadzenie przewodnika", "pt_PT": "Introdução do guia", "it_IT": "Introduzione guida", "ro_RO": "Introducer<PERSON>i", "ms_MY": "Pengenalan panduan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> thiệu hướng dẫn", "id_ID": "Pengenalan panduan", "fil_PH": "Pagpapakilala ng gabay", "cs_CZ": "Představení průvodce", "el_GR": "Εισαγωγή οδηγού", "pt_BR": "Introdução do guia", "hu_HU": "Útmutató bemu<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>ıtımı", "sk_SK": "Predstavenie sprievodcu", "ar_SA": "التقديم"}, "execute_function": "generate_route_introduction"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_SPECIFIC_ROUTE", "name_for_llm": "GUIDE_SPECIFIC_ROUTE", "category": "guide", "display_name": "指定路线导览", "en_display_name": "Select Specific Route", "desc": "Guide the user along a specific, named tour route that they have explicitly requested.", "desc_chinese": "导览功能，仅当用户明确提及特定路线名称时，按该路线进行导览。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original request that contains a specific route name.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "route_name", "type": "string", "desc": "The exact name of the selected route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GUIDE_SELECT_SPECIFIC_ROUTE", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "指定路线导览", "zh_TW": "指定路線導覽", "zh_GD": "指定路線導覽", "en_US": "Select Specific Route", "ja_JP": "特定ルート選択", "ko_KR": "특정 루트 선택", "th_TH": "เลือกเส้นทางเฉพาะ", "de_DE": "Spezifische Route wählen", "es_ES": "Seleccionar ruta específica", "fr_FR": "Sélectionner itinéraire spécifique", "da_DK": "Vælg specifik rute", "sv_SE": "<PERSON><PERSON><PERSON>j specifik rutt", "fi_FI": "<PERSON><PERSON><PERSON> tietty reitti", "nb_NO": "Velg spesifikk rute", "nl_NL": "Selecteer specifieke route", "ru_RU": "Выбрать конкретный маршрут", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> konkretną trasę", "pt_PT": "Selecionar rota específica", "it_IT": "Seleziona percorso specifico", "ro_RO": "Selectează rută specifică", "ms_MY": "<PERSON><PERSON><PERSON> k<PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> tuyến đường cụ thể", "id_ID": "<PERSON><PERSON>h rute spesifik", "fil_PH": "<PERSON><PERSON><PERSON> ang tukoy na ruta", "cs_CZ": "Vybrat konkrétní trasu", "el_GR": "Επιλογή συγκεκριμένης διαδρομής", "pt_BR": "Selecionar rota específica", "hu_HU": "Konkrét útvonal kiválasztása", "tr_TR": "<PERSON><PERSON><PERSON> rota seç", "sk_SK": "Vybrať konkrétnu trasu", "ar_SA": "اختيار مسار محدد"}, "execute_function": "convert_specific_route"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RECOMMEND_GUIDE_ROUTES", "name_for_llm": "RECOMMEND_GUIDE_ROUTES", "category": "guide", "display_name": "推荐路线", "en_display_name": "Route Recommendation", "desc": "Guide scenario, recommend the most suitable tour routes based on user interests or needs.", "desc_chinese": "导览场景，根据用户兴趣或需求推荐合适的参观路线。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's request expressing interest in a tour without specifying a route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GUIDE_ROUTE_RECOMMENDATION", "version": "oversea_v1.0.6", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "推荐路线", "zh_TW": "推薦路線", "zh_GD": "推薦路線", "en_US": "Route Recommendation", "ja_JP": "ルート推奨", "ko_KR": "루트 추천", "th_TH": "แนะนำเส้นทาง", "de_DE": "Routenempfehlung", "es_ES": "Recomendación de ruta", "fr_FR": "Recommandation d'itinéraire", "da_DK": "Ruteanbefaling", "sv_SE": "Ruttrekommendation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Ruteanbefaling", "nl_NL": "Route aanbeveling", "ru_RU": "Рекомендация маршрута", "pl_PL": "Rekomendacja trasy", "pt_PT": "Recomendação de rota", "it_IT": "Raccomandazione percorso", "ro_RO": "Recomanda<PERSON> rut<PERSON>", "ms_MY": "Cadangan la<PERSON>an", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị tuyến đường", "id_ID": "Rekomendasi rute", "fil_PH": "Rekomendasyon ng ruta", "cs_CZ": "Doporučení trasy", "el_GR": "Σύσταση διαδρομής", "pt_BR": "Recomendação de rota", "hu_HU": "Útvonal ajánlás", "tr_TR": "Rota önerisi", "sk_SK": "Odporúčanie trasy", "ar_SA": "توصية سير الرحلة"}, "execute_function": "generate_route_recommendation"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_IMMEDIATELY", "name_for_llm": "START_IMMEDIATELY", "category": "guide", "display_name": "直接开始", "en_display_name": "Start Immediately", "desc": "Start the current task or process immediately, without further confirmation. Common phrases include: 'Start', 'Start immediately', etc.", "desc_chinese": "直接或立即开始当前任务或流程，无需进一步确认。如：“开始”、“直接开始”等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.START_IMMEDIATELY", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "直接开始", "zh_TW": "直接開始", "zh_GD": "直接開始", "en_US": "Start Immediately", "ja_JP": "すぐに開始", "ko_KR": "즉시 시작", "th_TH": "เริ่มทันที", "de_DE": "Sofort beginnen", "es_ES": "Comenzar inmediatamente", "fr_FR": "Commencer immédiatement", "da_DK": "Start med det samme", "sv_SE": "<PERSON><PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Start umiddelbart", "nl_NL": "<PERSON><PERSON>", "ru_RU": "Начать немедленно", "pl_PL": "Rozpocznij natychmiast", "pt_PT": "Começar imediatamente", "it_IT": "Inizia immediatamente", "ro_RO": "<PERSON><PERSON><PERSON> im<PERSON>", "ms_MY": "<PERSON>la serta-merta", "vi_VN": "<PERSON><PERSON><PERSON> đ<PERSON>u ngay lập tức", "id_ID": "<PERSON><PERSON> segera", "fil_PH": "<PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Αρχίστε αμέσως", "pt_BR": "Começar imediatamente", "hu_HU": "Azonnal kezdés", "tr_TR": "<PERSON><PERSON> b<PERSON>", "sk_SK": "Začať okamžite", "ar_SA": "ابدأ فوراً"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SEARCH_WEB_INFORMATION", "name_for_llm": "SEARCH_WEB_INFORMATION", "category": "web_browser", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Search for information on the internet or open websites, such as checking stock prices, looking up ticket information, reading news, etc. or opening a specific website.", "desc_chinese": "搜索网络信息或者打开网站，例如查股票、看门票、看新闻、打开指定网站等。", "parameters": [{"name": "search_word", "type": "string", "desc": "Search word", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "网页搜索", "zh_TW": "網頁搜尋", "en_US": "Web Search", "ja_JP": "ウェブ検索", "ko_KR": "웹 검색", "fr_FR": "Recherche Web", "de_DE": "Websuche", "es_ES": "Búsqueda web", "nl_NL": "Web zoeken", "ru_RU": "Веб-поиск", "pl_PL": "Wyszukiwanie w sieci", "pt_PT": "Pesquisa na web", "pt_BR": "Pesquisa na web", "it_IT": "Ricerca web", "ro_RO": "Căutare web", "ms_MY": "Carian web", "vi_VN": "<PERSON><PERSON><PERSON> k<PERSON> web", "id_ID": "Pencarian web", "fil_PH": "Web search", "cs_CZ": "Webové v<PERSON>hledávání", "el_GR": "Αναζήτηση στον Ιστό", "hu_HU": "Webes kere<PERSON>", "tr_TR": "Web arama", "sk_SK": "Vyhľadávanie na webe", "zh_GD": "網頁搜尋", "th_TH": "ค้นหาเว็บ", "da_DK": "<PERSON> søgning", "sv_SE": "Web sökning", "fi_FI": "Web-haku", "nb_NO": "Web-søk", "ar_SA": "<PERSON><PERSON><PERSON> الويب"}, "post_processing": "convert_search_word_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL", "name_for_llm": "OPEN_WEB_URL", "category": "web_browser", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Web search, for example, if the user wants to search for stocks, tickets, news, sports events, etc., it is recommended to use Baidu search; official company websites can be accessed directly via their URLs.", "desc_chinese": "网络搜索，如查股票、门票、新闻、体育比赛等建议用百度，官网可直接输入网址访问。", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "A valid HTTPS or HTTP URL to open in the browser. Must include protocol (http:// or https://).", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": true, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打开浏览器", "zh_TW": "打開瀏覽器", "zh_GD": "打開瀏覽器", "en_US": "Open Browser", "ja_JP": "ブラウザを開く", "ko_KR": "브라우저 열기", "th_TH": "เปิดเบราว์เซอร์", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "da_DK": "Åbn browser", "sv_SE": "Öppna webbläsare", "fi_FI": "<PERSON><PERSON> selain", "nb_NO": "<PERSON><PERSON><PERSON> net<PERSON>", "nl_NL": "Browser openen", "ru_RU": "Открыть браузер", "pl_PL": "Otwórz przeglądarkę", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Apri browser", "ro_RO": "Deschide browser", "ms_MY": "<PERSON><PERSON> pelayar", "vi_VN": "Mở trình <PERSON>", "id_ID": "Buka browser", "fil_PH": "B<PERSON>san ang browser", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Άνοιγμα περιηγητή", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Böngés<PERSON><PERSON>", "tr_TR": "Tarayıcıyı aç", "sk_SK": "Otvoriť prehliadač", "ar_SA": "فتح المتصفح"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SET_VOLUME", "name_for_llm": "SET_VOLUME", "category": "system_control", "display_name": "调整音量", "en_display_name": "Adjust Volume", "desc": "Set the robot's speaker volume to a specific level (0-100). Typically changes by increments of 10-30 units based on user instruction intensity.", "desc_chinese": "设置机器人扬声器音量（0-100），通常以10或30为步长调整。", "parameters": [{"name": "volume_level", "type": "int", "desc": "The volume level(0-100) to be set.", "is_required": true, "length": null, "enum_constant": null, "max": 100, "min": 0, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SET_VOLUME", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调整音量", "zh_TW": "調整音量", "zh_GD": "調整音量", "en_US": "Adjust Volume", "ja_JP": "音量調整", "th_TH": "ปรับระดับเสียง", "de_DE": "Lautstärke anpassen", "es_ES": "Ajustar volumen", "ko_KR": "볼륨 조절", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Säädä äänenvoimakkuutta", "nb_NO": "<PERSON><PERSON>", "fr_FR": "Ajuster le volume", "nl_NL": "Volume aanpassen", "ru_RU": "Настроить громкость", "pl_PL": "<PERSON><PERSON><PERSON><PERSON>", "pt_PT": "Ajustar volume", "it_IT": "Regola volume", "ro_RO": "Ajustează volumul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>i<PERSON>u chỉnh âm lượng", "id_ID": "Sesuaikan volume", "fil_PH": "<PERSON><PERSON><PERSON> ang lakas ng tunog", "cs_CZ": "Upravit h<PERSON>", "el_GR": "Ρύθμιση έντασης", "pt_BR": "Ajustar volume", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON> sevi<PERSON><PERSON> a<PERSON>", "sk_SK": "Upraviť hlasitosť", "ar_SA": "<PERSON><PERSON>ط مستوى الصوت"}, "post_processing": "set_volume_post_processing"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY", "name_for_llm": "SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Basic verbal communication with the user. Use for general responses, information delivery, and conversations.", "desc_chinese": "与用户的基础语言交流。用于一般回应、信息传递和对话。", "parameters": [{"name": "text", "type": "string", "desc": "Reply in the first person using plain text only, no emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SAY", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "说", "zh_TW": "說", "zh_GD": "說", "en_US": "Say", "ja_JP": "話す", "th_TH": "พูด", "de_DE": "Sagen", "es_ES": "Decir", "ko_KR": "말하기", "da_DK": "Sig", "sv_SE": "Säg", "fi_FI": "Sanoa", "nb_NO": "Si", "fr_FR": "Dire", "nl_NL": "Zeggen", "ru_RU": "Сказать", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON>", "it_IT": "Dire", "ro_RO": "Spune", "ms_MY": "Berkata", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Mengatakan", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Říci", "el_GR": "Πούμε", "pt_BR": "<PERSON><PERSON>", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "Söylemek", "sk_SK": "Povedať", "ar_SA": "قل"}, "execute_function": "say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY_FOR_CLARIFICATION", "name_for_llm": "SAY_FOR_CLARIFICATION", "category": "interaction", "display_name": "澄清用户问题", "en_display_name": "Clarify User Question", "desc": "When the user's intent is unclear, please use this tool to clarify their question. Additionally, use this tool in the following situations: 1) If the user repeatedly asks the same question because their issue was not resolved, you should confirm their true intention. Note: Use this tool cautiously, avoid overuse, and give priority to other tools.", "desc_chinese": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具", "parameters": [{"name": "request_clarify_text", "type": "string", "desc": "Prompt the user to clarify their question, using plain text only and no emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SAY_FOR_CLARIFICATION", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "clarify"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SILENT", "name_for_llm": "SILENT", "category": "guide", "display_name": "默认兜底技能", "en_display_name": "Silent", "desc": "A default fallback skill that should be selected when the user's query has low relevance to other available actions.", "desc_chinese": "默认兜底的技能，在以下情况应选择此动作：用户的查询与其他可用动作相关性较低时，默认选择这个技能", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "默认兜底技能", "zh_TW": "默認兜底技能", "zh_GD": "默認兜底技能", "en_US": "Silent", "ja_JP": "沈黙", "ko_KR": "무음", "th_TH": "เงียบ", "de_DE": "Schweigen", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "Silence", "da_DK": "Stilhed", "sv_SE": "Tystnad", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Stillhet", "nl_NL": "Stilte", "ru_RU": "Молчание", "pl_PL": "Cisza", "pt_PT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "<PERSON><PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "Im lặng", "id_ID": "<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON>", "el_GR": "Σιωπή", "pt_BR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hu_HU": "Csend", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "<PERSON><PERSON><PERSON>", "ar_SA": "صمت"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CANCEL", "name_for_llm": "CANCEL", "category": "system_control", "display_name": "取消", "en_display_name": "Cancel", "desc": "Completely terminate the current behavior (such as dancing, nodding, navigating, speaking, moving, sending messages). Unlike pause, cancel means a full termination rather than a temporary suspension.", "desc_chinese": "取消当前行为（如跳舞、点头、导航、说话、移动、发送消息），与pause不同，cancel为完全终止而非暂停。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CANCEL", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "取消", "zh_TW": "取消", "zh_GD": "取消", "en_US": "Cancel", "ja_JP": "キャンセル", "th_TH": "ยกเลิก", "de_DE": "Abbrechen", "es_ES": "<PERSON><PERSON><PERSON>", "ko_KR": "취소", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Peruuta", "nb_NO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Annuler", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Отменить", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Anulează", "ms_MY": "<PERSON><PERSON>", "vi_VN": "Hủy bỏ", "id_ID": "<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "Zrušit", "el_GR": "Ακύρωση", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "M<PERSON>gs<PERSON>", "tr_TR": "İptal", "sk_SK": "Zrušiť", "ar_SA": "إلغاء"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "EXIT", "name_for_llm": "EXIT", "category": "system_control", "display_name": "退出", "en_display_name": "Exit", "desc": "Exit and completely close the current application. Different from BACK which returns to previous screen within same application.", "desc_chinese": "彻底关闭当前应用，区别于back，exit不会返回上一级界面。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.EXIT", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出", "zh_TW": "退出", "zh_GD": "退出", "en_US": "Exit", "ja_JP": "終了", "ko_KR": "종료", "th_TH": "ออก", "de_DE": "<PERSON>den", "es_ES": "Salir", "fr_FR": "<PERSON><PERSON><PERSON>", "da_DK": "A<PERSON>lut", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Avslutt", "nl_NL": "Afsluiten", "ru_RU": "Выход", "pl_PL": "Wyjście", "pt_PT": "<PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Ieșire", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "Lu<PERSON><PERSON>", "cs_CZ": "Konec", "el_GR": "Έξοδος", "pt_BR": "<PERSON><PERSON>", "hu_HU": "Kilépés", "tr_TR": "Çıkış", "sk_SK": "Koniec", "ar_SA": "الخروج"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "BACK", "name_for_llm": "BACK", "category": "system_control", "display_name": "返回上一级", "en_display_name": "Back", "desc": "Return to the previous screen or menu within the current application. Unlike exit, back does not completely close the application.", "desc_chinese": "返回当前应用的上一级界面或菜单，不同于exit，back不会退出应用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.BACK", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "返回上一级", "zh_TW": "返回上一級", "zh_GD": "返回上一級", "en_US": "Back", "ja_JP": "戻る", "ko_KR": "뒤로", "th_TH": "กลับ", "de_DE": "Zurück", "es_ES": "Atrás", "fr_FR": "Retour", "da_DK": "Tilbage", "sv_SE": "Tillbaka", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Tilbake", "nl_NL": "Terug", "ru_RU": "Назад", "pl_PL": "Wstecz", "pt_PT": "Voltar", "it_IT": "Indietro", "ro_RO": "Înapoi", "ms_MY": "Kembali", "vi_VN": "Quay lại", "id_ID": "Kembali", "fil_PH": "Bumalik", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Πίσω", "pt_BR": "Voltar", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Späť", "ar_SA": "الرجوع"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "NEXT", "name_for_llm": "NEXT", "category": "system_control", "display_name": "下一步", "en_display_name": "Next", "desc": "Proceed to the next step in a multi-step process or presentation. Use in guided tutorials, presentations, or sequential operations.", "desc_chinese": "在多步骤流程或演示中前进到下一步。如：讲解导览中下一个地点等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.NEXT", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "下一步", "zh_TW": "下一步", "zh_GD": "下一步", "en_US": "Next", "ja_JP": "次へ", "ko_KR": "다음", "th_TH": "ถัดไป", "de_DE": "<PERSON><PERSON>", "es_ES": "Siguient<PERSON>", "fr_FR": "Suivant", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Neste", "nl_NL": "Volgende", "ru_RU": "Далее", "pl_PL": "Następny", "pt_PT": "Próximo", "it_IT": "Successivo", "ro_RO": "Următorul", "ms_MY": "Seterusnya", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> theo", "id_ID": "Selanjutnya", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Dalš<PERSON>", "el_GR": "Επόμενο", "pt_BR": "Próximo", "hu_HU": "Következő", "tr_TR": "İleri", "sk_SK": "Ďalší", "ar_SA": "التالي"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CONFIRM", "name_for_llm": "CONFIRM", "category": "system_control", "display_name": "确认", "en_display_name": "Confirm", "desc": "User confirms the current action or selection, such as saying 'Confirm', 'OK', or 'No problem', based on the conversation context.", "desc_chinese": "用户确认当前操作或选择，如“确认”“确定”“好的”“没问题”“可以”“提交”等，必要时需结合对话历史判断。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CONFIRM", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "确认", "zh_TW": "確認", "zh_GD": "確認", "en_US": "Confirm", "ja_JP": "確認", "ko_KR": "확인", "th_TH": "ยืนยัน", "de_DE": "Bestätigen", "es_ES": "Confirmar", "fr_FR": "Confirmer", "da_DK": "Bekræft", "sv_SE": "Bekräfta", "fi_FI": "Vahvista", "nb_NO": "Bekreft", "nl_NL": "Bevestigen", "ru_RU": "Подтвердить", "pl_PL": "Potwierdź", "pt_PT": "Confirmar", "it_IT": "Conferma", "ro_RO": "Confirmă", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "Potvrdit", "el_GR": "Επιβεβαίωση", "pt_BR": "Confirmar", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Potvrdiť", "ar_SA": "تأكيد"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "PLAY", "name_for_llm": "PLAY", "category": "system_control", "display_name": "播放", "en_display_name": "Play", "desc": "Start playing any media or interactive content. For example, play a video or start the guided tour at the current point.", "desc_chinese": "开始播放任何媒体或交互内容。例如播放视频或者导览讲解中开始讲解当前的点。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MULTIMEDIA_PLAY", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "播放", "zh_TW": "播放", "zh_GD": "播放", "en_US": "Play", "ja_JP": "再生", "th_TH": "เล่น", "de_DE": "Abspielen", "es_ES": "Reproducir", "ko_KR": "재생", "da_DK": "Afspil", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "Toista", "nb_NO": "Spill av", "fr_FR": "<PERSON><PERSON>", "nl_NL": "Afspelen", "ru_RU": "Воспроизвести", "pl_PL": "Odtwórz", "pt_PT": "Reproduzir", "it_IT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ro_RO": "Redare", "ms_MY": "Main", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Putar", "fil_PH": "I-play", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Αναπαραγωγή", "pt_BR": "Reproduzir", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Prehrať", "ar_SA": "تشغيل"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "RESUME", "name_for_llm": "RESUME", "category": "system_control", "display_name": "继续服务", "en_display_name": "Resume Service", "desc": "Resume or continue the service, such as resuming playback or continuing the guided tour.", "desc_chinese": "恢复或继续服务，例如继续播放、继续讲解等场景。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MULTIMEDIA_PLAY", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "继续服务", "zh_TW": "繼續服務", "zh_GD": "繼續服務", "en_US": "Resume Service", "ja_JP": "サービス再開", "th_TH": "เดินหน้าการบริการ", "de_DE": "Service fortsetzen", "es_ES": "<PERSON><PERSON><PERSON> servicio", "ko_KR": "서비스 재개", "da_DK": "Genoptag service", "sv_SE": "Återuppta service", "fi_FI": "Jatka palvelua", "nb_NO": "Gjenoppta tje<PERSON>", "fr_FR": "Reprendre le service", "nl_NL": "Service hervatten", "ru_RU": "Возобновить сервис", "pl_PL": "Wznów usługę", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON><PERSON> servizio", "ro_RO": "Reluarea serviciului", "ms_MY": "Sambung perkhidmatan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> d<PERSON> vụ", "id_ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "fil_PH": "Ipagpatuloy ang serbisyo", "cs_CZ": "Pokračovat ve službě", "el_GR": "Συνέχιση υπηρεσίας", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Szolgáltatás folytatása", "tr_TR": "Hizmeti devam ettir", "sk_SK": "Pokračovať v službe", "ar_SA": "استئناف الخدمة"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "PAUSE", "name_for_llm": "PAUSE", "category": "guide", "display_name": "暂停", "en_display_name": "Pause", "desc": "Pause the current service. Users may want to pause actions such as video playback or movement. Common phrases include: 'Don’t go', 'Stay still', 'Wait for me', or 'Hold on'.", "desc_chinese": "暂定当前服务。用户希望暂停某个动作，例如「暂停播放视频」或者「暂停移动」等。常见表达如：“别走了”“别动了”“等下我”或“等一等”等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COMMON_PAUSE", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "暂停", "zh_TW": "暫停", "zh_GD": "暫停", "en_US": "Pause", "ja_JP": "一時停止", "th_TH": "หยุดชั่วคราว", "de_DE": "Pausieren", "es_ES": "Pausar", "ko_KR": "일시 정지", "da_DK": "Pause", "sv_SE": "Pausa", "fi_FI": "Keskeytä", "nb_NO": "Pause", "fr_FR": "Pause", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Пауза", "pl_PL": "<PERSON><PERSON>", "pt_PT": "Pausar", "it_IT": "Pausa", "ro_RO": "Pauză", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON>", "fil_PH": "I-pause", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Παύση", "pt_BR": "Pausar", "hu_HU": "Szünet", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Pozastaviť", "ar_SA": "إيقاف مؤقت"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "REPEAT_TOUR_EXPLANATION", "name_for_llm": "REPEAT_TOUR_EXPLANATION", "category": "guide", "display_name": "重新讲解", "en_display_name": "Repeat", "desc": "Completely restart a guided tour explanation from the beginning, typically after reaching the scoring/feedback phase.", "desc_chinese": "从头开始完全重新进行导览讲解，通常在达到评分/反馈阶段后使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COMMON_REPEAT", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "重新讲解", "zh_TW": "重新講解", "zh_GD": "重新講解", "en_US": "Repeat", "ja_JP": "再説明", "ko_KR": "다시 설명", "th_TH": "อธิบายซ้ำ", "de_DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON>r", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "da_DK": "Gentag", "sv_SE": "Upprepa", "fi_FI": "Toista", "nb_NO": "<PERSON><PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Повторить", "pl_PL": "Powtórz", "pt_PT": "<PERSON><PERSON>r", "it_IT": "R<PERSON><PERSON>", "ro_RO": "Repetă", "ms_MY": "Ulang", "vi_VN": "Lặp lại", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Opakovat", "el_GR": "Επαναλάβετε", "pt_BR": "<PERSON><PERSON>r", "hu_HU": "Ismételje meg", "tr_TR": "Tekrarla", "sk_SK": "Opakovať", "ar_SA": "إعادة الشرح"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SWITCH_GUIDE_ROUTE", "name_for_llm": "SWITCH_GUIDE_ROUTE", "category": "guide", "display_name": "选择其他导览路线", "en_display_name": "Choose Another Route", "desc": "Switch or choose to a different guided tour route from the current one. Use when user wants to choose or ask for another route.", "desc_chinese": "选择其他导览路线，用户想要选择或者询问其他路线时使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ROUTES_OTHERS", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "选择其他导览路线", "zh_TW": "選擇其他導覽路線", "zh_GD": "選擇其他導覽路線", "en_US": "Choose Another Route", "ja_JP": "別のルートを選択", "ko_KR": "다른 루트 선택", "th_TH": "เลือกเส้นทางอื่น", "de_DE": "Andere Route wählen", "es_ES": "Elegir otra ruta", "fr_FR": "Choisir un autre itinéraire", "da_DK": "<PERSON><PERSON><PERSON><PERSON> anden rute", "sv_SE": "<PERSON><PERSON><PERSON><PERSON> rutt", "fi_FI": "Valitse toinen reitti", "nb_NO": "Velg annen rute", "nl_NL": "Kies andere route", "ru_RU": "Выбрать другой маршрут", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> inną trasę", "pt_PT": "Escolher outra rota", "it_IT": "<PERSON><PERSON><PERSON> altro percorso", "ro_RO": "Alege altă rută", "ms_MY": "<PERSON><PERSON><PERSON> la<PERSON>an lain", "vi_VN": "<PERSON><PERSON><PERSON> tuy<PERSON>n kh<PERSON>c", "id_ID": "<PERSON>lih rute lain", "fil_PH": "<PERSON><PERSON><PERSON> ang ibang ruta", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON> jinou trasu", "el_GR": "Επιλέξτε άλλη διαδρομή", "pt_BR": "Escolher outra rota", "hu_HU": "Válasszon másik útvonalat", "tr_TR": "Başka rota seç", "sk_SK": "Vybrať inú trasu", "ar_SA": "اختيار مسار آخر"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RATE_EXPERIENCE", "name_for_llm": "RATE_EXPERIENCE", "category": "guide", "display_name": "打分", "en_display_name": "Score", "desc": "Rate an experience or service on a scale of 1-5 stars. Used for collecting user feedback after completing a guided tour or interaction.", "desc_chinese": "对体验或服务进行1-5星评分。用于在完成导览或交互后收集用户反馈。", "parameters": [{"name": "score", "type": "int", "desc": "Rating value from 1-5, where 5 represents highest satisfaction.", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SCORE", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打分", "zh_TW": "打分", "zh_GD": "打分", "en_US": "Score", "ja_JP": "評価", "ko_KR": "점수", "th_TH": "ให้คะแนน", "de_DE": "Bewerten", "es_ES": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Noter", "da_DK": "Score", "sv_SE": "Poäng", "fi_FI": "Pisteet", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Score", "ru_RU": "Оценка", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "Pontuação", "it_IT": "<PERSON><PERSON><PERSON><PERSON>", "ro_RO": "<PERSON><PERSON>", "ms_MY": "Skor", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "Skor", "fil_PH": "Iskor", "cs_CZ": "Skóre", "el_GR": "Βαθμολογία", "pt_BR": "Pontuação", "hu_HU": "Pontszám", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Skóre", "ar_SA": "تقييم"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ADJUST_WALKING_SPEED", "name_for_llm": "ADJUST_WALKING_SPEED", "category": "system_control", "display_name": "调整当前移动速度", "en_display_name": "Adjust Walking Speed", "desc": "Adjust the <current walking speed>. range: 0.1-1.2m/s. Note: If the speed exceeds the range, you can use `say_for_clarification` tool to reject the user.", "desc_chinese": "调整当前移动速度，范围0.1-1.2m/s，超出请用`say_for_clarification`动作拒绝。", "parameters": [{"name": "adjusted_speed", "type": "float", "desc": "The robot's walking speed, range: 0.1-1.2m/s.", "is_required": true, "length": null, "enum_constant": null, "max": 1.2, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ADJUST_SPEED", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调整当前移动速度", "zh_TW": "調整當前移動速度", "zh_GD": "調整當前移動速度", "en_US": "Adjust Walking Speed", "ja_JP": "歩行速度調整", "ko_KR": "보행 속도 조정", "th_TH": "ปรับความเร็วในการเดิน", "de_DE": "Gehgeschwindigkeit anpassen", "es_ES": "Ajustar velocidad de caminar", "fr_FR": "Ajuster la vitesse de marche", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Säädä kä<PERSON>a", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Loopsnelheid a<PERSON>en", "ru_RU": "Настроить скорость ходьбы", "pl_PL": "Dostosuj pręd<PERSON>ć chodzenia", "pt_PT": "Ajustar velocidade de caminhada", "it_IT": "Regola velocità camminata", "ro_RO": "Ajustează viteza de mers", "ms_MY": "<PERSON><PERSON>", "vi_VN": "Điều chỉnh tốc độ đi bộ", "id_ID": "Sesuaikan kecepatan jalan", "fil_PH": "<PERSON><PERSON><PERSON> ang bilis ng paglalakad", "cs_CZ": "Upravit rychlost chůze", "el_GR": "Ρύθμιση ταχύτητας βαδίσματος", "pt_BR": "Ajustar velocidade de caminhada", "hu_HU": "<PERSON><PERSON><PERSON> se<PERSON>g be<PERSON>llí<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON><PERSON><PERSON> hı<PERSON>ı<PERSON>ı a<PERSON>la", "sk_SK": "Upraviť rýchlosť chôdze", "ar_SA": "ضبط سرعة المشي"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GENERATE_WELCOME_FAREWELL_MESSAGE", "name_for_llm": "GENERATE_WELCOME_FAREWELL_MESSAGE", "category": "interaction", "display_name": "生成欢迎语/欢送语", "en_display_name": "Generate", "desc": "Generate welcome/farewell messages. Used when welcoming guests or saying goodbye.", "desc_chinese": "生成欢迎语/欢送语，一般在欢迎来客时使用，或者在送别时使用。", "parameters": [], "execute_side": "server", "execute_timeout_limit": 90, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GENERATE_MESSAGE", "version": "oversea_v1.0.6", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "生成", "zh_TW": "生成", "zh_GD": "生成", "en_US": "Generate", "ja_JP": "生成", "th_TH": "สร้าง", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "Generar", "ko_KR": "생성", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "<PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Сгенерировать", "pl_PL": "Generuj", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Genera", "ro_RO": "Generează", "ms_MY": "<PERSON>", "vi_VN": "Tạo ra", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Generovat", "el_GR": "Δημιουργία", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Gener<PERSON><PERSON><PERSON>", "tr_TR": "Oluştur", "sk_SK": "Generovať", "ar_SA": "إنشاء رسالة الترحيب والوداع"}, "execute_function": "generate_message"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "GO_CHARGING", "name_for_llm": "GO_CHARGING", "category": "system_admin", "display_name": "去充电", "en_display_name": "Go to Charging", "desc": "Instruct the robot to go to the charging station and start charging. Consider the current battery level if needed.", "desc_chinese": "要求机器人前往充电桩进行充电，必要时需结合当前电量判断。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GO_CHARGING", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "去充电", "zh_TW": "去充電", "zh_GD": "去充電", "en_US": "Go to Charging", "ja_JP": "充電に行く", "ko_KR": "충전하러 가기", "th_TH": "ไปชาร์จ", "de_DE": "Zum Laden gehen", "es_ES": "Ir a cargar", "fr_FR": "Aller charger", "da_DK": "Gå til opladning", "sv_SE": "Gå till laddning", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Gå til lading", "nl_NL": "Ga laden", "ru_RU": "Иди заряжаться", "pl_PL": "<PERSON><PERSON><PERSON> ładować", "pt_PT": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Vai a caricare", "ro_RO": "Du-te să te încarci", "ms_MY": "<PERSON><PERSON> men<PERSON>", "vi_VN": "<PERSON><PERSON>", "id_ID": "<PERSON><PERSON> mengisi daya", "fil_PH": "Pumunta sa pag-charge", "cs_CZ": "<PERSON><PERSON><PERSON>", "el_GR": "Πήγαινε για φόρτιση", "pt_BR": "<PERSON><PERSON><PERSON><PERSON>", "hu_HU": "<PERSON><PERSON>", "tr_TR": "Şarj olma<PERSON> git", "sk_SK": "Ísť sa nabiť", "ar_SA": "الذهاب للشحن"}}, {"namespace": "orion.agent.action", "level": "apk", "source": "builtin", "name": "SET_MAP_LOCATION", "name_for_llm": "SET_MAP_LOCATION", "category": "system_admin", "display_name": "设置位置", "en_display_name": "Set Location", "desc": "Add or modify a location point on the map for navigation and positioning reference.", "desc_chinese": "新增或修改地图上的位置点(点位)，方便机器人导航和定位。", "parameters": [{"name": "location", "type": "string", "desc": "The name of the location to set.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.maptool"], "client_alias": "orion.agent.action.SET_LOCATION", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "设置位置", "zh_TW": "設置位置", "zh_GD": "設置位置", "en_US": "Set Location", "ja_JP": "位置設定", "ko_KR": "위치 설정", "th_TH": "ตั้งค่าตำแหน่ง", "de_DE": "Standort festlegen", "es_ES": "Establecer ubicación", "fr_FR": "Définir l'emplacement", "da_DK": "Indstil placering", "sv_SE": "<PERSON><PERSON><PERSON> in plats", "fi_FI": "<PERSON><PERSON> si<PERSON>i", "nb_NO": "<PERSON><PERSON> p<PERSON>", "nl_NL": "Locatie instellen", "ru_RU": "Установить местоположение", "pl_PL": "Ustaw lokalizację", "pt_PT": "Definir localização", "it_IT": "Imposta posizione", "ro_RO": "Setați locația", "ms_MY": "Tetapkan lokasi", "vi_VN": "Đặt vị trí", "id_ID": "Atur lokasi", "fil_PH": "Itakda ang lokasyon", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Ορισμός τοποθεσίας", "pt_BR": "Definir localização", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Nastaviť polohu", "ar_SA": "تحديد الموقع"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ANSWER_KNOWLEDGE_QUESTION", "name_for_llm": "ANSWER_KNOWLEDGE_QUESTION", "category": "information", "display_name": "找答案", "en_display_name": "Find Answer", "desc": "Professionally answer user questions about company products, employee information by querying the knowledge base.", "desc_chinese": "需要查询知识库来回答用户的问题，如「公司产品」、「员工信息」等。", "parameters": [{"name": "question", "type": "string", "desc": "Based on conversation context, summarize the user's specific question that needs knowledge-based answers.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 600, "result_schema": [{"name": "knowledge_content", "desc": "The knowledge content.", "type": "string", "enum_constant": null}], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.KNOWLEDGE_QA", "version": "oversea_v1.0.6", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "找答案", "zh_TW": "找答案", "zh_GD": "找答案", "en_US": "Find Answer", "ja_JP": "答えを見つける", "ko_KR": "답 찾기", "th_TH": "หาคำตอบ", "de_DE": "Antwort finden", "es_ES": "Encontrar respuesta", "fr_FR": "Trouver la réponse", "da_DK": "Find svar", "sv_SE": "<PERSON><PERSON> svar", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON> svar", "nl_NL": "Antwoord vinden", "ru_RU": "Найти ответ", "pl_PL": "Znajdź odpowiedź", "pt_PT": "Encontrar resposta", "it_IT": "<PERSON><PERSON><PERSON> ris<PERSON>a", "ro_RO": "Găsiți răspunsul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> c<PERSON>u trả lời", "id_ID": "<PERSON><PERSON>n", "fil_PH": "<PERSON><PERSON><PERSON> ang sagot", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Βρείτε την απάντηση", "pt_BR": "Encontrar resposta", "hu_HU": "V<PERSON>lasz k<PERSON>ése", "tr_TR": "Cevap bul", "sk_SK": "Nájsť odpoveď", "ar_SA": "الإجابة على سؤال المعرفة"}, "execute_function": "knowledge_qa"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CHANGE_FACIAL_EXPRESSION", "name_for_llm": "CHANGE_FACIAL_EXPRESSION", "category": "entertainment", "display_name": "换脸", "en_display_name": "Change Face", "desc": "Change robot's facial expression or display different emotions on screen.", "desc_chinese": "切换机器人的面部表情。当用户要求机器人改变表情、展示不同情绪时使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_01917709e2d711f26014d0f1e78295fb"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CHANGE_FACE", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "换脸", "zh_TW": "換臉", "zh_GD": "換臉", "en_US": "Change Face", "ja_JP": "顔を変える", "ko_KR": "얼굴 바꾸기", "th_TH": "เปลี่ยนหน้า", "de_DE": "Gesicht wechseln", "es_ES": "Cambiar cara", "fr_FR": "Changer de visage", "da_DK": "<PERSON><PERSON> ansigt", "sv_SE": "Byt ansikte", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Gezicht veranderen", "ru_RU": "Сменить лицо", "pl_PL": "Zmień twarz", "pt_PT": "<PERSON>dar rosto", "it_IT": "Cambia faccia", "ro_RO": "Schimbați fața", "ms_MY": "<PERSON><PERSON> wajah", "vi_VN": "Đổi mặt", "id_ID": "Ganti wajah", "fil_PH": "<PERSON><PERSON><PERSON> ang mukha", "cs_CZ": "Změnit obličej", "el_GR": "Αλλαγ<PERSON> προσώπου", "pt_BR": "Trocar rosto", "hu_HU": "<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Zmeniť tvár", "ar_SA": "تغيير تعبير الوجه"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CLICK_WEB_ELEMENT", "name_for_llm": "CLICK_WEB_ELEMENT", "category": "web_browser", "display_name": "点击", "en_display_name": "Click", "desc": "Simulate a click action on clickable elements in the current web page being displayed.", "desc_chinese": "模拟在当前显示的网页上点击可点击元素。仅在用户明确要点击网页上某个元素时使用。", "parameters": [{"name": "element_tag", "type": "enum", "desc": "可点击元素的标签", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_clickable_elements_value"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CLICK", "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "点击", "zh_TW": "點擊", "zh_GD": "點擊", "en_US": "Click", "ja_JP": "クリック", "ko_KR": "클릭", "th_TH": "คลิก", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON> clic", "fr_FR": "C<PERSON>r", "da_DK": "Klik", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Klikk", "nl_NL": "Klikken", "ru_RU": "Щелчок", "pl_PL": "<PERSON><PERSON><PERSON><PERSON>", "pt_PT": "Clique", "it_IT": "Fare clic", "ro_RO": "Click", "ms_MY": "Klik", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "Klik", "fil_PH": "I-click", "cs_CZ": "Kliknout", "el_GR": "Κάντε κλικ", "pt_BR": "Clique", "hu_HU": "Kattintás", "tr_TR": "Tıkla", "sk_SK": "Kliknite", "ar_SA": "اضغط"}, "post_processing": "convert_element_id"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ANSWER_VISUAL_QUESTION", "name_for_llm": "ANSWER_VISUAL_QUESTION", "category": "information", "display_name": "根据视觉回答", "en_display_name": "Answer from Vision", "desc": "Only answer questions about clothing, facial expressions, gender, surroundings, and object recognition through the robot's camera. Topics involving user relationships are not supported.", "desc_chinese": "通过机器人摄像头，仅回答关于人物穿着、表情、性别，周边环境和物体识别的问题，不支持涉及用户关系的话题。", "parameters": [{"name": "image_url", "type": "string", "desc": "The image URL captured by the robot's camera.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "question", "type": "string", "desc": "The user's question, must be summarized as a first-person question, with a length limit of 15 characters.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The answer of the question", "type": "string", "enum_constant": null}], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ANSWER_QUESTION_FROM_VISION", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "根据视觉回答", "zh_TW": "根據視覺回答", "zh_GD": "根據視覺回答", "en_US": "Answer from Vision", "ja_JP": "視覚から回答", "ko_KR": "시각으로 답변", "th_TH": "ตอบจากการมองเห็น", "de_DE": "Antwort aus Sicht", "es_ES": "Responder desde visión", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON> depuis la vision", "da_DK": "<PERSON>var fra syn", "sv_SE": "<PERSON><PERSON><PERSON> från syn", "fi_FI": "Vastaa näöstä", "nb_NO": "<PERSON>var fra syn", "nl_NL": "Ant<PERSON><PERSON> van zicht", "ru_RU": "Ответить по зрению", "pl_PL": "Odpowiedz z wizji", "pt_PT": "Responder da visão", "it_IT": "<PERSON><PERSON><PERSON><PERSON> dalla visione", "ro_RO": "Răspunde din vedere", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> lời từ thị gi<PERSON>c", "id_ID": "<PERSON><PERSON><PERSON> da<PERSON>", "fil_PH": "Sumagot mula sa paningin", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t ze zraku", "el_GR": "Απάντηση από όραση", "pt_BR": "Responder da visão", "hu_HU": "Válaszolj a látásból", "tr_TR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cevap ver", "sk_SK": "Odpovedať zo zraku", "ar_SA": "الإجابة من الرؤية"}, "execute_function": "answer_question_from_vision"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REALTIME_SAY", "name_for_llm": "REALTIME_SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Think about it.", "desc_chinese": "思考说", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "stream_say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CALL_LLM", "name_for_llm": "CALL_LLM", "category": "interaction", "display_name": "调用llm", "en_display_name": "Call LLM", "desc": "Call llm to get the result.", "desc_chinese": "调用llm获取结果", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [{"name": "llm_response", "desc": "The response from llm", "type": "dict", "enum_constant": null}], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.6", "exported": false, "pre_execute": false, "hidden": true, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "call_llm"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OUTDOOR_NAVIGATE", "name_for_llm": "OUTDOOR_NAVIGATE", "category": "navigation", "display_name": "室外导航", "en_display_name": "Outdoor Navigation", "desc": "Provide outdoor navigation capability. Select this action when the user wants to know how to reach a place.", "desc_chinese": "", "parameters": [{"name": "origin", "type": "string", "desc": "Route origin, provided by the user. Set the value to \"-1\" if indeterminate.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "destination", "type": "string", "desc": "Route destination. Provided by the user", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "travelmode", "type": "enum", "desc": "Default to driving if not provided by the user.", "is_required": true, "length": null, "enum_constant": ["driving", "walking", "bicycling", "transit"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "oversea", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "室外导航", "zh_TW": "室外導航", "zh_GD": "室外導航", "en_US": "Outdoor Navigation", "ja_JP": "屋外ナビゲーション", "ko_KR": "실외 내비게이션", "th_TH": "การนำทางกลางแจ้ง", "de_DE": "Außennavigation", "es_ES": "Navegación exterior", "fr_FR": "Navigation extérieure", "da_DK": "Udendørs navigation", "sv_SE": "Utomhusnavigering", "fi_FI": "Ulkonavigaatio", "nb_NO": "Utendørs navigasjon", "nl_NL": "Buitennavigatie", "ru_RU": "Наружная навигация", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>nętrz<PERSON>", "pt_PT": "Navegação exterior", "it_IT": "Navigazione esterna", "ro_RO": "Navigație exterioară", "ms_MY": "Navigasi luar", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> hướng ngoài trời", "id_ID": "<PERSON><PERSON><PERSON><PERSON> luar ruangan", "fil_PH": "Outdoor navigation", "cs_CZ": "Venkovní navigace", "el_GR": "Εξωτερική πλοήγηση", "pt_BR": "Navegação exterior", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Dış mekan navigasyon", "sk_SK": "Vonkajšia navigácia", "ar_SA": "الملاحة الخارجية"}, "post_processing": "overseas_map_dir_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RECOMMEND_PLACES", "name_for_llm": "RECOMMEND_PLACES", "category": "other", "display_name": "推荐", "en_display_name": "Recommend", "desc": "Recommendations for various places, such as restaurants, attractions, shopping areas, bars, KTVs, subways, etc", "desc_chinese": "", "parameters": [{"name": "target", "type": "string", "desc": "The target location that the user wants to go to", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "ref_location", "type": "string", "desc": "The starting point. Search based on this location. Don't use <Current Indoor Point>. If not specificed by user, set ref_location -1)", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.6", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "oversea", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "推荐", "zh_TW": "推薦", "zh_GD": "推薦", "en_US": "Recommend", "ja_JP": "推奨", "ko_KR": "추천", "th_TH": "แนะนำ", "de_DE": "Empfehlen", "es_ES": "Recomendar", "fr_FR": "Recommander", "da_DK": "Anbefal", "sv_SE": "Re<PERSON>mmender<PERSON>", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Anbefal", "nl_NL": "Aanbevelen", "ru_RU": "Рекомендовать", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "Recomendar", "it_IT": "Ra<PERSON>mand<PERSON>", "ro_RO": "Recomandă", "ms_MY": "Cadangkan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị", "id_ID": "Rekomendasikan", "fil_PH": "Irekomenda", "cs_CZ": "Doporučit", "el_GR": "Συστήνω", "pt_BR": "Recomendar", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Önermek", "sk_SK": "Odporučiť", "ar_SA": "توصية"}, "post_processing": "overseas_map_search_url"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "GUIDE_INDOOR_NAVIGATION", "name_for_llm": "GUIDE_INDOOR_NAVIGATION", "category": "navigation", "display_name": "准备领位", "en_display_name": "Indoor Navigation Recommendation", "desc": "Point navigation intent. Users can only be taken to the locations provided below, and outdoor locations are not supported.If the user does not explicitly specify the destination, a list of up to 4 available navigation points is returned and sorted by similarity, with the closest point at the front.", "desc_chinese": "带领用户前往室内特定位置；结合对话历史和用户特征（如性别等），为用户精准推荐最符合其意图的地点。不支持室外导航！ 用户想要去室外时，请用`say_for_clarification`拒绝。", "parameters": [{"name": "destinations", "type": "String array", "desc": "Navigation points that match user intent. You can choose multiple. only choose from the list of points.", "is_required": false, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_robot_support_map_points"}, {"name": "guide_text", "type": "string", "desc": "A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 300, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.NAVIGATE_REC_START", "version": "oversea_v1.0.6", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "准备领位", "zh_TW": "準備領位", "zh_GD": "準備領位", "en_US": "Indoor Navigation Recommendation", "ja_JP": "屋内ナビゲーション推奨", "ko_KR": "실내 내비게이션 추천", "th_TH": "คำแนะนำการนำทางในร่ม", "de_DE": "Innennavigationsempfehlung", "es_ES": "Recomendación navegación interior", "fr_FR": "Recommandation navigation intérieure", "da_DK": "Indendørs navigationsanbefaling", "sv_SE": "Inomhusnavigeringsrekommendation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Innendørs navigasjonsanbefaling", "nl_NL": "Binnennavigatie a<PERSON>", "ru_RU": "Рекомендация внутренней навигации", "pl_PL": "Rekomendacja nawigacji wewnętrznej", "pt_PT": "Recomendação navegação interior", "it_IT": "Raccomandazione navigazione interna", "ro_RO": "Recomandare navigație interioară", "ms_MY": "Cadangan navigasi dalam ruangan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị điều hướng trong nhà", "id_ID": "Rekomendasi navigasi dalam ruangan", "fil_PH": "Rekomendasyon ng indoor navigation", "cs_CZ": "Doporučení vnitřní navigace", "el_GR": "Σύσταση εσωτερικής πλοήγησης", "pt_BR": "Recomendação navegação interior", "hu_HU": "<PERSON><PERSON><PERSON> na<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "İç mekan navigasyon önerisi", "sk_SK": "Odporúčanie vnútornej navigácie", "ar_SA": "توصية التنقل الداخلي"}, "post_processing": "convert_navigation_points"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL_DEFINED", "name_for_llm": "OPEN_WEB_URL_DEFINED", "category": "web_browser", "display_name": "打开轻应用", "en_display_name": "Open Light App", "desc": "Use a browser or app to browse information. Trigger this action if there's a semantically identical question in the predefined list, as it allows you to directly open the URL or app.", "desc_chinese": "使用浏览器或者APP浏览信息。*只要*预定义问题列表中存在与用户当前问题语义相同的问题，则选择此action。", "parameters": [{"name": "predefined_question", "type": "enum", "desc": "list of predefined question.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_light_app_enum"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.6", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": true, "page_id": "", "display_names": {"zh_CN": "打开轻应用", "zh_TW": "打開輕應用", "zh_GD": "打開輕應用", "en_US": "Open Light App", "ja_JP": "ライトアプリを開く", "ko_KR": "라이트 앱 열기", "th_TH": "เปิดแอปพลิเคชันเบา", "de_DE": "Light App <PERSON>", "es_ES": "Abrir aplicación ligera", "fr_FR": "Ouvrir application légère", "da_DK": "Åbn let app", "sv_SE": "Öppna lätt app", "fi_FI": "<PERSON><PERSON> kevyt sovellus", "nb_NO": "Å<PERSON>ne lett app", "nl_NL": "Lichte app openen", "ru_RU": "Открыть легкое приложение", "pl_PL": "O<PERSON><PERSON><PERSON><PERSON> aplikację", "pt_PT": "Abrir aplicação leve", "it_IT": "Apri app leggera", "ro_RO": "Deschide aplicația ușoară", "ms_MY": "<PERSON><PERSON> a<PERSON><PERSON>an", "vi_VN": "Mở ứng dụng nhẹ", "id_ID": "<PERSON><PERSON> a<PERSON><PERSON>an", "fil_PH": "Buksan ang light app", "cs_CZ": "Otev<PERSON><PERSON><PERSON> aplikaci", "el_GR": "Άνοιγμα ελαφριάς εφαρμογής", "pt_BR": "Abrir aplicativo leve", "hu_HU": "Könnyű alkalmazás megnyitása", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Otvoriť ľahkú aplikáciu", "ar_SA": "فتح التطبيق الخفيف"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GET_WEATHER", "name_for_llm": "GET_WEATHER", "category": "weather", "display_name": "获取天气信息", "en_display_name": "Get Weather Information", "desc": "Get weather information for today and the next 10 days. City name must be in English.", "desc_chinese": "获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！!!", "parameters": [{"name": "area_level", "type": "enum", "desc": "The level of the area corresponding to the city. province, city, or area.", "is_required": false, "length": null, "enum_constant": ["province", "city", "area"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "valid_city_name", "type": "string", "desc": "Enter a valid city name (not a venue); defaults to the current city if not specified.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_a8c9098ad7a91a66287b25d6befef6ec", "system_4f307c6d4cb0f187a3edb3dcc6f43749", "system_024280e32e73c00ad621710870d4cb18"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.WEATHER", "version": "oversea_v1.0.6", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "获取天气信息", "zh_TW": "獲取天氣資訊", "zh_GD": "獲取天氣資訊", "en_US": "Get Weather Information", "ja_JP": "天気情報取得", "ko_KR": "날씨 정보 얻기", "th_TH": "รับข้อมูลสภาพอากาศ", "de_DE": "Wetterinformationen abrufen", "es_ES": "Obtener información del tiempo", "fr_FR": "Obtenir informations météo", "da_DK": "Få vejrinformation", "sv_SE": "Få väderinformation", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Få værinformasjon", "nl_NL": "Weerinformatie ophalen", "ru_RU": "Получить информацию о погоде", "pl_PL": "Uzyskaj informacje o pogodzie", "pt_PT": "Obter informações meteorológicas", "it_IT": "Ottieni informazioni meteo", "ro_RO": "Obține informații meteo", "ms_MY": "Dapatkan maklumat cuaca", "vi_VN": "<PERSON><PERSON><PERSON> thông tin thời tiết", "id_ID": "Dapatkan informasi cuaca", "fil_PH": "Makakuha ng impormasyon ng panahon", "cs_CZ": "Získat informace o počasí", "el_GR": "Λήψη πληροφοριών καιρού", "pt_BR": "Obter informações meteorológicas", "hu_HU": "Időjárási információk lekérése", "tr_TR": "Hava durumu bilgisi al", "sk_SK": "Získať informácie o počasí", "ar_SA": "الحصول على معلومات عن الطقس"}, "execute_function": "query_weather", "post_processing": "query_weather_post_processing"}]}