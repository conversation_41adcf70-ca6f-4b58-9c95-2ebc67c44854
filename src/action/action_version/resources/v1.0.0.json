{"version": "v1.0.0", "created_at": "2025-04-16 22:07:09", "actions": [{"namespace": "orion.agent.action", "level": "opk", "name": "CRUISE", "category": "other", "display_name": "巡航", "en_display_name": "", "desc": "cruise", "desc_chinese": "巡航", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "NOT_MOVE", "category": "other", "display_name": "停止移动", "en_display_name": "", "desc": "not move", "desc_chinese": "停止移动", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "COME_FAR", "category": "other", "display_name": "让路", "en_display_name": "", "desc": "Give way to the other party and stay away from them", "desc_chinese": "让路", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "NAVIGATE_START", "category": "other", "display_name": "室内导航", "en_display_name": "", "desc": "Navigate to the destination, If the destination does not exist in the enumerated values, it should never be used", "desc_chinese": "室内导航，带用户去下面提供的位置，范围200米内，强调「仅仅去某地，不涉及其他操作」，例如去会议室，前台等等", "parameters": [{"name": "destination", "type": "enum", "desc": "The destination to navigate to", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 300, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "OUTDOOR_NAVIGATE_START", "category": "other", "display_name": "室外导航", "en_display_name": "", "desc": "Navigate to the outdoor destination, If the destination does not exist in the enumerated values, it should never be used", "desc_chinese": "室外导航。驾车、步行、公交、骑行去某个地方（地铁站，商场、景点等）", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "1. 基础URL: http://api.map.baidu.com/direction?  2. 参数: a. origin: 用户指定，如果未指定出发点使用 origin=latlng:{当前位置IP}|name:{当前地点} b. destination: 用户指定 c. mode: driving(驾车), transit(公交) 默认为: transit，用户要求时用 transit 替代 d. region: 终点所在城市，用户未指定默认北京 e. 固定参数: output=html&src=webapp.baidu.openAPIdemo", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "post_processing": "build_outdoor_navigate_url"}, {"namespace": "orion.agent.action", "level": "opk", "name": "TURN_DIRECTION", "category": "other", "display_name": "转圈", "en_display_name": "", "desc": "Turn clockwise at an angle", "desc_chinese": "身体左右转动，最大旋转圈数为30圈，默认左转一圈", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to turn, default is left", "is_required": true, "length": null, "enum_constant": ["left", "right"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "angle", "type": "int", "desc": "The value of the rotation angle", "is_required": false, "length": null, "enum_constant": null, "max": 10800, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "turns", "type": "float", "desc": "Number of turns", "is_required": false, "length": null, "enum_constant": null, "max": 30, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "post_processing": "convert_turns_to_degree"}, {"namespace": "orion.agent.action", "level": "opk", "name": "HEAD_NOD", "category": "other", "display_name": "点头", "en_display_name": "", "desc": "Nod the head", "desc_chinese": "点头、鞠躬", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "START_DANCE", "category": "other", "display_name": "唱歌跳舞", "en_display_name": "", "desc": "Start dancing", "desc_chinese": "唱歌跳舞", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "REGISTER", "category": "other", "display_name": "注册", "en_display_name": "", "desc": "Register the user's information", "desc_chinese": "注册。包含姓名和人脸注册", "parameters": [{"name": "nick_name", "type": "string", "desc": "The user's nickname", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "welcome_message", "type": "string", "desc": "message to greet the user. User nicknames are not allowed", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_d5a64441247aa63b70dd8d02e3f753f0"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "MOVE_DIRECTION", "category": "other", "display_name": "移动", "en_display_name": "", "desc": "Move a certain distance in a certain direction", "desc_chinese": "前后移动/走路/退，靠近或者远离用户。单位是米，往前最多5米，往后最多1米，超过范围不执行", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to move in, select from enumerated values", "is_required": true, "length": null, "enum_constant": ["forward", "backward"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "moving_distance", "type": "float", "desc": "The distance to move, unit is '米', default is 0.1 if not specified", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "INTERVIEW_START", "category": "other", "display_name": "打开访客接待页面", "en_display_name": "", "desc": "Standard reception processes. Currently supported are interviews, meeting check-in, visitor registration", "desc_chinese": "访客接待流程，支持面试、会议签到、访客登记", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "WEATHER_GET", "category": "other", "display_name": "查询国内天气", "en_display_name": "", "desc": "Answers based on user questions combined with weather information. Get weather information for a region or city over a period of time or at a point in time. ONLY support answering weather information within **CHINA**.", "desc_chinese": "查询未来10天「中国」的天气信息，默认查询`{所在城市}`的天气信息，注意：不支持查询国外天气。", "parameters": [{"name": "area_level", "type": "enum", "desc": "city 对应的区域等级", "is_required": true, "length": null, "enum_constant": ["province", "city", "area"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "city", "type": "string", "desc": "行政区域名称", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "user_question", "type": "string", "desc": "用户的问题", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": true, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The weather of the city", "type": "string", "enum_constant": null}], "app_ids": ["system_a8c9098ad7a91a66287b25d6befef6ec"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "execute_function": "weather"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CALENDAR", "category": "other", "display_name": "查询日历", "en_display_name": "", "desc": "Supports time and date calculations and date query function, such as 'How many days until Christmas?'", "desc_chinese": "日历功能，包含日期或节假日的查询，注意：无法查询天气", "parameters": [{"name": "user_question", "type": "string", "desc": "问题。形式为'{当前时间}年的{某节日}是哪一天？',首句必须包含完整的年份。", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_17bf9cfc230d17c94a19a0dc4faa6569"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "execute_function": "calendar"}, {"namespace": "orion.agent.action", "level": "opk", "name": "GUIDE_INTRODUCTION", "category": "other", "display_name": "导览", "en_display_name": "", "desc": "Guided tour function to show users around", "desc_chinese": "导览功能，带领用户参观，再没有明确的参观目的下使用", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 1800, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "OPEN_WEB_URL", "category": "other", "display_name": "打开浏览器", "en_display_name": "", "desc": "Open specified URLs based on user semantics", "desc_chinese": "模拟浏览器访问网址。例如查询股价、新闻、景点购票，推荐使用「百度」搜索引擎；机票、火车票以及酒店查询推荐使用携程搜索（https://flights.ctrip.com/online/list/oneway-{departureCityCode}-{arrivalCityCode}?depdate={departureDate}）;公司官网等指定网站直接通过对应网址打开", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "The URL to open, Must be a legitimate https or http link.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "SET_VOLUME", "category": "other", "display_name": "调整音量", "en_display_name": "", "desc": "One way is to set the volume by a given value. The other way is to set the volume according to the current volume level combined with the user's semantics, e.g., to turn up the volume is to add 20 to the current volume level.", "desc_chinese": "调整音量。调整的幅度是10或30，根据用户语气选择", "parameters": [{"name": "volume_level", "type": "int", "desc": "The volume level to be set", "is_required": true, "length": null, "enum_constant": null, "max": 100, "min": 0, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "SAY", "category": "other", "display_name": "说", "en_display_name": "", "desc": "Generate a one-sentence answer to the user's question, replying in the first person with a humorous, witty and funny tone of voice", "desc_chinese": "说，字数限制30字", "parameters": [{"name": "text", "type": "string", "desc": "Speak in the first person", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": true, "action_area": "all", "execute_function": "say"}, {"namespace": "orion.agent.action", "level": "global", "name": "CANCEL", "category": "other", "display_name": "取消", "en_display_name": "", "desc": "cancel the current action", "desc_chinese": "取消当前动作", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "EXIT", "category": "other", "display_name": "退出", "en_display_name": "", "desc": "", "desc_chinese": "退出当前应用", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "BACK", "category": "other", "display_name": "返回上一级", "en_display_name": "", "desc": "Back", "desc_chinese": "返回上一级", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "NEXT", "category": "other", "display_name": "下一步", "en_display_name": "", "desc": "Next", "desc_chinese": "下一步", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "CONFIRM", "category": "other", "display_name": "确认", "en_display_name": "", "desc": "Confirm", "desc_chinese": "用户确认操作", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "TAKE_PHOTO", "category": "other", "display_name": "拍照", "en_display_name": "", "desc": "Start taking photos", "desc_chinese": "开始拍照", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "VERIFICATION_CODE_INPUT", "category": "other", "display_name": "输入验证码", "en_display_name": "", "desc": "Fill in the captcha, Must be four valid digits", "desc_chinese": "填写验证码，必须是四位有效数字，数字范围0到9或者零到九", "parameters": [{"name": "verification_code", "type": "Integer array", "desc": "The captcha to be filled in", "is_required": true, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "LAST_4_DIGITS_INPUT", "category": "other", "display_name": "输入手机号后四位", "en_display_name": "", "desc": "Fill in the last four digits of the phone number, Must be four valid digits, Numeric range 0 to 9", "desc_chinese": "填写手机号后四位，必须是四位有效数字，数字范围0到9或者零到九", "parameters": [{"name": "last_4_digits", "type": "Integer array", "desc": "The last four digits of the phone number, Must be four valid digits, Must be converted to Arabic numerals, Numeric range 0 to 9", "is_required": true, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "COMMON_REPLAY", "category": "other", "display_name": "重播", "en_display_name": "", "desc": "Replay", "desc_chinese": "Triggered when there is a paused video on the screen information", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "MULTIMEDIA_PLAY", "category": "other", "display_name": "播放", "en_display_name": "", "desc": "Play multimedia content", "desc_chinese": "Triggered when there is a video to be played on the screen information", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "COMMON_PAUSE", "category": "other", "display_name": "暂停", "en_display_name": "", "desc": "Pause", "desc_chinese": "Triggered when there is a video playing on the screen information", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "START_QUESTION", "category": "other", "display_name": "提问", "en_display_name": "", "desc": "Start questioning", "desc_chinese": "开始提问", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "COMMON_REPEAT", "category": "other", "display_name": "重复一次", "en_display_name": "", "desc": "Repeat", "desc_chinese": "再讲一次", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "ROUTES_OTHERS", "category": "other", "display_name": "选择其他路线", "en_display_name": "", "desc": "Choose another route", "desc_chinese": "选择其他路线", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "SCORE", "category": "other", "display_name": "打分", "en_display_name": "", "desc": "Score", "desc_chinese": "打分", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "ADJUST_SPEED", "category": "other", "display_name": "设置移动速度", "en_display_name": "", "desc": "Adjust walking speed, either to increase or decrease it based on user input.", "desc_chinese": "调整最新的「当前移动速度」", "parameters": [{"name": "adjusted_speed", "type": "float", "desc": "新的移动速度", "is_required": true, "length": null, "enum_constant": null, "max": 1.2, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CONFIGURE_WELCOME_MESSAGE", "category": "other", "display_name": "设置欢迎语", "en_display_name": "", "desc": "Configure personalized welcome messages in Chinese, based on user interaction.", "desc_chinese": "设置机器人看到用户后的说的话", "parameters": [{"name": "nick_name", "type": "string", "desc": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "welcome_message", "type": "string", "desc": "message to greet the user. User nicknames are not allowed", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_d5a64441247aa63b70dd8d02e3f753f0"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "GENERATE_MESSAGE", "category": "other", "display_name": "生成", "en_display_name": "", "desc": "Say a welcome or farewell message to the user", "desc_chinese": "根据用户的指令生成文本，例如：欢迎、欢送语", "parameters": [{"name": "goal", "type": "string", "desc": "生成的目标", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 90, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "execute_function": "generate_message"}, {"namespace": "orion.agent.action", "level": "opk", "name": "RECOMMEND", "category": "other", "display_name": "推荐", "en_display_name": "", "desc": "Recommend hotels, routes (to subway stations, attractions, etc.), attractions, shopping, etc. through searches on Chinese websites", "desc_chinese": "各种「休闲娱乐场所」的推荐，例如餐厅、景点、购物、酒吧、KTV等.", "parameters": [{"name": "shop_name", "type": "string", "desc": "Must be a physical place that exists in reality", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "source_location", "type": "string", "desc": "The starting point provided by the user. If not provided, the default is the current location.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "url", "type": "HttpUrl", "desc": "Add current location information to the URL. The default is to use the current location.\n                If the current location is \"A市B区C街道D大厦\", search for \"D大厦附近的蛋糕店\". For recommended routes, food, and attractions, use Baidu Maps. Example: https://map.baidu.com/?newmap=1&ie=utf-8&s=s&wd={source_location}附近的{shop_name}", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.0", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "post_processing": "build_recommend_url"}, {"namespace": "orion.agent.action", "level": "opk", "name": "FACE_RECOGNITION", "category": "other", "display_name": "人脸识别", "en_display_name": "", "desc": "Who am I", "desc_chinese": "人脸识别", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "admin", "name": "GO_CHARGING", "category": "other", "display_name": "去充电", "en_display_name": "", "desc": "Go to the charging station", "desc_chinese": "自动去充电桩充电", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "apk", "name": "SET_LOCATION", "category": "other", "display_name": "设置位置", "en_display_name": "", "desc": "Set the robot's location", "desc_chinese": "设置位置", "parameters": [{"name": "location", "type": "string", "desc": "The location to set, a valid location name", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.maptool"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "global", "name": "KNOWLEDGE_QA", "category": "other", "display_name": "查询知识库", "en_display_name": "", "desc": "Knowledge Q&A", "desc_chinese": "知识问答，详细介绍自己、公司、产品、业务、领导等", "parameters": [{"name": "question", "type": "string", "desc": "The question to ask", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 600, "result_schema": [], "app_ids": [], "package_names": [], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "execute_function": "knowledge_qa"}, {"namespace": "orion.agent.action", "level": "opk", "name": "GUIDE_ROUTE_SELECTION_FROM_MAP", "category": "other", "display_name": "随机导览", "en_display_name": "", "desc": "Select a guided tour route from the map", "desc_chinese": "根据用户意图去多个地点参观。", "parameters": [{"name": "points", "type": "String array", "desc": "从给定的地图点位中有目的性「顺序」选择导览点。", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_guide_whole_point"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "post_processing": "convert_tour_id"}, {"namespace": "orion.agent.action", "level": "opk", "name": "INTERVIEW_START_PHOTO", "category": "other", "display_name": "合影", "en_display_name": "", "desc": "Start the interview process and take a photo", "desc_chinese": "合影", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CHANGE_FACE", "category": "other", "display_name": "换脸", "en_display_name": "", "desc": "Change face", "desc_chinese": "切换表情", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_01917709e2d711f26014d0f1e78295fb"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all"}, {"namespace": "orion.agent.action", "level": "opk", "name": "WEATHER_GET_REALTIME", "category": "other", "display_name": "查询国内实时天气", "en_display_name": "", "desc": "Get real-time weather information", "desc_chinese": "查询「中国」的实时天气,默认查询`{所在城市}`的天气信息，注意：不支持查询国外天气。", "parameters": [{"name": "city", "type": "string", "desc": "行政区域名称", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "user_question", "type": "string", "desc": "用户的问题", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": true, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "execute_function": "get_realtime_weather"}, {"namespace": "orion.agent.action", "level": "opk", "name": "CLICK", "category": "other", "display_name": "点击", "en_display_name": "", "desc": "Click", "desc_chinese": "模拟网页点击，根据“应用页面HTML”信息，理解用户需求，帮助用户点击网页上的元素。如果存在可点击元素，则优先考虑使用，而不是生成 URL。", "parameters": [{"name": "element_tag", "type": "enum", "desc": "可点击元素的标签", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_clickable_elements_value"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "post_processing": "convert_element_id"}, {"namespace": "orion.agent.action", "level": "opk", "name": "ANSWER_QUESTION_FROM_VISION", "category": "other", "display_name": "视觉问答", "en_display_name": "", "desc": "Answer questions based on visual data collected by the robot", "desc_chinese": "通过机器人的摄像头观察并回答问题，包括外貌评价、环境描述、物体识别等", "parameters": [{"name": "image_url", "type": "string", "desc": "视觉采集的图片url", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "question", "type": "string", "desc": "根据图片要回答的问题，必须总结为第一人称的问题，生成的问题要简单", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The answer of the question", "type": "string", "enum_constant": null}], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "execute_function": "answer_question_from_vision"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REALTIME_SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Think about it.", "desc_chinese": "思考说", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "stream_say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY_FOR_CLARIFICATION", "category": "interaction", "display_name": "澄清用户问题", "en_display_name": "Clarify User Question", "desc": "Clarify the user's question. When you need to clarify the user's question, please use this tool.", "desc_chinese": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具", "parameters": [{"name": "request_clarify_text", "type": "string", "desc": "Ask user to clarify the question", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.0", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "clarify"}]}