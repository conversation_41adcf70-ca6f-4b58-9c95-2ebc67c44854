{"version": "v1.0.4", "created_at": "2025-05-19 10:34:00", "actions": [{"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CRUISE", "category": "basic_movement", "display_name": "巡航", "en_display_name": "Cruise", "desc": "cruise", "desc_chinese": "巡航，巡逻。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "EXIT_CRUISE", "category": "basic_movement", "display_name": "退出巡逻", "en_display_name": "Exit Cruise", "desc": "stop cruise", "desc_chinese": "退出巡逻、巡航模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "COME_FAR", "category": "basic_movement", "display_name": "让路", "en_display_name": "Give Way", "desc": "Make way.", "desc_chinese": "让路", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OUTDOOR_NAVIGATE_START", "category": "navigation", "display_name": "调用地图", "en_display_name": "Open Map", "desc": "Navigate to the outdoor destination.", "desc_chinese": "室外路线导航。只有当起点终点是中国境内地点时，才能使用此工具；如果是海外地点时，通过说的方式拒绝。", "parameters": [{"name": "origin", "type": "string", "desc": "Route origin, provided by the user. Set the value to '-1' if indeterminate.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "destination", "type": "string", "desc": "Destination location name. Strip modifiers such as 'nearest', 'nearby', etc. Keep only the core place name.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "mode", "type": "enum", "desc": "驾车、步行、公交、骑行,若无法确定，则默认为驾车。", "is_required": true, "length": null, "enum_constant": ["driving", "walking", "transit", "riding"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "post_processing": "convert_map_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "TURN_DIRECTION", "category": "basic_movement", "display_name": "转圈", "en_display_name": "Turn Circle", "desc": "The body can only turn left and right 10 times at most. If it exceeds 10 times, use `SAY` to reject the user, and then choose the number of circles or angle according to the user's requirements.", "desc_chinese": "身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to turn, default is left", "is_required": true, "length": null, "enum_constant": ["left", "right"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "angle", "type": "int", "desc": "The value of the rotation angle", "is_required": false, "length": null, "enum_constant": null, "max": 3600, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "turns", "type": "float", "desc": "Number of turns.", "is_required": false, "length": null, "enum_constant": null, "max": 10, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "post_processing": "convert_turns_to_degree"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "HEAD_NOD", "category": "basic_movement", "display_name": "点头", "en_display_name": "Nod Head", "desc": "Nod and bow.", "desc_chinese": "点头、鞠躬", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_DANCE", "category": "entertainment", "display_name": "唱歌跳舞", "en_display_name": "Sing and Dance", "desc": "Dancing, playing music.", "desc_chinese": "跳舞、播放音乐。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "REGISTER", "category": "user_recognition", "display_name": "注册", "en_display_name": "Register", "desc": "Register the user's information, including name and face registration.", "desc_chinese": "注册。包含姓名和人脸注册", "parameters": [{"name": "nick_name", "type": "string", "desc": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "welcome_message", "type": "string", "desc": "message to greet the user. MUST NOT CONTAIN USER'S NICKNAME", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_d5a64441247aa63b70dd8d02e3f753f0", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "MOVE_DIRECTION", "category": "basic_movement", "display_name": "移动", "en_display_name": "Move", "desc": "Move forward or backward, closer or farther away from the user. If the movement distance exceeds the limit, you can use `SAY` to reject the user.", "desc_chinese": "前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to move in, select from enumerated values", "is_required": true, "length": null, "enum_constant": ["forward", "backward"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "moving_distance", "type": "float", "desc": "单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "INTERVIEW_START", "category": "visitor_reception", "display_name": "打开访客接待页面", "en_display_name": "Open Visitor Reception Page", "desc": "Visitor reception process, supporting interviews, meeting sign-ins, and visitor registration", "desc_chinese": "访客接待流程，支持面试、会议签到、访客登记。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CALENDAR", "category": "information", "display_name": "查询日历", "en_display_name": "Query Calendar", "desc": "Supports time and date calculations and date query function, such as 'How many days until Christmas?'", "desc_chinese": "日历功能，包含日期或节假日的查询，注意：无法查询天气", "parameters": [{"name": "user_question", "type": "string", "desc": "question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_17bf9cfc230d17c94a19a0dc4faa6569", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "calendar"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_INTRODUCTION", "category": "guide", "display_name": "导览", "en_display_name": "Guide Introduction", "desc": "A guide function that leads the user on a tour, triggered when the user says something like take me on a tour without specifying any particular location or route.", "desc_chinese": "导览功能，带领用户参观，在用户只说带我参观等，后面不跟或指明任何具体地点或路线时触发。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original query or requirements", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "generate_route_introduction"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_SELECT_SPECIFIC_ROUTE", "category": "guide", "display_name": "指定路线导览", "en_display_name": "Select Specific Route", "desc": "This guide action is triggered when the user requests to visit a specific location or route. The system will select one matching route from the predefined list based on user intent. Outdoor locations are not supported.", "desc_chinese": "导览功能,不支持去室外位置,用户指明参观路线", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original query", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "route_name", "type": "string", "desc": "The exact name of the selected route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "convert_specific_route"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_ROUTE_RECOMMENDATION", "category": "guide", "display_name": "推荐路线", "en_display_name": "Route Recommendation", "desc": "Generate route recommendations and confirm user's intention based on their query and available routes.", "desc_chinese": "导览功能,基于用户需求和可用路线生成推荐，并确认用户意图。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original query or requirements", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "generate_route_recommendation"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_IMMEDIATELY", "category": "guide", "display_name": "直接开始", "en_display_name": "Start Immediately", "desc": "Start the current task or process immediately.", "desc_chinese": "立即开始当前任务或流程。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL", "category": "web_browser", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Web Search.Search for information that users want to know through google or official website.", "desc_chinese": "网络搜索，例如股票、门票、新闻等。推荐使用「百度」搜索引擎；公司官网等特定网站直接通过对应网址打开。", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "Must be a legitimate https or http link.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "FLIGHT_TICKET_QUERY", "category": "transportation", "display_name": "查询机票", "en_display_name": "Query Flight Tickets", "desc": "Query flight tickets", "desc_chinese": "飞机票查询", "parameters": [{"name": "departure_city_code", "type": "string", "desc": "The IATA code of the departure city", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "arrival_city_code", "type": "string", "desc": "The IATA code of the arrival city", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "departure_date", "type": "string", "desc": "The departure date, for example: 2024-01-01", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "post_processing": "build_flight_ticket_query_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "TRAIN_TICKET_QUERY", "category": "transportation", "display_name": "查询火车票", "en_display_name": "Query Train Tickets", "desc": "Query train tickets", "desc_chinese": "火车票查询", "parameters": [{"name": "departure_city", "type": "string", "desc": "城市名称，例如`北京`不要带`市`字", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "arrival_city", "type": "string", "desc": "城市名称，例如`北京`不要带`市`字", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "departure_date", "type": "string", "desc": "出发日期，例如`2024-01-01`", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "post_processing": "build_train_ticket_query_url"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SET_VOLUME", "category": "system_control", "display_name": "调整音量", "en_display_name": "Adjust Volume", "desc": "Adjust the volume. The default increase or decrease range is between 10 and 30.", "desc_chinese": "调整音量。调整的幅度是10或30，根据用户语气选择", "parameters": [{"name": "volume_level", "type": "int", "desc": "The volume level to be set.", "is_required": true, "length": null, "enum_constant": null, "max": 100, "min": 0, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Say. Basic communication with the user.", "desc_chinese": "说话，与用户的基础交流。", "parameters": [{"name": "text", "type": "string", "desc": "Speak in the first person, using pure text without emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY_FOR_CLARIFICATION", "category": "interaction", "display_name": "澄清用户问题", "en_display_name": "Clarify User Question", "desc": "Clarify the user's question. When you need to clarify the user's question, please use this tool.", "desc_chinese": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具", "parameters": [{"name": "request_clarify_text", "type": "string", "desc": "Ask user to clarify the question", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "clarify"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SILENT", "category": "guide", "display_name": "默认兜底技能", "en_display_name": "Silent", "desc": "A default fallback skill that should be selected when the user's query has low relevance to other available actions.", "desc_chinese": "默认兜底的技能，在以下情况应选择此动作：用户的查询与其他可用动作相关性较低时，默认选择这个技能", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CANCEL", "category": "system_control", "display_name": "取消", "en_display_name": "Cancel", "desc": "Cancel the current behavior, such as dancing, nodding, navigating, or speaking (e.g., stop or pause speaking).", "desc_chinese": "取消当前行为，例如跳舞、点头、导航或说话（如停止或暂停说话）。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "EXIT", "category": "system_control", "display_name": "退出", "en_display_name": "Exit", "desc": "exit current application", "desc_chinese": "退出当前应用", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "BACK", "category": "system_control", "display_name": "返回上一级", "en_display_name": "Back", "desc": "Back to the previous level.", "desc_chinese": "返回上一级", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "NEXT", "category": "system_control", "display_name": "下一步", "en_display_name": "Next", "desc": "Next step.", "desc_chinese": "下一步", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CONFIRM", "category": "system_control", "display_name": "确认", "en_display_name": "Confirm", "desc": "Confirm the operation.", "desc_chinese": "用户确认操作", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "TAKE_PHOTO", "category": "entertainment", "display_name": "拍照", "en_display_name": "Take Photo", "desc": "Start taking photos", "desc_chinese": "开始拍照", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "VERIFICATION_CODE_INPUT", "category": "verification", "display_name": "输入验证码", "en_display_name": "Input Verification Code", "desc": "Fill in the captcha, Must be four valid digits, Numeric range 0 to 9.", "desc_chinese": "填写验证码，必须是四位有效数字，数字范围0到9或者零到九", "parameters": [{"name": "verification_code", "type": "Integer array", "desc": "The captcha to be filled in", "is_required": true, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "LAST_4_DIGITS_INPUT", "category": "verification", "display_name": "输入手机号后四位", "en_display_name": "Input Last 4 Digits of Phone Number", "desc": "Fill in the last four digits of the phone number, Must be four valid digits, Numeric range 0 to 9", "desc_chinese": "填写手机号后四位，必须是四位有效数字，数字范围0到9或者零到九", "parameters": [{"name": "last_4_digits", "type": "Integer array", "desc": "The last four digits of the phone number, Must be four valid digits, Must be converted to Arabic numerals, Numeric range 0 to 9", "is_required": true, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "COMMON_REPLAY", "category": "guide", "display_name": "重播", "en_display_name": "Replay", "desc": "Triggered when there is a paused video on the screen information.", "desc_chinese": "正在播视频或者正在导览讲解中，用户想重新播报当前的点", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "MULTIMEDIA_PLAY", "category": "system_control", "display_name": "播放", "en_display_name": "Play", "desc": "Triggered to play a video", "desc_chinese": "播放视频", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SERVICE_RESUME", "category": "system_control", "display_name": "继续服务", "en_display_name": "Resume Service", "desc": "Triggered in scenarios such as resuming playback or restarting a service, typically after a pause or interruption.", "desc_chinese": "继续播放、继续服务等场景", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MULTIMEDIA_PLAY", "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "COMMON_PAUSE", "category": "guide", "display_name": "暂停", "en_display_name": "Pause", "desc": "Pause the guide narration, media playback, or movement. Commonly triggered when users want to temporarily stop an action, such as saying 'don’t go', 'stay still', 'wait for me', or 'hold on'.", "desc_chinese": "暂停导览讲解过程。例如「暂停播放视频」或者「暂停移动」。常见于用户希望暂停某个动作的场景，如表达“别走了”“别动了”“等下我”或“等一等”等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_QUESTION", "category": "guide", "display_name": "提问", "en_display_name": "Start Questioning", "desc": "When the guide explanation is completed, the user wants to ask a question.", "desc_chinese": "导览讲解中，用户想提问。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "COMMON_REPEAT", "category": "guide", "display_name": "重新讲解", "en_display_name": "Repeat", "desc": "After a round of explanation ends, it enters the scoring session, and the user wants to re-explain.", "desc_chinese": "当一轮讲解结束后，进入打分环节，用户想重新进行讲解。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ROUTES_OTHERS", "category": "guide", "display_name": "选择其他导览路线", "en_display_name": "Choose Another Route", "desc": "Choose another route", "desc_chinese": "选择其他导览路线。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SCORE", "category": "guide", "display_name": "打分", "en_display_name": "Score", "desc": "Score.", "desc_chinese": "评分。", "parameters": [{"name": "score", "type": "int", "desc": "score 1-5 代表五颗星", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ADJUST_SPEED", "category": "system_control", "display_name": "调整当前移动速度", "en_display_name": "Adjust Walking Speed", "desc": "Adjust the <current walking speed>.", "desc_chinese": "调整<当前移动速度>。", "parameters": [{"name": "adjusted_speed", "type": "float", "desc": "The robot's walking speed.", "is_required": true, "length": null, "enum_constant": null, "max": 1.2, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CONFIGURE_WELCOME_MESSAGE", "category": "interaction", "display_name": "设置欢迎语", "en_display_name": "Configure Welcome Message", "desc": "Configure the welcome message for the robot when it sees the user.", "desc_chinese": "设置机器人看到用户后的说的话", "parameters": [{"name": "nick_name", "type": "string", "desc": "The nickname can be extracted from the user query, MUST BE REAL NICKNAME", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "welcome_message", "type": "string", "desc": "message to greet the user. MUST NOT CONTAIN USER'S NICKNAME", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_d5a64441247aa63b70dd8d02e3f753f0"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GENERATE_MESSAGE", "category": "interaction", "display_name": "生成", "en_display_name": "Generate", "desc": "Text generation. Only for 'Welcome Someone', '<PERSON><PERSON><PERSON> Someone', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'. Cannot generate 'Suggestion', 'Recommendation', 'Introduction', etc.", "desc_chinese": "文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等", "parameters": [{"name": "goal", "type": "string", "desc": "user instructions", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 90, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "generate_message"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RECOMMEND", "category": "navigation", "display_name": "调用地图", "en_display_name": "Open Map", "desc": "Recommend hotels, routes (to subway stations, attractions, etc.), attractions, shopping, etc. through searches on Chinese websites", "desc_chinese": "调用地图搜索附近的某一类地点。不支持路线推荐！！！", "parameters": [{"name": "query", "type": "string", "desc": "The user's original query", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "place", "type": "string", "desc": "Extracted place name from the query. If none is found, set as '-1'.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "target", "type": "string", "desc": "Extracted search target from the query (e.g., hotel, food, subway station). If none is found, set as '-1'.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "post_processing": "convert_recommend_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "FACE_RECOGNITION", "category": "user_recognition", "display_name": "人脸识别", "en_display_name": "Face Recognition", "desc": "Facial recognition. Identify the face in front of the robot, answer the user's identity (mainly the name), but does not support identifying other people.", "desc_chinese": "人脸识别。识别机器人面前的人脸，回答用户的身份（主要是姓名），但不支持识别其他人。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "GO_CHARGING", "category": "system_admin", "display_name": "去充电", "en_display_name": "Go to Charging", "desc": "Go to the charging station", "desc_chinese": "自动去充电桩充电", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "ENTER_PROMOTE_APP", "category": "system_admin", "display_name": "进入推销APP", "en_display_name": "Enter Promotion App", "desc": "Enter the promotion app.", "desc_chinese": "进入推销APP", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "EXIT_PROMOTE_APP", "category": "system_admin", "display_name": "退出推销APP", "en_display_name": "Exit Promotion App", "desc": "Exit the promotion app.", "desc_chinese": "退出推销APP", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "post_processing": "clear_cache_for_key"}, {"namespace": "orion.agent.action", "level": "apk", "source": "builtin", "name": "SET_LOCATION", "category": "system_admin", "display_name": "设置位置", "en_display_name": "Set Location", "desc": "Set the location on the map.", "desc_chinese": "在地图上设置点位。", "parameters": [{"name": "location", "type": "string", "desc": "The location to set, a valid location name", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.maptool"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "KNOWLEDGE_QA", "category": "information", "display_name": "找答案", "en_display_name": "Find Answer", "desc": "Query knowledge to answer user questions, including 'encyclopedic knowledge,' 'company products,' 'employee information,' 'city and scenic spot introductions,' and 'travel planning.'", "desc_chinese": "查询知识回答用户的问题，包含「百科知识」、「公司产品」、「员工信息」、「城市美食、景点介绍」、「旅游计划规划」等", "parameters": [{"name": "question", "type": "string", "desc": "The user's question has been summarized based on the context of the conversation.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 600, "result_schema": [{"name": "knowledge_content", "desc": "The knowledge content.", "type": "string", "enum_constant": null}], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "knowledge_qa"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "INTERVIEW_START_PHOTO", "category": "entertainment", "display_name": "合影", "en_display_name": "Group Photo", "desc": "Group photo.", "desc_chinese": "合影", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CHANGE_FACE", "category": "entertainment", "display_name": "换脸", "en_display_name": "Change Face", "desc": "Change face", "desc_chinese": "切换表情", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_01917709e2d711f26014d0f1e78295fb", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CLICK", "category": "web_browser", "display_name": "点击", "en_display_name": "Click", "desc": "Simulate web page click.", "desc_chinese": "模拟网页点击。", "parameters": [{"name": "element_tag", "type": "enum", "desc": "可点击元素的标签", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_clickable_elements_value"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "post_processing": "convert_element_id"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ANSWER_QUESTION_FROM_VISION", "category": "information", "display_name": "根据视觉回答", "en_display_name": "Answer from Vision", "desc": "In front of the robot, only answer questions related to 'clothing, expression, gender,' 'surrounding environment,' and 'object recognition.'", "desc_chinese": "在机器人面前，仅回答有关「穿着、表情、性别」、「周边环境」、「物体识别」问题，无法回答用户关系的话题。", "parameters": [{"name": "image_url", "type": "string", "desc": "视觉采集的图片url", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "question", "type": "string", "desc": "用户的问题，必须总结为第一人称的问题，长度限制在15字以内。", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The answer of the question", "type": "string", "enum_constant": null}], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "answer_question_from_vision"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "HOME", "category": "product", "display_name": "展示首页", "en_display_name": "Show Home Page", "desc": "Home page.", "desc_chinese": "首页。列举所有产品，用户表达要退出当前视频、页面时，返回首页", "parameters": [], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "data", "desc": "home page data", "type": "dict", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "home_v2"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "PRODUCT_DETAIL", "category": "product", "display_name": "展示产品详情", "en_display_name": "Show Product Detail", "desc": "Show the product detail", "desc_chinese": "展示产品详情。", "parameters": [{"name": "production", "type": "enum", "desc": "用户明确想要了解的产品", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_product_list_v2"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "product", "desc": "The product detail data", "type": "dict", "enum_constant": null}, {"name": "product_detail", "desc": "The media resources of the product", "type": "list", "enum_constant": null}, {"name": "text_content", "desc": "The text content of the product", "type": "page_info", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "production_detail_v2"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "PRODUCT_FUNCTION", "category": "product", "display_name": "介绍产品功能", "en_display_name": "Introduce Product Function", "desc": "Provide videos or images that explain a specific feature of the product.", "desc_chinese": "从产品功能名称中选择一项功能或者参数规格进行介绍，来推销和推荐产品", "parameters": [{"name": "production_function", "type": "enum", "desc": "产品功能名称", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_product_functions_v2"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "title", "desc": "The title of the product function", "type": "string", "enum_constant": null}, {"name": "url", "desc": "The url of the product function", "type": "string", "enum_constant": null}, {"name": "type", "desc": "The type of media resource", "type": "enum", "enum_constant": ["image", "video"]}, {"name": "text_content", "desc": "The text content of the product function", "type": "page_info", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "production_function_v2"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "RECORD_FEEDBACK", "category": "product", "display_name": "记录反馈", "en_display_name": "Record Feedback", "desc": "Collect feedback after the user experience.", "desc_chinese": "保存用户反馈", "parameters": [{"name": "production", "type": "enum", "desc": "用户体验的产品", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_product_list_v2"}, {"name": "function", "type": "string", "desc": "用户体验的产品功能", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "user_review", "type": "string", "desc": "用户关于产品功能的评价、体验", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "record_feedback"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "SHOW_CONTACT_INFORMATION", "category": "product", "display_name": "提供联系方式", "en_display_name": "Show Contact Information", "desc": "Show contact information.", "desc_chinese": "提供联系方式，用户有询问价格、购买等相关信息，或者用户表达出要离开意图，或者用户主动问询联系方式时触发。", "parameters": [], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "show_contact_information"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REALTIME_SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Think about it.", "desc_chinese": "思考说", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "stream_say"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "SALES_PITCH", "category": "product", "display_name": "说", "en_display_name": "Say", "desc": "sales pitch.", "desc_chinese": "疑虑解答、演示跟进、推荐、感谢、客户、介绍产品的整体的功能。", "parameters": [], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "sales_pitch_v2"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "COMPETITOR_QUESTION", "category": "product", "display_name": "竞品信息", "en_display_name": "Competitive Information", "desc": "Answer questions about competitors.", "desc_chinese": "回答涉及到竞品的问题。", "parameters": [{"name": "competitor_production_name", "type": "enum", "desc": "竞品产品名称", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_competitor_production_name"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "competitors_answer"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "CASE_INTRODUCTION", "category": "product", "display_name": "案例介绍", "en_display_name": "Case Introduction", "desc": "Case introduction. Play case video", "desc_chinese": "案例介绍。播放案例视频。", "parameters": [{"name": "case", "type": "enum", "desc": "案例场景", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_case_name_v2"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "title", "desc": "The title of the product function", "type": "string", "enum_constant": null}, {"name": "url", "desc": "The url of the product function", "type": "string", "enum_constant": null}, {"name": "type", "desc": "The type of media resource", "type": "enum", "enum_constant": ["image", "video"]}, {"name": "text_content", "desc": "The text content of the product function", "type": "page_info", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "execute_function": "case_introduction_v2"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "NAVIGATE_REC_START", "category": "navigation", "display_name": "准备领位", "en_display_name": "Indoor Navigation Recommendation", "desc": "Point navigation intent. Users can only be taken to the locations provided below, and outdoor locations are not supported.If the user does not explicitly specify the destination, a list of up to 4 available navigation points is returned and sorted by similarity, with the closest point at the front.", "desc_chinese": "室内导航意图。不支持去室外位置，应直接根据「CHAT CONVERSATION」中的对话历史和用户特征（包括但不限于性别信息），为用户选择最符合其真实意图的具体地点。", "parameters": [{"name": "destinations", "type": "String array", "desc": "Navigation points that match user intent. You can choose multiple. ", "is_required": false, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_robot_support_map_points"}, {"name": "guide_text", "type": "string", "desc": "A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 300, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "post_processing": "convert_navigation_points"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL_DEFINED", "category": "web_browser", "display_name": "打开轻应用", "en_display_name": "Open Light App", "desc": "Use a browser or app to browse information. Trigger this action if there's a semantically identical question in the predefined list, as it allows you to directly open the URL or app.", "desc_chinese": "使用浏览器或者APP浏览信息。*只要*预定义问题列表中存在与用户当前问题语义相同的问题，则选择此action。", "parameters": [{"name": "predefined_question", "type": "enum", "desc": "list of predefined question.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_light_app_enum"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": true, "page_id": ""}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "WEATHER", "category": "weather", "display_name": "获取天气信息", "en_display_name": "Get Weather Information", "desc": "Get weather information. Time: 'Today and the next 10 days', Do not support querying foreign weather!", "desc_chinese": "获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！！！", "parameters": [{"name": "area_level", "type": "enum", "desc": "city 对应的区域等级", "is_required": false, "length": null, "enum_constant": ["province", "city", "area"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "city", "type": "string", "desc": "城市名称，最小粒度到区/县。", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_a8c9098ad7a91a66287b25d6befef6ec", "system_4f307c6d4cb0f187a3edb3dcc6f43749", "system_024280e32e73c00ad621710870d4cb18"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "execute_function": "query_weather", "post_processing": "query_weather_post_processing"}]}