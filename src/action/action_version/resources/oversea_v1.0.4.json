{"version": "oversea_v1.0.4", "created_at": "2025-06-16 14:48:44", "actions": [{"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CRUISE", "category": "basic_movement", "display_name": "巡航", "en_display_name": "Cruise", "desc": "cruise", "desc_chinese": "巡航，巡逻。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "巡航", "zh_TW": "巡航", "zh_GD": "巡航", "en_US": "Cruise", "ja_JP": "巡航", "th_TH": "ลาดตระเวน", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "ko_KR": "순찰", "da_DK": "<PERSON><PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "Partio", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Croisière", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Патруль", "pl_PL": "Patrol", "pt_PT": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Pattuglia", "ro_RO": "<PERSON><PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "Tu<PERSON>n tra", "id_ID": "Patroli", "fil_PH": "Patrol", "cs_CZ": "Hlídka", "el_GR": "Περιπολία", "pt_BR": "<PERSON><PERSON><PERSON><PERSON>", "hu_HU": "Járőrözés", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Hliadka"}}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "EXIT_CRUISE", "category": "basic_movement", "display_name": "退出巡逻", "en_display_name": "Exit Cruise", "desc": "stop cruise", "desc_chinese": "退出巡逻、巡航模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出巡逻", "zh_TW": "退出巡邏", "zh_GD": "退出巡邏", "en_US": "Exit Cruise", "ja_JP": "巡航終了", "th_TH": "ออกจากการลาดตระเวน", "de_DE": "<PERSON><PERSON><PERSON>den", "es_ES": "<PERSON><PERSON>", "ko_KR": "순찰 종료", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Lopeta partio", "nb_NO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON>rouille", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Выйти из патруля", "pl_PL": "Zakończ patrol", "pt_PT": "<PERSON><PERSON> <PERSON>", "it_IT": "<PERSON><PERSON><PERSON> dalla pattuglia", "ro_RO": "Ieși din patrulare", "ms_MY": "<PERSON><PERSON><PERSON> dari rondaan", "vi_VN": "<PERSON><PERSON><PERSON>t khỏi tuần tra", "id_ID": "<PERSON><PERSON><PERSON> dari <PERSON>i", "fil_PH": "Umalis sa patrol", "cs_CZ": "Ukončit hlídku", "el_GR": "Έξοδος από περιπολία", "pt_BR": "<PERSON><PERSON> <PERSON>", "hu_HU": "Járőrözés befejezése", "tr_TR": "<PERSON><PERSON><PERSON><PERSON> ç<PERSON>", "sk_SK": "Ukončiť hliadku"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "COME_FAR", "category": "basic_movement", "display_name": "让路", "en_display_name": "Give Way", "desc": "Make way.", "desc_chinese": "让路", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "让路", "zh_TW": "讓路", "zh_GD": "讓路", "en_US": "Give Way", "ja_JP": "道を譲る", "th_TH": "หลีกทาง", "de_DE": "Platz machen", "es_ES": "Ceder el paso", "ko_KR": "길을 비키다", "da_DK": "Giv plads", "sv_SE": "Ge plats", "fi_FI": "<PERSON>", "nb_NO": "Gi plass", "fr_FR": "<PERSON><PERSON><PERSON> le passage", "nl_NL": "Plaats maken", "ru_RU": "Уступить дорогу", "pl_PL": "Ustąp mi<PERSON>", "pt_PT": "Dar <PERSON>", "it_IT": "Dare la precedenza", "ro_RO": "<PERSON><PERSON> drumul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>hư<PERSON><PERSON> đường", "id_ID": "Memberi jalan", "fil_PH": "Magbigay ng daan", "cs_CZ": "Uhnout z cesty", "el_GR": "Δώσε δρόμο", "pt_BR": "Dar <PERSON>", "hu_HU": "Utat enged", "tr_TR": "Yol ver", "sk_SK": "Ustúpiť z cesty"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "TURN_DIRECTION", "category": "basic_movement", "display_name": "转圈", "en_display_name": "Turn Circle", "desc": "The body can only turn left and right 10 times at most. If it exceeds 10 times, use `SAY` to reject the user, and then choose the number of circles or angle according to the user's requirements.", "desc_chinese": "身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to turn, default is left", "is_required": true, "length": null, "enum_constant": ["left", "right"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "angle", "type": "int", "desc": "The value of the rotation angle", "is_required": false, "length": null, "enum_constant": null, "max": 3600, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "turns", "type": "float", "desc": "Number of turns.", "is_required": false, "length": null, "enum_constant": null, "max": 10, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "转圈", "zh_TW": "轉圈", "zh_GD": "轉圈", "en_US": "Turn Circle", "ja_JP": "回転", "ko_KR": "회전", "th_TH": "หมุน", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON>", "da_DK": "<PERSON><PERSON>", "sv_SE": "Vänd", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Поворот", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "G<PERSON><PERSON>", "ro_RO": "Rotește", "ms_MY": "Putar", "vi_VN": "Xoay", "id_ID": "Putar", "fil_PH": "Ikot", "cs_CZ": "O<PERSON>č<PERSON>", "el_GR": "Στροφή", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Fordulás", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "post_processing": "convert_turns_to_degree"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "HEAD_NOD", "category": "basic_movement", "display_name": "点头", "en_display_name": "Nod Head", "desc": "Nod and bow.", "desc_chinese": "点头、鞠躬", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "点头", "zh_TW": "點頭", "zh_GD": "點頭", "en_US": "Nod Head", "ja_JP": "うなずく", "ko_KR": "고개 끄덕이기", "th_TH": "พยักหน้า", "de_DE": "<PERSON><PERSON> nicken", "es_ES": "Asentir con la cabeza", "fr_FR": "Hocher la tête", "da_DK": "Nikke med hovedet", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Nyökkää päätä", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Knikken", "ru_RU": "Кивнуть головой", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> głową", "pt_PT": "Acenar com a cabeça", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Dă din cap", "ms_MY": "Angguk kepala", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON><PERSON>", "fil_PH": "Tumango", "cs_CZ": "Pokývat hlavou", "el_GR": "Νεύμα κεφαλιού", "pt_BR": "Acenar com a cabeça", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Başını salla", "sk_SK": "Prikývnuť"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_DANCE", "category": "entertainment", "display_name": "唱歌跳舞", "en_display_name": "Sing and Dance", "desc": "Dancing, playing music.", "desc_chinese": "跳舞、播放音乐。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "唱歌跳舞", "zh_TW": "唱歌跳舞", "zh_GD": "唱歌跳舞", "en_US": "Sing and Dance", "ja_JP": "歌って踊る", "ko_KR": "노래하고 춤추기", "th_TH": "ร้องเพลงและเต้นรำ", "de_DE": "Singen und tanzen", "es_ES": "Cantar y bailar", "fr_FR": "<PERSON><PERSON> et danser", "da_DK": "Synge og danse", "sv_SE": "Sjunga och dansa", "fi_FI": "<PERSON><PERSON><PERSON> ja tanssia", "nb_NO": "Synge og danse", "nl_NL": "Zingen en dansen", "ru_RU": "Петь и танцевать", "pl_PL": "Śpiewać i tańczyć", "pt_PT": "Cantar e dançar", "it_IT": "Cantare e ballare", "ro_RO": "Cântă și dansează", "ms_MY": "<PERSON><PERSON><PERSON> dan menari", "vi_VN": "H<PERSON><PERSON> v<PERSON> n<PERSON>ả<PERSON>", "id_ID": "<PERSON><PERSON><PERSON> dan menari", "fil_PH": "Kumanta at sumayaw", "cs_CZ": "Zpívat a tančit", "el_GR": "Τραγούδι και χορός", "pt_BR": "Cantar e dançar", "hu_HU": "Énekelni és táncolni", "tr_TR": "Şarkı söyle ve dans et", "sk_SK": "Spievať a tancovať"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "MOVE_DIRECTION", "category": "basic_movement", "display_name": "移动", "en_display_name": "Move", "desc": "Move forward or backward, closer or farther away from the user. If the movement distance exceeds the limit, you can use `SAY` to reject the user.", "desc_chinese": "前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to move in, select from enumerated values", "is_required": true, "length": null, "enum_constant": ["forward", "backward"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "moving_distance", "type": "float", "desc": "单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "移动", "zh_TW": "移動", "zh_GD": "移動", "en_US": "Move", "ja_JP": "移動", "ko_KR": "이동", "th_TH": "เคลื่อนที่", "de_DE": "Bewegen", "es_ES": "Mover", "fr_FR": "<PERSON><PERSON><PERSON>", "da_DK": "<PERSON><PERSON><PERSON><PERSON>", "sv_SE": "Flytta", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Beveg", "nl_NL": "Verplaatsen", "ru_RU": "Двигаться", "pl_PL": "P<PERSON><PERSON><PERSON>ś", "pt_PT": "Mover", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Mi<PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>", "id_ID": "<PERSON><PERSON>", "fil_PH": "Ilipat", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Κίνηση", "pt_BR": "Mover", "hu_HU": "Mozgatás", "tr_TR": "Hareket et", "sk_SK": "Presunúť"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CALENDAR", "category": "information", "display_name": "查询日历", "en_display_name": "Query Calendar", "desc": "Supports time and date calculations and date query function, such as 'How many days until Christmas?'", "desc_chinese": "日历功能，包含日期或节假日的查询，注意：无法查询天气", "parameters": [{"name": "user_question", "type": "string", "desc": "question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_17bf9cfc230d17c94a19a0dc4faa6569", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "查询日历", "zh_TW": "查詢日曆", "zh_GD": "查詢日曆", "en_US": "Query Calendar", "ja_JP": "カレンダーを照会", "ko_KR": "캘린더 조회", "th_TH": "สอบถามปฏิทิน", "vi_VN": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> l<PERSON>ch", "id_ID": "<PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON> kalen<PERSON>", "fil_PH": "Tanong sa kalendaryo", "de_DE": "<PERSON><PERSON><PERSON> ab<PERSON>", "es_ES": "Consultar calendario", "fr_FR": "<PERSON><PERSON><PERSON> le calendrier", "it_IT": "Interroga calendario", "pt_PT": "Consultar calendário", "pt_BR": "Consultar calendário", "nl_NL": "<PERSON><PERSON><PERSON>v<PERSON>n", "ru_RU": "Запросить календарь", "pl_PL": "Zapytaj o kalendarz", "sv_SE": "<PERSON><PERSON><PERSON> kalender", "da_DK": "<PERSON><PERSON><PERSON><PERSON><PERSON> kalender", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Kysy ka<PERSON>", "cs_CZ": "Dotaz na kalendář", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "sk_SK": "Dotaz na kalendár", "ro_RO": "Interogare calendar", "el_GR": "Ερώτημα ημερολογίου", "tr_TR": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>"}, "execute_function": "calendar"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_INTRODUCTION", "category": "guide", "display_name": "导览", "en_display_name": "Guide Introduction", "desc": "A guide function that leads the user on a tour, triggered when the user says something like take me on a tour without specifying any particular location or route.", "desc_chinese": "导览功能，带领用户参观，在用户只说带我参观等，后面不跟或指明任何具体地点或路线时触发。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original query or requirements", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "导览", "zh_TW": "導覽", "zh_GD": "導覽", "en_US": "Guide Introduction", "ja_JP": "ガイド紹介", "ko_KR": "가이드 소개", "th_TH": "แนะนำไกด์", "de_DE": "Führung", "es_ES": "Introducción guía", "fr_FR": "Introduction guide", "da_DK": "Guide introduktion", "sv_SE": "Guide introduktion", "fi_FI": "Opas esittely", "nb_NO": "Guide introduksjon", "nl_NL": "Gids introductie", "ru_RU": "Представление гида", "pl_PL": "Wprowadzenie przewodnika", "pt_PT": "Introdução do guia", "it_IT": "Introduzione guida", "ro_RO": "Introducer<PERSON>i", "ms_MY": "Pengenalan panduan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> thiệu hướng dẫn", "id_ID": "Pengenalan panduan", "fil_PH": "Pagpapakilala ng gabay", "cs_CZ": "Představení průvodce", "el_GR": "Εισαγωγή οδηγού", "pt_BR": "Introdução do guia", "hu_HU": "Útmutató bemu<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>ıtımı", "sk_SK": "Predstavenie sprievodcu"}, "execute_function": "generate_route_introduction"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_SELECT_SPECIFIC_ROUTE", "category": "guide", "display_name": "指定路线导览", "en_display_name": "Select Specific Route", "desc": "Guide feature. Outdoor locations are not supported. The user specifies a route to visit.", "desc_chinese": "导览功能,不支持去室外位置,用户指明参观路线", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original query", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "route_name", "type": "string", "desc": "The exact name of the selected route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "指定路线导览", "zh_TW": "指定路線導覽", "zh_GD": "指定路線導覽", "en_US": "Select Specific Route", "ja_JP": "特定ルート選択", "ko_KR": "특정 루트 선택", "th_TH": "เลือกเส้นทางเฉพาะ", "de_DE": "Spezifische Route wählen", "es_ES": "Seleccionar ruta específica", "fr_FR": "Sélectionner itinéraire spécifique", "da_DK": "Vælg specifik rute", "sv_SE": "<PERSON><PERSON><PERSON>j specifik rutt", "fi_FI": "<PERSON><PERSON><PERSON> tietty reitti", "nb_NO": "Velg spesifikk rute", "nl_NL": "Selecteer specifieke route", "ru_RU": "Выбрать конкретный маршрут", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> konkretną trasę", "pt_PT": "Selecionar rota específica", "it_IT": "Seleziona percorso specifico", "ro_RO": "Selectează rută specifică", "ms_MY": "<PERSON><PERSON><PERSON> k<PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> tuyến đường cụ thể", "id_ID": "<PERSON><PERSON>h rute spesifik", "fil_PH": "<PERSON><PERSON><PERSON> ang tukoy na ruta", "cs_CZ": "Vybrat konkrétní trasu", "el_GR": "Επιλογή συγκεκριμένης διαδρομής", "pt_BR": "Selecionar rota específica", "hu_HU": "Konkrét útvonal kiválasztása", "tr_TR": "<PERSON><PERSON><PERSON> rota seç", "sk_SK": "Vybrať konkrétnu trasu"}, "execute_function": "convert_specific_route"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_ROUTE_RECOMMENDATION", "category": "guide", "display_name": "推荐路线", "en_display_name": "Route Recommendation", "desc": "Generate route recommendations and confirm user's intention based on their query and available routes.", "desc_chinese": "导览功能,基于用户需求和可用路线生成推荐，并确认用户意图。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original query or requirements", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "推荐路线", "zh_TW": "推薦路線", "zh_GD": "推薦路線", "en_US": "Route Recommendation", "ja_JP": "ルート推奨", "ko_KR": "루트 추천", "th_TH": "แนะนำเส้นทาง", "de_DE": "Routenempfehlung", "es_ES": "Recomendación de ruta", "fr_FR": "Recommandation d'itinéraire", "da_DK": "Ruteanbefaling", "sv_SE": "Ruttrekommendation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Ruteanbefaling", "nl_NL": "Route aanbeveling", "ru_RU": "Рекомендация маршрута", "pl_PL": "Rekomendacja trasy", "pt_PT": "Recomendação de rota", "it_IT": "Raccomandazione percorso", "ro_RO": "Recomanda<PERSON> rut<PERSON>", "ms_MY": "Cadangan la<PERSON>an", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị tuyến đường", "id_ID": "Rekomendasi rute", "fil_PH": "Rekomendasyon ng ruta", "cs_CZ": "Doporučení trasy", "el_GR": "Σύσταση διαδρομής", "pt_BR": "Recomendação de rota", "hu_HU": "Útvonal ajánlás", "tr_TR": "Rota önerisi", "sk_SK": "Odporúčanie trasy"}, "execute_function": "generate_route_recommendation"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_IMMEDIATELY", "category": "guide", "display_name": "直接开始", "en_display_name": "Start Immediately", "desc": "Start the current task or process immediately.", "desc_chinese": "立即开始当前任务或流程。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "直接开始", "zh_TW": "直接開始", "zh_GD": "直接開始", "en_US": "Start Immediately", "ja_JP": "すぐに開始", "ko_KR": "즉시 시작", "th_TH": "เริ่มทันที", "de_DE": "Sofort beginnen", "es_ES": "Comenzar inmediatamente", "fr_FR": "Commencer immédiatement", "da_DK": "Start med det samme", "sv_SE": "<PERSON><PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Start umiddelbart", "nl_NL": "<PERSON><PERSON>", "ru_RU": "Начать немедленно", "pl_PL": "Rozpocznij natychmiast", "pt_PT": "Começar imediatamente", "it_IT": "Inizia immediatamente", "ro_RO": "<PERSON><PERSON><PERSON> im<PERSON>", "ms_MY": "<PERSON>la serta-merta", "vi_VN": "<PERSON><PERSON><PERSON> đ<PERSON>u ngay lập tức", "id_ID": "<PERSON><PERSON> segera", "fil_PH": "<PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Αρχίστε αμέσως", "pt_BR": "Começar imediatamente", "hu_HU": "Azonnal kezdés", "tr_TR": "<PERSON><PERSON> b<PERSON>", "sk_SK": "Začať okamžite"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL", "category": "web_browser", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Web Search.Search for information that users want to know through google or official website.", "desc_chinese": "网络搜索，例如股票、门票、新闻等。推荐使用「百度」搜索引擎；公司官网等特定网站直接通过对应网址打开。", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "Must be a legitimate https or http link.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打开浏览器", "zh_TW": "打開瀏覽器", "zh_GD": "打開瀏覽器", "en_US": "Open Browser", "ja_JP": "ブラウザを開く", "ko_KR": "브라우저 열기", "th_TH": "เปิดเบราว์เซอร์", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "da_DK": "Åbn browser", "sv_SE": "Öppna webbläsare", "fi_FI": "<PERSON><PERSON> selain", "nb_NO": "<PERSON><PERSON><PERSON> net<PERSON>", "nl_NL": "Browser openen", "ru_RU": "Открыть браузер", "pl_PL": "Otwórz przeglądarkę", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Apri browser", "ro_RO": "Deschide browser", "ms_MY": "<PERSON><PERSON> pelayar", "vi_VN": "Mở trình <PERSON>", "id_ID": "Buka browser", "fil_PH": "B<PERSON>san ang browser", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Άνοιγμα περιηγητή", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Böngés<PERSON><PERSON>", "tr_TR": "Tarayıcıyı aç", "sk_SK": "Otvoriť prehliadač"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SET_VOLUME", "category": "system_control", "display_name": "调整音量", "en_display_name": "Adjust Volume", "desc": "Adjust the volume. The default increase or decrease range is between 10 and 30.", "desc_chinese": "调整音量。调整的幅度是10或30，根据用户语气选择", "parameters": [{"name": "volume_level", "type": "int", "desc": "The volume level to be set.", "is_required": true, "length": null, "enum_constant": null, "max": 100, "min": 0, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调整音量", "zh_TW": "調整音量", "zh_GD": "調整音量", "en_US": "Adjust Volume", "ja_JP": "音量調整", "th_TH": "ปรับระดับเสียง", "de_DE": "Lautstärke anpassen", "es_ES": "Ajustar volumen", "ko_KR": "볼륨 조절", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Säädä äänenvoimakkuutta", "nb_NO": "<PERSON><PERSON>", "fr_FR": "Ajuster le volume", "nl_NL": "Volume aanpassen", "ru_RU": "Настроить громкость", "pl_PL": "<PERSON><PERSON><PERSON><PERSON>", "pt_PT": "Ajustar volume", "it_IT": "Regola volume", "ro_RO": "Ajustează volumul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>i<PERSON>u chỉnh âm lượng", "id_ID": "Sesuaikan volume", "fil_PH": "<PERSON><PERSON><PERSON> ang lakas ng tunog", "cs_CZ": "Upravit h<PERSON>", "el_GR": "Ρύθμιση έντασης", "pt_BR": "Ajustar volume", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON> sevi<PERSON><PERSON> a<PERSON>", "sk_SK": "Upraviť hlasitosť"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Say. Basic communication with the user.", "desc_chinese": "说话，与用户的基础交流。", "parameters": [{"name": "text", "type": "string", "desc": "Speak in the first person, using pure text without emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "说", "zh_TW": "說", "zh_GD": "說", "en_US": "Say", "ja_JP": "話す", "th_TH": "พูด", "de_DE": "Sagen", "es_ES": "Decir", "ko_KR": "말하기", "da_DK": "Sig", "sv_SE": "Säg", "fi_FI": "Sanoa", "nb_NO": "Si", "fr_FR": "Dire", "nl_NL": "Zeggen", "ru_RU": "Сказать", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON>", "it_IT": "Dire", "ro_RO": "Spune", "ms_MY": "Berkata", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Mengatakan", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Říci", "el_GR": "Πούμε", "pt_BR": "<PERSON><PERSON>", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "Söylemek", "sk_SK": "Povedať"}, "execute_function": "say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY_FOR_CLARIFICATION", "category": "interaction", "display_name": "澄清用户问题", "en_display_name": "Clarify User Question", "desc": "Clarify the user's question. When you need to clarify the user's question, please use this tool.", "desc_chinese": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具", "parameters": [{"name": "request_clarify_text", "type": "string", "desc": "Ask user to clarify the question, using pure text without emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "clarify"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SILENT", "category": "guide", "display_name": "默认兜底技能", "en_display_name": "Silent", "desc": "A default fallback skill that should be selected when the user's query has low relevance to other available actions.", "desc_chinese": "默认兜底的技能，在以下情况应选择此动作：用户的查询与其他可用动作相关性较低时，默认选择这个技能", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "默认兜底技能", "zh_TW": "默認兜底技能", "zh_GD": "默認兜底技能", "en_US": "Silent", "ja_JP": "沈黙", "ko_KR": "무음", "th_TH": "เงียบ", "de_DE": "Schweigen", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "Silence", "da_DK": "Stilhed", "sv_SE": "Tystnad", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Stillhet", "nl_NL": "Stilte", "ru_RU": "Молчание", "pl_PL": "Cisza", "pt_PT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "<PERSON><PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "Im lặng", "id_ID": "<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON>", "el_GR": "Σιωπή", "pt_BR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hu_HU": "Csend", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "<PERSON><PERSON><PERSON>"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CANCEL", "category": "system_control", "display_name": "取消", "en_display_name": "Cancel", "desc": "Cancel the current behavior, such as dancing, nodding, navigating, or speaking (e.g., stop or pause speaking).", "desc_chinese": "取消当前行为，例如跳舞、点头、导航或说话（如停止或暂停说话）。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "取消", "zh_TW": "取消", "zh_GD": "取消", "en_US": "Cancel", "ja_JP": "キャンセル", "th_TH": "ยกเลิก", "de_DE": "Abbrechen", "es_ES": "<PERSON><PERSON><PERSON>", "ko_KR": "취소", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Peruuta", "nb_NO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Annuler", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Отменить", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Anulează", "ms_MY": "<PERSON><PERSON>", "vi_VN": "Hủy bỏ", "id_ID": "<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "Zrušit", "el_GR": "Ακύρωση", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "M<PERSON>gs<PERSON>", "tr_TR": "İptal", "sk_SK": "Zrušiť"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "EXIT", "category": "system_control", "display_name": "退出", "en_display_name": "Exit", "desc": "Exit the current application, suitable for scenarios such as ending guided tours and exiting applications.", "desc_chinese": "退出当前应用", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出", "zh_TW": "退出", "zh_GD": "退出", "en_US": "Exit", "ja_JP": "終了", "ko_KR": "종료", "th_TH": "ออก", "de_DE": "<PERSON>den", "es_ES": "Salir", "fr_FR": "<PERSON><PERSON><PERSON>", "da_DK": "A<PERSON>lut", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Avslutt", "nl_NL": "Afsluiten", "ru_RU": "Выход", "pl_PL": "Wyjście", "pt_PT": "<PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Ieșire", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "Lu<PERSON><PERSON>", "cs_CZ": "Konec", "el_GR": "Έξοδος", "pt_BR": "<PERSON><PERSON>", "hu_HU": "Kilépés", "tr_TR": "Çıkış", "sk_SK": "Koniec"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "BACK", "category": "system_control", "display_name": "返回上一级", "en_display_name": "Back", "desc": "Back to the previous level.", "desc_chinese": "返回上一级", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "返回上一级", "zh_TW": "返回上一級", "zh_GD": "返回上一級", "en_US": "Back", "ja_JP": "戻る", "ko_KR": "뒤로", "th_TH": "กลับ", "de_DE": "Zurück", "es_ES": "Atrás", "fr_FR": "Retour", "da_DK": "Tilbage", "sv_SE": "Tillbaka", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Tilbake", "nl_NL": "Terug", "ru_RU": "Назад", "pl_PL": "Wstecz", "pt_PT": "Voltar", "it_IT": "Indietro", "ro_RO": "Înapoi", "ms_MY": "Kembali", "vi_VN": "Quay lại", "id_ID": "Kembali", "fil_PH": "Bumalik", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Πίσω", "pt_BR": "Voltar", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Späť"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "NEXT", "category": "system_control", "display_name": "下一步", "en_display_name": "Next", "desc": "Proceed to the next step or point.", "desc_chinese": "下一步", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "下一步", "zh_TW": "下一步", "zh_GD": "下一步", "en_US": "Next", "ja_JP": "次へ", "ko_KR": "다음", "th_TH": "ถัดไป", "de_DE": "<PERSON><PERSON>", "es_ES": "Siguient<PERSON>", "fr_FR": "Suivant", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Neste", "nl_NL": "Volgende", "ru_RU": "Далее", "pl_PL": "Następny", "pt_PT": "Próximo", "it_IT": "Successivo", "ro_RO": "Următorul", "ms_MY": "Seterusnya", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> theo", "id_ID": "Selanjutnya", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Dalš<PERSON>", "el_GR": "Επόμενο", "pt_BR": "Próximo", "hu_HU": "Következő", "tr_TR": "İleri", "sk_SK": "Ďalší"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CONFIRM", "category": "system_control", "display_name": "确认", "en_display_name": "Confirm", "desc": "Confirm the operation.", "desc_chinese": "用户确认操作", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "确认", "zh_TW": "確認", "zh_GD": "確認", "en_US": "Confirm", "ja_JP": "確認", "ko_KR": "확인", "th_TH": "ยืนยัน", "de_DE": "Bestätigen", "es_ES": "Confirmar", "fr_FR": "Confirmer", "da_DK": "Bekræft", "sv_SE": "Bekräfta", "fi_FI": "Vahvista", "nb_NO": "Bekreft", "nl_NL": "Bevestigen", "ru_RU": "Подтвердить", "pl_PL": "Potwierdź", "pt_PT": "Confirmar", "it_IT": "Conferma", "ro_RO": "Confirmă", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "Potvrdit", "el_GR": "Επιβεβαίωση", "pt_BR": "Confirmar", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Potvrdiť"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "MULTIMEDIA_PLAY", "category": "system_control", "display_name": "播放", "en_display_name": "Play", "desc": "Triggered to play a video", "desc_chinese": "播放视频", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "播放", "zh_TW": "播放", "zh_GD": "播放", "en_US": "Play", "ja_JP": "再生", "th_TH": "เล่น", "de_DE": "Abspielen", "es_ES": "Reproducir", "ko_KR": "재생", "da_DK": "Afspil", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "Toista", "nb_NO": "Spill av", "fr_FR": "<PERSON><PERSON>", "nl_NL": "Afspelen", "ru_RU": "Воспроизвести", "pl_PL": "Odtwórz", "pt_PT": "Reproduzir", "it_IT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ro_RO": "Redare", "ms_MY": "Main", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Putar", "fil_PH": "I-play", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Αναπαραγωγή", "pt_BR": "Reproduzir", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Prehrať"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SERVICE_RESUME", "category": "system_control", "display_name": "继续服务", "en_display_name": "Resume Service", "desc": "Triggered in scenarios such as resuming playback or restarting a service, typically after a pause or interruption.", "desc_chinese": "继续播放、继续服务等场景", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MULTIMEDIA_PLAY", "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "继续服务", "zh_TW": "繼續服務", "zh_GD": "繼續服務", "en_US": "Resume Service", "ja_JP": "サービス再開", "th_TH": "เดินหน้าการบริการ", "de_DE": "Service fortsetzen", "es_ES": "<PERSON><PERSON><PERSON> servicio", "ko_KR": "서비스 재개", "da_DK": "Genoptag service", "sv_SE": "Återuppta service", "fi_FI": "Jatka palvelua", "nb_NO": "Gjenoppta tje<PERSON>", "fr_FR": "Reprendre le service", "nl_NL": "Service hervatten", "ru_RU": "Возобновить сервис", "pl_PL": "Wznów usługę", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON><PERSON> servizio", "ro_RO": "Reluarea serviciului", "ms_MY": "Sambung perkhidmatan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> d<PERSON> vụ", "id_ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "fil_PH": "Ipagpatuloy ang serbisyo", "cs_CZ": "Pokračovat ve službě", "el_GR": "Συνέχιση υπηρεσίας", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Szolgáltatás folytatása", "tr_TR": "Hizmeti devam ettir", "sk_SK": "Pokračovať v službe"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "COMMON_PAUSE", "category": "guide", "display_name": "暂停", "en_display_name": "Pause", "desc": "Pause the guide narration, media playback, or movement. Commonly triggered when users want to temporarily stop an action, such as saying 'don't go', 'stay still', 'wait for me', or 'hold on'.", "desc_chinese": "暂停导览讲解过程。例如「暂停播放视频」或者「暂停移动」。常见于用户希望暂停某个动作的场景，如表达\"别走了\"\"别动了\"\"等下我\"或\"等一等\"等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "暂停", "zh_TW": "暫停", "zh_GD": "暫停", "en_US": "Pause", "ja_JP": "一時停止", "th_TH": "หยุดชั่วคราว", "de_DE": "Pausieren", "es_ES": "Pausar", "ko_KR": "일시 정지", "da_DK": "Pause", "sv_SE": "Pausa", "fi_FI": "Keskeytä", "nb_NO": "Pause", "fr_FR": "Pause", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Пауза", "pl_PL": "<PERSON><PERSON>", "pt_PT": "Pausar", "it_IT": "Pausa", "ro_RO": "Pauză", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON>", "fil_PH": "I-pause", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Παύση", "pt_BR": "Pausar", "hu_HU": "Szünet", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Pozastaviť"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "COMMON_REPEAT", "category": "guide", "display_name": "重新讲解", "en_display_name": "Repeat", "desc": "After a round of explanation ends, it enters the scoring session, and the user wants to re-explain.", "desc_chinese": "当一轮讲解结束后，进入打分环节，用户想重新进行讲解。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "重新讲解", "zh_TW": "重新講解", "zh_GD": "重新講解", "en_US": "Repeat", "ja_JP": "再説明", "ko_KR": "다시 설명", "th_TH": "อธิบายซ้ำ", "de_DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON>r", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "da_DK": "Gentag", "sv_SE": "Upprepa", "fi_FI": "Toista", "nb_NO": "<PERSON><PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Повторить", "pl_PL": "Powtórz", "pt_PT": "<PERSON><PERSON>r", "it_IT": "R<PERSON><PERSON>", "ro_RO": "Repetă", "ms_MY": "Ulang", "vi_VN": "Lặp lại", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Opakovat", "el_GR": "Επαναλάβετε", "pt_BR": "<PERSON><PERSON>r", "hu_HU": "Ismételje meg", "tr_TR": "Tekrarla", "sk_SK": "Opakovať"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ROUTES_OTHERS", "category": "guide", "display_name": "选择其他导览路线", "en_display_name": "Choose Another Route", "desc": "Choose another route", "desc_chinese": "选择其他导览路线。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "选择其他导览路线", "zh_TW": "選擇其他導覽路線", "zh_GD": "選擇其他導覽路線", "en_US": "Choose Another Route", "ja_JP": "別のルートを選択", "ko_KR": "다른 루트 선택", "th_TH": "เลือกเส้นทางอื่น", "de_DE": "Andere Route wählen", "es_ES": "Elegir otra ruta", "fr_FR": "Choisir un autre itinéraire", "da_DK": "<PERSON><PERSON><PERSON><PERSON> anden rute", "sv_SE": "<PERSON><PERSON><PERSON><PERSON> rutt", "fi_FI": "Valitse toinen reitti", "nb_NO": "Velg annen rute", "nl_NL": "Kies andere route", "ru_RU": "Выбрать другой маршрут", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> inną trasę", "pt_PT": "Escolher outra rota", "it_IT": "<PERSON><PERSON><PERSON> altro percorso", "ro_RO": "Alege altă rută", "ms_MY": "<PERSON><PERSON><PERSON> la<PERSON>an lain", "vi_VN": "<PERSON><PERSON><PERSON> tuy<PERSON>n kh<PERSON>c", "id_ID": "<PERSON>lih rute lain", "fil_PH": "<PERSON><PERSON><PERSON> ang ibang ruta", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON> jinou trasu", "el_GR": "Επιλέξτε άλλη διαδρομή", "pt_BR": "Escolher outra rota", "hu_HU": "Válasszon másik útvonalat", "tr_TR": "Başka rota seç", "sk_SK": "Vybrať inú trasu"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SCORE", "category": "guide", "display_name": "打分", "en_display_name": "Score", "desc": "Score.", "desc_chinese": "评分。", "parameters": [{"name": "score", "type": "int", "desc": "score 1-5 代表五颗星", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打分", "zh_TW": "打分", "zh_GD": "打分", "en_US": "Score", "ja_JP": "評価", "ko_KR": "점수", "th_TH": "ให้คะแนน", "de_DE": "Bewerten", "es_ES": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Noter", "da_DK": "Score", "sv_SE": "Poäng", "fi_FI": "Pisteet", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Score", "ru_RU": "Оценка", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "Pontuação", "it_IT": "<PERSON><PERSON><PERSON><PERSON>", "ro_RO": "<PERSON><PERSON>", "ms_MY": "Skor", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "Skor", "fil_PH": "Iskor", "cs_CZ": "Skóre", "el_GR": "Βαθμολογία", "pt_BR": "Pontuação", "hu_HU": "Pontszám", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Skóre"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ADJUST_SPEED", "category": "system_control", "display_name": "调整当前移动速度", "en_display_name": "Adjust Walking Speed", "desc": "Adjust the <current walking speed>.", "desc_chinese": "调整<当前移动速度>。", "parameters": [{"name": "adjusted_speed", "type": "float", "desc": "The robot's walking speed.", "is_required": true, "length": null, "enum_constant": null, "max": 1.2, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调整当前移动速度", "zh_TW": "調整當前移動速度", "zh_GD": "調整當前移動速度", "en_US": "Adjust Walking Speed", "ja_JP": "歩行速度調整", "ko_KR": "보행 속도 조정", "th_TH": "ปรับความเร็วในการเดิน", "de_DE": "Gehgeschwindigkeit anpassen", "es_ES": "Ajustar velocidad de caminar", "fr_FR": "Ajuster la vitesse de marche", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Säädä kä<PERSON>a", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Loopsnelheid a<PERSON>en", "ru_RU": "Настроить скорость ходьбы", "pl_PL": "Dostosuj pręd<PERSON>ć chodzenia", "pt_PT": "Ajustar velocidade de caminhada", "it_IT": "Regola velocità camminata", "ro_RO": "Ajustează viteza de mers", "ms_MY": "<PERSON><PERSON>", "vi_VN": "Điều chỉnh tốc độ đi bộ", "id_ID": "Sesuaikan kecepatan jalan", "fil_PH": "<PERSON><PERSON><PERSON> ang bilis ng paglalakad", "cs_CZ": "Upravit rychlost chůze", "el_GR": "Ρύθμιση ταχύτητας βαδίσματος", "pt_BR": "Ajustar velocidade de caminhada", "hu_HU": "<PERSON><PERSON><PERSON> se<PERSON>g be<PERSON>llí<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON><PERSON><PERSON> hı<PERSON>ı<PERSON>ı a<PERSON>la", "sk_SK": "Upraviť rýchlosť chôdze"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GENERATE_MESSAGE", "category": "interaction", "display_name": "生成", "en_display_name": "Generate", "desc": "Text generation. Only for 'Welcome Someone', '<PERSON><PERSON><PERSON> Someone', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'. Cannot generate 'Suggestion', 'Recommendation', 'Introduction', etc.", "desc_chinese": "文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等", "parameters": [{"name": "goal", "type": "string", "desc": "user instructions", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 90, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "生成", "zh_TW": "生成", "zh_GD": "生成", "en_US": "Generate", "ja_JP": "生成", "th_TH": "สร้าง", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "Generar", "ko_KR": "생성", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "<PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Сгенерировать", "pl_PL": "Generuj", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Genera", "ro_RO": "Generează", "ms_MY": "<PERSON>", "vi_VN": "Tạo ra", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Generovat", "el_GR": "Δημιουργία", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Gener<PERSON><PERSON><PERSON>", "tr_TR": "Oluştur", "sk_SK": "Generovať"}, "execute_function": "generate_message"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "GO_CHARGING", "category": "system_admin", "display_name": "去充电", "en_display_name": "Go to Charging", "desc": "Go to the charging station", "desc_chinese": "自动去充电桩充电", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "去充电", "zh_TW": "去充電", "zh_GD": "去充電", "en_US": "Go to Charging", "ja_JP": "充電に行く", "ko_KR": "충전하러 가기", "th_TH": "ไปชาร์จ", "de_DE": "Zum Laden gehen", "es_ES": "Ir a cargar", "fr_FR": "Aller charger", "da_DK": "Gå til opladning", "sv_SE": "Gå till laddning", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Gå til lading", "nl_NL": "Ga laden", "ru_RU": "Иди заряжаться", "pl_PL": "<PERSON><PERSON><PERSON> ładować", "pt_PT": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Vai a caricare", "ro_RO": "Du-te să te încarci", "ms_MY": "<PERSON><PERSON> men<PERSON>", "vi_VN": "<PERSON><PERSON>", "id_ID": "<PERSON><PERSON> mengisi daya", "fil_PH": "Pumunta sa pag-charge", "cs_CZ": "<PERSON><PERSON><PERSON>", "el_GR": "Πήγαινε για φόρτιση", "pt_BR": "<PERSON><PERSON><PERSON><PERSON>", "hu_HU": "<PERSON><PERSON>", "tr_TR": "Şarj olma<PERSON> git", "sk_SK": "Ísť sa nabiť"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ANSWER_KNOWLEDGE_QUESTION", "category": "information", "display_name": "找答案", "en_display_name": "Find Answer", "desc": "Query knowledge to answer user questions, including 'encyclopedic knowledge,' 'company products,' 'employee information,' 'city and scenic spot introductions,' and 'travel planning.'", "desc_chinese": "查询知识回答用户的问题，包含「百科知识」、「公司产品」、「员工信息」、「城市美食、景点介绍」、「旅游计划规划」等", "parameters": [{"name": "question", "type": "string", "desc": "The user's question has been summarized based on the context of the conversation.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 600, "result_schema": [{"name": "knowledge_content", "desc": "The knowledge content.", "type": "string", "enum_constant": null}], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.KNOWLEDGE_QA", "version": "oversea_v1.0.4", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "找答案", "zh_TW": "找答案", "zh_GD": "找答案", "en_US": "Find Answer", "ja_JP": "答えを見つける", "ko_KR": "답 찾기", "th_TH": "หาคำตอบ", "de_DE": "Antwort finden", "es_ES": "Encontrar respuesta", "fr_FR": "Trouver la réponse", "da_DK": "Find svar", "sv_SE": "<PERSON><PERSON> svar", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON> svar", "nl_NL": "Antwoord vinden", "ru_RU": "Найти ответ", "pl_PL": "Znajdź odpowiedź", "pt_PT": "Encontrar resposta", "it_IT": "<PERSON><PERSON><PERSON> ris<PERSON>a", "ro_RO": "Găsiți răspunsul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> c<PERSON>u trả lời", "id_ID": "<PERSON><PERSON>n", "fil_PH": "<PERSON><PERSON><PERSON> ang sagot", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Βρείτε την απάντηση", "pt_BR": "Encontrar resposta", "hu_HU": "V<PERSON>lasz k<PERSON>ése", "tr_TR": "Cevap bul", "sk_SK": "Nájsť odpoveď"}, "execute_function": "knowledge_qa"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CLICK", "category": "web_browser", "display_name": "点击", "en_display_name": "Click", "desc": "Simulate web page click.", "desc_chinese": "模拟网页点击。", "parameters": [{"name": "element_tag", "type": "enum", "desc": "可点击元素的标签", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_clickable_elements_value"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "点击", "zh_TW": "點擊", "zh_GD": "點擊", "en_US": "Click", "ja_JP": "クリック", "ko_KR": "클릭", "th_TH": "คลิก", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON> clic", "fr_FR": "C<PERSON>r", "da_DK": "Klik", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Klikk", "nl_NL": "Klikken", "ru_RU": "Щелчок", "pl_PL": "<PERSON><PERSON><PERSON><PERSON>", "pt_PT": "Clique", "it_IT": "Fare clic", "ro_RO": "Click", "ms_MY": "Klik", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "Klik", "fil_PH": "I-click", "cs_CZ": "Kliknout", "el_GR": "Κάντε κλικ", "pt_BR": "Clique", "hu_HU": "Kattintás", "tr_TR": "Tıkla", "sk_SK": "Kliknite"}, "post_processing": "convert_element_id"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ANSWER_QUESTION_FROM_VISION", "category": "information", "display_name": "根据视觉回答", "en_display_name": "Answer from Vision", "desc": "In front of the robot, only answer questions related to 'clothing, expression, gender,' 'surrounding environment,' and 'object recognition.'", "desc_chinese": "在机器人面前，仅回答有关「穿着、表情、性别」、「周边环境」、「物体识别」问题，无法回答用户关系的话题。", "parameters": [{"name": "image_url", "type": "string", "desc": "视觉采集的图片url", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "question", "type": "string", "desc": "用户的问题，必须总结为第一人称的问题，长度限制在15字以内。", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The answer of the question", "type": "string", "enum_constant": null}], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "根据视觉回答", "zh_TW": "根據視覺回答", "zh_GD": "根據視覺回答", "en_US": "Answer from Vision", "ja_JP": "視覚から回答", "ko_KR": "시각으로 답변", "th_TH": "ตอบจากการมองเห็น", "de_DE": "Antwort aus Sicht", "es_ES": "Responder desde visión", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON> depuis la vision", "da_DK": "<PERSON>var fra syn", "sv_SE": "<PERSON><PERSON><PERSON> från syn", "fi_FI": "Vastaa näöstä", "nb_NO": "<PERSON>var fra syn", "nl_NL": "Ant<PERSON><PERSON> van zicht", "ru_RU": "Ответить по зрению", "pl_PL": "Odpowiedz z wizji", "pt_PT": "Responder da visão", "it_IT": "<PERSON><PERSON><PERSON><PERSON> dalla visione", "ro_RO": "Răspunde din vedere", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> lời từ thị gi<PERSON>c", "id_ID": "<PERSON><PERSON><PERSON> da<PERSON>", "fil_PH": "Sumagot mula sa paningin", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t ze zraku", "el_GR": "Απάντηση από όραση", "pt_BR": "Responder da visão", "hu_HU": "Válaszolj a látásból", "tr_TR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cevap ver", "sk_SK": "Odpovedať zo zraku"}, "execute_function": "answer_question_from_vision"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REALTIME_SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Think about it.", "desc_chinese": "思考说", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "stream_say"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OUTDOOR_NAVIGATE", "category": "navigation", "display_name": "室外导航", "en_display_name": "Outdoor Navigation", "desc": "Provide outdoor navigation capability. Select this action when the user wants to know how to reach a place.", "desc_chinese": "", "parameters": [{"name": "origin", "type": "string", "desc": "Route origin, provided by the user. Set the value to \"-1\" if indeterminate.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "destination", "type": "string", "desc": "Route destination. Provided by the user", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "travelmode", "type": "enum", "desc": "Default to driving if not provided by the user.", "is_required": true, "length": null, "enum_constant": ["driving", "walking", "bicycling", "transit"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "oversea", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "室外导航", "zh_TW": "室外導航", "zh_GD": "室外導航", "en_US": "Outdoor Navigation", "ja_JP": "屋外ナビゲーション", "ko_KR": "실외 내비게이션", "th_TH": "การนำทางกลางแจ้ง", "de_DE": "Außennavigation", "es_ES": "Navegación exterior", "fr_FR": "Navigation extérieure", "da_DK": "Udendørs navigation", "sv_SE": "Utomhusnavigering", "fi_FI": "Ulkonavigaatio", "nb_NO": "Utendørs navigasjon", "nl_NL": "Buitennavigatie", "ru_RU": "Наружная навигация", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>nętrz<PERSON>", "pt_PT": "Navegação exterior", "it_IT": "Navigazione esterna", "ro_RO": "Navigație exterioară", "ms_MY": "Navigasi luar", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> hướng ngoài trời", "id_ID": "<PERSON><PERSON><PERSON><PERSON> luar ruangan", "fil_PH": "Outdoor navigation", "cs_CZ": "Venkovní navigace", "el_GR": "Εξωτερική πλοήγηση", "pt_BR": "Navegação exterior", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Dış mekan navigasyon", "sk_SK": "Vonkajšia navigácia"}, "post_processing": "overseas_map_dir_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RECOMMEND_PLACES", "category": "other", "display_name": "推荐", "en_display_name": "Recommend", "desc": "Recommendations for various places, such as restaurants, attractions, shopping areas, bars, KTVs, subways, etc", "desc_chinese": "", "parameters": [{"name": "target", "type": "string", "desc": "The target location that the user wants to go to", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "ref_location", "type": "string", "desc": "The starting point. Search based on this location. Don't use <Current Indoor Point>. If not specificed by user, set ref_location -1)", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "oversea", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "推荐", "zh_TW": "推薦", "zh_GD": "推薦", "en_US": "Recommend", "ja_JP": "推奨", "ko_KR": "추천", "th_TH": "แนะนำ", "de_DE": "Empfehlen", "es_ES": "Recomendar", "fr_FR": "Recommander", "da_DK": "Anbefal", "sv_SE": "Re<PERSON>mmender<PERSON>", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Anbefal", "nl_NL": "Aanbevelen", "ru_RU": "Рекомендовать", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "Recomendar", "it_IT": "Ra<PERSON>mand<PERSON>", "ro_RO": "Recomandă", "ms_MY": "Cadangkan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị", "id_ID": "Rekomendasikan", "fil_PH": "Irekomenda", "cs_CZ": "Doporučit", "el_GR": "Συστήνω", "pt_BR": "Recomendar", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Önermek", "sk_SK": "Odporučiť"}, "post_processing": "overseas_map_search_url"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "NAVIGATE_REC_START", "category": "navigation", "display_name": "准备领位", "en_display_name": "Indoor Navigation Recommendation", "desc": "Point navigation intent. Users can only be taken to the locations provided below, and outdoor locations are not supported.If the user does not explicitly specify the destination, a list of up to 4 available navigation points is returned and sorted by similarity, with the closest point at the front.", "desc_chinese": "室内导航意图。不支持去室外位置，应直接根据「CHAT CONVERSATION」中的对话历史和用户特征（包括但不限于性别信息），为用户选择最符合其真实意图的具体地点。", "parameters": [{"name": "destinations", "type": "String array", "desc": "Navigation points that match user intent. You can choose multiple. ", "is_required": false, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_robot_support_map_points"}, {"name": "guide_text", "type": "string", "desc": "A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 300, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "准备领位", "zh_TW": "準備領位", "zh_GD": "準備領位", "en_US": "Indoor Navigation Recommendation", "ja_JP": "屋内ナビゲーション推奨", "ko_KR": "실내 내비게이션 추천", "th_TH": "คำแนะนำการนำทางในร่ม", "de_DE": "Innennavigationsempfehlung", "es_ES": "Recomendación navegación interior", "fr_FR": "Recommandation navigation intérieure", "da_DK": "Indendørs navigationsanbefaling", "sv_SE": "Inomhusnavigeringsrekommendation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Innendørs navigasjonsanbefaling", "nl_NL": "Binnennavigatie a<PERSON>", "ru_RU": "Рекомендация внутренней навигации", "pl_PL": "Rekomendacja nawigacji wewnętrznej", "pt_PT": "Recomendação navegação interior", "it_IT": "Raccomandazione navigazione interna", "ro_RO": "Recomandare navigație interioară", "ms_MY": "Cadangan navigasi dalam ruangan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị điều hướng trong nhà", "id_ID": "Rekomendasi navigasi dalam ruangan", "fil_PH": "Rekomendasyon ng indoor navigation", "cs_CZ": "Doporučení vnitřní navigace", "el_GR": "Σύσταση εσωτερικής πλοήγησης", "pt_BR": "Recomendação navegação interior", "hu_HU": "<PERSON><PERSON><PERSON> na<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "İç mekan navigasyon önerisi", "sk_SK": "Odporúčanie vnútornej navigácie"}, "post_processing": "convert_navigation_points"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL_DEFINED", "category": "web_browser", "display_name": "打开轻应用", "en_display_name": "Open Light App", "desc": "Use a browser or app to browse information. Trigger this action if there's a semantically identical question in the predefined list, as it allows you to directly open the URL or app.", "desc_chinese": "使用浏览器或者APP浏览信息。*只要*预定义问题列表中存在与用户当前问题语义相同的问题，则选择此action。", "parameters": [{"name": "predefined_question", "type": "enum", "desc": "list of predefined question.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_light_app_enum"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "oversea_v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": true, "page_id": "", "display_names": {"zh_CN": "打开轻应用", "zh_TW": "打開輕應用", "zh_GD": "打開輕應用", "en_US": "Open Light App", "ja_JP": "ライトアプリを開く", "ko_KR": "라이트 앱 열기", "th_TH": "เปิดแอปพลิเคชันเบา", "de_DE": "Light App <PERSON>", "es_ES": "Abrir aplicación ligera", "fr_FR": "Ouvrir application légère", "da_DK": "Åbn let app", "sv_SE": "Öppna lätt app", "fi_FI": "<PERSON><PERSON> kevyt sovellus", "nb_NO": "Å<PERSON>ne lett app", "nl_NL": "Lichte app openen", "ru_RU": "Открыть легкое приложение", "pl_PL": "O<PERSON><PERSON><PERSON><PERSON> aplikację", "pt_PT": "Abrir aplicação leve", "it_IT": "Apri app leggera", "ro_RO": "Deschide aplicația ușoară", "ms_MY": "<PERSON><PERSON> a<PERSON><PERSON>an", "vi_VN": "Mở ứng dụng nhẹ", "id_ID": "<PERSON><PERSON> a<PERSON><PERSON>an", "fil_PH": "Buksan ang light app", "cs_CZ": "Otev<PERSON><PERSON><PERSON> aplikaci", "el_GR": "Άνοιγμα ελαφριάς εφαρμογής", "pt_BR": "Abrir aplicativo leve", "hu_HU": "Könnyű alkalmazás megnyitása", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Otvoriť ľahkú aplikáciu"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "WEATHER", "category": "weather", "display_name": "获取天气信息", "en_display_name": "Get Weather Information", "desc": "Get weather information. Time: 'Today and the next 10 days'. City must be in English.", "desc_chinese": "获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！！！", "parameters": [{"name": "area_level", "type": "enum", "desc": "city 对应的区域等级", "is_required": false, "length": null, "enum_constant": ["province", "city", "area"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "city", "type": "string", "desc": "城市名称，最小粒度到区/县。", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_a8c9098ad7a91a66287b25d6befef6ec", "system_4f307c6d4cb0f187a3edb3dcc6f43749", "system_024280e32e73c00ad621710870d4cb18"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "oversea_v1.0.4", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "获取天气信息", "zh_TW": "獲取天氣資訊", "zh_GD": "獲取天氣資訊", "en_US": "Get Weather Information", "ja_JP": "天気情報取得", "ko_KR": "날씨 정보 얻기", "th_TH": "รับข้อมูลสภาพอากาศ", "de_DE": "Wetterinformationen abrufen", "es_ES": "Obtener información del tiempo", "fr_FR": "Obtenir informations météo", "da_DK": "Få vejrinformation", "sv_SE": "Få väderinformation", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Få værinformasjon", "nl_NL": "Weerinformatie ophalen", "ru_RU": "Получить информацию о погоде", "pl_PL": "Uzyskaj informacje o pogodzie", "pt_PT": "Obter informações meteorológicas", "it_IT": "Ottieni informazioni meteo", "ro_RO": "Obține informații meteo", "ms_MY": "Dapatkan maklumat cuaca", "vi_VN": "<PERSON><PERSON><PERSON> thông tin thời tiết", "id_ID": "Dapatkan informasi cuaca", "fil_PH": "Makakuha ng impormasyon ng panahon", "cs_CZ": "Získat informace o počasí", "el_GR": "Λήψη πληροφοριών καιρού", "pt_BR": "Obter informações meteorológicas", "hu_HU": "Időjárási információk lekérése", "tr_TR": "Hava durumu bilgisi al", "sk_SK": "Získať informácie o počasí"}, "execute_function": "query_weather", "post_processing": "query_weather_post_processing"}]}