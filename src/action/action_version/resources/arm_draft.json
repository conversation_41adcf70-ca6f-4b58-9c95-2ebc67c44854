{"version": "arm_draft", "actions": [{"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_CRUISE_MODE", "category": "basic_movement", "display_name": "巡航", "en_display_name": "Cruise", "desc": "Activate autonomous cruise, patrol, or inspection mode.", "desc_chinese": "启动自主巡航/巡逻/巡视模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CRUISE", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "巡航", "zh_TW": "巡航", "zh_GD": "巡航", "en_US": "Cruise", "ja_JP": "巡航", "th_TH": "ลาดตระเวน", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "ko_KR": "순찰", "da_DK": "<PERSON><PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "Partio", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Croisière", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Патруль", "pl_PL": "Patrol", "pt_PT": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Pattuglia", "ro_RO": "<PERSON><PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "Tu<PERSON>n tra", "id_ID": "Patroli", "fil_PH": "Patrol", "cs_CZ": "Hlídka", "el_GR": "Περιπολία", "pt_BR": "<PERSON><PERSON><PERSON><PERSON>", "hu_HU": "Járőrözés", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Hliadka", "ar_SA": "التجوال"}}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "EXIT_CRUISE_MODE", "category": "basic_movement", "display_name": "退出巡逻", "en_display_name": "Exit Cruise", "desc": "Exit or stop the autonomous patrol mode. ", "desc_chinese": "退出或停止巡逻/巡航模式。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.EXIT_CRUISE", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出巡逻", "zh_TW": "退出巡邏", "zh_GD": "退出巡邏", "en_US": "Exit Cruise", "ja_JP": "巡航終了", "th_TH": "ออกจากการลาดตระเวน", "de_DE": "<PERSON><PERSON><PERSON>den", "es_ES": "<PERSON><PERSON>", "ko_KR": "순찰 종료", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Lopeta partio", "nb_NO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON>rouille", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Выйти из патруля", "pl_PL": "Zakończ patrol", "pt_PT": "<PERSON><PERSON> <PERSON>", "it_IT": "<PERSON><PERSON><PERSON> dalla pattuglia", "ro_RO": "Ieși din patrulare", "ms_MY": "<PERSON><PERSON><PERSON> dari rondaan", "vi_VN": "<PERSON><PERSON><PERSON>t khỏi tuần tra", "id_ID": "<PERSON><PERSON><PERSON> dari <PERSON>i", "fil_PH": "Umalis sa patrol", "cs_CZ": "Ukončit hlídku", "el_GR": "Έξοδος από περιπολία", "pt_BR": "<PERSON><PERSON> <PERSON>", "hu_HU": "Járőrözés befejezése", "tr_TR": "<PERSON><PERSON><PERSON><PERSON> ç<PERSON>", "sk_SK": "Ukončiť hliadku", "ar_SA": "الخروج من التجوال"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GIVE_WAY", "category": "basic_movement", "display_name": "让路", "en_display_name": "Give Way", "desc": "Robot will move aside to clear the path for people.", "desc_chinese": "机器人会主动让路，方便他人通行。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COME_FAR", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "让路", "zh_TW": "讓路", "zh_GD": "讓路", "en_US": "Give Way", "ja_JP": "道を譲る", "th_TH": "หลีกทาง", "de_DE": "Platz machen", "es_ES": "Ceder el paso", "ko_KR": "길을 비키다", "da_DK": "Giv plads", "sv_SE": "Ge plats", "fi_FI": "<PERSON>", "nb_NO": "Gi plass", "fr_FR": "<PERSON><PERSON><PERSON> le passage", "nl_NL": "Plaats maken", "ru_RU": "Уступить дорогу", "pl_PL": "Ustąp mi<PERSON>", "pt_PT": "Dar <PERSON>", "it_IT": "Dare la precedenza", "ro_RO": "<PERSON><PERSON> drumul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>hư<PERSON><PERSON> đường", "id_ID": "Memberi jalan", "fil_PH": "Magbigay ng daan", "cs_CZ": "Uhnout z cesty", "el_GR": "Δώσε δρόμο", "pt_BR": "Dar <PERSON>", "hu_HU": "Utat enged", "tr_TR": "Yol ver", "sk_SK": "Ustúpiť z cesty", "ar_SA": "<PERSON><PERSON><PERSON><PERSON><PERSON> الطريق"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_OUTDOOR_NAVIGATION", "category": "navigation", "display_name": "调用地图", "en_display_name": "Open Map", "desc": "Navigate to the outdoor destination.", "desc_chinese": "仅当用户明确要求使用“地图导航”且起点和终点均为中国境内地点时可用，若有海外地点使用`say_for_clarification`工具拒绝。", "parameters": [{"name": "origin", "type": "string", "desc": "Route origin, provided by the user. Set the value to '-1' if indeterminate.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "destination", "type": "string", "desc": "Destination location name. Strip modifiers such as 'nearest', 'nearby', etc. Keep only the core place name.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "mode", "type": "enum", "desc": "The mode of navigation that must be one of `driving`, `walking`, `transit`, or `riding`. Defaults to `driving` when not specified in any available context.", "is_required": true, "length": null, "enum_constant": ["driving", "walking", "transit", "riding"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {}, "post_processing": "convert_map_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ROTATE", "category": "basic_movement", "display_name": "转圈", "en_display_name": "Turn Circle", "desc": "The robot can rotate left or right by a specified angle or number of turns, but not both at the same time. Maximum rotation of 10 turns, exceeding 10 turns should be rejected using the `say_for_clarification` tool.", "desc_chinese": "机器人可向左或向右旋转指定角度或圈数，二者不可同时设置。最多旋转10圈，超出请用`say_for_clarification`工具拒绝。", "parameters": [{"name": "direction", "type": "enum", "desc": "The direction to turn, default is left", "is_required": true, "length": null, "enum_constant": ["left", "right"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "angle", "type": "int", "desc": "The value of the rotation angle", "is_required": false, "length": null, "enum_constant": null, "max": 3600, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "turns", "type": "float", "desc": "Number of turns.", "is_required": false, "length": null, "enum_constant": null, "max": 10, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.TURN_DIRECTION", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "转圈", "zh_TW": "轉圈", "zh_GD": "轉圈", "en_US": "Turn Circle", "ja_JP": "回転", "ko_KR": "회전", "th_TH": "หมุน", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON>", "da_DK": "<PERSON><PERSON>", "sv_SE": "Vänd", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Поворот", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "G<PERSON><PERSON>", "ro_RO": "Rotește", "ms_MY": "Putar", "vi_VN": "Xoay", "id_ID": "Putar", "fil_PH": "Ikot", "cs_CZ": "O<PERSON>č<PERSON>", "el_GR": "Στροφή", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Fordulás", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ar_SA": "دوران"}, "post_processing": "convert_turns_to_degree"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "HEAD_NOD_OR_BOW", "category": "basic_movement", "display_name": "点头", "en_display_name": "Nod Head", "desc": "<PERSON> performs a head nodding or bowing gesture.", "desc_chinese": "机器人执行点头或鞠躬动作。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.HEAD_NOD", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "点头", "zh_TW": "點頭", "zh_GD": "點頭", "en_US": "Nod Head", "ja_JP": "うなずく", "ko_KR": "고개 끄덕이기", "th_TH": "พยักหน้า", "de_DE": "<PERSON><PERSON> nicken", "es_ES": "Asentir con la cabeza", "fr_FR": "Hocher la tête", "da_DK": "Nikke med hovedet", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Nyökkää päätä", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Knikken", "ru_RU": "Кивнуть головой", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> głową", "pt_PT": "Acenar com a cabeça", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Dă din cap", "ms_MY": "Angguk kepala", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON><PERSON>", "fil_PH": "Tumango", "cs_CZ": "Pokývat hlavou", "el_GR": "Νεύμα κεφαλιού", "pt_BR": "Acenar com a cabeça", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "tr_TR": "Başını salla", "sk_SK": "Prikývnuť", "ar_SA": "إيماءة الرأس"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SING_AND_DANCE", "category": "entertainment", "display_name": "唱歌跳舞", "en_display_name": "Sing and Dance", "desc": "<PERSON> performs singing and dancing entertainment routines. Use for entertainment, demonstrations, or to create an engaging atmosphere.", "desc_chinese": "机器人表演唱歌和跳舞。用于音乐播放、娱乐、演示或创造活跃氛围。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.START_DANCE", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "唱歌跳舞", "zh_TW": "唱歌跳舞", "zh_GD": "唱歌跳舞", "en_US": "Sing and Dance", "ja_JP": "歌って踊る", "ko_KR": "노래하고 춤추기", "th_TH": "ร้องเพลงและเต้นรำ", "de_DE": "Singen und tanzen", "es_ES": "Cantar y bailar", "fr_FR": "<PERSON><PERSON> et danser", "da_DK": "Synge og danse", "sv_SE": "Sjunga och dansa", "fi_FI": "<PERSON><PERSON><PERSON> ja tanssia", "nb_NO": "Synge og danse", "nl_NL": "Zingen en dansen", "ru_RU": "Петь и танцевать", "pl_PL": "Śpiewać i tańczyć", "pt_PT": "Cantar e dançar", "it_IT": "Cantare e ballare", "ro_RO": "Cântă și dansează", "ms_MY": "<PERSON><PERSON><PERSON> dan menari", "vi_VN": "H<PERSON><PERSON> v<PERSON> n<PERSON>ả<PERSON>", "id_ID": "<PERSON><PERSON><PERSON> dan menari", "fil_PH": "Kumanta at sumayaw", "cs_CZ": "Zpívat a tančit", "el_GR": "Τραγούδι και χορός", "pt_BR": "Cantar e dançar", "hu_HU": "Énekelni és táncolni", "tr_TR": "Şarkı söyle ve dans et", "sk_SK": "Spievať a tancovať", "ar_SA": "الغناء والرقص"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "USER_REGISTER", "category": "user_recognition", "display_name": "注册", "en_display_name": "Register", "desc": "Register a new user with the system, capturing their name and facial data.", "desc_chinese": "注册新用户，包含姓名和人脸注册。", "parameters": [{"name": "valid_name", "type": "string", "desc": "The user's name, must be a real name, not a pronoun. Leave blank if not provided.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "welcome_message", "type": "string", "desc": "The message to greet the user, default is empty.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_d5a64441247aa63b70dd8d02e3f753f0", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.REGISTER", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "注册", "zh_TW": "註冊", "zh_GD": "註冊", "en_US": "Register", "ja_JP": "登録", "th_TH": "ลงทะเบียน", "de_DE": "Registrieren", "es_ES": "Registrarse", "ko_KR": "등록", "da_DK": "Registrer", "sv_SE": "Registrera", "fi_FI": "Rekisteröidy", "nb_NO": "Registrer", "fr_FR": "S'inscrire", "nl_NL": "Registreren", "ru_RU": "Зарегистрироваться", "pl_PL": "Zarejestruj się", "pt_PT": "Registar", "it_IT": "Registrati", "ro_RO": "Înregistrează-te", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> ký", "id_ID": "Mendaftar", "fil_PH": "Mag-<PERSON><PERSON><PERSON>", "cs_CZ": "Registrovat se", "el_GR": "Εγγραφή", "pt_BR": "Registrar", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON> ol", "sk_SK": "Registrovať sa", "ar_SA": "تسجيل"}, "post_processing": "register_action_post_processing"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "MOVE_FORWARD_OR_BACKWARD", "category": "basic_movement", "display_name": "移动", "en_display_name": "Move", "desc": "Move forward or backward, closer or farther away from the user. Note:If forward exceeds 5m or backward exceeds 1m, you can use `say_for_clarification` tool to reject the user.", "desc_chinese": "让机器人向前或向后移动指定距离。注意：若前进超5米或后退超1米时，用`say_for_clarification`工具拒绝用户。", "parameters": [{"name": "direction", "type": "enum", "desc": "Movement direction: choose 'forward' or 'backward'.", "is_required": true, "length": null, "enum_constant": ["forward", "backward"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "moving_distance", "type": "float", "desc": "Movement distance in meters (default 0.1).", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MOVE_DIRECTION", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "移动", "zh_TW": "移動", "zh_GD": "移動", "en_US": "Move", "ja_JP": "移動", "ko_KR": "이동", "th_TH": "เคลื่อนที่", "de_DE": "Bewegen", "es_ES": "Mover", "fr_FR": "<PERSON><PERSON><PERSON>", "da_DK": "<PERSON><PERSON><PERSON><PERSON>", "sv_SE": "Flytta", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Beveg", "nl_NL": "Verplaatsen", "ru_RU": "Двигаться", "pl_PL": "P<PERSON><PERSON><PERSON>ś", "pt_PT": "Mover", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Mi<PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>", "id_ID": "<PERSON><PERSON>", "fil_PH": "Ilipat", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Κίνηση", "pt_BR": "Mover", "hu_HU": "Mozgatás", "tr_TR": "Hareket et", "sk_SK": "Presunúť", "ar_SA": "تحرك"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_VISITOR_RECEPTION", "category": "visitor_reception", "display_name": "打开访客接待页面", "en_display_name": "Open Visitor Reception Page", "desc": "Start visitor reception for interviews, meetings, or guest registration.", "desc_chinese": "开启访客接待登记，支持面试、会议签到或访客登记。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 600, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.INTERVIEW_START", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打开访客接待页面", "zh_TW": "打開訪客接待頁面", "zh_GD": "打開訪客接待頁面", "en_US": "Open Visitor Reception Page", "ja_JP": "来客受付ページを開く", "ko_KR": "방문자 접수 페이지 열기", "th_TH": "เปิดหน้าต้อนรับผู้เยี่ยมชม", "de_DE": "Besucherempfangsseite öffnen", "es_ES": "Abrir página de recepción", "fr_FR": "Ouv<PERSON>r page d'accueil visiteur", "da_DK": "Åbn besøgsmodtagelsesside", "sv_SE": "Öppna besöksmottagningssida", "fi_FI": "Avaa v<PERSON>", "nb_NO": "<PERSON><PERSON><PERSON>", "nl_NL": "Bezoekontvangstpagina openen", "ru_RU": "Открыть страницу приема посетителей", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> przyj<PERSON> gości", "pt_PT": "Abrir página de receção de visitantes", "it_IT": "Apri pagina ricevimento visitatori", "ro_RO": "Deschide pagina de primire vizitatori", "ms_MY": "<PERSON><PERSON> halaman peneri<PERSON> pelawat", "vi_VN": "Mở trang tiếp đ<PERSON> kh<PERSON>ch", "id_ID": "<PERSON><PERSON> halaman peneri<PERSON>an tamu", "fil_PH": "<PERSON><PERSON><PERSON> ang pahina ng pagtanggap sa bisita", "cs_CZ": "Otevřít stránku příjmu návštěvníků", "el_GR": "Άνοιγμα σελίδας υποδοχής επισκεπτών", "pt_BR": "Abrir página de recepção de visitantes", "hu_HU": "Látogatófogadó oldal megnyitása", "tr_TR": "Ziyaretçi karşılama sayfasını aç", "sk_SK": "Otvoriť stránku prijímania návštevníkov", "ar_SA": "فتح صفحة الاستقبال"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "QUERY_CALENDAR", "category": "information", "display_name": "查询日历", "en_display_name": "Query Calendar", "desc": "Calendar function, provides date, holiday or weekday queries, not support query weather.", "desc_chinese": "日历功能，包含日期、节假日、星期的查询，不支持查询天气。", "parameters": [{"name": "user_question", "type": "string", "desc": "The complete calendar or date-related question. Format should specify the target date/event and reference time frame (e.g., 'When is [holiday] in [year]').", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_17bf9cfc230d17c94a19a0dc4faa6569", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CALENDAR", "version": "arm_draft", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "查询日历", "zh_TW": "查詢日曆", "zh_GD": "查詢日曆", "en_US": "Query Calendar", "ja_JP": "カレンダーを照会", "ko_KR": "캘린더 조회", "th_TH": "สอบถามปฏิทิน", "vi_VN": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> l<PERSON>ch", "id_ID": "<PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON> kalen<PERSON>", "fil_PH": "Tanong sa kalendaryo", "de_DE": "<PERSON><PERSON><PERSON> ab<PERSON>", "es_ES": "Consultar calendario", "fr_FR": "<PERSON><PERSON><PERSON> le calendrier", "it_IT": "Interroga calendario", "pt_PT": "Consultar calendário", "pt_BR": "Consultar calendário", "nl_NL": "<PERSON><PERSON><PERSON>v<PERSON>n", "ru_RU": "Запросить календарь", "pl_PL": "Zapytaj o kalendarz", "sv_SE": "<PERSON><PERSON><PERSON> kalender", "da_DK": "<PERSON><PERSON><PERSON><PERSON><PERSON> kalender", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Kysy ka<PERSON>", "cs_CZ": "Dotaz na kalendář", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "sk_SK": "Dotaz na kalendár", "ro_RO": "Interogare calendar", "el_GR": "Ερώτημα ημερολογίου", "tr_TR": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>", "ar_SA": "استعلام عن التقويم"}, "execute_function": "calendar"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_GUIDE_TOUR", "category": "guide", "display_name": "导览", "en_display_name": "Guide Introduction", "desc": "Tour guide function, triggered when the user requests a tour without specifying a location or route, providing a general tour introduction.", "desc_chinese": "导览功能，用户未指定具体地点或路线，仅提出参观请求时触发，带领用户参观，提供总体导览介绍。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original request expressing general interest in a tour without specifying a route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GUIDE_INTRODUCTION", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "导览", "zh_TW": "導覽", "zh_GD": "導覽", "en_US": "Guide Introduction", "ja_JP": "ガイド紹介", "ko_KR": "가이드 소개", "th_TH": "แนะนำไกด์", "de_DE": "Führung", "es_ES": "Introducción guía", "fr_FR": "Introduction guide", "da_DK": "Guide introduktion", "sv_SE": "Guide introduktion", "fi_FI": "Opas esittely", "nb_NO": "Guide introduksjon", "nl_NL": "Gids introductie", "ru_RU": "Представление гида", "pl_PL": "Wprowadzenie przewodnika", "pt_PT": "Introdução do guia", "it_IT": "Introduzione guida", "ro_RO": "Introducer<PERSON>i", "ms_MY": "Pengenalan panduan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> thiệu hướng dẫn", "id_ID": "Pengenalan panduan", "fil_PH": "Pagpapakilala ng gabay", "cs_CZ": "Představení průvodce", "el_GR": "Εισαγωγή οδηγού", "pt_BR": "Introdução do guia", "hu_HU": "Útmutató bemu<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>ıtımı", "sk_SK": "Predstavenie sprievodcu", "ar_SA": "التقديم"}, "execute_function": "generate_route_introduction"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GUIDE_SPECIFIC_ROUTE", "category": "guide", "display_name": "指定路线导览", "en_display_name": "Select Specific Route", "desc": "Guide the user along a specific, named tour route that they have explicitly requested.", "desc_chinese": "导览功能，仅当用户明确提及特定路线名称时，按该路线进行导览。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's original request that contains a specific route name.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "route_name", "type": "string", "desc": "Route name selected according to user_query, must be from the predefined route list (enum_func). If uncertain, set to '-1'.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GUIDE_SELECT_SPECIFIC_ROUTE", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "指定路线导览", "zh_TW": "指定路線導覽", "zh_GD": "指定路線導覽", "en_US": "Select Specific Route", "ja_JP": "特定ルート選択", "ko_KR": "특정 루트 선택", "th_TH": "เลือกเส้นทางเฉพาะ", "de_DE": "Spezifische Route wählen", "es_ES": "Seleccionar ruta específica", "fr_FR": "Sélectionner itinéraire spécifique", "da_DK": "Vælg specifik rute", "sv_SE": "<PERSON><PERSON><PERSON>j specifik rutt", "fi_FI": "<PERSON><PERSON><PERSON> tietty reitti", "nb_NO": "Velg spesifikk rute", "nl_NL": "Selecteer specifieke route", "ru_RU": "Выбрать конкретный маршрут", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> konkretną trasę", "pt_PT": "Selecionar rota específica", "it_IT": "Seleziona percorso specifico", "ro_RO": "Selectează rută specifică", "ms_MY": "<PERSON><PERSON><PERSON> k<PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> tuyến đường cụ thể", "id_ID": "<PERSON><PERSON>h rute spesifik", "fil_PH": "<PERSON><PERSON><PERSON> ang tukoy na ruta", "cs_CZ": "Vybrat konkrétní trasu", "el_GR": "Επιλογή συγκεκριμένης διαδρομής", "pt_BR": "Selecionar rota específica", "hu_HU": "Konkrét útvonal kiválasztása", "tr_TR": "<PERSON><PERSON><PERSON> rota seç", "sk_SK": "Vybrať konkrétnu trasu", "ar_SA": "اختيار مسار محدد"}, "execute_function": "convert_specific_route"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RECOMMEND_GUIDE_ROUTES", "category": "guide", "display_name": "推荐路线", "en_display_name": "Route Recommendation", "desc": "Guide scenario, recommend the most suitable tour routes based on user interests or needs.", "desc_chinese": "导览场景，根据用户兴趣或需求推荐合适的参观路线。", "parameters": [{"name": "user_query", "type": "string", "desc": "The user's request expressing interest in a tour without specifying a route.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GUIDE_ROUTE_RECOMMENDATION", "version": "arm_draft", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "推荐路线", "zh_TW": "推薦路線", "zh_GD": "推薦路線", "en_US": "Route Recommendation", "ja_JP": "ルート推奨", "ko_KR": "루트 추천", "th_TH": "แนะนำเส้นทาง", "de_DE": "Routenempfehlung", "es_ES": "Recomendación de ruta", "fr_FR": "Recommandation d'itinéraire", "da_DK": "Ruteanbefaling", "sv_SE": "Ruttrekommendation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Ruteanbefaling", "nl_NL": "Route aanbeveling", "ru_RU": "Рекомендация маршрута", "pl_PL": "Rekomendacja trasy", "pt_PT": "Recomendação de rota", "it_IT": "Raccomandazione percorso", "ro_RO": "Recomanda<PERSON> rut<PERSON>", "ms_MY": "Cadangan la<PERSON>an", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị tuyến đường", "id_ID": "Rekomendasi rute", "fil_PH": "Rekomendasyon ng ruta", "cs_CZ": "Doporučení trasy", "el_GR": "Σύσταση διαδρομής", "pt_BR": "Recomendação de rota", "hu_HU": "Útvonal ajánlás", "tr_TR": "Rota önerisi", "sk_SK": "Odporúčanie trasy", "ar_SA": "توصية سير الرحلة"}, "execute_function": "generate_route_recommendation"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "START_IMMEDIATELY", "category": "guide", "display_name": "直接开始", "en_display_name": "Start Immediately", "desc": "Start the current task or process immediately, without further confirmation. Common phrases include: 'Start', 'Start immediately', etc.", "desc_chinese": "直接或立即开始当前任务或流程，无需进一步确认。如：“开始”、“直接开始”等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.START_IMMEDIATELY", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "直接开始", "zh_TW": "直接開始", "zh_GD": "直接開始", "en_US": "Start Immediately", "ja_JP": "すぐに開始", "ko_KR": "즉시 시작", "th_TH": "เริ่มทันที", "de_DE": "Sofort beginnen", "es_ES": "Comenzar inmediatamente", "fr_FR": "Commencer immédiatement", "da_DK": "Start med det samme", "sv_SE": "<PERSON><PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Start umiddelbart", "nl_NL": "<PERSON><PERSON>", "ru_RU": "Начать немедленно", "pl_PL": "Rozpocznij natychmiast", "pt_PT": "Começar imediatamente", "it_IT": "Inizia immediatamente", "ro_RO": "<PERSON><PERSON><PERSON> im<PERSON>", "ms_MY": "<PERSON>la serta-merta", "vi_VN": "<PERSON><PERSON><PERSON> đ<PERSON>u ngay lập tức", "id_ID": "<PERSON><PERSON> segera", "fil_PH": "<PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Αρχίστε αμέσως", "pt_BR": "Começar imediatamente", "hu_HU": "Azonnal kezdés", "tr_TR": "<PERSON><PERSON> b<PERSON>", "sk_SK": "Začať okamžite", "ar_SA": "ابدأ فوراً"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SEARCH_WEB_INFORMATION", "category": "web_browser", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Search for information on the internet or open websites, such as checking stock prices, looking up ticket information, reading news, etc. or opening a specific website.", "desc_chinese": "搜索网络信息或者打开网站，例如查股票、看门票、看新闻、打开指定网站等。", "parameters": [{"name": "search_word", "type": "string", "desc": "Search word", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "网页搜索", "zh_TW": "網頁搜尋", "en_US": "Web Search", "ja_JP": "ウェブ検索", "ko_KR": "웹 검색", "fr_FR": "Recherche Web", "de_DE": "Websuche", "es_ES": "Búsqueda web", "nl_NL": "Web zoeken", "ru_RU": "Веб-поиск", "pl_PL": "Wyszukiwanie w sieci", "pt_PT": "Pesquisa na web", "pt_BR": "Pesquisa na web", "it_IT": "Ricerca web", "ro_RO": "Căutare web", "ms_MY": "Carian web", "vi_VN": "<PERSON><PERSON><PERSON> k<PERSON> web", "id_ID": "Pencarian web", "fil_PH": "Web search", "cs_CZ": "Webové v<PERSON>hledávání", "el_GR": "Αναζήτηση στον Ιστό", "hu_HU": "Webes kere<PERSON>", "tr_TR": "Web arama", "sk_SK": "Vyhľadávanie na webe", "zh_GD": "網頁搜尋", "th_TH": "ค้นหาเว็บ", "da_DK": "<PERSON> søgning", "sv_SE": "Web sökning", "fi_FI": "Web-haku", "nb_NO": "Web-søk", "ar_SA": "<PERSON><PERSON><PERSON> الويب"}, "post_processing": "convert_search_word_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL", "category": "web_browser", "display_name": "打开浏览器", "en_display_name": "Open Browser", "desc": "Web search, for example, if the user wants to search for stocks, tickets, news, sports events, etc., it is recommended to use Baidu search; official company websites can be accessed directly via their URLs.", "desc_chinese": "网络搜索，如查股票、门票、新闻、体育比赛等建议用百度，官网可直接输入网址访问。", "parameters": [{"name": "url", "type": "HttpUrl", "desc": "A valid HTTPS or HTTP URL to open in the browser. Must include protocol (http:// or https://).", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": true, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打开浏览器", "zh_TW": "打開瀏覽器", "zh_GD": "打開瀏覽器", "en_US": "Open Browser", "ja_JP": "ブラウザを開く", "ko_KR": "브라우저 열기", "th_TH": "เปิดเบราว์เซอร์", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "da_DK": "Åbn browser", "sv_SE": "Öppna webbläsare", "fi_FI": "<PERSON><PERSON> selain", "nb_NO": "<PERSON><PERSON><PERSON> net<PERSON>", "nl_NL": "Browser openen", "ru_RU": "Открыть браузер", "pl_PL": "Otwórz przeglądarkę", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Apri browser", "ro_RO": "Deschide browser", "ms_MY": "<PERSON><PERSON> pelayar", "vi_VN": "Mở trình <PERSON>", "id_ID": "Buka browser", "fil_PH": "B<PERSON>san ang browser", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Άνοιγμα περιηγητή", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Böngés<PERSON><PERSON>", "tr_TR": "Tarayıcıyı aç", "sk_SK": "Otvoriť prehliadač", "ar_SA": "فتح المتصفح"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SEARCH_FLIGHT_TICKETS", "category": "transportation", "display_name": "查询机票", "en_display_name": "Query Flight Tickets", "desc": "Query flight tickets, search for flight tickets between specified locations and dates.", "desc_chinese": "查询机票，搜索指定地点和日期之间的航班机票。", "parameters": [{"name": "departure_city_code", "type": "string", "desc": "IATA airport code for departure city (e.g., 'PEK' for Beijing, 'SHA' for Shanghai).", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "arrival_city_code", "type": "string", "desc": "IATA airport code for arrival city (e.g., 'PEK' for Beijing, 'SHA' for Shanghai).", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "departure_date", "type": "string", "desc": "Date of departure in YYYY-MM-DD format (e.g., '2024-05-01').", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "查询机票", "zh_TW": "查詢機票", "zh_GD": "查詢機票", "en_US": "Query Flight Tickets", "ja_JP": "航空券を照会", "th_TH": "สอบถามตั๋วเครื่องบิน", "de_DE": "Flugtickets abfragen", "es_ES": "Consultar boletos de vuelo", "ko_KR": "항공권 조회", "da_DK": "<PERSON><PERSON><PERSON><PERSON><PERSON> flybilletter", "sv_SE": "Fråga flygbiljetter", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON><PERSON><PERSON> flybilletter", "fr_FR": "Interroger les billets d'avion", "nl_NL": "Vliegtickets opvragen", "ru_RU": "Запросить авиабилеты", "pl_PL": "Zapytaj o bilety lotnicze", "pt_PT": "Consultar bilhetes de avião", "it_IT": "<PERSON><PERSON><PERSON> aerei", "ro_RO": "Interogează biletele de avion", "ms_MY": "<PERSON> tiket pener<PERSON>an", "vi_VN": "Truy vấn vé máy bay", "id_ID": "<PERSON> tiket pesawat", "fil_PH": "<PERSON><PERSON>in ang mga tiket ng eroplano", "cs_CZ": "Dotázat se na letenky", "el_GR": "Ερώτηση εισιτηρίων πτήσης", "pt_BR": "Consultar passagens aéreas", "hu_HU": "Repülőjegyek lekérdezése", "tr_TR": "Uçak biletlerini sorgula", "sk_SK": "Opýtať sa na letenky", "ar_SA": "البحث عن تذاكر الطيران"}, "post_processing": "build_flight_ticket_query_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SEARCH_TRAIN_TICKETS", "category": "transportation", "display_name": "查询火车票", "en_display_name": "Query Train Tickets", "desc": "Search for train tickets between specified cities and dates.", "desc_chinese": "查询、搜索指定城市和日期之间的火车票。", "parameters": [{"name": "departure_city", "type": "string", "desc": "Departure city name without '市' suffix (e.g., '北京' not '北京市').", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "arrival_city", "type": "string", "desc": "Arrival city name without '市' suffix (e.g., '上海' not '上海市').", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "departure_date", "type": "string", "desc": "Date of departure in YYYY-MM-DD format (e.g., '2024-05-01').", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "查询火车票", "zh_TW": "查詢火車票", "zh_GD": "查詢火車票", "en_US": "Query Train Tickets", "ja_JP": "電車の切符を照会", "th_TH": "สอบถามตั๋วรถไฟ", "de_DE": "Zugtickets abfragen", "es_ES": "Consultar boletos de tren", "ko_KR": "기차표 조회", "da_DK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sv_SE": "Fråga tågbiljetter", "fi_FI": "K<PERSON><PERSON>", "nb_NO": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Interroger les billets de train", "nl_NL": "Treinkaartjes opvragen", "ru_RU": "Запросить билеты на поезд", "pl_PL": "Zapytaj o bilety kolejowe", "pt_PT": "Consultar bilhetes de comboio", "it_IT": "<PERSON>rog<PERSON> del treno", "ro_RO": "Interogează biletele de tren", "ms_MY": "<PERSON> tiket kereta api", "vi_VN": "<PERSON><PERSON><PERSON> vấn vé tàu hỏa", "id_ID": "<PERSON> tiket kereta", "fil_PH": "<PERSON><PERSON><PERSON> ang mga tiket ng tren", "cs_CZ": "Dotázat se na jízdenky", "el_GR": "Ερώτηση εισιτηρίων τρένου", "pt_BR": "Consultar passagens de trem", "hu_HU": "Vonatjegyek lekérdezése", "tr_TR": "<PERSON><PERSON> bi<PERSON><PERSON>ini sorgula", "sk_SK": "Opýtať sa na vlakovые lístky", "ar_SA": "البحث عن تذاكر القطار"}, "post_processing": "build_train_ticket_query_url"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SET_VOLUME", "category": "system_control", "display_name": "调整音量", "en_display_name": "Adjust Volume", "desc": "Set the robot's speaker volume to a specific level (0-100). Typically changes by increments of 10-30 units based on user instruction intensity.", "desc_chinese": "设置机器人扬声器音量（0-100），通常以10或30为步长调整。", "parameters": [{"name": "volume_level", "type": "int", "desc": "The volume level(0-100) to be set.", "is_required": true, "length": null, "enum_constant": null, "max": 100, "min": 0, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SET_VOLUME", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调整音量", "zh_TW": "調整音量", "zh_GD": "調整音量", "en_US": "Adjust Volume", "ja_JP": "音量調整", "th_TH": "ปรับระดับเสียง", "de_DE": "Lautstärke anpassen", "es_ES": "Ajustar volumen", "ko_KR": "볼륨 조절", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Säädä äänenvoimakkuutta", "nb_NO": "<PERSON><PERSON>", "fr_FR": "Ajuster le volume", "nl_NL": "Volume aanpassen", "ru_RU": "Настроить громкость", "pl_PL": "<PERSON><PERSON><PERSON><PERSON>", "pt_PT": "Ajustar volume", "it_IT": "Regola volume", "ro_RO": "Ajustează volumul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON>i<PERSON>u chỉnh âm lượng", "id_ID": "Sesuaikan volume", "fil_PH": "<PERSON><PERSON><PERSON> ang lakas ng tunog", "cs_CZ": "Upravit h<PERSON>", "el_GR": "Ρύθμιση έντασης", "pt_BR": "Ajustar volume", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON> sevi<PERSON><PERSON> a<PERSON>", "sk_SK": "Upraviť hlasitosť", "ar_SA": "<PERSON><PERSON>ط مستوى الصوت"}, "post_processing": "set_volume_post_processing"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY_FOR_CLARIFICATION", "category": "interaction", "display_name": "澄清用户问题", "en_display_name": "Clarify User Question", "desc": "Clarify the user's question. When you need to clarify the user's question, please use this tool.", "desc_chinese": "当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具", "parameters": [{"name": "request_clarify_text", "type": "string", "desc": "Prompt the user to clarify their question, using plain text only and no emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SAY_FOR_CLARIFICATION", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "clarify"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SILENT", "category": "guide", "display_name": "默认兜底技能", "en_display_name": "Silent", "desc": "A default fallback skill that should be selected when the user's query has low relevance to other available actions.", "desc_chinese": "默认兜底的技能，在以下情况应选择此动作：用户的查询与其他可用动作相关性较低时，默认选择这个技能", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "默认兜底技能", "zh_TW": "默認兜底技能", "zh_GD": "默認兜底技能", "en_US": "Silent", "ja_JP": "沈黙", "ko_KR": "무음", "th_TH": "เงียบ", "de_DE": "Schweigen", "es_ES": "<PERSON><PERSON><PERSON>", "fr_FR": "Silence", "da_DK": "Stilhed", "sv_SE": "Tystnad", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Stillhet", "nl_NL": "Stilte", "ru_RU": "Молчание", "pl_PL": "Cisza", "pt_PT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "<PERSON><PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "Im lặng", "id_ID": "<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON>", "el_GR": "Σιωπή", "pt_BR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hu_HU": "Csend", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "<PERSON><PERSON><PERSON>", "ar_SA": "صمت"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CANCEL", "category": "system_control", "display_name": "取消", "en_display_name": "Cancel", "desc": "Completely terminate the current behavior (such as dancing, nodding, navigating, speaking, moving, sending messages). Unlike pause, cancel means a full termination rather than a temporary suspension.", "desc_chinese": "取消当前行为（如跳舞、点头、导航、说话、移动、发送消息），与pause不同，cancel为完全终止而非暂停。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CANCEL", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "取消", "zh_TW": "取消", "zh_GD": "取消", "en_US": "Cancel", "ja_JP": "キャンセル", "th_TH": "ยกเลิก", "de_DE": "Abbrechen", "es_ES": "<PERSON><PERSON><PERSON>", "ko_KR": "취소", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "Peruuta", "nb_NO": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Annuler", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Отменить", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Anulează", "ms_MY": "<PERSON><PERSON>", "vi_VN": "Hủy bỏ", "id_ID": "<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "Zrušit", "el_GR": "Ακύρωση", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "M<PERSON>gs<PERSON>", "tr_TR": "İptal", "sk_SK": "Zrušiť", "ar_SA": "إلغاء"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "EXIT", "category": "system_control", "display_name": "退出", "en_display_name": "Exit", "desc": "Exit and completely close the current application. Different from BACK which returns to previous screen within same application.", "desc_chinese": "彻底关闭当前应用，区别于back，exit不会返回上一级界面。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.EXIT", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出", "zh_TW": "退出", "zh_GD": "退出", "en_US": "Exit", "ja_JP": "終了", "ko_KR": "종료", "th_TH": "ออก", "de_DE": "<PERSON>den", "es_ES": "Salir", "fr_FR": "<PERSON><PERSON><PERSON>", "da_DK": "A<PERSON>lut", "sv_SE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Avslutt", "nl_NL": "Afsluiten", "ru_RU": "Выход", "pl_PL": "Wyjście", "pt_PT": "<PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON>", "ro_RO": "Ieșire", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "Lu<PERSON><PERSON>", "cs_CZ": "Konec", "el_GR": "Έξοδος", "pt_BR": "<PERSON><PERSON>", "hu_HU": "Kilépés", "tr_TR": "Çıkış", "sk_SK": "Koniec", "ar_SA": "الخروج"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "BACK", "category": "system_control", "display_name": "返回上一级", "en_display_name": "Back", "desc": "Return to the previous screen or menu within the current application. Unlike exit, back does not completely close the application.", "desc_chinese": "返回当前应用的上一级界面或菜单，不同于exit，back不会退出应用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.BACK", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "返回上一级", "zh_TW": "返回上一級", "zh_GD": "返回上一級", "en_US": "Back", "ja_JP": "戻る", "ko_KR": "뒤로", "th_TH": "กลับ", "de_DE": "Zurück", "es_ES": "Atrás", "fr_FR": "Retour", "da_DK": "Tilbage", "sv_SE": "Tillbaka", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Tilbake", "nl_NL": "Terug", "ru_RU": "Назад", "pl_PL": "Wstecz", "pt_PT": "Voltar", "it_IT": "Indietro", "ro_RO": "Înapoi", "ms_MY": "Kembali", "vi_VN": "Quay lại", "id_ID": "Kembali", "fil_PH": "Bumalik", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Πίσω", "pt_BR": "Voltar", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Späť", "ar_SA": "الرجوع"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "NEXT", "category": "system_control", "display_name": "下一步", "en_display_name": "Next", "desc": "Proceed to the next step in a multi-step process or presentation. Use in guided tutorials, presentations, or sequential operations.", "desc_chinese": "在多步骤流程或演示中前进到下一步。如：讲解导览中下一个地点等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.NEXT", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "下一步", "zh_TW": "下一步", "zh_GD": "下一步", "en_US": "Next", "ja_JP": "次へ", "ko_KR": "다음", "th_TH": "ถัดไป", "de_DE": "<PERSON><PERSON>", "es_ES": "Siguient<PERSON>", "fr_FR": "Suivant", "da_DK": "<PERSON><PERSON><PERSON>", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Neste", "nl_NL": "Volgende", "ru_RU": "Далее", "pl_PL": "Następny", "pt_PT": "Próximo", "it_IT": "Successivo", "ro_RO": "Următorul", "ms_MY": "Seterusnya", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> theo", "id_ID": "Selanjutnya", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Dalš<PERSON>", "el_GR": "Επόμενο", "pt_BR": "Próximo", "hu_HU": "Következő", "tr_TR": "İleri", "sk_SK": "Ďalší", "ar_SA": "التالي"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CONFIRM", "category": "system_control", "display_name": "确认", "en_display_name": "Confirm", "desc": "User confirms the current action or selection, such as saying 'Confirm', 'OK', or 'No problem', based on the conversation context.", "desc_chinese": "用户确认当前操作或选择，如“确认”“确定”“好的”“没问题”“可以”“提交”等，必要时需结合对话历史判断。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CONFIRM", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "确认", "zh_TW": "確認", "zh_GD": "確認", "en_US": "Confirm", "ja_JP": "確認", "ko_KR": "확인", "th_TH": "ยืนยัน", "de_DE": "Bestätigen", "es_ES": "Confirmar", "fr_FR": "Confirmer", "da_DK": "Bekræft", "sv_SE": "Bekräfta", "fi_FI": "Vahvista", "nb_NO": "Bekreft", "nl_NL": "Bevestigen", "ru_RU": "Подтвердить", "pl_PL": "Potwierdź", "pt_PT": "Confirmar", "it_IT": "Conferma", "ro_RO": "Confirmă", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs_CZ": "Potvrdit", "el_GR": "Επιβεβαίωση", "pt_BR": "Confirmar", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Potvrdiť", "ar_SA": "تأكيد"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "TAKE_PHOTO", "category": "entertainment", "display_name": "拍照", "en_display_name": "Take Photo", "desc": "Activate the robot's camera to take a photo of the current scene or people in front of it.", "desc_chinese": "激活机器人相机，对当前场景或面前的人拍照。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 30, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.TAKE_PHOTO", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "拍照", "zh_TW": "拍照", "zh_GD": "拍照", "en_US": "Take Photo", "ja_JP": "写真を撮る", "th_TH": "ถ่ายรูป", "de_DE": "Foto machen", "es_ES": "Tomar foto", "ko_KR": "사진 찍기", "da_DK": "<PERSON>", "sv_SE": "Ta foto", "fi_FI": "<PERSON><PERSON> kuva", "nb_NO": "<PERSON> bilde", "fr_FR": "<PERSON><PERSON><PERSON> une photo", "nl_NL": "Foto maken", "ru_RU": "Сфотографировать", "pl_PL": "Zrób zdjęcie", "pt_PT": "Tirar foto", "it_IT": "Scatta foto", "ro_RO": "Fă o poză", "ms_MY": "Ambil gambar", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Ambil foto", "fil_PH": "Kumuha ng larawan", "cs_CZ": "Vyfotit", "el_GR": "Βγ<PERSON><PERSON><PERSON> φωτογραφία", "pt_BR": "Tirar foto", "hu_HU": "<PERSON>ot<PERSON>", "tr_TR": "Foto<PERSON><PERSON><PERSON>", "sk_SK": "Urobiť fotografiu", "ar_SA": "التقاط صورة"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "INPUT_VERIFICATION_CODE", "category": "verification", "display_name": "输入验证码", "en_display_name": "Input Verification Code", "desc": "Enter a 4-digit verification code for authentication. Each digit must be between 0-9.", "desc_chinese": "输入4位数验证码进行身份验证。每位数字必须在0-9之间。", "parameters": [{"name": "verification_code", "type": "Integer array", "desc": "The 4-digit verification code to be entered. Each digit must be between 0-9.", "is_required": true, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.VERIFICATION_CODE_INPUT", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "输入验证码", "zh_TW": "輸入驗證碼", "zh_GD": "輸入驗證碼", "en_US": "Input Verification Code", "ja_JP": "認証コードを入力", "th_TH": "ใส่รหัสยืนยัน", "de_DE": "Bestätigungscode eingeben", "es_ES": "Ingresar código de verificación", "ko_KR": "인증 코드 입력", "da_DK": "Indtast bekræftelseskode", "sv_SE": "Ange verifieringskod", "fi_FI": "Syötä v<PERSON>oodi", "nb_NO": "Skriv inn bekreftelseskode", "fr_FR": "Entrer le code de vérification", "nl_NL": "Verificatiecode invoeren", "ru_RU": "Введите код подтверждения", "pl_PL": "Wprowadź kod weryfikacyjny", "pt_PT": "Introduzir código de verificação", "it_IT": "Inserisci codice di verifica", "ro_RO": "Introduceți codul de verificare", "ms_MY": "<PERSON>sukka<PERSON> kod pengesahan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> mã xác thực", "id_ID": "Masukkan kode verifikasi", "fil_PH": "Ilagay ang verification code", "cs_CZ": "Zadat ověřovací kód", "el_GR": "Εισάγετε κωδικό επαλήθευσης", "pt_BR": "Inserir código de verificação", "hu_HU": "<PERSON><PERSON><PERSON><PERSON> kód bevitele", "tr_TR": "Doğ<PERSON>lama kodunu gir", "sk_SK": "<PERSON><PERSON>ť overovací kód", "ar_SA": "إدخال رمز التحقق"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "INPUT_LAST_4_PHONE_DIGITS", "category": "verification", "display_name": "输入手机号后四位", "en_display_name": "Input Last 4 Digits of Phone Number", "desc": "Enter the last 4 digits of a phone number for verification purposes. Each digit must be between 0-9.", "desc_chinese": "输入手机号码后四位用于验证。每位数字必须在0-9之间。", "parameters": [{"name": "last_4_digits", "type": "Integer array", "desc": "The last four digits of the phone number, Must be four valid digits, Must be converted to Arabic numerals, Numeric range 0 to 9", "is_required": true, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_640ce92cb1553953254fc90ae92ea9bd"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.LAST_4_DIGITS_INPUT", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "输入手机号后四位", "zh_TW": "輸入手機號後四位", "zh_GD": "輸入手機號後四位", "en_US": "Input Last 4 Digits of Phone Number", "ja_JP": "電話番号の下4桁を入力", "th_TH": "ใส่หมายเลขโทรศัพท์ 4 ตัวท้าย", "de_DE": "Letzten 4 Ziffern der Telefonnummer eingeben", "es_ES": "Ingresar últimos 4 dígitos del teléfono", "ko_KR": "휴대폰 번호 뒤 4자리 입력", "da_DK": "Indtast sidste 4 cifre af telefonnummer", "sv_SE": "Ange sista 4 siffrorna i telefonnumret", "fi_FI": "Syötä puhelinnumeron 4 viimeistä numeroa", "nb_NO": "Skriv inn siste 4 siffer av telefonnummer", "fr_FR": "Entrer les 4 derniers chiffres du téléphone", "nl_NL": "Laatste 4 cijfers van telefoonnummer invoeren", "ru_RU": "Введите последние 4 цифры номера телефона", "pl_PL": "Wprowadź ostatnie 4 cyfry numeru telefonu", "pt_PT": "Introduzir últimos 4 dígitos do telefone", "it_IT": "Inserisci ultime 4 cifre del telefono", "ro_RO": "Introduceți ultimele 4 cifre ale telefonului", "ms_MY": "Masukkan 4 digit terakhir nombor telefon", "vi_VN": "Nhập 4 số cuối của số điện thoại", "id_ID": "Masukkan 4 digit terakhir nomor telepon", "fil_PH": "Ilagay ang huling 4 na numero ng telepono", "cs_CZ": "Zadat poslední 4 číslice telefonu", "el_GR": "Εισάγετε τα τελευταία 4 ψηφία του τηλεφώνου", "pt_BR": "Inserir últimos 4 dígitos do telefone", "hu_HU": "Telefonszám utolsó 4 számjegyének bevitele", "tr_TR": "Telefon numarasının son 4 hanesini gir", "sk_SK": "Zadať posledné 4 číslice telefónu", "ar_SA": "إدخال آخر 4 أرقام من رقم الهاتف"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REPLAY", "category": "guide", "display_name": "重播", "en_display_name": "Replay", "desc": "Replay any media or interactive content. For example, replay a video or replay the current point in the guided tour.", "desc_chinese": "重新播放任何媒体或交互内容。例如播放视频或者导览讲解中重新讲解当前的点。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COMMON_REPLAY", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "重播", "zh_TW": "重播", "zh_GD": "重播", "en_US": "Replay", "ja_JP": "リプレイ", "th_TH": "เล่นซ้ำ", "de_DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es_ES": "Reproducir", "ko_KR": "다시 재생", "da_DK": "Genafspil", "sv_SE": "Spela om", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Spill av igjen", "fr_FR": "Rejouer", "nl_NL": "Opnieuw afspelen", "ru_RU": "Повторить", "pl_PL": "Odtwórz ponownie", "pt_PT": "Reproduzir novamente", "it_IT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ro_RO": "Redare", "ms_MY": "Main semula", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON> ulang", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Επανάληψη", "pt_BR": "Reproduzir novamente", "hu_HU": "Újrajátszás", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Prehrať znovu", "ar_SA": "إعادة التشغيل"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "PLAY", "category": "system_control", "display_name": "播放", "en_display_name": "Play", "desc": "Start playing any media or interactive content. For example, play a video or start the guided tour at the current point.", "desc_chinese": "开始播放任何媒体或交互内容。例如播放视频或者导览讲解中开始讲解当前的点。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MULTIMEDIA_PLAY", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "播放", "zh_TW": "播放", "zh_GD": "播放", "en_US": "Play", "ja_JP": "再生", "th_TH": "เล่น", "de_DE": "Abspielen", "es_ES": "Reproducir", "ko_KR": "재생", "da_DK": "Afspil", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "Toista", "nb_NO": "Spill av", "fr_FR": "<PERSON><PERSON>", "nl_NL": "Afspelen", "ru_RU": "Воспроизвести", "pl_PL": "Odtwórz", "pt_PT": "Reproduzir", "it_IT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ro_RO": "Redare", "ms_MY": "Main", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Putar", "fil_PH": "I-play", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Αναπαραγωγή", "pt_BR": "Reproduzir", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Prehrať", "ar_SA": "تشغيل"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "RESUME", "category": "system_control", "display_name": "继续服务", "en_display_name": "Resume Service", "desc": "Resume or continue the service, such as resuming playback or continuing the guided tour.", "desc_chinese": "恢复或继续服务，例如继续播放、继续讲解等场景。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MULTIMEDIA_PLAY", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "继续服务", "zh_TW": "繼續服務", "zh_GD": "繼續服務", "en_US": "Resume Service", "ja_JP": "サービス再開", "th_TH": "เดินหน้าการบริการ", "de_DE": "Service fortsetzen", "es_ES": "<PERSON><PERSON><PERSON> servicio", "ko_KR": "서비스 재개", "da_DK": "Genoptag service", "sv_SE": "Återuppta service", "fi_FI": "Jatka palvelua", "nb_NO": "Gjenoppta tje<PERSON>", "fr_FR": "Reprendre le service", "nl_NL": "Service hervatten", "ru_RU": "Возобновить сервис", "pl_PL": "Wznów usługę", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON><PERSON> servizio", "ro_RO": "Reluarea serviciului", "ms_MY": "Sambung perkhidmatan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> d<PERSON> vụ", "id_ID": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "fil_PH": "Ipagpatuloy ang serbisyo", "cs_CZ": "Pokračovat ve službě", "el_GR": "Συνέχιση υπηρεσίας", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Szolgáltatás folytatása", "tr_TR": "Hizmeti devam ettir", "sk_SK": "Pokračovať v službe", "ar_SA": "استئناف الخدمة"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "PAUSE", "category": "guide", "display_name": "暂停", "en_display_name": "Pause", "desc": "Pause the current service. Users may want to pause actions such as video playback or movement. Common phrases include: 'Don’t go', 'Stay still', 'Wait for me', or 'Hold on'.", "desc_chinese": "暂定当前服务。用户希望暂停某个动作，例如「暂停播放视频」或者「暂停移动」等。常见表达如：“别走了”“别动了”“等下我”或“等一等”等。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COMMON_PAUSE", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "暂停", "zh_TW": "暫停", "zh_GD": "暫停", "en_US": "Pause", "ja_JP": "一時停止", "th_TH": "หยุดชั่วคราว", "de_DE": "Pausieren", "es_ES": "Pausar", "ko_KR": "일시 정지", "da_DK": "Pause", "sv_SE": "Pausa", "fi_FI": "Keskeytä", "nb_NO": "Pause", "fr_FR": "Pause", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Пауза", "pl_PL": "<PERSON><PERSON>", "pt_PT": "Pausar", "it_IT": "Pausa", "ro_RO": "Pauză", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "<PERSON><PERSON>", "fil_PH": "I-pause", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el_GR": "Παύση", "pt_BR": "Pausar", "hu_HU": "Szünet", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Pozastaviť", "ar_SA": "إيقاف مؤقت"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ASK_QUESTION_IN_GUIDE", "category": "guide", "display_name": "提问", "en_display_name": "Start Questioning", "desc": "During the guided tour, the user wants to ask a question. Unlike SAY, ask_question_in_guide is specifically for user questions during the tour.", "desc_chinese": "在导览讲解过程中，用户想提问。与SAY不同，ASK_QUESTION_IN_GUIDE专用于导览讲解场景过程用户提问场景。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.START_QUESTION", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "提问", "zh_TW": "提問", "zh_GD": "提問", "en_US": "Start Questioning", "ja_JP": "質問開始", "ko_KR": "질문 시작", "th_TH": "เริ่มถาม", "de_DE": "Fragestunde beginnen", "es_ES": "<PERSON><PERSON><PERSON> a preguntar", "fr_FR": "Commencer à questionner", "da_DK": "Start spørgsmål", "sv_SE": "Börja fråga", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Start spørsmål", "nl_NL": "<PERSON><PERSON> v<PERSON>ag<PERSON>lling", "ru_RU": "Начать вопросы", "pl_PL": "Rozpocznij pytania", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Inizia <PERSON>", "ro_RO": "<PERSON><PERSON><PERSON>", "ms_MY": "<PERSON><PERSON> berta<PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> đầu hỏi", "id_ID": "<PERSON><PERSON>", "fil_PH": "Magsimula ng tanong", "cs_CZ": "Zač<PERSON><PERSON> ptát", "el_GR": "Αρχίστε να ρωτάτε", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Kérdezés kezdése", "tr_TR": "<PERSON><PERSON> sormaya ba<PERSON>la", "sk_SK": "Začať pýtanie", "ar_SA": "بدء الأسئلة"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "REPEAT_TOUR_EXPLANATION", "category": "guide", "display_name": "重新讲解", "en_display_name": "Repeat", "desc": "Completely restart a guided tour explanation from the beginning, typically after reaching the scoring/feedback phase.", "desc_chinese": "从头开始完全重新进行导览讲解，通常在达到评分/反馈阶段后使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.COMMON_REPEAT", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "重新讲解", "zh_TW": "重新講解", "zh_GD": "重新講解", "en_US": "Repeat", "ja_JP": "再説明", "ko_KR": "다시 설명", "th_TH": "อธิบายซ้ำ", "de_DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON>r", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "da_DK": "Gentag", "sv_SE": "Upprepa", "fi_FI": "Toista", "nb_NO": "<PERSON><PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Повторить", "pl_PL": "Powtórz", "pt_PT": "<PERSON><PERSON>r", "it_IT": "R<PERSON><PERSON>", "ro_RO": "Repetă", "ms_MY": "Ulang", "vi_VN": "Lặp lại", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Opakovat", "el_GR": "Επαναλάβετε", "pt_BR": "<PERSON><PERSON>r", "hu_HU": "Ismételje meg", "tr_TR": "Tekrarla", "sk_SK": "Opakovať", "ar_SA": "إعادة الشرح"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SWITCH_GUIDE_ROUTE", "category": "guide", "display_name": "选择其他导览路线", "en_display_name": "Choose Another Route", "desc": "Switch or choose to a different guided tour route from the current one. Use when user wants to choose or ask for another route.", "desc_chinese": "选择其他导览路线，用户想要选择或者询问其他路线时使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ROUTES_OTHERS", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "选择其他导览路线", "zh_TW": "選擇其他導覽路線", "zh_GD": "選擇其他導覽路線", "en_US": "Choose Another Route", "ja_JP": "別のルートを選択", "ko_KR": "다른 루트 선택", "th_TH": "เลือกเส้นทางอื่น", "de_DE": "Andere Route wählen", "es_ES": "Elegir otra ruta", "fr_FR": "Choisir un autre itinéraire", "da_DK": "<PERSON><PERSON><PERSON><PERSON> anden rute", "sv_SE": "<PERSON><PERSON><PERSON><PERSON> rutt", "fi_FI": "Valitse toinen reitti", "nb_NO": "Velg annen rute", "nl_NL": "Kies andere route", "ru_RU": "Выбрать другой маршрут", "pl_PL": "<PERSON><PERSON><PERSON><PERSON> inną trasę", "pt_PT": "Escolher outra rota", "it_IT": "<PERSON><PERSON><PERSON> altro percorso", "ro_RO": "Alege altă rută", "ms_MY": "<PERSON><PERSON><PERSON> la<PERSON>an lain", "vi_VN": "<PERSON><PERSON><PERSON> tuy<PERSON>n kh<PERSON>c", "id_ID": "<PERSON>lih rute lain", "fil_PH": "<PERSON><PERSON><PERSON> ang ibang ruta", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON> jinou trasu", "el_GR": "Επιλέξτε άλλη διαδρομή", "pt_BR": "Escolher outra rota", "hu_HU": "Válasszon másik útvonalat", "tr_TR": "Başka rota seç", "sk_SK": "Vybrať inú trasu", "ar_SA": "اختيار مسار آخر"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "RATE_EXPERIENCE", "category": "guide", "display_name": "打分", "en_display_name": "Score", "desc": "Rate an experience or service on a scale of 1-5 stars. Used for collecting user feedback after completing a guided tour or interaction.", "desc_chinese": "对体验或服务进行1-5星评分。用于在完成导览或交互后收集用户反馈。", "parameters": [{"name": "score", "type": "int", "desc": "Rating value from 1-5, where 5 represents highest satisfaction.", "is_required": true, "length": null, "enum_constant": null, "max": 5, "min": 1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_00760f1d425189480a4f957fdeefe77a", "system_33e51e3e560ef8dab9f29cb1aaa7058c"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SCORE", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "打分", "zh_TW": "打分", "zh_GD": "打分", "en_US": "Score", "ja_JP": "評価", "ko_KR": "점수", "th_TH": "ให้คะแนน", "de_DE": "Bewerten", "es_ES": "<PERSON><PERSON><PERSON><PERSON>", "fr_FR": "Noter", "da_DK": "Score", "sv_SE": "Poäng", "fi_FI": "Pisteet", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Score", "ru_RU": "Оценка", "pl_PL": "<PERSON><PERSON><PERSON>", "pt_PT": "Pontuação", "it_IT": "<PERSON><PERSON><PERSON><PERSON>", "ro_RO": "<PERSON><PERSON>", "ms_MY": "Skor", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "Skor", "fil_PH": "Iskor", "cs_CZ": "Skóre", "el_GR": "Βαθμολογία", "pt_BR": "Pontuação", "hu_HU": "Pontszám", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Skóre", "ar_SA": "تقييم"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ADJUST_WALKING_SPEED", "category": "system_control", "display_name": "调整当前移动速度", "en_display_name": "Adjust Walking Speed", "desc": "Adjust the <current walking speed>. range: 0.1-1.2m/s. Note: If the speed exceeds the range, you can use `say_for_clarification` tool to reject the user.", "desc_chinese": "调整当前移动速度，范围0.1-1.2m/s，超出请用`say_for_clarification`动作拒绝。", "parameters": [{"name": "adjusted_speed", "type": "float", "desc": "The robot's walking speed, range: 0.1-1.2m/s.", "is_required": true, "length": null, "enum_constant": null, "max": 1.2, "min": 0.1, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ADJUST_SPEED", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调整当前移动速度", "zh_TW": "調整當前移動速度", "zh_GD": "調整當前移動速度", "en_US": "Adjust Walking Speed", "ja_JP": "歩行速度調整", "ko_KR": "보행 속도 조정", "th_TH": "ปรับความเร็วในการเดิน", "de_DE": "Gehgeschwindigkeit anpassen", "es_ES": "Ajustar velocidad de caminar", "fr_FR": "Ajuster la vitesse de marche", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "Säädä kä<PERSON>a", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Loopsnelheid a<PERSON>en", "ru_RU": "Настроить скорость ходьбы", "pl_PL": "Dostosuj pręd<PERSON>ć chodzenia", "pt_PT": "Ajustar velocidade de caminhada", "it_IT": "Regola velocità camminata", "ro_RO": "Ajustează viteza de mers", "ms_MY": "<PERSON><PERSON>", "vi_VN": "Điều chỉnh tốc độ đi bộ", "id_ID": "Sesuaikan kecepatan jalan", "fil_PH": "<PERSON><PERSON><PERSON> ang bilis ng paglalakad", "cs_CZ": "Upravit rychlost chůze", "el_GR": "Ρύθμιση ταχύτητας βαδίσματος", "pt_BR": "Ajustar velocidade de caminhada", "hu_HU": "<PERSON><PERSON><PERSON> se<PERSON>g be<PERSON>llí<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON><PERSON><PERSON> hı<PERSON>ı<PERSON>ı a<PERSON>la", "sk_SK": "Upraviť rýchlosť chôdze", "ar_SA": "ضبط سرعة المشي"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SET_GREETING_MESSAGE", "category": "interaction", "display_name": "设置问候语", "en_display_name": "Configure Greeting Message", "desc": "Set up a greeting message that the robot will use when greeting users.", "desc_chinese": "设置机器人见到用户时的问候语/欢迎语。", "parameters": [{"name": "valid_name", "type": "string", "desc": "The user's name, must be a real name, not a pronoun. Leave blank if not provided.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "one_sentence", "type": "string", "desc": "Greeting message content.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_d5a64441247aa63b70dd8d02e3f753f0"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CONFIGURE_WELCOME_MESSAGE", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "设置欢迎语", "zh_TW": "設置歡迎語", "zh_GD": "設置歡迎語", "en_US": "Configure Welcome Message", "ja_JP": "ウェルカムメッセージ設定", "ko_KR": "환영 메시지 설정", "th_TH": "ตั้งค่าข้อความต้อนรับ", "de_DE": "Willkommensnachricht konfigurieren", "es_ES": "Configurar mensaje de bienvenida", "fr_FR": "Configurer message d'accueil", "da_DK": "Konfigurer velkomstbesked", "sv_SE": "Konfigurera välkomstmeddelande", "fi_FI": "Määritä tervetuloviesti", "nb_NO": "Konfigurer velkomstmelding", "nl_NL": "Welkomstbericht configureren", "ru_RU": "Настроить приветственное сообщение", "pl_PL": "Skonfiguruj wiadom<PERSON> powitalną", "pt_PT": "Configurar mensagem de boas-vindas", "it_IT": "Configura messaggio di benvenuto", "ro_RO": "Configurează mesajul de bun venit", "ms_MY": "Konfigurasi mesej selamat datang", "vi_VN": "<PERSON><PERSON><PERSON> hình tin nhắn chào mừng", "id_ID": "Konfigurasi pesan selamat datang", "fil_PH": "I-configure ang welcome message", "cs_CZ": "Konfigurovat uvítací zprávu", "el_GR": "Διαμόρφωση μηνύματος καλωσορίσματος", "pt_BR": "Configurar mensagem de boas-vindas", "hu_HU": "Üdvözlő üzenet konfigurálása", "tr_TR": "Karşılama mesajını yapılandır", "sk_SK": "Konfigurovať uvítaciu správu", "ar_SA": "إعداد رسالة الترحيب"}, "post_processing": "register_action_post_processing"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GENERATE_WELCOME_FAREWELL_MESSAGE", "category": "interaction", "display_name": "生成欢迎语/欢送语", "en_display_name": "Generate", "desc": "Generate welcome/farewell messages. Used when welcoming guests or saying goodbye.", "desc_chinese": "生成欢迎语/欢送语，一般在欢迎来客时使用，或者在送别时使用。", "parameters": [], "execute_side": "server", "execute_timeout_limit": 90, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GENERATE_MESSAGE", "version": "arm_draft", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "生成", "zh_TW": "生成", "zh_GD": "生成", "en_US": "Generate", "ja_JP": "生成", "th_TH": "สร้าง", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "Generar", "ko_KR": "생성", "da_DK": "<PERSON><PERSON>", "sv_SE": "<PERSON><PERSON>", "fi_FI": "<PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "fr_FR": "<PERSON><PERSON><PERSON><PERSON>", "nl_NL": "<PERSON><PERSON><PERSON>", "ru_RU": "Сгенерировать", "pl_PL": "Generuj", "pt_PT": "<PERSON><PERSON><PERSON>", "it_IT": "Genera", "ro_RO": "Generează", "ms_MY": "<PERSON>", "vi_VN": "Tạo ra", "id_ID": "<PERSON><PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Generovat", "el_GR": "Δημιουργία", "pt_BR": "<PERSON><PERSON><PERSON>", "hu_HU": "Gener<PERSON><PERSON><PERSON>", "tr_TR": "Oluştur", "sk_SK": "Generovať", "ar_SA": "إنشاء رسالة الترحيب والوداع"}, "execute_function": "generate_message"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SEND_MESSAGE", "category": "interaction", "display_name": "发消息", "en_display_name": "Send Message", "desc": "Send a message to a designated person via <PERSON><PERSON><PERSON>. The recipient's real name and message content are required. Message urgency is optional. Suitable for scenarios such as locating someone or leaving a message.", "desc_chinese": "通过飞书向指定人员发送消息，需提供收件人真实姓名和消息内容，可选紧急程度，适用于找人、留言等。", "parameters": [{"name": "recipient_name", "type": "string", "desc": "Must use real names, not pronouns.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "message_content", "type": "string", "desc": "Send the message content. Please polish the content to avoid being too direct or stiff.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "message_type", "type": "enum", "desc": "Priority level of the message (urgent or normal).", "is_required": true, "length": null, "enum_constant": ["urgent", "normal"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "sender_photo_url", "type": "HttpUrl", "desc": "The URL of the sender's photo.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "person_id", "type": "string", "desc": "The unique identifier of the user, provided by the client.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SEND_MESSAGE", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "send_message"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "SEARCH_NEARBY_PLACES", "category": "navigation", "display_name": "调用地图", "en_display_name": "Open Map", "desc": "Call the map to search for or recommend nearby types of places (such as attractions, hotels, subway stations, food, etc.), but route recommendations are not provided.", "desc_chinese": "调用地图搜索或推荐附近的某类地点（如景点、酒店、地铁站、美食等），不提供路线推荐。", "parameters": [{"name": "query", "type": "string", "desc": "The user's original query.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "place", "type": "string", "desc": "Geographic location mentioned in the query (city, province, district, landmark, or any specific place name). Extract the exact place name as mentioned by the user. default '-1' if not found. ", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "target", "type": "string", "desc": "Search target extracted from the query (e.g., hotel, food, subway station, etc.); ", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 60, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "调用地图", "zh_TW": "調用地圖", "zh_GD": "調用地圖", "en_US": "Open Map", "ja_JP": "マップを開く", "ko_KR": "지도 열기", "th_TH": "เปิดแผนที่", "de_DE": "<PERSON><PERSON>", "es_ES": "<PERSON><PERSON><PERSON> mapa", "fr_FR": "Ouv<PERSON>r la carte", "da_DK": "Åbn kort", "sv_SE": "Öppna karta", "fi_FI": "Avaa kartta", "nb_NO": "Åpne kart", "nl_NL": "<PERSON><PERSON> openen", "ru_RU": "Открыть карту", "pl_PL": "Otwórz <PERSON>ę", "pt_PT": "<PERSON><PERSON><PERSON> mapa", "it_IT": "<PERSON>i mappa", "ro_RO": "Deschide harta", "ms_MY": "<PERSON>uka peta", "vi_VN": "Mở bản đồ", "id_ID": "<PERSON>uka peta", "fil_PH": "<PERSON><PERSON><PERSON> ang mapa", "cs_CZ": "Otevř<PERSON>t mapu", "el_GR": "Άνοιγμα χάρτη", "pt_BR": "<PERSON><PERSON><PERSON> mapa", "hu_HU": "Térkép <PERSON>", "tr_TR": "Haritayı aç", "sk_SK": "Otvoriť mapu", "ar_SA": "فتح الخريطة"}, "post_processing": "convert_recommend_url"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "FACE_RECOGNITION", "category": "user_recognition", "display_name": "人脸识别", "en_display_name": "Face Recognition", "desc": "Facial recognition. Identify the face in front of the robot, answer the user's identity (mainly the name), but does not support identifying other people.", "desc_chinese": "人脸识别，仅识别当前在机器人面前的用户（主要为姓名），无法识别他人。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.FACE_RECOGNITION", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "人脸识别", "zh_TW": "人臉識別", "zh_GD": "人臉識別", "en_US": "Face Recognition", "ja_JP": "顔認識", "ko_KR": "얼굴 인식", "th_TH": "การจดจำใบหน้า", "de_DE": "Gesichtserkennung", "es_ES": "Reconocimiento facial", "fr_FR": "Reconnaissance faciale", "da_DK": "Ansigtsgenkendelse", "sv_SE": "Ansiktsigenkänning", "fi_FI": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "An<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nl_NL": "Gezichtsherkenning", "ru_RU": "Распознавание лиц", "pl_PL": "Rozpoznawanie twarzy", "pt_PT": "Reconhecimento facial", "it_IT": "Riconoscimento facciale", "ro_RO": "Recunoașterea feței", "ms_MY": "Pengecaman wajah", "vi_VN": "Nhận dạng khuôn mặt", "id_ID": "Pengenalan wajah", "fil_PH": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa mukha", "cs_CZ": "Rozpoznávání obličeje", "el_GR": "Αναγνώριση προσώπου", "pt_BR": "Reconhecimento facial", "hu_HU": "Arcfelismerés", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Rozpoznávanie tváre", "ar_SA": "التعرف على الوجه"}}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "GO_CHARGING", "category": "system_admin", "display_name": "去充电", "en_display_name": "Go to Charging", "desc": "Instruct the robot to go to the charging station and start charging. Consider the current battery level if needed.", "desc_chinese": "要求机器人前往充电桩进行充电，必要时需结合当前电量判断。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.GO_CHARGING", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "去充电", "zh_TW": "去充電", "zh_GD": "去充電", "en_US": "Go to Charging", "ja_JP": "充電に行く", "ko_KR": "충전하러 가기", "th_TH": "ไปชาร์จ", "de_DE": "Zum Laden gehen", "es_ES": "Ir a cargar", "fr_FR": "Aller charger", "da_DK": "Gå til opladning", "sv_SE": "Gå till laddning", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Gå til lading", "nl_NL": "Ga laden", "ru_RU": "Иди заряжаться", "pl_PL": "<PERSON><PERSON><PERSON> ładować", "pt_PT": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Vai a caricare", "ro_RO": "Du-te să te încarci", "ms_MY": "<PERSON><PERSON> men<PERSON>", "vi_VN": "<PERSON><PERSON>", "id_ID": "<PERSON><PERSON> mengisi daya", "fil_PH": "Pumunta sa pag-charge", "cs_CZ": "<PERSON><PERSON><PERSON>", "el_GR": "Πήγαινε για φόρτιση", "pt_BR": "<PERSON><PERSON><PERSON><PERSON>", "hu_HU": "<PERSON><PERSON>", "tr_TR": "Şarj olma<PERSON> git", "sk_SK": "Ísť sa nabiť", "ar_SA": "الذهاب للشحن"}}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "OPEN_PRODUCT_PROMOTION_APP", "category": "system_admin", "display_name": "进入推销APP", "en_display_name": "Enter Promotion App", "desc": "Open the product promotion application for demonstrating and selling products/services.", "desc_chinese": "打开产品推广/推销APP，用于演示和销售产品/服务。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ENTER_PROMOTE_APP", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "进入推销APP", "zh_TW": "進入推銷APP", "zh_GD": "進入推銷APP", "en_US": "Enter Promotion App", "ja_JP": "プロモーションアプリに入る", "ko_KR": "프로모션 앱 진입", "th_TH": "เข้าแอปโปรโมชั่น", "de_DE": "Promotion-App betreten", "es_ES": "Entrar a app de promoción", "fr_FR": "Entrer dans l'app de promotion", "da_DK": "Gå ind i promotions-app", "sv_SE": "Gå in i marknadsföringsapp", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "Gå inn i markedsføringsapp", "nl_NL": "Promotie-app openen", "ru_RU": "Войти в приложение продвижения", "pl_PL": "Wejść do aplikacji promocyjnej", "pt_PT": "Entrar na app de promoção", "it_IT": "Entra nell'app promozionale", "ro_RO": "Intrați în aplicația de promovare", "ms_MY": "Masuk ke aplikasi promosi", "vi_VN": "<PERSON><PERSON><PERSON> dụng k<PERSON>ến mãi", "id_ID": "Masuk ke aplikasi promosi", "fil_PH": "Pumasok sa promotion app", "cs_CZ": "Vstoupit do propagační aplikace", "el_GR": "Εισαγωγή στην εφαρμογή προώθησης", "pt_BR": "Entrar no app de promoção", "hu_HU": "Promóciós alkalmazásba belépés", "tr_TR": "Promosyon uygulamasına gir", "sk_SK": "Vstúpiť do propagačnej aplikácie", "ar_SA": "الدخول إلى تطبيق الترويج"}}, {"namespace": "orion.agent.action", "level": "admin", "source": "builtin", "name": "EXIT_PRODUCT_PROMOTION_APP", "category": "system_admin", "display_name": "退出推销APP", "en_display_name": "Exit Promotion App", "desc": "Exit the product promotion application.", "desc_chinese": "退出推广/推销APP。", "parameters": [], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.EXIT_PROMOTE_APP", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "退出推销APP", "zh_TW": "退出推銷APP", "zh_GD": "退出推銷APP", "en_US": "Exit Promotion App", "ja_JP": "プロモーションアプリを終了", "ko_KR": "프로모션 앱 종료", "th_TH": "ออกจากแอปโปรโมชั่น", "de_DE": "Promotion-A<PERSON> verlassen", "es_ES": "Salir de app de promoción", "fr_FR": "Quitter l'app de promotion", "da_DK": "Forlad promotions-app", "sv_SE": "<PERSON><PERSON><PERSON><PERSON>föringsap<PERSON>", "fi_FI": "<PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>ø<PERSON>", "nl_NL": "Promotie-app afsluiten", "ru_RU": "Выйти из приложения продвижения", "pl_PL": "Wyjść z aplikacji promocyjnej", "pt_PT": "Sair da app de promoção", "it_IT": "Esci dall'app promozionale", "ro_RO": "Ieșiți din aplicația de promovare", "ms_MY": "<PERSON><PERSON><PERSON> dari aplikasi promosi", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>ng dụng k<PERSON>ến mãi", "id_ID": "<PERSON><PERSON><PERSON> dari aplikasi promosi", "fil_PH": "Lumabas sa promotion app", "cs_CZ": "Opustit propagační aplikaci", "el_GR": "Έξοδος από την εφαρμογή προώθησης", "pt_BR": "Sair do app de promoção", "hu_HU": "Kilépés a promóciós alkalmazásból", "tr_TR": "Promosyon uygulamasından çık", "sk_SK": "Opustiť propagačnú aplikáciu", "ar_SA": "الخروج من تطبيق الترويج"}, "execute_function": "execute_clear_cache_for_key"}, {"namespace": "orion.agent.action", "level": "apk", "source": "builtin", "name": "SET_MAP_LOCATION", "category": "system_admin", "display_name": "设置位置", "en_display_name": "Set Location", "desc": "Add or modify a location point on the map for navigation and positioning reference.", "desc_chinese": "新增或修改地图上的位置点(点位)，方便机器人导航和定位。", "parameters": [{"name": "location", "type": "string", "desc": "The name of the location to set.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.maptool"], "client_alias": "orion.agent.action.SET_LOCATION", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "设置位置", "zh_TW": "設置位置", "zh_GD": "設置位置", "en_US": "Set Location", "ja_JP": "位置設定", "ko_KR": "위치 설정", "th_TH": "ตั้งค่าตำแหน่ง", "de_DE": "Standort festlegen", "es_ES": "Establecer ubicación", "fr_FR": "Définir l'emplacement", "da_DK": "Indstil placering", "sv_SE": "<PERSON><PERSON><PERSON> in plats", "fi_FI": "<PERSON><PERSON> si<PERSON>i", "nb_NO": "<PERSON><PERSON> p<PERSON>", "nl_NL": "Locatie instellen", "ru_RU": "Установить местоположение", "pl_PL": "Ustaw lokalizację", "pt_PT": "Definir localização", "it_IT": "Imposta posizione", "ro_RO": "Setați locația", "ms_MY": "Tetapkan lokasi", "vi_VN": "Đặt vị trí", "id_ID": "Atur lokasi", "fil_PH": "Itakda ang lokasyon", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Ορισμός τοποθεσίας", "pt_BR": "Definir localização", "hu_HU": "<PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON>", "sk_SK": "Nastaviť polohu", "ar_SA": "تحديد الموقع"}}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "ANSWER_KNOWLEDGE_QUESTION", "category": "information", "display_name": "找答案", "en_display_name": "Find Answer", "desc": "Professionally answer user questions about company products, employee information by querying the knowledge base.", "desc_chinese": "需要查询知识库来回答用户的问题，如「公司产品」、「员工信息」等。", "parameters": [{"name": "question", "type": "string", "desc": "Based on conversation context, summarize the user's specific question that needs knowledge-based answers.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 600, "result_schema": [{"name": "knowledge_content", "desc": "The knowledge content.", "type": "string", "enum_constant": null}], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.KNOWLEDGE_QA", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "找答案", "zh_TW": "找答案", "zh_GD": "找答案", "en_US": "Find Answer", "ja_JP": "答えを見つける", "ko_KR": "답 찾기", "th_TH": "หาคำตอบ", "de_DE": "Antwort finden", "es_ES": "Encontrar respuesta", "fr_FR": "Trouver la réponse", "da_DK": "Find svar", "sv_SE": "<PERSON><PERSON> svar", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON> svar", "nl_NL": "Antwoord vinden", "ru_RU": "Найти ответ", "pl_PL": "Znajdź odpowiedź", "pt_PT": "Encontrar resposta", "it_IT": "<PERSON><PERSON><PERSON> ris<PERSON>a", "ro_RO": "Găsiți răspunsul", "ms_MY": "<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> c<PERSON>u trả lời", "id_ID": "<PERSON><PERSON>n", "fil_PH": "<PERSON><PERSON><PERSON> ang sagot", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON>", "el_GR": "Βρείτε την απάντηση", "pt_BR": "Encontrar resposta", "hu_HU": "V<PERSON>lasz k<PERSON>ése", "tr_TR": "Cevap bul", "sk_SK": "Nájsť odpoveď", "ar_SA": "الإجابة على سؤال المعرفة"}, "execute_function": "knowledge_qa"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "TAKE_PHOTO_WITH_ROBOT", "category": "entertainment", "display_name": "合影", "en_display_name": "Group Photo", "desc": "Take a group photo with users. Use when users explicitly request a photo together.", "desc_chinese": "与机器人合影拍照。当用户明确表示要合影或拍照时使用此功能。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_01917709e2d711f26014d0f1e78295fb"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.INTERVIEW_START_PHOTO", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "合影", "zh_TW": "合影", "zh_GD": "合影", "en_US": "Group Photo", "ja_JP": "集合写真", "ko_KR": "단체 사진", "th_TH": "ถ่ายรูปกลุ่ม", "de_DE": "Gruppenfoto", "es_ES": "Foto grupal", "fr_FR": "Photo de groupe", "da_DK": "Gruppebillede", "sv_SE": "Gruppfoto", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Gruppebilde", "nl_NL": "Groepsfoto", "ru_RU": "Групповое фото", "pl_PL": "Zdjęcie grupowe", "pt_PT": "Foto de grupo", "it_IT": "Foto di gruppo", "ro_RO": "Fotografie de grup", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Foto bersama", "fil_PH": "Group photo", "cs_CZ": "S<PERSON>pinové foto", "el_GR": "Ομαδική φωτογραφία", "pt_BR": "Foto em grupo", "hu_HU": "Csoportkép", "tr_TR": "Grup fotoğrafı", "sk_SK": "<PERSON><PERSON><PERSON>ová fotografia", "ar_SA": "التقاط صورة مع الروبوت"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CHANGE_FACIAL_EXPRESSION", "category": "entertainment", "display_name": "换脸", "en_display_name": "Change Face", "desc": "Change robot's facial expression or display different emotions on screen.", "desc_chinese": "切换机器人的面部表情。当用户要求机器人改变表情、展示不同情绪时使用。", "parameters": [], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_01917709e2d711f26014d0f1e78295fb"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CHANGE_FACE", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "换脸", "zh_TW": "換臉", "zh_GD": "換臉", "en_US": "Change Face", "ja_JP": "顔を変える", "ko_KR": "얼굴 바꾸기", "th_TH": "เปลี่ยนหน้า", "de_DE": "Gesicht wechseln", "es_ES": "Cambiar cara", "fr_FR": "Changer de visage", "da_DK": "<PERSON><PERSON> ansigt", "sv_SE": "Byt ansikte", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "<PERSON><PERSON>", "nl_NL": "Gezicht veranderen", "ru_RU": "Сменить лицо", "pl_PL": "Zmień twarz", "pt_PT": "<PERSON>dar rosto", "it_IT": "Cambia faccia", "ro_RO": "Schimbați fața", "ms_MY": "<PERSON><PERSON> wajah", "vi_VN": "Đổi mặt", "id_ID": "Ganti wajah", "fil_PH": "<PERSON><PERSON><PERSON> ang mukha", "cs_CZ": "Změnit obličej", "el_GR": "Αλλαγ<PERSON> προσώπου", "pt_BR": "Trocar rosto", "hu_HU": "<PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Zmeniť tvár", "ar_SA": "تغيير تعبير الوجه"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "CLICK_WEB_ELEMENT", "category": "web_browser", "display_name": "点击", "en_display_name": "Click", "desc": "Simulate a click action on clickable elements in the current web page being displayed.", "desc_chinese": "模拟在当前显示的网页上点击可点击元素。仅在用户明确要点击网页上某个元素时使用。", "parameters": [{"name": "element_tag", "type": "enum", "desc": "可点击元素的标签", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_clickable_elements_value"}], "execute_side": "robot", "execute_timeout_limit": 0, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.CLICK", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "点击", "zh_TW": "點擊", "zh_GD": "點擊", "en_US": "Click", "ja_JP": "クリック", "ko_KR": "클릭", "th_TH": "คลิก", "de_DE": "<PERSON><PERSON><PERSON>", "es_ES": "<PERSON><PERSON> clic", "fr_FR": "C<PERSON>r", "da_DK": "Klik", "sv_SE": "<PERSON><PERSON><PERSON>", "fi_FI": "<PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Klikk", "nl_NL": "Klikken", "ru_RU": "Щелчок", "pl_PL": "<PERSON><PERSON><PERSON><PERSON>", "pt_PT": "Clique", "it_IT": "Fare clic", "ro_RO": "Click", "ms_MY": "Klik", "vi_VN": "<PERSON><PERSON><PERSON><PERSON>", "id_ID": "Klik", "fil_PH": "I-click", "cs_CZ": "Kliknout", "el_GR": "Κάντε κλικ", "pt_BR": "Clique", "hu_HU": "Kattintás", "tr_TR": "Tıkla", "sk_SK": "Kliknite", "ar_SA": "اضغط"}, "post_processing": "convert_element_id"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "SHOW_HOME_PAGE", "category": "product", "display_name": "展示首页", "en_display_name": "Show Home Page", "desc": "Homepage. Use when the user wants to return to the home page or view an overview of all products.", "desc_chinese": "首页。当用户表达要回到首页，或需要查看所有产品概览时使用。", "parameters": [], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "data", "desc": "home page data", "type": "dict", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.HOME", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "展示首页", "zh_TW": "展示首頁", "zh_GD": "展示首頁", "en_US": "Show Home Page", "ja_JP": "ホームページを表示", "th_TH": "แสดงหน้าแรก", "de_DE": "Startseite anzeigen", "es_ES": "Mostrar página de inicio", "ko_KR": "홈페이지 표시", "da_DK": "Vis startside", "sv_SE": "Visa startsida", "fi_FI": "Näytä kotisivu", "nb_NO": "<PERSON><PERSON>", "fr_FR": "Afficher la page d'accueil", "nl_NL": "Toon startpagina", "ru_RU": "Показать главную страницу", "pl_PL": "Po<PERSON>ż stronę główną", "pt_PT": "Mostrar página inicial", "it_IT": "Mostra pagina iniziale", "ro_RO": "Afișează pagina de pornire", "ms_MY": "<PERSON><PERSON><PERSON><PERSON><PERSON> halaman utama", "vi_VN": "<PERSON><PERSON><PERSON> thị trang chủ", "id_ID": "<PERSON><PERSON><PERSON><PERSON> halaman beranda", "fil_PH": "Ipakita ang home page", "cs_CZ": "Zobrazit domovskou stránku", "el_GR": "Εμφάνιση αρχικής σελίδας", "pt_BR": "Mostrar página inicial", "hu_HU": "Főoldal megjeleníté<PERSON>", "tr_TR": "<PERSON>", "sk_SK": "Zobraziť domovskú stránku", "ar_SA": "عرض الصفحة الرئيسية"}, "execute_function": "home_v3"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "GET_PRODUCT_DETAILS", "category": "product", "display_name": "展示产品详情", "en_display_name": "Show Product Detail", "desc": "Display detailed information about a specific product that user wants to learn more about.", "desc_chinese": "展示具体产品的详细信息。当用户明确表达想了解特定产品时使用。", "parameters": [{"name": "production", "type": "enum", "desc": "The product the user wants to learn more about. Use explicit user intent if specified; otherwise, refer to `product_name` in Robot Screen Information.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_product_list_v2"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "product", "desc": "The product detail data", "type": "dict", "enum_constant": null}, {"name": "product_detail", "desc": "The media resources of the product", "type": "list", "enum_constant": null}, {"name": "text_content", "desc": "The text content of the product", "type": "page_info", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.PRODUCT_DETAIL", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "展示产品详情", "zh_TW": "展示產品詳情", "zh_GD": "展示產品詳情", "en_US": "Show Product Detail", "ja_JP": "製品詳細を表示", "th_TH": "แสดงรายละเอียดผลิตภัณฑ์", "de_DE": "Produktdetails anzeigen", "es_ES": "Mostrar detalle del producto", "ko_KR": "제품 상세 정보 표시", "da_DK": "<PERSON>is produkt<PERSON>", "sv_SE": "Visa produktdetaljer", "fi_FI": "Näytä tuote<PERSON>dot", "nb_NO": "<PERSON>is produkt<PERSON>", "fr_FR": "Affiche<PERSON> les détails du produit", "nl_NL": "Toon productdetails", "ru_RU": "Показать детали продукта", "pl_PL": "Pokaż szczegóły produktu", "pt_PT": "Mostrar detalhes do produto", "it_IT": "Mostra de<PERSON>gli prodotto", "ro_RO": "Afișează detaliile produsului", "ms_MY": "<PERSON><PERSON><PERSON><PERSON><PERSON> butiran produk", "vi_VN": "<PERSON><PERSON><PERSON> thị chi tiết sản phẩm", "id_ID": "Tam<PERSON>lkan detail produk", "fil_PH": "Ipakita ang detalye ng produkto", "cs_CZ": "Zobrazit detail produktu", "el_GR": "Εμφάνιση λεπτομερειών προϊόντος", "pt_BR": "Mostrar detalhes do produto", "hu_HU": "Termék részletek megjelenítése", "tr_TR": "<PERSON><PERSON><PERSON><PERSON>ı gö<PERSON>", "sk_SK": "Zobraziť detail produktu", "ar_SA": "عرض تفاصيل المنتج"}, "execute_function": "production_detail_v2"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "GET_PRODUCT_FEATURE_DETAILS", "category": "product", "display_name": "介绍产品功能", "en_display_name": "Introduce Product Function", "desc": "Get specific features of a product for promotion. Use when the user wants to know about a particular feature.", "desc_chinese": "获取产品的特定功能等，用于产品推销。当用户想了解产品某项具体功能时使用。", "parameters": [{"name": "production_function", "type": "enum", "desc": "The specific feature of the product.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_product_functions_v2"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "title", "desc": "The title of the product function", "type": "string", "enum_constant": null}, {"name": "url", "desc": "The url of the product function", "type": "string", "enum_constant": null}, {"name": "type", "desc": "The type of media resource", "type": "enum", "enum_constant": ["image", "video"]}, {"name": "text_content", "desc": "The text content of the product function", "type": "page_info", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.PRODUCT_FUNCTION", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "介绍产品功能", "zh_TW": "介紹產品功能", "zh_GD": "介紹產品功能", "en_US": "Introduce Product Function", "ja_JP": "製品機能を紹介", "th_TH": "แนะนำฟีเจอร์ผลิตภัณฑ์", "de_DE": "Produktfunktion vorstellen", "es_ES": "Introducir función del producto", "ko_KR": "제품 기능 소개", "da_DK": "Introducer produktfunktion", "sv_SE": "Introducera produktfunktion", "fi_FI": "Esittele tuotteen ominaisuus", "nb_NO": "Introduser produktfunksjon", "fr_FR": "Présenter la fonction du produit", "nl_NL": "Productfunctie introduceren", "ru_RU": "Представить функцию продукта", "pl_PL": "Przedstaw funkcję produktu", "pt_PT": "Apresentar função do produto", "it_IT": "Presenta funzione prodotto", "ro_RO": "Prezintă funcția produsului", "ms_MY": "Perkenalkan fungsi produk", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> chức năng sản phẩm", "id_ID": "Perkenalkan fungsi produk", "fil_PH": "Ipakilala ang function ng produkto", "cs_CZ": "Představit funkci produktu", "el_GR": "Παρουσίαση λειτουργίας προϊόντος", "pt_BR": "Apresentar função do produto", "hu_HU": "Termék funk<PERSON> be<PERSON>", "tr_TR": "<PERSON>rün işlevini tanıt", "sk_SK": "Predstaviť funkciu produktu", "ar_SA": "تقديم وظيفة المنتج"}, "execute_function": "production_function_v2"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "GET_PRODUCT_PARAMETERS_SPECIFICATIONS", "category": "product", "display_name": "介绍产品参数规格", "en_display_name": "Introduce Product Parameters", "desc": "Get parameter specifications of a product for promotion. Use when the user wants to know about specification.", "desc_chinese": "获取产品的参数规格，用于产品推销。当用户想了解产品参数规格时使用。", "parameters": [{"name": "production_function", "type": "enum", "desc": "The specific feature or parameter specification of the product.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_product_parameters"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "获取产品参数规格", "zh_TW": "獲取產品參數規格", "zh_GD": "獲取產品參數規格", "en_US": "Get product parameter specifications", "ja_JP": "製品パラメータ仕様を取得", "ko_KR": "제품 파라미터 사양 가져오기", "th_TH": "รับข้อมูลสเปคพารามิเตอร์สินค้า", "de_DE": "Produktparameterspezifikationen abrufen", "es_ES": "Obtener especificaciones de parámetros del producto", "fr_FR": "Obtenir les spécifications des paramètres du produit", "da_DK": "He<PERSON> produktspec<PERSON><PERSON><PERSON>er", "sv_SE": "Hämta produktspecifikationer", "fi_FI": "Hae tuotteen parametrien tekniset tiedot", "nb_NO": "He<PERSON> produktspecifikasjoner", "nl_NL": "Productparameterspecificaties ophalen", "ru_RU": "Получить характеристики параметров продукта", "pl_PL": "Pobierz specyfikacje parametrów produktu", "pt_PT": "Obter especificações dos parâmetros do produto", "it_IT": "Ottieni specifiche dei parametri del prodotto", "ro_RO": "Obține specificațiile parametrilor produsului", "ms_MY": "Dapatkan spesifikasi parameter produk", "vi_VN": "<PERSON><PERSON><PERSON> thông số kỹ thuật tham số sản phẩm", "id_ID": "Dapatkan spesifikasi parameter produk", "fil_PH": "Kunin ang mga detalye ng parameter ng produkto", "cs_CZ": "Získat specifikace parametrů produktu", "el_GR": "Λήψη προδιαγραφών παραμέτρων προϊόντος", "pt_BR": "Obter especificações dos parâmetros do produto", "hu_HU": "Termék paramétereinek specifikációinak lekérése", "tr_TR": "Ürün parametre özelliklerini al", "sk_SK": "Získať špecifikácie parametrov produktu", "ar_SA": "الحصول على مواصفات معايير المنتج"}, "execute_function": "production_parameters"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "COLLECT_USER_INFO", "category": "product", "display_name": "收集用户信息", "en_display_name": "Collect User Info", "desc": "Collect user info in promotional scenarios. Triggered only when the user's last dialogue contains specific fields.", "desc_chinese": "用于推销场景中收集用户信息字段。仅当用户最后一轮对话中包含需收集的字段（`user_info_fields` 可选列表中的字段）时触发此技能，用于构建精准用户画像。", "parameters": [{"name": "user_info_fields", "type": "enum", "desc": "List of specific user information fields to collect from the predefined enum list. Supports multiple keys when user input contains multiple types of information.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_userinfo_collect"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "收集用户信息", "zh_TW": "收集用戶信息", "zh_GD": "收集用戶信息", "en_US": "Collect user information", "ja_JP": "ユーザー情報を収集する", "th_TH": "รวบรวมข้อมูลผู้ใช้", "ko_KR": "사용자 정보 수집", "da_DK": "Indsaml brugeroplysninger", "sv_SE": "<PERSON><PERSON> in användarinformation", "fi_FI": "Kerää k<PERSON>äj<PERSON>", "nb_NO": "Samle inn brukerinfo", "fr_FR": "Collecter les informations utilisateur", "de_DE": "Benutzerinformationen sammeln", "es_ES": "Recopilar información del usuario", "nl_NL": "Gebruikersinformatie verzamelen", "ru_RU": "Собрать информацию о пользователе", "pl_PL": "Zbierz informacje o użytkowniku", "pt_PT": "Recolher informações do usuário", "it_IT": "Raccogli informazioni sull'utente", "ro_RO": "Colectează informații despre utilizator", "ms_MY": "Kumpul maklumat pengguna", "vi_VN": "<PERSON>hu thập thông tin người dùng", "id_ID": "Kumpulkan informasi pengguna", "fil_PH": "Kolektahin ang impormasyon ng gumagamit", "cs_CZ": "Shromáždit informace o uživateli", "el_GR": "Συλλογή πληροφοριών χρήστη", "pt_BR": "Coletar informações do usuário", "hu_HU": "Felhasználói információk gyűjtése", "tr_TR": "Kullanıcı bilgilerini topla", "sk_SK": "Zhromaždiť informácie o používateľovi", "ar_SA": "جمع معلومات المستخدم"}, "execute_function": "collect_user_info_and_recommend"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "SHOW_CONTACT_INFORMATION", "category": "product", "display_name": "提供联系方式", "en_display_name": "Show Contact Information", "desc": "Display sales or service contact information when users inquire about pricing, purchasing, express intent to leave, or directly request contact details.", "desc_chinese": "展示销售或服务联系方式，适用于用户咨询价格、购买、表达离开意向或直接索要联系方式的场景。", "parameters": [], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.SHOW_CONTACT_INFORMATION", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "提供联系方式", "zh_TW": "提供聯繫方式", "zh_GD": "提供聯繫方式", "en_US": "Show Contact Information", "ja_JP": "連絡先情報を表示", "th_TH": "แสดงข้อมูลติดต่อ", "de_DE": "Kontaktinformationen anzeigen", "es_ES": "Mostrar información de contacto", "ko_KR": "연락처 정보 표시", "da_DK": "<PERSON><PERSON>", "sv_SE": "Visa kontaktinformation", "fi_FI": "Näytä <PERSON>", "nb_NO": "<PERSON><PERSON>", "fr_FR": "Afficher les informations de contact", "nl_NL": "Contactgegevens tonen", "ru_RU": "Показать контактную информацию", "pl_PL": "Pokaż informacje kontaktowe", "pt_PT": "Mostrar informações de contacto", "it_IT": "Mostra informazioni di contatto", "ro_RO": "Afișează informațiile de contact", "ms_MY": "Tunjukkan maklumat hubungan", "vi_VN": "<PERSON><PERSON><PERSON> thị thông tin liên hệ", "id_ID": "Tampilkan informasi kontak", "fil_PH": "Ipakita ang contact information", "cs_CZ": "Zobrazit kontaktní informace", "el_GR": "Εμφάνιση στοιχείων επικοινωνίας", "pt_BR": "Mostrar informações de contato", "hu_HU": "Ka<PERSON>csolati információk megjelenítése", "tr_TR": "İletişim bilgilerini göster", "sk_SK": "Zobraziť kontaktné informácie", "ar_SA": "عرض معلومات الاتصال"}, "execute_function": "show_contact_information"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "REALTIME_SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Think about it.", "desc_chinese": "思考说", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": true, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "stream_say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "CALL_LLM", "category": "interaction", "display_name": "调用llm", "en_display_name": "Call LLM", "desc": "Call llm to get the result.", "desc_chinese": "调用llm获取结果", "parameters": [{"name": "messages", "type": "list", "desc": "请求内容", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "llm_config", "type": "dict", "desc": "llm配置", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [{"name": "llm_response", "desc": "The response from llm", "type": "dict", "enum_constant": null}], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": null, "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": true, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}, "execute_function": "call_llm"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "GENERAL_SALES_SERVICE", "category": "product", "display_name": "说", "en_display_name": "Say", "desc": "For product presentations, Q&A, demonstrations, and recommendations in sales mode; suitable for general product promotion or non-specific inquiries.", "desc_chinese": "推销模式下用于产品介绍、答疑、演示、推荐等销售话术，适用于一般产品推销、推荐。", "parameters": [], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.SALES_PITCH", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "说", "zh_TW": "說", "zh_GD": "說", "en_US": "Say", "ja_JP": "言う", "ko_KR": "말하기", "th_TH": "พูด", "de_DE": "Sagen", "es_ES": "Decir", "fr_FR": "Dire", "da_DK": "Sig", "sv_SE": "Säga", "fi_FI": "Sanoa", "nb_NO": "Si", "nl_NL": "Zeggen", "ru_RU": "Сказать", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON>", "it_IT": "Dire", "ro_RO": "Spune", "ms_MY": "Kat<PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Kat<PERSON><PERSON>", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Říci", "el_GR": "Πείτε", "pt_BR": "<PERSON><PERSON>", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Povedať", "ar_SA": "خدمة المبيعات العامة"}, "execute_function": "sales_pitch_v3"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "ANSWER_COMPETITOR_INQUIRY", "category": "product", "display_name": "竞品信息", "en_display_name": "Competitive Information", "desc": "Provide answers to explicit user inquiries regarding competitor products. Use only when the user directly requests information about competitors.", "desc_chinese": "回答用户明确询问的关于竞争对手产品的问题。如：“竞品产品介绍”，“竞品产品参数”，“竞品产品价格”等。", "parameters": [{"name": "competitor_production_name", "type": "enum", "desc": "The name of the competitor product the user is inquiring about.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_competitor_production_name"}], "execute_side": "server", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.COMPETITOR_QUESTION", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "竞品信息", "zh_TW": "競品資訊", "zh_GD": "競品資訊", "en_US": "Competitive Information", "ja_JP": "競合情報", "th_TH": "ข้อมูลการแข่งขัน", "de_DE": "Wettbewerbsinformationen", "es_ES": "Información competitiva", "ko_KR": "경쟁 정보", "da_DK": "Konkurrenceinformation", "sv_SE": "Konkurrensinformation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Konkurranseinformasjon", "fr_FR": "Informations <PERSON><PERSON><PERSON>", "nl_NL": "Concurrentiële informatie", "ru_RU": "Конкурентная информация", "pl_PL": "Informacje konkurencyjne", "pt_PT": "Informações competitivas", "it_IT": "Informazioni competitive", "ro_RO": "Informații competitive", "ms_MY": "Maklumat persaingan", "vi_VN": "Thông tin cạnh tranh", "id_ID": "Informasi kompetitif", "fil_PH": "Competitive na impormasyon", "cs_CZ": "Informace o konkurenci", "el_GR": "Ανταγω<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς πληροφορίες", "pt_BR": "Informações competitivas", "hu_HU": "Versenytársi információk", "tr_TR": "Rekabet bilgileri", "sk_SK": "Informácie o konkurencii", "ar_SA": "معلومات المنافسة"}, "execute_function": "competitors_answer"}, {"namespace": "orion.app.promote", "level": "opk", "source": "builtin", "name": "SHOW_PRODUCT_CASES", "category": "product", "display_name": "案例介绍", "en_display_name": "Case Introduction", "desc": "Showcase real-world product application cases through videos or images. Use when users want to learn about actual product usage scenarios.", "desc_chinese": "通过视频或图片展示产品在实际场景中的应用案例。当用户想了解产品实际应用情况时使用。", "parameters": [{"name": "case", "type": "enum", "desc": "The specific application scenario to be showcased.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_case_name_v2"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [{"name": "title", "desc": "The title of the product function", "type": "string", "enum_constant": null}, {"name": "url", "desc": "The url of the product function", "type": "string", "enum_constant": null}, {"name": "type", "desc": "The type of media resource", "type": "enum", "enum_constant": ["image", "video"]}, {"name": "text_content", "desc": "The text content of the product function", "type": "page_info", "enum_constant": null}], "app_ids": ["system_2b56d6156937eba639086e597942abf8"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.app.promote.CASE_INTRODUCTION", "version": "arm_draft", "exported": false, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "domestic", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "案例介绍", "zh_TW": "案例介紹", "zh_GD": "案例介紹", "en_US": "Case Introduction", "ja_JP": "事例紹介", "th_TH": "การแนะนำกรณีศึกษา", "de_DE": "Fallstudie Einführung", "es_ES": "Introducción de caso", "ko_KR": "사례 소개", "da_DK": "Case introduktion", "sv_SE": "Fallintroduktion", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Case introduksjon", "fr_FR": "Introduction de cas", "nl_NL": "Case introductie", "ru_RU": "Введение в случай", "pl_PL": "Wprowadzenie przypadku", "pt_PT": "Introdução do caso", "it_IT": "Introduzione del caso", "ro_RO": "Introducer<PERSON>i", "ms_MY": "Pengenalan kes", "vi_VN": "<PERSON><PERSON><PERSON><PERSON> thiệu tr<PERSON><PERSON><PERSON> hợp", "id_ID": "Pengenalan kasus", "fil_PH": "Pagpapakilala ng kaso", "cs_CZ": "Představení případu", "el_GR": "Εισαγωγή περίπτωσης", "pt_BR": "Introdução do caso", "hu_HU": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "<PERSON>aka tanıtımı", "sk_SK": "Predstavenie prípadu", "ar_SA": "مقدمة الحالة"}, "execute_function": "case_introduction_v2"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "GUIDE_INDOOR_NAVIGATION", "category": "navigation", "display_name": "准备领位", "en_display_name": "Indoor Navigation Recommendation", "desc": "Point navigation intent. Users can only be taken to the locations provided below, and outdoor locations are not supported.If the user does not explicitly specify the destination, a list of up to 4 available navigation points is returned and sorted by similarity, with the closest point at the front.", "desc_chinese": "带领用户前往室内特定位置；结合对话历史和用户特征（如性别等），为用户精准推荐最符合其意图的地点。不支持室外导航！ 用户想要去室外时，请用`say_for_clarification`拒绝。", "parameters": [{"name": "destinations", "type": "String array", "desc": "Navigation points that match user intent. You can choose multiple. only choose from the list of points.", "is_required": false, "length": 4, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_robot_support_map_points"}, {"name": "guide_text", "type": "string", "desc": "A natural guiding response that varies based on the number of destinations:\n    - For single destination: Direct guidance to that location\n    - For multiple destinations: Present options and ask for user's choice\n    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 300, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.NAVIGATE_REC_START", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "准备领位", "zh_TW": "準備領位", "zh_GD": "準備領位", "en_US": "Indoor Navigation Recommendation", "ja_JP": "屋内ナビゲーション推奨", "ko_KR": "실내 내비게이션 추천", "th_TH": "คำแนะนำการนำทางในร่ม", "de_DE": "Innennavigationsempfehlung", "es_ES": "Recomendación navegación interior", "fr_FR": "Recommandation navigation intérieure", "da_DK": "Indendørs navigationsanbefaling", "sv_SE": "Inomhusnavigeringsrekommendation", "fi_FI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nb_NO": "Innendørs navigasjonsanbefaling", "nl_NL": "Binnennavigatie a<PERSON>", "ru_RU": "Рекомендация внутренней навигации", "pl_PL": "Rekomendacja nawigacji wewnętrznej", "pt_PT": "Recomendação navegação interior", "it_IT": "Raccomandazione navigazione interna", "ro_RO": "Recomandare navigație interioară", "ms_MY": "Cadangan navigasi dalam ruangan", "vi_VN": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị điều hướng trong nhà", "id_ID": "Rekomendasi navigasi dalam ruangan", "fil_PH": "Rekomendasyon ng indoor navigation", "cs_CZ": "Doporučení vnitřní navigace", "el_GR": "Σύσταση εσωτερικής πλοήγησης", "pt_BR": "Recomendação navegação interior", "hu_HU": "<PERSON><PERSON><PERSON> na<PERSON><PERSON><PERSON><PERSON><PERSON>", "tr_TR": "İç mekan navigasyon önerisi", "sk_SK": "Odporúčanie vnútornej navigácie", "ar_SA": "توصية التنقل الداخلي"}, "post_processing": "convert_navigation_points"}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "OPEN_WEB_URL_DEFINED", "category": "web_browser", "display_name": "打开轻应用", "en_display_name": "Open Light App", "desc": "Use a browser or app to browse information. Trigger this action if there's a semantically identical question in the predefined list, as it allows you to directly open the URL or app.", "desc_chinese": "使用浏览器或者APP浏览信息。*只要*预定义问题列表中存在与用户当前问题语义相同的问题，则选择此action。", "parameters": [{"name": "predefined_question", "type": "enum", "desc": "list of predefined question.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_light_app_enum"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_f1383df09054ea2ae5ce28a977b54a5d", "system_95b3de1d8f68b33892e5ac2b42662517"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.OPEN_WEB_URL", "version": "arm_draft", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": true, "page_id": "", "display_names": {"zh_CN": "打开轻应用", "zh_TW": "打開輕應用", "zh_GD": "打開輕應用", "en_US": "Open Light App", "ja_JP": "ライトアプリを開く", "ko_KR": "라이트 앱 열기", "th_TH": "เปิดแอปพลิเคชันเบา", "de_DE": "Light App <PERSON>", "es_ES": "Abrir aplicación ligera", "fr_FR": "Ouvrir application légère", "da_DK": "Åbn let app", "sv_SE": "Öppna lätt app", "fi_FI": "<PERSON><PERSON> kevyt sovellus", "nb_NO": "Å<PERSON>ne lett app", "nl_NL": "Lichte app openen", "ru_RU": "Открыть легкое приложение", "pl_PL": "O<PERSON><PERSON><PERSON><PERSON> aplikację", "pt_PT": "Abrir aplicação leve", "it_IT": "Apri app leggera", "ro_RO": "Deschide aplicația ușoară", "ms_MY": "<PERSON><PERSON> a<PERSON><PERSON>an", "vi_VN": "Mở ứng dụng nhẹ", "id_ID": "<PERSON><PERSON> a<PERSON><PERSON>an", "fil_PH": "Buksan ang light app", "cs_CZ": "Otev<PERSON><PERSON><PERSON> aplikaci", "el_GR": "Άνοιγμα ελαφριάς εφαρμογής", "pt_BR": "Abrir aplicativo leve", "hu_HU": "Könnyű alkalmazás megnyitása", "tr_TR": "<PERSON><PERSON><PERSON>", "sk_SK": "Otvoriť ľahkú aplikáciu", "ar_SA": "فتح التطبيق الخفيف"}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "GET_WEATHER", "category": "weather", "display_name": "获取天气信息", "en_display_name": "Get Weather Information", "desc": "Get weather information for today and the next 10 days. City name must be in English.", "desc_chinese": "获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！!!", "parameters": [{"name": "area_level", "type": "enum", "desc": "The level of the area corresponding to the city. province, city, or area.", "is_required": false, "length": null, "enum_constant": ["province", "city", "area"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "valid_city_name", "type": "string", "desc": "Enter a valid city name (not a venue); defaults to the current city if not specified.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": ["system_a8c9098ad7a91a66287b25d6befef6ec", "system_4f307c6d4cb0f187a3edb3dcc6f43749", "system_024280e32e73c00ad621710870d4cb18"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.WEATHER", "version": "arm_draft", "exported": true, "pre_execute": true, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "获取天气信息", "zh_TW": "獲取天氣資訊", "zh_GD": "獲取天氣資訊", "en_US": "Get Weather Information", "ja_JP": "天気情報取得", "ko_KR": "날씨 정보 얻기", "th_TH": "รับข้อมูลสภาพอากาศ", "de_DE": "Wetterinformationen abrufen", "es_ES": "Obtener información del tiempo", "fr_FR": "Obtenir informations météo", "da_DK": "Få vejrinformation", "sv_SE": "Få väderinformation", "fi_FI": "<PERSON><PERSON>", "nb_NO": "Få værinformasjon", "nl_NL": "Weerinformatie ophalen", "ru_RU": "Получить информацию о погоде", "pl_PL": "Uzyskaj informacje o pogodzie", "pt_PT": "Obter informações meteorológicas", "it_IT": "Ottieni informazioni meteo", "ro_RO": "Obține informații meteo", "ms_MY": "Dapatkan maklumat cuaca", "vi_VN": "<PERSON><PERSON><PERSON> thông tin thời tiết", "id_ID": "Dapatkan informasi cuaca", "fil_PH": "Makakuha ng impormasyon ng panahon", "cs_CZ": "Získat informace o počasí", "el_GR": "Λήψη πληροφοριών καιρού", "pt_BR": "Obter informações meteorológicas", "hu_HU": "Időjárási információk lekérése", "tr_TR": "Hava durumu bilgisi al", "sk_SK": "Získať informácie o počasí", "ar_SA": "الحصول على معلومات عن الطقس"}, "execute_function": "query_weather", "post_processing": "query_weather_post_processing"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "SAY", "category": "interaction", "display_name": "说", "en_display_name": "Say", "desc": "Basic verbal communication with the user. Use for general responses, information delivery, and conversations.", "desc_chinese": "与用户的基础语言交流。用于一般回应、信息传递和对话。", "parameters": [{"name": "text", "type": "string", "desc": "Reply in the first person using plain text only, no emojis.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}, {"name": "arm_gesture", "type": "enum", "desc": "Optional arm gesture executed during communication with the user. Choose an appropriate gesture based on either the speaking content or user requests, similar to natural human gestures during conversation. Not required if no suitable gesture applies.", "is_required": false, "length": null, "enum_constant": ["wave-left-hand", "wave-right-hand", "wave-both-hands", "hand-heart", "dancing", "clap-hands", "raise-left-hand", "raise-right-hand", "raise-both-hands", "cheer-up", "thumbs-up", "salute", "open-arms", "cross-arms", "hands-on-hips", "bend-left-arm", "bend-right-arm", "lower-arms"], "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "both", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.SAY", "version": "arm_draft", "exported": false, "pre_execute": false, "hidden": false, "audio_output": true, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "说", "zh_TW": "說", "zh_GD": "說", "en_US": "Say", "ja_JP": "話す", "th_TH": "พูด", "de_DE": "Sagen", "es_ES": "Decir", "ko_KR": "말하기", "da_DK": "Sig", "sv_SE": "Säg", "fi_FI": "Sanoa", "nb_NO": "Si", "fr_FR": "Dire", "nl_NL": "Zeggen", "ru_RU": "Сказать", "pl_PL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt_PT": "<PERSON><PERSON>", "it_IT": "Dire", "ro_RO": "Spune", "ms_MY": "Berkata", "vi_VN": "<PERSON><PERSON><PERSON>", "id_ID": "Mengatakan", "fil_PH": "<PERSON><PERSON><PERSON>", "cs_CZ": "Říci", "el_GR": "Πούμε", "pt_BR": "<PERSON><PERSON>", "hu_HU": "<PERSON><PERSON><PERSON>", "tr_TR": "Söylemek", "sk_SK": "Povedať", "ar_SA": "قل"}, "execute_function": "say"}, {"namespace": "orion.agent.action", "level": "global", "source": "builtin", "name": "INQUIRE_MAP_POINT_INFO", "category": "information", "display_name": "点位问询", "en_display_name": "Location Inquiry", "desc": "Answer user inquiries about location information, such as 'Where is xx?', 'How to get to xx?'. Provide location details and directions without actually starting navigation.", "desc_chinese": "回答用户关于导航地图中位置信息的询问，如“xx在哪？”、“xx怎么走？”等。仅提供位置详情和方向指引，但不实际开始导航。", "parameters": [{"name": "map_point", "type": "enum", "desc": "The specific map point the user is asking about. Must match exactly from the available navigation map points.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan", "enum_func": "load_robot_support_map_points"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [], "app_ids": [], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.MAP_POINT_INQUIRY", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {}}, {"namespace": "orion.agent.action", "level": "opk", "source": "builtin", "name": "ANSWER_VISUAL_QUESTION", "category": "information", "display_name": "根据视觉回答", "en_display_name": "Answer from Vision", "desc": "Only answer questions about clothing, facial expressions, gender, surroundings, and object recognition through the robot's camera. Topics involving user relationships are not supported.", "desc_chinese": "通过机器人摄像头，仅回答关于人物穿着、表情、性别，周边环境和物体识别的问题，不支持涉及用户关系的话题。", "parameters": [{"name": "image_url", "type": "string", "desc": "The image URL captured by the robot's camera.", "is_required": false, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": true, "need_summary": false, "generate_side": "robot"}, {"name": "question", "type": "string", "desc": "The user's question, must be summarized as a first-person question, with a length limit of 15 characters.", "is_required": true, "length": null, "enum_constant": null, "max": null, "min": null, "is_hidden": false, "need_summary": false, "generate_side": "plan"}], "execute_side": "robot", "execute_timeout_limit": 180, "result_schema": [{"name": "answer_text", "desc": "The answer of the question", "type": "string", "enum_constant": null}], "app_ids": ["system_bf6d2b672273c7369fa557efd61d7a07", "system_4f307c6d4cb0f187a3edb3dcc6f43749"], "package_names": ["com.ainirobot.moduleapp"], "client_alias": "orion.agent.action.ANSWER_QUESTION_FROM_VISION", "version": "arm_draft", "exported": true, "pre_execute": false, "hidden": false, "audio_output": false, "action_area": "all", "only_intervention": false, "page_id": "", "display_names": {"zh_CN": "根据视觉回答", "zh_TW": "根據視覺回答", "zh_GD": "根據視覺回答", "en_US": "Answer from Vision", "ja_JP": "視覚から回答", "ko_KR": "시각으로 답변", "th_TH": "ตอบจากการมองเห็น", "de_DE": "Antwort aus Sicht", "es_ES": "Responder desde visión", "fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON> depuis la vision", "da_DK": "<PERSON>var fra syn", "sv_SE": "<PERSON><PERSON><PERSON> från syn", "fi_FI": "Vastaa näöstä", "nb_NO": "<PERSON>var fra syn", "nl_NL": "Ant<PERSON><PERSON> van zicht", "ru_RU": "Ответить по зрению", "pl_PL": "Odpowiedz z wizji", "pt_PT": "Responder da visão", "it_IT": "<PERSON><PERSON><PERSON><PERSON> dalla visione", "ro_RO": "Răspunde din vedere", "ms_MY": "<PERSON><PERSON><PERSON>", "vi_VN": "<PERSON><PERSON><PERSON> lời từ thị gi<PERSON>c", "id_ID": "<PERSON><PERSON><PERSON> da<PERSON>", "fil_PH": "Sumagot mula sa paningin", "cs_CZ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t ze zraku", "el_GR": "Απάντηση από όραση", "pt_BR": "Responder da visão", "hu_HU": "Válaszolj a látásból", "tr_TR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cevap ver", "sk_SK": "Odpovedať zo zraku", "ar_SA": "الإجابة من الرؤية"}, "execute_function": "answer_question_from_vision_with_arm"}]}