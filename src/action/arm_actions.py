from loguru import logger

from src.action.model import (
    Action,
    Package_Main_Name,
    Package_Map_Name,
)
from src.action.resource import (
    load_robot_support_map_points,
)
from src.action.server_function import (
    say, answer_question_from_vision_with_arm,
)
from src.common.agent_config import Launcher_App_Id, OverSea_Opk_Launcher_App_Id
from src.common.constant import Area
from src.common.action_display_names import ACTION_DISPLAY_NAMES
from src.action.actions import ACTIONS

SUPPORTED_PACKAGE_NAMES = (Package_Main_Name, Package_Map_Name)


ARM_ACTIONS = (
    # 说
    Action(
        name="SAY",
        desc_chinese="与用户的基础语言交流。用于一般回应、信息传递和对话。",
        desc="Basic verbal communication with the user. Use for general responses, information delivery, and conversations.",
        level="global",
        display_name="说",
        en_display_name="Say",
        display_names=ACTION_DISPLAY_NAMES.get("SAY", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="text",
                type="string",
                desc="Reply in the first person using plain text only, no emojis.",
            ),
            Action.Parameter(
                name="arm_gesture",
                type="enum",
                desc="Optional arm gesture executed during communication with the user. Choose an appropriate gesture based on either the speaking content or user requests, similar to natural human gestures during conversation. Not required if no suitable gesture applies.",
                enum_constant=[
                    "wave-left-hand",
                    "wave-right-hand",
                    "wave-both-hands",
                    "hand-heart",
                    "dancing",
                    "clap-hands",
                    "raise-left-hand",
                    "raise-right-hand",
                    "raise-both-hands",
                    "cheer-up",
                    "thumbs-up",
                    "salute",
                    "open-arms",
                    "cross-arms",
                    "hands-on-hips",
                    "bend-left-arm",
                    "bend-right-arm",
                    "lower-arms",
                ],
                is_required=False,
            ),
        ],
        execute_function=say,
        execute_side="both",
        audio_output=True,
        category="interaction",
        client_alias="orion.agent.action.SAY",
    ),
    # 点位问询： 回答用户关于导航地图中位置信息的询问
    Action(
        name="INQUIRE_MAP_POINT_INFO",
        level="global",
        desc="Answer user inquiries about location information, such as 'Where is xx?', 'How to get to xx?'. Provide location details and directions without actually starting navigation.",
        desc_chinese="回答用户关于导航地图中位置信息的询问，如“xx在哪？”、“xx怎么走？”等。仅提供位置详情和方向指引，但不实际开始导航。",
        display_name="点位问询",
        en_display_name="Location Inquiry",
        display_names=ACTION_DISPLAY_NAMES.get("INQUIRE_MAP_POINT_INFO", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="map_point",
                type="enum",
                desc="The specific map point the user is asking about. Must match exactly from the available navigation map points.",
                is_required=True,
                enum_func=load_robot_support_map_points,
            ),
        ],
        action_area=Area.all,
        exported=True,
        category="information",
        client_alias="orion.agent.action.MAP_POINT_INQUIRY",
    ),
    # 根据视觉回答
    Action(
        name="ANSWER_VISUAL_QUESTION",
        level="opk",
        desc_chinese="通过机器人摄像头，仅回答关于人物穿着、表情、性别，周边环境和物体识别的问题，不支持涉及用户关系的话题。",
        desc="Only answer questions about clothing, facial expressions, gender, surroundings, and object recognition through the robot's camera. Topics involving user relationships are not supported.",
        display_name="根据视觉回答",
        en_display_name="Answer from Vision",
        display_names=ACTION_DISPLAY_NAMES.get("ANSWER_VISUAL_QUESTION", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_function=answer_question_from_vision_with_arm,
        parameters=[
            Action.Parameter(
                name="image_url",
                type="string",
                desc="The image URL captured by the robot's camera.",
                is_hidden=True,
                is_required=False,
                generate_side="robot",
            ),
            Action.Parameter(
                name="question",
                type="string",
                desc="The user's question, must be summarized as a first-person question, with a length limit of 15 characters.",
            ),
        ],
        result_schema=[
            Action.Result(
                name="answer_text",
                desc="The answer of the question",
                type="string",
            ),
        ],
        execute_side="robot",
        pre_execute=False,
        exported=True,
        category="information",
        client_alias="orion.agent.action.ANSWER_QUESTION_FROM_VISION",
    ),
)


def __reload_action(
    build_draft_action: bool = True,
    exclude_actions: list[str] = [],
    arm_only: bool = False,
):
    import json
    from pathlib import Path

    exclude_actions = set(exclude_actions or [])
    exclude_actions.add("orion.agent.action.WEATHER_GET".lower())
    exclude_actions.add("orion.agent.action.WEATHER_GET_REALTIME".lower())
    exclude_actions.add("orion.agent.action.NOT_MOVE".lower())
    exclude_actions.add(
        "orion.agent.action.NAVIGATE_START".lower(),
    )

    if build_draft_action:
        # Constants
        if arm_only:
            VERSIONS = {Area.domestic: "arm_draft", Area.overseas: "arm_oversea_draft"}
        else:
            VERSIONS = {Area.domestic: "draft", Area.overseas: "oversea_draft"}

        current_path = Path(__file__).parent
        resources_path = current_path / "action_version" / "resources"
        resources_path.mkdir(parents=True, exist_ok=True)

        for region_version, version in VERSIONS.items():
            region_actions = []
            # Create a set of ARM_ACTION names for faster lookup
            arm_action_names = {action.name for action in ARM_ACTIONS}

            # Add actions from ACTIONS that are not in ARM_ACTIONS
            total_actions = [
                action for action in ACTIONS if action.name not in arm_action_names
            ]

            # Add all ARM_ACTIONS
            total_actions.extend(ARM_ACTIONS)

            for action in total_actions:
                # if action.hidden:
                #     continue

                if action.full_name in exclude_actions:
                    continue

                if action.action_area not in (region_version, "all"):
                    continue

                # 创建一个新的字典用于序列化
                action_dict = {}

                # 将函数字段转换为函数名（字符串）
                action_model_dict = action.model_dump(
                    exclude={"execute_function", "post_processing"}
                )
                action_dict.update(action_model_dict)

                if action.execute_function:
                    action_dict["execute_function"] = action.execute_function.__name__

                if action.post_processing:
                    action_dict["post_processing"] = action.post_processing.__name__

                # 处理parameters中的enum_func
                if "parameters" in action_dict and action_dict["parameters"]:
                    for i, parameter in enumerate(action.parameters):
                        if parameter.enum_func:
                            action_dict["parameters"][i]["enum_func"] = (
                                parameter.enum_func.__name__
                            )
                if (
                    action.name == "GO_CHARGING" and region_version == Area.overseas
                ):  # 海外版GO_CHARGING改为global
                    action_dict["level"] = "global"

                action_dict["version"] = version
                region_actions.append(action_dict)

            config_path = resources_path / f"{version}.json"
            action_config = {"version": version, "actions": region_actions}

            with config_path.open("w", encoding="utf-8") as f:
                json.dump(action_config, f, indent=4, ensure_ascii=False)
                logger.info(
                    f"[{version}] Action config saved to {config_path}, Action Count: {len(region_actions)}"
                )
    logger.info("Action functions reloaded successfully")


if __name__ == "__main__":
    __reload_action(
        exclude_actions=[
            "orion.agent.action.WEATHER_GET".lower(),
            "orion.agent.action.WEATHER_GET_REALTIME".lower(),
            "orion.agent.action.NAVIGATE_START".lower(),
            "orion.agent.action.NOT_MOVE".lower(),
        ],
        arm_only=True,
    )
