[{"Input": "Lade die Batterie auf.", "Output": {"action": "GO_CHARGING"}}, {"Input": "Checken Sie das Wetter in Berlin Mitte.", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "Berlin"}}}, {"Input": "Verfassen Si<PERSON> ein Gedicht zum Abschied von <PERSON>.", "Output": {"action": "GENERATE_MESSAGE", "params": {"instruction": "Verfassen Si<PERSON> ein Gedicht zum Abschied von <PERSON>"}}}, {"Input": "Die letzten vier Ziffern meiner Telefonnummer sind 178B.", "Output": {"action": "SAY", "params": {"text": "Die letzten 4 Ziffern der Handynummer müssen Zahlen sein, Buchstaben funktionieren möglicherweise nicht."}}}, {"Input": "Welche Kleidung sollte ich tragen, wenn ich nach Shenzhen gehe? Muss ich einen Regenschirm mitbringen?", "Output": {"action": "WEATHER_GET", "params": {"city": "Shenzhen", "area_level": "city"}}}, {"Input": "Wird es morgen heiß in Chongqing sein?", "Output": {"action": "WEATHER_GET", "params": {"city": "Chongqing", "area_level": "city"}}}, {"Input": "Wie lange wird es noch in Shanghai regnen?", "Output": {"action": "WEATHER_GET", "params": {"city": "Shanghai", "area_level": "city"}}}, {"Input": "Ausgang", "Output": {"action": "EXIT"}}, {"Input": "<PERSON>ergiss es, das ist nicht nötig, abbrechen.", "Output": {"action": "CANCEL"}}, {"Input": "<PERSON><PERSON>usgesetzt, die aktuelle Geschwindigkeit beträgt 0,3, gehe schnell<PERSON>.", "Output": {"action": "ADJUST_SPEED", "params": {"adjusted_speed": "0.8"}}}, {"Input": "1689", "Output": {"action": "LAST_4_DIGITS_Input", "params": {"last_4_digits": [1, 6, 8, 9]}}}, {"Input": "Wer in der Firma kann am besten mit Alkohol umgehen?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Wer in der Firma kann am meisten trinken?"}}}, {"Input": "Bitte überprüfen Sie das Wetter in Stadt C, Bezirk B, Stadt A.", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "Zone B"}}}, {"Input": "Wann wird es schneien?", "Output": {"action": "WEATHER_GET", "params": {"city": "{City Name}", "area_level": "city"}}}, {"Input": "Wann wird es regnen?", "Output": {"action": "WEATHER_GET", "params": {"city": "{City}", "area_level": "city"}}}, {"Input": "Überprüfen Sie das Wetter.", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "{City}"}}}, {"Input": "WeChat-Nachricht senden.", "Output": {"action": "SAY", "params": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ich weiß noch nicht, wie man WeChat-Nachrichten sendet."}}}, {"Input": "Wie lautet das TikTok-Konto des Chefs?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "<PERSON><PERSON>, ich weiß noch nicht, wie man WeChat-Nach<PERSON><PERSON> sendet"}}}, {"Input": "Biege rechts in einen Kreis ab.", "Output": {"action": "TURN_DIRECTION", "params": {"direction": "right", "angle": "360"}}}, {"Input": "<PERSON>ehe dich rückwärts.", "Output": {"action": "TURN_DIRECTION", "params": {"direction": "right", "angle": 180}}}, {"Input": "Tritt aus den Bindungen der Verwandtschaft ohne Anerkennung heraus.", "Output": {"action": "START_DANCE"}}, {"Input": "Eine unbeschwerte und fröhliche Stimmung.", "Output": {"action": "START_DANCE"}}, {"Input": "Einzigartiger Gang", "Output": {"action": "START_DANCE"}}, {"Input": "Gehen Sie 200 Meter zurück.", "Output": {"action": "SAY", "params": {"text": "Oh, ich kann nur 1 <PERSON>er zurückgehen, wenn ich weiter gehe, schimpft der Chef mit mir."}}}, {"Input": "<PERSON>nn nicht hören.", "Output": {"action": "SET_VOLUME", "params": {"volume_level": "70"}}}, {"Input": "<PERSON><PERSON> ruh<PERSON>, halt den Mund.", "Output": {"action": "SET_VOLUME", "params": {"volume_level": "0"}}}, {"Input": "Hier ist die Empfangstheke.", "Output": {"action": "SET_LOCATION", "params": {"location": "Rezeption"}}}, {"Input": "<PERSON><PERSON> du so-und-so?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "<PERSON><PERSON> du den und den?"}}}, {"Input": "<PERSON><PERSON>?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "<PERSON><PERSON>?"}}}, {"Input": "<Roboter> SAGE 'Bist du sicher, dass du xxx machen möchtest?' <Benutzer> SAGE 'Bestätigt'", "Output": {"action": "CONFIRM"}}, {"Input": "Bitte geben Sie eine detaillierte Einführung in Baoxiaomi.", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Bitte geben Si<PERSON> eine detaillierte Einführung zu Xiaomis Xiao Ai."}}}, {"Input": "Ich möchte ein besseres Verständnis für die Implementierung dieser Hindernisvermeidungsfunktion gewinnen.", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Wie wird die Hindernisvermeidungsfunktion erreicht?"}}}, {"Input": "<PERSON><PERSON><PERSON> dieses Video ab.", "Output": {"CONDITION": "Der Roboterbildschirmstatus ist im Videomodus", "action": "MULTIMEDIA_PLAY"}}, {"Input": "Ausgang", "Output": {"action": "HOME"}}, {"Input": "<PERSON><PERSON> das Video.", "Output": {"CONDITION": "Der Bildschirmstatus des Roboters ist der Videomodus", "action": "COMMON_PAUSE"}}, {"Input": "Was ist der aktuellste Preis des iPhone 16?", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=What+is+the+latest+price+of+the+iPhone+16"}}}, {"Input": "<PERSON><PERSON>, den aktuellen Aktienkurs von Apple zu überprüfen.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Help+me+check+the+latest+stock+price+of+Apple."}}}, {"Input": "<PERSON><PERSON><PERSON><PERSON> von Los Angeles nach New York morgen.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Flights+from+Los+Angeles+to+New+York+tomorrow."}}}, {"Input": "Schauen Sie sich Hotels in New York an.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Look+at+hotels+in+New+York"}}}, {"Input": "Zugtickets von New York nach Los Angeles, Abfahrt am 9. November 2024.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Train+tickets+from+New+York+to+Los+Angeles,+departing+on+November+9,+2024"}}}, {"Input": "Nachrichtenseite", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=news+website"}}}, {"Input": "Musikempfehlung", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=music+recommendation"}}}, {"Input": "Filmempfehlung", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=movie+recommendation"}}}, {"Input": "Buchempfehlung", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=book+recommendation"}}}, {"Input": "Tickets für die Universal Studios kaufen.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=buy+tickets+for+Universal+Studios"}}}, {"Input": "xxx Tickets", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=xxx+tickets"}}}, {"Input": "<PERSON><PERSON><PERSON> von <PERSON> nach Brooklyn", "Output": {"action": "OUTDOOR_NAVIGATE", "params": {"origin": "Queens", "destination": "Brooklyn", "travelmode": "driving"}}}, {"Input": "von hier aus nach Zhongguancun", "Output": {"action": "OUTDOOR_NAVIGATE", "params": {"origin": "-1", "destination": "Zhongguancun", "travelmode": "driving"}}}, {"Input": "Nehmen Sie den Bus zum MIT", "Output": {"action": "OUTDOOR_NAVIGATE", "params": {"origin": "-1", "destination": "MIT", "travelmode": "transit"}}}, {"Input": "Gibt es in der Nähe empfohlene Orte zum Verabreden?", "Output": {"action": "RECOMMEND_PLACES", "params": {"shop_name": "cafe", "source_location": "Central Park", "url": "https://www.google.com/maps/search/cafe+Central+Park"}}}, {"Input": "nahegelegene Nachhilfekurse", "Output": {"action": "RECOMMEND_PLACES", "params": {"shop_name": "tutoring+class", "source_location": "Times Square", "url": "https://www.google.com/maps/search/tutoring+class+Times+Square"}}}, {"Input": "<PERSON><PERSON> mir, die Restaurants in der Nähe der beliebtesten Touristenattraktionen in Hangzhou zu überprüfen.", "Output": {"action": "RECOMMEND_PLACES", "params": {"shop_name": "food", "source_location": "Westsee", "url": "https://www.google.com/maps/search/food+Hangzhou"}}}, {"Input": "nahegelegenes Mitnehmessen", "Output": {"action": "RECOMMEND_PLACES", "params": {"shop_name": "takeout", "source_location": "Hollywood", "url": "https://www.google.com/maps/search/takeout+Hollywood"}}}, {"Input": "nahegelegene U-Bahn-Stationen", "Output": {"action": "RECOMMEND_PLACES", "params": {"shop_name": "subway stations", "source_location": "-1", "url": "https://www.google.com/maps/search/subway+stations/@49.1231,161.1321321,13z"}}}, {"Input": "Bring mich zum Fitnessstudio", "Output": {"action": "NAVIGATE_REC_START", "params": {"destinations": ["<PERSON><PERSON>um", "Sportzentrum"], "guide_text": "<PERSON><PERSON>chten Si<PERSON> zum Fitnessraum oder zum Sportzentrum gehen?"}}}, {"Input": "Bring mich zur Pazifikzone", "Output": {"action": "NAVIGATE_REC_START", "params": {"destinations": ["Pazifikzone", "Pazifikzone A", "Ost-Pazifikzone"], "guide_text": "Es gibt mehrere verfügbare Standorte: Pazifikzone, Pazifikzone A und Ost-Pazifikzone. Welchen möchten Si<PERSON> besuchen?"}}}]