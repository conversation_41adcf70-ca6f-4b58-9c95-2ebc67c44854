[{"Input": "去充电", "Output": {"action": "GO_CHARGING"}}, {"Input": "查天气一下济南历城区的天气", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "历城区"}}}, {"Input": "生成一段五言绝句欢送一下 A", "Output": {"action": "GENERATE_MESSAGE", "params": {"goal": "生成一段五言绝句欢送一下 A"}}}, {"Input": "你帮我热烈欢迎一下张总", "Output": {"action": "GENERATE_MESSAGE", "params": {"goal": "生成一段欢迎张总的到来的文案"}}}, {"Input": "我的手机号后四位是一七八B", "Output": {"action": "SAY", "params": {"text": "哎呀！手机号码后4位需要数字哦，字母可能不太行，亲"}}}, {"Input": "下次见到我，叫我朱大哥，告诉我辛苦了”", "Output": {"action": "CONFIGURE_WELCOME_MESSAGE", "params": {"nick_name": "朱大哥", "welcome_message": "工作辛苦了"}}}, {"Input": "还有多久要过年呀？", "Output": {"action": "CALENDAR", "params": {"user_question": "还有多久要过年呀？"}}}, {"Input": "我去深圳要穿什么衣服？要带伞吗？", "Output": {"action": "WEATHER_GET", "params": {"city": "深圳", "area_level": "city"}}}, {"Input": "明天去重庆热不热", "Output": {"action": "WEATHER_GET", "params": {"city": "重庆", "area_level": "city"}}}, {"Input": "上海还有多久下雨？", "Output": {"action": "WEATHER_GET", "params": {"city": "上海", "area_level": "city"}}}, {"Input": "退出", "Output": {"action": "EXIT"}}, {"Input": "算了，不用了,取消", "Output": {"action": "CANCEL"}}, {"Input": "一六八九", "Output": {"action": "LAST_4_DIGITS_Input", "params": {"last_4_digits": [1, 6, 8, 9]}}}, {"Input": "mac m4的最新价格是多少？", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.baidu.com/s?wd=mac+m4+价格"}}}, {"Input": "帮我查一下苹果最新的股价", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.baidu.com/s?wd=苹果股价"}}}, {"Input": "看一下香港的酒店", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://hotels.ctrip.com/hotels/listPage?cityename=hongkong&city=58&checkin=2024/11/01&checkout=2024/11/04&optionId=58&optionType=City"}}}, {"Input": "公司里谁最能喝酒", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "公司里谁最能喝酒"}}}, {"Input": "查一下A市B区C乡的天气", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "B区"}}}, {"Input": "什么时候下雪呢", "Output": {"action": "WEATHER_GET", "params": {"city": "使用 `机器人基础信息`中的 <所在城市>", "area_level": "city"}}}, {"Input": "找一下刘明", "Output": {"action": "SEND_MESSAGE", "params": {"message_content": "有人在找你", "recipient_name": "刘明", "message_type": "normal"}}}, {"Input": "我是赵德柱", "Output": {"action": "REGISTER", "params": {"nickname": "赵德柱", "welcome_message": "赵德柱君，欢迎您的到来"}}}, {"Input": "发送微信消息", "Output": {"action": "SAY", "params": {"text": "亲，我还不会发送微信消息哦"}}}, {"Input": "停下、别走了", "Output": {"action": "NOT_MOVE"}}, {"Input": "老板的抖音号是什么", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "老板的抖音号是什么"}}}, {"Input": "附近有什么推荐的约会地点吗", "Output": {"action": "RECOMMEND", "params": {"source_location": "{当前机器人的所在地点}", "query": "<source_location>附近的约会地点"}}}, {"Input": "向后转", "Output": {"action": "TURN_DIRECTION", "params": {"direction": "right", "angle": 180}}}, {"Input": "走出六亲不认的步伐", "Output": {"action": "START_DANCE"}}, {"Input": "轻松、愉快的心情", "Output": {"action": "START_DANCE"}}, {"Input": "独特步伐", "Output": {"action": "START_DANCE"}}, {"Input": "我是谁", "Output": {"action": "FACE_RECOGNITION"}}, {"Input": "那你看一下那谁来了吗？", "Output": {"action": "SAY", "params": {"text": "对不起，我还没有这个能力哟。"}}}, {"Input": "向后走200米", "Output": {"action": "SAY", "params": {"text": "哎呀，我只能向后走1米哟，多了，老板会骂我的哟"}}}, {"Input": "从故宫公交到颐和园", "source_location": "故宫博物院", "Output": {"action": "OUTDOOR_NAVIGATE_START", "params": {"origin": "故宫博物院", "destination": "颐和园", "mode": "transit"}}}, {"Input": "听不见啊", "Output": {"action": "SET_VOLUME", "params": {"volume_level": "70"}}}, {"Input": "从这儿去中关村", "Output": {"action": "OUTDOOR_NAVIGATE_START", "params": {"origin": "<当前机器人的所在地点>", "destination": "中关村", "mode": "driving"}}}, {"Input": "我的名字叫张翰", "Output": {"action": "REGISTER", "params": {"nick_name": "张翰", "welcome_message": "张翰，欢迎您的到来"}}}, {"Input": "安静、闭嘴", "Output": {"action": "SET_VOLUME", "params": {"volume_level": "0"}}}, {"Input": "帮我看一下杭州游客最多的地方附近的美食", "Output": {"action": "RECOMMEND", "params": {"source_location": "西湖", "query": "西湖附近的美食"}}}, {"Input": "附近的外卖", "Output": {"action": "RECOMMEND", "params": {"source_location": "{当前机器人的所在地点}", "query": "<source_location>附近的餐厅"}}}, {"Input": "新闻网站", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.toutiao.com/"}}}, {"Input": "这里是接待点", "Output": {"action": "SET_LOCATION", "params": {"location": "接待点"}}}, {"Input": "音乐推荐", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://music.douban.com/"}}}, {"Input": "电影推荐", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://movie.douban.com/"}}}, {"Input": "读书推荐", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://book.douban.com/"}}}, {"Input": "景点门票", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.baidu.com/s?wd=景点门票"}}}, {"Input": "读一首古诗", "Output": {"action": "SAY", "params": {"text": "床前明月光，疑是地上霜。举头望明月，低头思故乡。"}}}, {"Input": "你认识我吗？", "Output": {"action": "FACE_RECOGNITION"}}, {"Input": "你认识某某某吗？", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "你认识某某某吗？"}}}, {"Input": "你知道张大千吗？", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "你知道张大千吗？"}}}, {"Input": "我叫某某某，下次见到叫我小君君", "Output": {"action": "CONFIGURE_WELCOME_MESSAGE", "params": {"nick_name": "某某某", "welcome_message": "hi，小君君"}}}, {"Input": "<Robot> SAY '您觉得我们的送餐机器人怎么样?' <User> SAY '还行，就是有点慢'", "Output": {"action": "RECORD_FEEDBACK", "params": {"production": "送餐机器人", "function": "送餐功能", "feedback": "还行，就是有点慢"}}}, {"Input": "我在前台，要找你们部门的张大大", "Output": {"action": "SEND_MESSAGE", "params": {"message_content": "Hi，有人在前台找你，要不要去接待一下下呀", "recipient_name": "张大大", "message_type": "normal"}}}, {"Input": "退出推销模式", "Output": {"action": "EXIT_PROMOTE_APP"}}, {"Input": "详细介绍一下豹小秘", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "详细介绍一下豹小秘"}}}, {"Input": "我想更深入的了解这个避障功能的实现", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "避障功能是什么实现的"}}}, {"Input": "查一下国外某地的天气", "Output": {"action": "SAY", "params": {"text": "亲，我只能查询中国境内的天气哦"}}}, {"Input": "这几个产品谁最贵", "Output": {"action": "SHOW_CONTACT_INFORMATION"}}, {"Input": "价格", "Output": {"action": "SHOW_CONTACT_INFORMATION"}}, {"Input": "想加深了解、合作", "Output": {"action": "SHOW_CONTACT_INFORMATION"}}, {"Input": "用户长时间无交互", "Output": {"action": "SALES_PITCH"}}, {"Input": "我想看看豹小秘2这个产品", "Output": {"action": "PRODUCT_DETAIL", "params": {"production": "豹小秘2"}}}, {"Input": "介绍一下xxx功能", "Output": {"action": "PRODUCT_FUNCTION", "params": {"production_function": "xxx功能"}}}, {"Input": "看一下xx功能的视频", "Output": {"action": "PRODUCT_FUNCTION", "params": {"production_function": "xx功能"}}}, {"Input": "这个xxx功能是怎么实现的", "Output": {"action": "SALES_PITCH"}}, {"Input": "我是做某某行业的，有什么推荐的产品吗", "Output": {"action": "SALES_PITCH"}}, {"Input": "谢谢", "Output": {"action": "SALES_PITCH"}}, {"Input": "再见", "Output": {"action": "SALES_PITCH"}}, {"Input": "放一下这个视频", "Output": {"CONDITION": "The robot screen status is in video mode", "action": "MULTIMEDIA_PLAY"}}, {"Input": "退出", "Output": {"action": "HOME"}}, {"Input": "播放视频", "Output": {"CONDITION": "Robot screen state is text display state", "action": "SALES_PITCH"}}, {"Input": "暂停视频", "Output": {"CONDITION": "Robot screen state is video mode", "action": "COMMON_PAUSE"}}, {"Input": "有什么可以体验的吗？", "Output": {"action": "SALES_PITCH"}}, {"Input": "这个功能怎么实现？", "Output": {"action": "SALES_PITCH"}}, {"Input": "没兴趣、走了", "Output": {"action": "SALES_PITCH"}}, {"Input": "带我去运动", "Output": {"action": "NAVIGATE_REC_START", "params": {"destinations": ["运动室", "运动部"], "guide_text": "请问你是想去运动点，还是运动部呢？"}}}, {"Input": "带我去东极州", "Output": {"action": "NAVIGATE_REC_START", "params": {"destinations": ["东极州", "XX东极州", "东极州A"], "guide_text": "这里有多个位置可供选择：东极州、XX东极州、东极州A，你想去哪个？"}}}, {"Input": "推荐旅游路线", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "推荐旅游路线"}}}, {"Input": "继续走", "Output": {"action": "MULTIMEDIA_PLAY"}}, {"Input": "别走了", "Output": {"action": "COMMON_PAUSE"}}, {"Input": "磕头", "Output": {"action": "HEAD_NOD"}}]