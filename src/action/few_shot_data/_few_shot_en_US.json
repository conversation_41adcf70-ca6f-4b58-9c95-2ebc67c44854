[{"Input": "Charge the battery.", "Output": {"action": "GO_CHARGING"}}, {"Input": "Please check the weather in Manhattan, New York.", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "Manhattan"}}}, {"Input": "Compose a five-character quatrain to bid farewell to General <PERSON>.", "Output": {"action": "GENERATE_MESSAGE", "params": {"instruction": "Write a quatrain to bid farewell to General <PERSON> in five-character lines."}}}, {"Input": "The last four digits of my phone number are 178B.", "Output": {"action": "SAY", "params": {"text": "Oops! The last four digits of the phone number should be numbers, not letters."}}}, {"Input": "How long until Chinese New Year?", "Output": {"action": "CALENDAR"}}, {"Input": "What clothes should I wear when I go to Shenzhen? Do I need to bring an umbrella?", "Output": {"action": "WEATHER_GET", "params": {"city": "Shenzhen", "area_level": "city"}}}, {"Input": "Is it going to be hot in Chongqing tomorrow?", "Output": {"action": "WEATHER_GET", "params": {"city": "Chongqing", "area_level": "city"}}}, {"Input": "How much longer will it rain in Shanghai?", "Output": {"action": "WEATHER_GET", "params": {"city": "Shanghai", "area_level": "city"}}}, {"Input": "Exit", "Output": {"action": "EXIT"}}, {"Input": "Forget it, no need, cancel.", "Output": {"action": "CANCEL"}}, {"Input": "Assuming the current speed is 0.3, walk faster.", "Output": {"action": "ADJUST_SPEED", "params": {"adjusted_speed": "0.8"}}}, {"Input": "1689", "Output": {"action": "LAST_4_DIGITS_Input", "params": {"last_4_digits": [1, 6, 8, 9]}}}, {"Input": "Who in the company can hold their liquor the best?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Who in the company can drink the most?"}}}, {"Input": "Please check the weather in Town C, District B, City A.", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "Zone B"}}}, {"Input": "When will it snow?", "Output": {"action": "WEATHER_GET", "params": {"city": "-1", "area_level": "city"}}}, {"Input": "When will it rain?", "Output": {"action": "WEATHER_GET", "params": {"city": "-1", "area_level": "city"}}}, {"Input": "Check the weather.", "Output": {"action": "WEATHER_GET_REALTIME", "params": {"city": "-1"}}}, {"Input": "When does it rain in foreign countries?", "Output": {"action": "SAY", "params": {"text": "Sorry, I can only check the weather within China."}}}, {"Input": "Send WeChat message.", "Output": {"action": "SAY", "params": {"text": "Sorry, I don't know how to send WeChat messages yet."}}}, {"Input": "What is the boss's TikTok account?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "What is the boss's <PERSON><PERSON><PERSON> account?"}}}, {"Input": "Turn right in a circle.", "Output": {"action": "TURN_DIRECTION", "params": {"direction": "right", "angle": "360"}}}, {"Input": "Rotate backwards.", "Output": {"action": "TURN_DIRECTION", "params": {"direction": "right", "angle": 180}}}, {"Input": "Step out from the ties of kinship without recognition.", "Output": {"action": "START_DANCE"}}, {"Input": "A light-hearted and joyful mood.", "Output": {"action": "START_DANCE"}}, {"Input": "Unique gait", "Output": {"action": "START_DANCE"}}, {"Input": "Go back 200 meters.", "Output": {"action": "SAY", "params": {"text": "Oh, I can only walk back 1 meter, if I go further, the boss will scold me."}}}, {"Input": "Can't hear.", "Output": {"action": "SET_VOLUME", "params": {"volume_level": "70"}}}, {"Input": "Quiet, shut up.", "Output": {"action": "SET_VOLUME", "params": {"volume_level": "0"}}}, {"Input": "Here is the reception desk.", "Output": {"action": "SET_LOCATION", "params": {"location": "Reception desk."}}}, {"Input": "Read a classical poem.", "Output": {"action": "SAY", "params": {"text": "The bright moonlight before the bed appears like frost on the ground. Lifting my head, I gaze at the bright moon; lowering it, I miss my hometown."}}}, {"Input": "Do you know so-and-so?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Do you know so and so?"}}}, {"Input": "Do you know <PERSON>?", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Do you know <PERSON>?"}}}, {"Input": "<Robot> SAY 'Are you sure you want to xxx?' <User> SAY 'Confirmed'", "Output": {"action": "CONFIRM"}}, {"Input": "Please provide a detailed introduction of <PERSON><PERSON><PERSON><PERSON>.", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "Please provide a detailed introduction to <PERSON><PERSON>'s <PERSON>."}}}, {"Input": "I would like to gain a deeper understanding of the implementation of this obstacle avoidance feature.", "Output": {"action": "KNOWLEDGE_QA", "params": {"question": "How is the obstacle avoidance function achieved?"}}}, {"Input": "Play this video.", "Output": {"CONDITION": "The robot screen status is in video mode", "action": "MULTIMEDIA_PLAY"}}, {"Input": "Exit", "Output": {"action": "HOME"}}, {"Input": "Pause the video.", "Output": {"CONDITION": "Robot screen state is video mode", "action": "COMMON_PAUSE"}}, {"Input": "What is the latest price of the iPhone 16?", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=What+is+the+latest+price+of+the+iPhone+16"}}}, {"Input": "Help me check the latest stock price of Apple.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Help+me+check+the+latest+stock+price+of+Apple."}}}, {"Input": "Flights from Los Angeles to New York tomorrow.", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Flights+from+Los+Angeles+to+New+York+tomorrow."}}}, {"Input": "Look at hotels in New York", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Look+at+hotels+in+New+York"}}}, {"Input": "Train tickets from New York to Los Angeles departing on November 9  2024", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=Train+tickets+from+New+York+to+Los+Angeles,+departing+on+November+9,+2024"}}}, {"Input": "news website", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=news+website"}}}, {"Input": "music recommendation", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=music+recommendation"}}}, {"Input": "movie recommendation", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=movie+recommendation"}}}, {"Input": "book recommendation", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=book+recommendation"}}}, {"Input": "buy tickets for Universal Studios", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=buy+tickets+for+Universal+Studios"}}}, {"Input": "xxx tickets", "Output": {"action": "OPEN_WEB_URL", "params": {"url": "https://www.google.com/search?q=xxx+tickets"}}}, {"Input": "drive from Queens to Brooklyn", "Output": {"action": "OUTDOOR_NAVIGATE", "params": {"origin": "Queens", "destination": "Brooklyn", "travelmode": "driving"}}}, {"Input": "go from here to Zhongguancun", "Output": {"action": "OUTDOOR_NAVIGATE", "params": {"origin": "-1", "destination": "Zhongguancun", "travelmode": "driving"}}}, {"Input": "Take the bus to MIT", "Output": {"action": "OUTDOOR_NAVIGATE", "params": {"origin": "-1", "destination": "MIT", "travelmode": "transit"}}}, {"Input": "Are there any recommended dating spots in New York", "Output": {"action": "RECOMMEND_PLACES", "params": {"target": "cafe", "source_location": "New York"}}}, {"Input": "nearby tutoring classes", "Output": {"action": "RECOMMEND_PLACES", "params": {"target": "tutoring+class", "ref_location": "-1"}}}, {"Input": "Help me check the food near the most popular tourist spots in Hangzhou", "Output": {"action": "RECOMMEND_PLACES", "params": {"target": "food", "ref_location": "Hangzhou"}}}, {"Input": "nearby takeout", "Output": {"action": "RECOMMEND_PLACES", "params": {"target": "takeout", "ref_location": "-1"}}}, {"Input": "subway stations around queens", "Output": {"action": "RECOMMEND_PLACES", "params": {"target": "subway stations", "ref_location": "queens"}}}, {"Input": "Take me to the gym", "Output": {"action": "NAVIGATE_REC_START", "params": {"destinations": ["The gym Room", "The gym Center"], "guide_text": "Would you like to go to the gym Room or the gym Center?"}}}, {"Input": "Take me to Pacific Zone", "Output": {"action": "NAVIGATE_REC_START", "params": {"destinations": ["Pacific Zone", "Pacific Zone A", "East Pacific Zone"], "guide_text": "There are several locations available: Pacific Zone, Pacific Zone A, and East Pacific Zone. Which one would you like to visit?"}}}]