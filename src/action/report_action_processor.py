from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Type

from loguru import logger
from src.action.actions import ActionLib
from src.action.model import Action
from src.utils.feishu_alarm import send_feishu_alarm


class ReportActionProcessor:
    """
    处理客户端上报的动作的处理器
    """

    def process(
        self, action_infos: List[dict], action_type: str
    ) -> Tuple[List[Action], List[Action]]:
        """
        处理客户端上报的actions

        Args:
            action_infos: 客户端上报的action信息列表

        Returns:
            Tuple[List[Action], List[Action]]: (processed_actions, report_actions)
        """
        logger.debug(f"ReportActionProcessor process: {action_infos}")
        processed_actions = []
        report_actions = []  # 客户端定义的actions

        for action_info in action_infos:
            namespace = action_info.get("namespace")

            if (
                namespace == "orion.agent.action"
            ):  # 内置OPK的Action已经被注册到ActionLib。仅需要根据fullname找到action
                fullname = action_info.get("namespace") + "." + action_info.get("name")
                try:
                    action = ActionLib().get_one_action(full_name=fullname)
                    if action:
                        processed_actions.append(action)
                except Exception as e:
                    logger.error(f"Error when register action: {e} {action_info}")
                    send_feishu_alarm(f"Error when register action: {e} {action_info}")
            else:
                # 二开APP协议
                try:
                    if action_type == "page":
                        action_info["level"] = "page"
                    else:
                        action_info["level"] = "app"
                    if parameters := action_info.get("parameters"):
                        for parameter in parameters:
                            if parameter.get("type") == "array":
                                parameter["type"] = "list"
                            elif parameter.get("type") == "string_array":
                                parameter["type"] = "String array"
                            elif parameter.get("type") == "number_array":
                                parameter["type"] = "Integer array"
                            elif parameter.get("type") == "boolean":
                                parameter["type"] = "bool"
                    action = Action(**action_info)
                    processed_actions.append(action)
                    report_actions.append(action)
                except Exception as e:
                    logger.error(f"Error when register action: {e} {action_info}")
                    send_feishu_alarm(f"Error when register action: {e} {action_info}")

        return processed_actions, report_actions


class ActionRegistrationStrategy(ABC):
    """
    动作注册策略的抽象基类
    """

    @abstractmethod
    def register(
        self, processed_actions: List[Action], report_actions: List[Action]
    ) -> None:
        """
        注册动作

        Args:
            processed_actions: 处理后的动作列表
            report_actions: 需要注册的动作列表
        """
        pass


class AppActionRegistrationStrategy(ActionRegistrationStrategy):
    """
    App动作注册策略
    """

    def register(
        self, processed_actions: List[Action], report_actions: List[Action]
    ) -> None:
        """
        注册App动作，report的action， 需要先清除之前的actions再注册新的

        """
        ActionLib().update_app_actions(
            report_actions
        )  # unregister以前的app_actions, register新的app_actions; app_actions 如果为空，是在清除app_actions


class PageActionRegistrationStrategy(ActionRegistrationStrategy):
    """
    Page动作注册策略
    """

    def register(
        self, processed_actions: List[Action], report_actions: List[Action]
    ) -> None:
        """
        更新Page action
        """
        ActionLib().update_page_actions(report_actions)


class BlockActionRegistrationStrategy(ActionRegistrationStrategy):
    """
    Block动作注册策略
    """

    def register(
        self, processed_actions: List[Action], report_actions: List[Action]
    ) -> None:
        """
        Block动作不需要更新ActionLib，后续action选择时直接过滤
        """
        pass


class RegistrationStrategyFactory:
    """
    注册策略工厂
    """

    _strategies: Dict[str, Type[ActionRegistrationStrategy]] = {
        "app": AppActionRegistrationStrategy,
        "page": PageActionRegistrationStrategy,
        "block": BlockActionRegistrationStrategy,
    }

    @classmethod
    def get_strategy(cls, action_type: str) -> ActionRegistrationStrategy:
        """
        获取注册策略

        Args:
            action_type: 动作类型

        Returns:
            相应的注册策略
        """
        strategy_class = cls._strategies.get(action_type)
        if not strategy_class:
            raise ValueError(f"Unknown action type: {action_type}")
        return strategy_class()


class ReportActionManager:
    """
    报告动作管理器，负责处理和注册客户端上报的动作
    """

    def __init__(self):
        self.processor = ReportActionProcessor()

    def handle_action_update(
        self, action_infos: List[dict], action_type: str
    ) -> List[Action]:
        """
        处理动作更新

        Args:
            action_infos: 客户端上报的动作信息列表
            action_type: 动作类型，"app"、"page"或"block"

        Returns:
            处理后的动作列表
        """
        logger.debug(
            f"ReportActionManager handle_action_update: {action_infos} {action_type}"
        )
        processed_actions, report_actions = self.processor.process(
            action_infos, action_type
        )

        # 使用工厂获取相应的注册策略
        strategy = RegistrationStrategyFactory.get_strategy(action_type)
        strategy.register(processed_actions, report_actions)

        return processed_actions


def handle_report_action_update(
    action_infos: List[dict], action_type: str
) -> List[Action]:
    """
    处理actions更新的完整流程（兼容接口）

    Args:
        action_infos: 客户端上报的action信息列表
        action_type: actions类型，"app"、"page"或"block"

    Returns:
        处理后的actions列表
    """
    manager = ReportActionManager()
    return manager.handle_action_update(action_infos, action_type)
