import re
import time
import urllib.parse

import aiohttp
from loguru import logger

from src.action.function_register import action_function_register
from src.action.resource import get_light_app_info, load_robot_support_map_points
from src.common.enums import LightAppType
from src.settings import agent_setting
from src.utils.feishu_alarm import send_feishu_alarm
from src.common.constant import Area


@action_function_register
async def build_recommend_url(action_parameters: dict, agent_parameters):
    place = action_parameters["place"]
    near_by = action_parameters.get("near_by")
    if not near_by or near_by == "-1":
        near_by = agent_parameters.robot.geo_location.describe

    query_params = {
        "wd": f"{near_by}附近的{place}",
        "newmap": "1",
        "ie": "utf-8",
        "s": "s",
    }
    url = urllib.parse.urljoin(
        "https://map.baidu.com/",
        "?" + urllib.parse.urlencode(query_params),
    )

    return {
        "place": place,
        "near_by": near_by,
        "url": url,
    }


@action_function_register
async def convert_map_url(action_parameters: dict, agent_parameters) -> dict:
    base_url = agent_setting.map_tool_base_url
    origin = action_parameters.get("origin")
    if not origin or origin == "-1":
        origin = agent_parameters.robot.geo_location.describe
    destination = action_parameters.get("destination", "")
    mode = action_parameters.get("mode", "transit")
    agent_location = agent_parameters.robot.geo_location

    query_params = {
        "start": origin,
        "end": destination,
        "mode": mode,
        "lng": agent_location.longitude,
        "lat": agent_location.latitude,
    }
    url = urllib.parse.urljoin(
        base_url, "route-planning?" + urllib.parse.urlencode(query_params)
    )
    return {
        "url": url,
    }


@action_function_register
async def convert_recommend_url(action_parameters: dict, agent_parameters) -> dict:
    base_url = agent_setting.map_tool_base_url
    agent_location = agent_parameters.robot.geo_location
    place = (
        agent_location.describe
        if action_parameters["place"] == "-1"
        else action_parameters["place"]
    )
    target = action_parameters["target"]
    # 判断 place 是否包含“附近”或“附近的”
    if "附近" in place:
        keyword = f"{place}{target}"
    else:
        keyword = f"{place}附近的{target}"
    query_params = {
        "keyword": keyword,
        "lng": agent_location.longitude,
        "lat": agent_location.latitude,
    }
    url = urllib.parse.urljoin(base_url, "?" + urllib.parse.urlencode(query_params))
    logger.info("recommend_url:{}".format(url))
    return {
        "place": place,
        "target": target,
        "url": url,
    }


@action_function_register
async def build_outdoor_navigate_url(action_parameters: dict, agent_parameters):
    origin = action_parameters.get("origin")
    if not origin or origin == "-1":
        origin = agent_parameters.robot.geo_location.describe

    destination = action_parameters["destination"]
    mode = action_parameters["mode"]
    region = action_parameters["region"]

    url = f"http://api.map.baidu.com/direction?origin={origin}&region={region}&destination={destination}&mode={mode}&output=html&src=webapp.baidu.openAPIdemo"

    return {
        "origin": origin,
        "destination": destination,
        "mode": mode,
        "url": url,
    }


@action_function_register
async def build_flight_ticket_query_url(action_parameters: dict, agent_parameters):
    departure_city_code = action_parameters["departure_city_code"]
    arrival_city_code = action_parameters["arrival_city_code"]
    departure_date = action_parameters["departure_date"]

    url = f"https://flights.ctrip.com/online/list/oneway-{departure_city_code}-{arrival_city_code}?depdate={departure_date}"

    return {
        "url": url,
        "departure_city_code": departure_city_code,
        "arrival_city_code": arrival_city_code,
        "departure_date": departure_date,
    }


@action_function_register
async def build_train_ticket_query_url(action_parameters: dict, agent_parameters):
    departure_city = (
        action_parameters.get("departure_city")
        or agent_parameters.robot.geo_location.city
    )
    arrival_city = action_parameters["arrival_city"]
    departure_date = action_parameters["departure_date"]

    url = f"https://trains.ctrip.com/webapp/train/list?ticketType=0&dStation={departure_city}&aStation={arrival_city}&dDate={departure_date}"

    return {
        "url": url,
        "departure_city": departure_city,
        "arrival_city": arrival_city,
        "departure_date": departure_date,
    }


@action_function_register
async def overseas_map_dir_url(action_parameters: dict, agent_parameters) -> dict:
    from src.settings import agent_setting

    url = agent_setting.map_dir_url
    query_params = {"api": "1"}
    if str(action_parameters["origin"]) == "-1":
        action_parameters["origin"] = agent_parameters.robot.latitude_longitude
    for key, value in action_parameters.items():
        query_params[key] = value
    url = urllib.parse.urljoin(url, "?" + urllib.parse.urlencode(query_params))
    res = {
        "url": url,
    }
    res.update(action_parameters)
    return res


@action_function_register
async def overseas_map_search_url(action_parameters: dict, agent_parameters) -> dict:
    from src.settings import agent_setting

    def is_valid_latitude(latitude):
        return -90 <= float(latitude) <= 90

    def is_valid_longitude(longitude):
        return -180 <= float(longitude) <= 180

    def is_valid_coordinate(coordinate):
        try:
            lat, lon = map(float, coordinate.split(","))
            return is_valid_latitude(lat) and is_valid_longitude(lon)
        except ValueError:
            return False

    url = agent_setting.map_search_url
    url += action_parameters["target"].replace(" ", "+")
    ref_location = str(action_parameters["ref_location"])
    if ref_location == "-1" or (
        agent_parameters.robot.indoor_location_history
        and ref_location.replace(" ", "").lower()
        == agent_parameters.robot.indoor_location_history[-1].replace(" ", "").lower()
    ):
        # if is_valid_coordinate(action_parameters["ref_location"]):
        latitude_longitude = agent_parameters.robot.latitude_longitude
        url = url + "/" + f"@{latitude_longitude},13z"
    else:
        url += "+" + action_parameters["ref_location"].replace(" ", "+")

    return {
        "url": url,
        "ref_location": action_parameters.get("ref_location", ""),
        "target": action_parameters.get("target", ""),
    }


@action_function_register
async def find_light_app_answer(action_parameters: dict, agent_parameters) -> dict:
    light_app_dict = await get_light_app_info(agent_parameters)
    question = action_parameters["predefined_question"].lower()
    web_info = light_app_dict[question]
    prefix = ""
    if web_info["type"] == LightAppType.APP.value:
        prefix = "package_name://"
    app_url = web_info["app_url"]
    if prefix:
        app_url = prefix + app_url
    return {
        "url": app_url,
    }


@action_function_register
async def convert_navigation_points(action_result: dict, agent_parameters) -> dict:
    """
    处理多点导航的点位信息
    如果提供了有效的导航点位，则直接使用
    如果没有提供有效点位，则返回所有可用的导航点位列表
    """
    start = time.time()
    destinations = action_result.get("destinations", [])
    # destinations = json.loads(destinations)

    # 调用 load_robot_support_map_points 获取所有可用点位
    points = await load_robot_support_map_points(agent_parameters)

    # 从 points 中移除已经在 destinations 中的元素
    extend_destination = [p for p in points if p not in destinations]

    action_result["destinations"] = destinations
    action_result["extend_destinations"] = extend_destination

    logger.debug(f"Navigation points conversion elapsed {time.time() - start}")
    return action_result


@action_function_register
async def convert_tour_id(action_parameters: dict, agent_parameters) -> dict:
    if not action_parameters.get("points", []):
        return action_parameters

    device_id = agent_parameters.robot.device_id
    enterprise_id = agent_parameters.robot.enterprise_id
    start = time.time()
    async with aiohttp.ClientSession() as session:
        request_body = {
            "get_action": "guide_whole_point",
            "ov_corp_id": enterprise_id,
            "robot_sn": device_id,
        }
        url = urllib.parse.urljoin(
            agent_setting.robot_openapi_host, agent_setting.convert_tour_id_path
        )
        async with session.post(
            url=url,
            json=request_body,
            headers={
                "orionstar-api-key": agent_setting.robot_openapi_key,
                "Content-Type": "application/json",
            },
        ) as response:
            if response.status != 200:
                logger.error(
                    f"[load_guide_whole_default_tour_id] device_id:{device_id},enterprise_id:{enterprise_id},url:{url}. get map data failed."
                )
                await send_feishu_alarm(
                    f"convert_tour_id调用异常，地址：{url}, status: {response.status}"
                )
                return {
                    "tour_id": "",
                    "points": action_parameters.get("points", []),
                }

            data = await response.json()
            logger.debug(
                f"[load_guide_whole_default_tour_id] device_id:{device_id},enterprise_id:{enterprise_id}. get map data: {data} elapsed {time.time() - start}"
            )

            obj_list = data["data"].get("obj_list", [])
            if not obj_list:
                return {
                    "tour_id": "",
                    "points": action_parameters.get("points", []),
                }
            logger.debug(
                f"Load guide whole default tour id: {obj_list[0]['obj']['guide_tour_id']}"
            )
            return {
                "tour_id": obj_list[0]["obj"]["guide_tour_id"],
                "points": action_parameters.get("points", []),
            }


@action_function_register
async def convert_element_id(action_parameters: dict, agent_parameters) -> dict:
    """
    Get clickable elements description
    :param agent_parameters:
    :return:
    """
    if not action_parameters.get("element_tag", ""):
        return action_parameters

    clickable_elements = agent_parameters.robot.interface_state.clickable_elements
    if not clickable_elements:
        return {
            "element_tag": action_parameters.get("element_tag", ""),
            "element_id": "",
        }

    if clickable_elements:
        """<a id="1">酒店</a>
        <a id="2">机票</a>
        <a id="3">火车票</a>
        """
        # 使用re.DOTALL标志使.能匹配换行符，并在提取内容时去除首尾空白
        element_tag_pattern = re.compile(r'<a id="(\d+)">(.*?)</a>', re.DOTALL)
        # regex match id number
        for element in clickable_elements:
            id_match = re.search(element_tag_pattern, element)
            if id_match:
                element_id = id_match.group(1)
                # 提取内容并去除首尾空白字符（包括空格、换行符、制表符等）
                element_tag = id_match.group(2).strip()
                if element_tag == action_parameters.get("element_tag", ""):
                    return {
                        "element_tag": element_tag,
                        "element_id": element_id,
                    }
            else:
                logger.warning(f"Failed to extract id from element: {element}")

        return {
            "element_tag": action_parameters.get("element_tag", ""),
            "element_id": "",
        }


@action_function_register
async def query_weather_post_processing(action_result: dict, agent_parameters) -> dict:
    real_city = action_result.get("valid_city_name", "") or action_result.get(
        "city", ""
    )  # 1.0.4: city
    if not real_city or str(real_city) == "-1":
        if agent_parameters.robot.geo_location.city:
            return {
                "city": agent_parameters.robot.geo_location.city,
                "area_level": "city",
            }
        else:
            if agent_setting.region_version == Area.overseas:  # 海外地区使用经纬度
                return {
                    "city": f"{agent_parameters.robot.geo_location.latitude},{agent_parameters.robot.geo_location.longitude}",
                    "area_level": "city",
                }
            else:  # 国内或其他情况置空
                return {
                    "city": "",
                    "area_level": "city",
                }
    else:
        return {
            "city": real_city,
            "area_level": action_result.get("area_level", "city"),
        }


@action_function_register
async def register_action_post_processing(action_result: dict, agent_parameters):
    welcome_message = action_result.get("welcome_message", "") or action_result.get(
        "one_sentence", ""
    )
    nick_name = action_result.get("nick_name", "") or action_result.get(
        "valid_name", ""
    )
    return {
        "nick_name": nick_name,
        "welcome_message": welcome_message,
    }


@action_function_register
async def convert_search_word_url(action_parameters: dict, agent_parameters):
    search_word = action_parameters["search_word"]
    # 对搜索词进行URL安全转码
    encoded_word = urllib.parse.quote(search_word, safe="")
    if agent_setting.region_version == Area.overseas:
        search_engine = "https://www.google.com/search?q="
    else:
        search_engine = "https://www.baidu.com/s?wd="
    # 拼接完整URL
    url = f"{search_engine}{encoded_word}"
    return {"url": url}


@action_function_register
async def set_volume_post_processing(action_parameters: dict, agent_parameters):
    try:
        # 将float转成整型并向下取整为10的倍数，比如，15.0 -> 10
        original_volume_level = action_parameters["volume_level"]
        volume_level = int(max(0, min(original_volume_level, 100)) / 10) * 10
        action_parameters["volume_level"] = volume_level
        logger.info(
            f"set_volume_post_processing: original_volume_level: {original_volume_level}, volume_level: {volume_level}"
        )
    except Exception as e:
        logger.error(
            f"set_volume_post_processing error: {e}, action_parameters: {action_parameters}"
        )
    return action_parameters


@action_function_register
async def clear_cache_for_key(action_parameters: dict, agent_parameters):
    from src.action.resource import get_promote_project_origin_product_info
    from src.action.resource import get_promote_settings_info

    func = get_promote_project_origin_product_info
    if hasattr(func, "cache_delete"):
        await func.cache_delete(agent_parameters.robot)
    else:
        pass
    func = get_promote_settings_info
    if hasattr(func, "cache_delete"):
        await func.cache_delete(agent_parameters.robot)
    else:
        pass
    return {}


if __name__ == "__main__":
    import asyncio

    print(
        asyncio.run(
            build_outdoor_navigate_url(
                {
                    "origin": "万东科技文化创意产业园",
                    "destination": "中关村",
                    "mode": "transit",
                    "region": "北京",
                },
                {},
            )
        )
    )
