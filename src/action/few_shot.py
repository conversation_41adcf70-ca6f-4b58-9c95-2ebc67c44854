import json
from pathlib import Path

from loguru import logger

from src.common.constant import LanguageEnum


def load_all_few_shot(language: str = LanguageEnum.zh) -> list[dict[str, str]]:
    """
    根据语言加载对应的few-shot数据
    Args:
        language: 语言代码，支持 "zh"(中文) 和 "en"(英文)
    Returns:
        list[dict[str, str]]: few-shot数据列表，如果找不到对应语言的数据则返回空列表
    """
    file_name = f"_few_shot_{language}.json"
    
    # 从 LanguageEnum 获取所有支持的语言
    supported_languages = [getattr(LanguageEnum, attr) for attr in dir(LanguageEnum) 
                         if not attr.startswith('_')]
    
    if language not in supported_languages:
        logger.warning(f"Language {language} not supported. Available languages: {', '.join(supported_languages)}")
        return []
        
    path = Path(__file__).parent / "few_shot_data"/ file_name
    
    if not path.exists():
        logger.warning(f"Few shot data file not found: {path}")
        return []
    
    with open(path, "r", encoding="utf-8") as f:
        few_shot = json.load(f)
        logger.info(f"Load {language} few shot data from {path}, count: {len(few_shot)}")
        return few_shot
