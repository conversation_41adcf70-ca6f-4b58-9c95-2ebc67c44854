import asyncio
from collections.abc import Callable
from typing import Dict, List, Optional
from urllib.parse import urljoin

import aiohttp
from livekit.agents.utils import http_context
from loguru import logger

from src.action.model import (
    Action,
    Package_Main_Name,
    Package_Map_Name,
    StudioBlockActionResult,
)
from src.action.post_processing import (
    build_flight_ticket_query_url,
    build_train_ticket_query_url,
    convert_element_id,
    convert_map_url,
    convert_navigation_points,
    convert_recommend_url,
    overseas_map_dir_url,
    overseas_map_search_url,
    query_weather_post_processing,
    register_action_post_processing,
    set_volume_post_processing,
    convert_search_word_url,
)
from src.action.resource import (
    load_case_name_v2,
    load_clickable_elements_value,
    load_competitor_production_name,
    load_light_app_enum,
    load_product_functions_v2,
    load_product_parameters,
    load_product_list_v2,
    load_robot_support_map_points,
    load_userinfo_collect,
)
from src.action.server_function import (
    answer_question_from_vision,
    calendar,
    case_introduction_v2,
    clarify,
    competitors_answer,
    convert_specific_route,
    convert_turns_to_degree,
    generate_message,
    generate_route_introduction,
    generate_route_recommendation,
    get_realtime_weather,
    home_v3,
    knowledge_qa,
    production_detail_v2,
    production_function_v2,
    production_parameters,
    query_weather,
    collect_user_info_and_recommend,
    sales_pitch_v3,
    say,
    send_message,
    show_contact_information,
    stream_say,
    weather,
    call_llm,
    execute_clear_cache_for_key,
)
from src.common.agent_config import (
    AGENT_CONFIG,
    LAUNCHER_AGENT_ID,
    AgentConfig,
    Launcher_App_Id,
    Opk_Calendar_App_Id,
    Opk_Guide_App_Id,
    Opk_Promote_App_Id,
    Opk_Receive_App_Id,
    Opk_Register_App_Id,
    Opk_Small_App_Id,
    Opk_TakePicture_App_Id,
    Opk_Weather_App_Id,
    Opk_Welcome_Message_App_Id,
    OpkConfig,
    OverSea_Opk_Guide_App_Id,
    OverSea_Opk_Launcher_App_Id,
    OverSea_Opk_Small_App_Id,
    OverSea_Opk_Weather_App_Id,
)
from src.common.constant import Area, CLARIFY_ACTION_NAME, KNOWLEDGE_QA_ACTION_NAME
from src.common.action_display_names import ACTION_DISPLAY_NAMES
from src.settings import agent_setting
from src.utils.async_utils import is_multilingual
from src.utils.double_cache import ttl_cache
from src.utils.feishu_alarm import send_feishu_alarm

SUPPORTED_PACKAGE_NAMES = (Package_Main_Name, Package_Map_Name)


ACTIONS = (
    #####----------------- Launcher Actions -----------------#####
    ## 巡航/巡逻opk
    # 开启巡航/巡逻模式
    Action(
        name="START_CRUISE_MODE",
        name_for_llm="START_CRUISE_MODE",
        level="opk",
        desc="Activate autonomous cruise, patrol, or inspection mode.",
        desc_chinese="启动自主巡航/巡逻/巡视模式。",
        display_name="巡航",
        en_display_name="Cruise",
        display_names=ACTION_DISPLAY_NAMES.get("START_CRUISE_MODE", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=600,
        exported=True,
        category="basic_movement",
        client_alias="orion.agent.action.CRUISE",
    ),
    # 退出巡航/巡逻模式
    Action(
        name="EXIT_CRUISE_MODE",
        name_for_llm="EXIT_CRUISE_MODE",
        level="admin",
        desc="Exit or stop the autonomous patrol mode. ",
        desc_chinese="退出或停止巡逻/巡航模式。",
        display_name="退出巡逻",
        en_display_name="Exit Cruise",
        display_names=ACTION_DISPLAY_NAMES.get("EXIT_CRUISE_MODE", {}),
        execute_timeout_limit=30,
        exported=False,
        category="basic_movement",
        client_alias="orion.agent.action.EXIT_CRUISE",
    ),
    # 停止移动：废弃
    Action(
        name="NOT_MOVE",
        name_for_llm="NOT_MOVE",
        level="opk",
        desc="not move.",
        desc_chinese="停止走路。",
        display_name="停止移动",
        en_display_name="Stop Moving",
        display_names=ACTION_DISPLAY_NAMES.get("NOT_MOVE", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=0,
        exported=True,
        category="basic_movement",
    ),
    # 让路
    Action(
        name="GIVE_WAY",
        name_for_llm="GIVE_WAY",
        level="opk",
        desc="Robot will move aside to clear the path for people.",
        desc_chinese="机器人会主动让路，方便他人通行。",
        display_name="让路",
        en_display_name="Give Way",
        display_names=ACTION_DISPLAY_NAMES.get("GIVE_WAY", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=30,
        exported=True,
        category="basic_movement",
        client_alias="orion.agent.action.COME_FAR",
    ),
    # 室内导航：废弃
    Action(
        name="NAVIGATE_START",
        name_for_llm="NAVIGATE_START",
        level="global",
        desc="Indoor navigation. It can only take users to the locations provided below and does not support outdoor locations.",
        desc_chinese="室内导航。只能带用户去下面提供的位置，不支持去室外位置。",
        display_name="室内导航",
        en_display_name="Indoor Navigation",
        display_names=ACTION_DISPLAY_NAMES.get("NAVIGATE_START", {}),
        execute_timeout_limit=300,
        parameters=[
            Action.Parameter(
                name="destination",
                type="enum",
                desc="室内位置，绝不可捏造不存在位置。",
                is_required=False,
                enum_func=load_robot_support_map_points,
            )
        ],
        hidden=True,
        category="navigation",
    ),
    # 通过地图路线导航
    Action(
        name="START_OUTDOOR_NAVIGATION",
        name_for_llm="START_OUTDOOR_NAVIGATION",
        level="opk",
        desc="Navigate to the outdoor destination.",
        desc_chinese="仅当用户明确要求使用“地图导航”且起点和终点均为中国境内地点时可用，若有海外地点使用`{clarify_action_name}`工具拒绝。".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        display_name="调用地图",
        en_display_name="Open Map",
        display_names=ACTION_DISPLAY_NAMES.get("OUTDOOR_NAVIGATE_START", {}),
        execute_timeout_limit=60,
        parameters=[
            Action.Parameter(
                name="origin",
                type="string",
                desc="Route origin, provided by the user. Set the value to '-1' if indeterminate.",
                is_required=False,
            ),
            Action.Parameter(
                name="destination",
                type="string",
                desc="Destination location name. Strip modifiers such as 'nearest', 'nearby', etc. Keep only the core place name.",
            ),
            Action.Parameter(
                name="mode",
                type="enum",
                desc="The mode of navigation that must be one of `driving`, `walking`, `transit`, or `riding`. Defaults to `driving` when not specified in any available context.",
                enum_constant=["driving", "walking", "transit", "riding"],
            ),
        ],
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        post_processing=convert_map_url,
        action_area=Area.domestic,
        exported=True,
        category="navigation",
        client_alias="orion.agent.action.OPEN_WEB_URL",
    ),
    # 转圈或指定角度
    Action(
        name="ROTATE",
        name_for_llm="ROTATE",
        level="opk",
        desc_chinese="机器人可向左或向右旋转指定角度或圈数，二者不可同时设置。最多旋转10圈，超出请用`{clarify_action_name}`工具拒绝。".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        desc="The robot can rotate left or right by a specified angle or number of turns, but not both at the same time. Maximum rotation of 10 turns, exceeding 10 turns should be rejected using the `{clarify_action_name}` tool.".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        display_name="转圈",
        en_display_name="Turn Circle",
        display_names=ACTION_DISPLAY_NAMES.get("ROTATE", {}),
        parameters=[
            Action.Parameter(
                name="direction",
                type="enum",
                desc="The direction to turn, default is left",
                enum_constant=["left", "right"],
            ),
            Action.Parameter(
                name="angle",
                type="int",
                desc="The value of the rotation angle",
                max=360 * 10,
                is_required=False,
            ),
            Action.Parameter(
                name="turns",
                type="float",
                desc="Number of turns.",
                max=10,
                is_required=False,
            ),
        ],
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        post_processing=convert_turns_to_degree,
        exported=True,
        category="basic_movement",
        client_alias="orion.agent.action.TURN_DIRECTION",
    ),
    # 点头或鞠躬
    Action(
        name="HEAD_NOD_OR_BOW",
        name_for_llm="HEAD_NOD_OR_BOW",
        level="opk",
        desc="Robot performs a head nodding or bowing gesture.",
        desc_chinese="机器人执行点头或鞠躬动作。",
        display_name="点头",
        en_display_name="Nod Head",
        display_names=ACTION_DISPLAY_NAMES.get("HEAD_NOD_OR_BOW", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=60,
        exported=True,
        category="basic_movement",
        client_alias="orion.agent.action.HEAD_NOD",
    ),
    # 唱歌跳舞
    Action(
        name="SING_AND_DANCE",
        name_for_llm="SING_AND_DANCE",
        level="opk",
        desc="Robot performs singing and dancing entertainment routines. Use for entertainment, demonstrations, or to create an engaging atmosphere.",
        # desc_chinese="机器人表演唱歌和跳舞。",
        desc_chinese="机器人表演唱歌和跳舞。用于音乐播放、娱乐、演示或创造活跃氛围。",
        display_name="唱歌跳舞",
        en_display_name="Sing and Dance",
        display_names=ACTION_DISPLAY_NAMES.get("SING_AND_DANCE", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=180,
        exported=True,
        category="entertainment",
        client_alias="orion.agent.action.START_DANCE",
    ),
    # 用户注册
    Action(
        name="USER_REGISTER",
        name_for_llm="USER_REGISTER",
        level="opk",
        desc_chinese="注册新用户，包含姓名和人脸注册。",
        desc="Register a new user with the system, capturing their name and facial data.",
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="valid_name",
                type="string",
                desc="The user's name, must be a real name, not a pronoun. Leave blank if not provided.",
                is_required=False,
            ),
            Action.Parameter(
                name="welcome_message",
                type="string",
                desc="The message to greet the user, default is empty.",
                is_required=False,
            ),
        ],
        display_name="注册",
        en_display_name="Register",
        display_names=ACTION_DISPLAY_NAMES.get("USER_REGISTER", {}),
        app_ids=(Opk_Register_App_Id, OverSea_Opk_Launcher_App_Id),
        action_area=Area.domestic,
        exported=True,
        post_processing=register_action_post_processing,
        category="user_recognition",
        client_alias="orion.agent.action.REGISTER",
    ),
    # 向前或向后移动指定距离
    Action(
        name="MOVE_FORWARD_OR_BACKWARD",
        name_for_llm="MOVE_FORWARD_OR_BACKWARD",
        level="opk",
        desc_chinese="让机器人向前或向后移动指定距离。注意：若前进超5米或后退超1米时，用`{clarify_action_name}`工具拒绝用户。".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        desc="Move forward or backward, closer or farther away from the user. Note:If forward exceeds 5m or backward exceeds 1m, you can use `{clarify_action_name}` tool to reject the user.".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        display_name="移动",
        en_display_name="Move",
        display_names=ACTION_DISPLAY_NAMES.get("MOVE_FORWARD_OR_BACKWARD", {}),
        execute_timeout_limit=30,
        parameters=[
            Action.Parameter(
                name="direction",
                type="enum",
                desc="Movement direction: choose 'forward' or 'backward'.",
                enum_constant=["forward", "backward"],
            ),
            Action.Parameter(
                name="moving_distance",
                type="float",
                desc="Movement distance in meters (default 0.1).",
                min=0.1,
                max=5,
            ),
        ],
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        exported=True,
        category="basic_movement",
        client_alias="orion.agent.action.MOVE_DIRECTION",
    ),
    # 打开访客接待
    Action(
        name="START_VISITOR_RECEPTION",
        name_for_llm="START_VISITOR_RECEPTION",
        desc_chinese="开启访客接待登记，支持面试、会议签到或访客登记。",
        desc="Start visitor reception for interviews, meetings, or guest registration.",
        level="opk",
        display_name="打开访客接待页面",
        en_display_name="Open Visitor Reception Page",
        display_names=ACTION_DISPLAY_NAMES.get("START_VISITOR_RECEPTION", {}),
        app_ids=(Opk_Receive_App_Id,),
        execute_timeout_limit=600,
        action_area=Area.domestic,
        exported=True,
        category="visitor_reception",
        client_alias="orion.agent.action.INTERVIEW_START",
    ),
    # 获取未来天气 已废弃
    Action(
        name="WEATHER_GET",
        name_for_llm="WEATHER_GET",
        level="opk",
        desc="Answers based on user questions combined with weather information. Get weather information for a region or city over a period of time or at a point in time.",
        desc_chinese="未来天气查询。查询「明天及未来10天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！",
        display_name="查询未来天气",
        en_display_name="Query Future Weather",
        display_names=ACTION_DISPLAY_NAMES.get("WEATHER_GET", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="area_level",
                type="enum",
                enum_constant=["province", "city", "area"],
                desc="city 对应的区域等级",
            ),
            Action.Parameter(
                name="city",
                type="string",
                desc="行政区域名称，默认查询<city>。注意：如果用户的机器语言（robot语言）不是中文（非zh_CN），则提取的城市名称**必须**为英文。例如，用户询问 '내일 시안 날씨 어때요?' 时，应提取 'Xi'an' 而非 '西安'。如果用户没有指明城市，则设置值为'-1'",
            ),
        ],
        execute_side="both",
        result_schema=[
            Action.Result(
                name="answer_text", desc="The weather of the city", type="string"
            )
        ],
        execute_function=weather,
        app_ids=(Opk_Weather_App_Id, OverSea_Opk_Launcher_App_Id),
        pre_execute=True,
        exported=True,
        category="weather",
    ),
    # CALENDAR
    Action(
        name="QUERY_CALENDAR",
        name_for_llm="QUERY_CALENDAR",
        level="opk",
        desc="Calendar function, provides date, holiday or weekday queries, not support query weather.",
        desc_chinese="日历功能，包含日期、节假日、星期的查询，不支持查询天气。",
        display_name="查询日历",
        en_display_name="Query Calendar",
        display_names=ACTION_DISPLAY_NAMES.get("QUERY_CALENDAR", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="user_question",
                type="string",
                desc="The complete calendar or date-related question. Format should specify the target date/event and reference time frame (e.g., 'When is [holiday] in [year]').",
            ),
        ],
        execute_side="both",
        app_ids=(Opk_Calendar_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_function=calendar,
        pre_execute=True,
        exported=True,
        category="information",
        client_alias="orion.agent.action.CALENDAR",
    ),
    # 导览讲解 opk: 开始导览
    Action(
        name="START_GUIDE_TOUR",
        name_for_llm="START_GUIDE_TOUR",
        level="opk",
        desc="Tour guide function, triggered when the user requests a tour without specifying a location or route, providing a general tour introduction.",
        # desc_chinese="导览参观功能，用户未指定具体地点或路线，仅提出参观请求时触发，带领用户参观，提供总体导览介绍。",
        desc_chinese="导览功能，用户未指定具体地点或路线，仅提出参观请求时触发，带领用户参观，提供总体导览介绍。",
        display_name="导览",
        en_display_name="Guide Introduction",
        display_names=ACTION_DISPLAY_NAMES.get("START_GUIDE_TOUR", {}),
        execute_side="both",
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="user_query",
                type="string",
                desc="The user's original request expressing general interest in a tour without specifying a route.",
                is_required=True,
            ),
        ],
        execute_function=generate_route_introduction,
        exported=True,
        category="guide",
        client_alias="orion.agent.action.GUIDE_INTRODUCTION",
    ),
    # 指定路线导览（用户明确指定某条路线）
    Action(
        name="GUIDE_SPECIFIC_ROUTE",
        name_for_llm="GUIDE_SPECIFIC_ROUTE",
        level="opk",
        desc="Guide the user along a specific, named tour route that they have explicitly requested.",
        desc_chinese="导览功能，仅当用户明确提及特定路线名称时，按该路线进行导览。",
        display_name="指定路线导览",
        en_display_name="Select Specific Route",
        display_names=ACTION_DISPLAY_NAMES.get("GUIDE_SPECIFIC_ROUTE", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="user_query",
                type="string",
                desc="The user's original request that contains a specific route name.",
                is_required=True,
            ),
            Action.Parameter(
                name="route_name",
                type="string",
                desc="The exact name of the selected route.",
                is_required=True,
            ),
        ],
        execute_side="both",
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        execute_function=convert_specific_route,
        exported=True,
        category="guide",
        client_alias="orion.agent.action.GUIDE_SELECT_SPECIFIC_ROUTE",
    ),
    # 开局播报 - 路线推荐和意图确认
    Action(
        name="RECOMMEND_GUIDE_ROUTES",
        name_for_llm="RECOMMEND_GUIDE_ROUTES",
        level="opk",
        desc="Guide scenario, recommend the most suitable tour routes based on user interests or needs.",
        desc_chinese="导览场景，根据用户兴趣或需求推荐合适的参观路线。",
        display_name="推荐路线",
        en_display_name="Route Recommendation",
        display_names=ACTION_DISPLAY_NAMES.get("RECOMMEND_GUIDE_ROUTES", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="user_query",
                type="string",
                desc="The user's request expressing interest in a tour without specifying a route.",
                is_required=True,
            ),
        ],
        execute_side="both",
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        execute_function=generate_route_recommendation,  # 使用大模型生成推荐和确认
        pre_execute=True,  # 需要预执行
        exported=True,
        category="guide",
        client_alias="orion.agent.action.GUIDE_ROUTE_RECOMMENDATION",
    ),
    # 直接开始的技能
    Action(
        name="START_IMMEDIATELY",
        name_for_llm="START_IMMEDIATELY",
        level="opk",
        desc="Start the current task or process immediately, without further confirmation. Common phrases include: 'Start', 'Start immediately', etc.",
        desc_chinese="直接或立即开始当前任务或流程，无需进一步确认。如：“开始”、“直接开始”等。",
        display_name="直接开始",
        en_display_name="Start Immediately",
        display_names=ACTION_DISPLAY_NAMES.get("START_IMMEDIATELY", {}),
        execute_timeout_limit=0,  # 设置较短的超时时间，因为这是需要立即响应的操作
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),  # 使用基础应用ID
        category="guide",  # 系统控制类别
        action_area=Area.all,  # 适用于所有区域
        client_alias="orion.agent.action.START_IMMEDIATELY",
    ),
    # 打开网址
    Action(
        name="SEARCH_WEB_INFORMATION",
        name_for_llm="SEARCH_WEB_INFORMATION",
        level="opk",
        desc_chinese="搜索网络信息或者打开网站，例如查股票、看门票、看新闻、打开指定网站等。",
        desc="Search for information on the internet or open websites, such as checking stock prices, looking up ticket information, reading news, etc. or opening a specific website.",
        display_name="打开浏览器",
        en_display_name="Open Browser",
        display_names=ACTION_DISPLAY_NAMES.get("SEARCH_WEB_INFORMATION", {}),
        execute_timeout_limit=60,
        parameters=[
            Action.Parameter(
                name="search_word",
                type="string",
                desc="Search word",
            )
        ],
        post_processing=convert_search_word_url,
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        exported=True,
        category="web_browser",
        client_alias="orion.agent.action.OPEN_WEB_URL",
    ),
    # 打开网址
    Action(
        name="OPEN_WEB_URL",
        name_for_llm="OPEN_WEB_URL",
        level="opk",
        desc_chinese="网络搜索，如查股票、门票、新闻、体育比赛等建议用百度，官网可直接输入网址访问。",
        desc="Web search, for example, if the user wants to search for stocks, tickets, news, sports events, etc., it is recommended to use Baidu search; official company websites can be accessed directly via their URLs.",
        display_name="打开浏览器",
        en_display_name="Open Browser",
        display_names=ACTION_DISPLAY_NAMES.get("OPEN_WEB_URL", {}),
        execute_timeout_limit=60,
        parameters=[
            Action.Parameter(
                name="url",
                type="HttpUrl",
                desc="A valid HTTPS or HTTP URL to open in the browser. Must include protocol (http:// or https://).",
            )
        ],
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        exported=True,
        category="web_browser",
        client_alias="orion.agent.action.OPEN_WEB_URL",
        hidden=True,
    ),
    # 查询机票
    Action(
        name="SEARCH_FLIGHT_TICKETS",
        name_for_llm="SEARCH_FLIGHT_TICKETS",
        level="opk",
        desc="Query flight tickets, search for flight tickets between specified locations and dates.",
        desc_chinese="查询机票，搜索指定地点和日期之间的航班机票。",
        display_name="查询机票",
        en_display_name="Query Flight Tickets",
        display_names=ACTION_DISPLAY_NAMES.get("SEARCH_FLIGHT_TICKETS", {}),
        parameters=[
            Action.Parameter(
                name="departure_city_code",
                type="string",
                desc="IATA airport code for departure city (e.g., 'PEK' for Beijing, 'SHA' for Shanghai).",
            ),
            Action.Parameter(
                name="arrival_city_code",
                type="string",
                desc="IATA airport code for arrival city (e.g., 'PEK' for Beijing, 'SHA' for Shanghai).",
            ),
            Action.Parameter(
                name="departure_date",
                type="string",
                desc="Date of departure in YYYY-MM-DD format (e.g., '2024-05-01').",
            ),
        ],
        post_processing=build_flight_ticket_query_url,
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        action_area=Area.domestic,
        exported=True,
        category="transportation",
        client_alias="orion.agent.action.OPEN_WEB_URL",
    ),
    # 查询火车票
    Action(
        name="SEARCH_TRAIN_TICKETS",
        name_for_llm="SEARCH_TRAIN_TICKETS",
        level="opk",
        desc="Search for train tickets between specified cities and dates.",
        desc_chinese="查询、搜索指定城市和日期之间的火车票。",
        display_name="查询火车票",
        en_display_name="Query Train Tickets",
        display_names=ACTION_DISPLAY_NAMES.get("SEARCH_TRAIN_TICKETS", {}),
        parameters=[
            Action.Parameter(
                name="departure_city",
                type="string",
                desc="Departure city name without '市' suffix (e.g., '北京' not '北京市').",
                is_required=False,
            ),
            Action.Parameter(
                name="arrival_city",
                type="string",
                desc="Arrival city name without '市' suffix (e.g., '上海' not '上海市').",
            ),
            Action.Parameter(
                name="departure_date",
                type="string",
                desc="Date of departure in YYYY-MM-DD format (e.g., '2024-05-01').",
            ),
        ],
        post_processing=build_train_ticket_query_url,
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        action_area=Area.domestic,
        exported=True,
        category="transportation",
        client_alias="orion.agent.action.OPEN_WEB_URL",
    ),
    # 调整音量
    Action(
        name="SET_VOLUME",
        name_for_llm="SET_VOLUME",
        level="global",
        desc_chinese="设置机器人扬声器音量（0-100），通常以10或30为步长调整。",
        desc="Set the robot's speaker volume to a specific level (0-100). Typically changes by increments of 10-30 units based on user instruction intensity.",
        display_name="调整音量",
        en_display_name="Adjust Volume",
        display_names=ACTION_DISPLAY_NAMES.get("SET_VOLUME", {}),
        parameters=[
            Action.Parameter(
                name="volume_level",
                type="int",
                desc="The volume level(0-100) to be set.",
                min=0,
                max=100,
            ),
        ],
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.SET_VOLUME",
        post_processing=set_volume_post_processing,
    ),
    # 说
    Action(
        name="SAY",
        name_for_llm="SAY",
        desc_chinese="与用户的基础语言交流。用于一般回应、信息传递和对话。",
        desc="Basic verbal communication with the user. Use for general responses, information delivery, and conversations.",
        level="global",
        display_name="说",
        en_display_name="Say",
        display_names=ACTION_DISPLAY_NAMES.get("SAY", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="text",
                type="string",
                desc="Reply in the first person using plain text only, no emojis.",
            ),
        ],
        execute_function=say,
        execute_side="server",
        audio_output=True,
        category="interaction",
        client_alias="orion.agent.action.SAY",
    ),
    # 澄清用户问题
    Action(
        name=CLARIFY_ACTION_NAME,
        name_for_llm=CLARIFY_ACTION_NAME,
        desc_chinese="当用户的对话query触发澄清机制（Clarification Mechanism），需要对用户问题进行澄清时，请使用该工具",
        # desc_chinese="当用户query的意图不明确，需要对用户问题进行澄清时，请使用该工具。另外，以下情况也需要考虑澄清：1）当用户对问题的回答不满意而重复询问同一个问题的时候，需要确认一下用户的意图。注意：谨慎使用，优先使用其它工具",
        # desc_chinese="When the user's intent is unclear, please use this tool to clarify their question. Additionally, use this tool in the following situations: 1) If the user repeatedly asks the same question because their issue was not resolved, you should confirm their true intention. Note: Use this tool cautiously, avoid overuse, and give priority to other tools.",
        desc="When the user's intent is unclear, please use this tool to clarify their question. Additionally, use this tool in the following situations: 1) If the user repeatedly asks the same question because their issue was not resolved, you should confirm their true intention. Note: Use this tool cautiously, avoid overuse, and give priority to other tools.",
        level="global",
        display_name="澄清用户问题",
        en_display_name="Clarify User Question",
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="request_clarify_text",
                type="string",
                desc="Prompt the user to clarify their question, using plain text only and no emojis.",
                is_required=True,
            ),
        ],
        execute_function=clarify,
        execute_side="server",
        action_area=Area.all,
        audio_output=True,
        category="interaction",
        hidden=True,
        client_alias=f"orion.agent.action.{CLARIFY_ACTION_NAME}",
    ),
    # 静默兜底技能
    Action(
        name="SILENT",
        name_for_llm="SILENT",
        level="opk",
        desc="A default fallback skill that should be selected when the user's query has low relevance to other available actions.",
        desc_chinese="默认兜底的技能，在以下情况应选择此动作：用户的查询与其他可用动作相关性较低时，默认选择这个技能",
        display_name="默认兜底技能",
        display_names=ACTION_DISPLAY_NAMES.get("SILENT", {}),
        en_display_name="Silent",
        execute_timeout_limit=0,
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        category="guide",
        action_area=Area.all,
    ),
    # 取消
    Action(
        name="CANCEL",
        name_for_llm="CANCEL",
        desc="Completely terminate the current behavior (such as dancing, nodding, navigating, speaking, moving, sending messages). Unlike pause, cancel means a full termination rather than a temporary suspension.",
        desc_chinese="取消当前行为（如跳舞、点头、导航、说话、移动、发送消息），与pause不同，cancel为完全终止而非暂停。",
        level="global",
        display_name="取消",
        en_display_name="Cancel",
        display_names=ACTION_DISPLAY_NAMES.get("CANCEL", {}),
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.CANCEL",
    ),
    # 退出 1.3可做到页面级别，重做OPK时移除desc后面的描述
    Action(
        name="EXIT",
        name_for_llm="EXIT",
        desc="Exit and completely close the current application. Different from BACK which returns to previous screen within same application.",
        desc_chinese="彻底关闭当前应用，区别于back，exit不会返回上一级界面。",
        level="global",
        display_name="退出",
        en_display_name="Exit",
        display_names=ACTION_DISPLAY_NAMES.get("EXIT", {}),
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.EXIT",
    ),
    # 返回上一级
    Action(
        name="BACK",
        name_for_llm="BACK",
        level="global",
        desc="Return to the previous screen or menu within the current application. Unlike exit, back does not completely close the application.",
        desc_chinese="返回当前应用的上一级界面或菜单，不同于exit，back不会退出应用。",
        display_name="返回上一级",
        en_display_name="Back",
        display_names=ACTION_DISPLAY_NAMES.get("BACK", {}),
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.BACK",
    ),
    # 下一步
    Action(
        name="NEXT",
        name_for_llm="NEXT",
        level="global",
        desc="Proceed to the next step in a multi-step process or presentation. Use in guided tutorials, presentations, or sequential operations.",
        desc_chinese="在多步骤流程或演示中前进到下一步。如：讲解导览中下一个地点等。",
        display_name="下一步",
        en_display_name="Next",
        display_names=ACTION_DISPLAY_NAMES.get("NEXT", {}),
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.NEXT",
    ),
    # 确认
    Action(
        name="CONFIRM",
        name_for_llm="CONFIRM",
        level="global",
        desc="User confirms the current action or selection, such as saying 'Confirm', 'OK', or 'No problem', based on the conversation context.",
        desc_chinese="用户确认当前操作或选择，如“确认”“确定”“好的”“没问题”“可以”“提交”等，必要时需结合对话历史判断。",
        display_name="确认",
        en_display_name="Confirm",
        display_names=ACTION_DISPLAY_NAMES.get("CONFIRM", {}),
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.CONFIRM",
    ),
    #####----------------- OPK Actions -----------------#####
    # 拍照
    Action(
        name="TAKE_PHOTO",
        name_for_llm="TAKE_PHOTO",
        level="opk",
        desc="Activate the robot's camera to take a photo of the current scene or people in front of it.",
        desc_chinese="激活机器人相机，对当前场景或面前的人拍照。",
        display_name="拍照",
        en_display_name="Take Photo",
        display_names=ACTION_DISPLAY_NAMES.get("TAKE_PHOTO", {}),
        app_ids=(Opk_Receive_App_Id,),
        execute_timeout_limit=30,
        exported=False,
        action_area=Area.domestic,
        category="entertainment",
        client_alias="orion.agent.action.TAKE_PHOTO",
    ),
    # 填写验证码
    Action(
        name="INPUT_VERIFICATION_CODE",
        name_for_llm="INPUT_VERIFICATION_CODE",
        level="opk",
        desc="Enter a 4-digit verification code for authentication. Each digit must be between 0-9.",
        desc_chinese="输入4位数验证码进行身份验证。每位数字必须在0-9之间。",
        display_name="输入验证码",
        en_display_name="Input Verification Code",
        display_names=ACTION_DISPLAY_NAMES.get("INPUT_VERIFICATION_CODE", {}),
        parameters=[
            Action.Parameter(
                name="verification_code",
                type="Integer array",
                length=4,
                desc="The 4-digit verification code to be entered. Each digit must be between 0-9.",
            )
        ],
        app_ids=(Opk_Receive_App_Id,),
        exported=False,
        execute_timeout_limit=0,
        action_area=Area.domestic,
        category="verification",
        client_alias="orion.agent.action.VERIFICATION_CODE_INPUT",
    ),
    # 填写手机号后四位
    Action(
        name="INPUT_LAST_4_PHONE_DIGITS",
        name_for_llm="INPUT_LAST_4_PHONE_DIGITS",
        level="opk",
        desc="Enter the last 4 digits of a phone number for verification purposes. Each digit must be between 0-9.",
        desc_chinese="输入手机号码后四位用于验证。每位数字必须在0-9之间。",
        display_name="输入手机号后四位",
        en_display_name="Input Last 4 Digits of Phone Number",
        display_names=ACTION_DISPLAY_NAMES.get("INPUT_LAST_4_PHONE_DIGITS", {}),
        parameters=[
            Action.Parameter(
                name="last_4_digits",
                type="Integer array",
                length=4,
                desc="The last four digits of the phone number, Must be four valid digits, Must be converted to Arabic numerals, Numeric range 0 to 9",
            )
        ],
        app_ids=(Opk_Receive_App_Id,),
        exported=False,
        execute_timeout_limit=0,
        action_area=Area.domestic,
        category="verification",
        client_alias="orion.agent.action.LAST_4_DIGITS_INPUT",
    ),
    # 重新播放
    Action(
        name="REPLAY",
        name_for_llm="REPLAY",
        level="global",
        desc="Replay any media or interactive content. For example, replay a video or replay the current point in the guided tour.",
        desc_chinese="重新播放任何媒体或交互内容。例如播放视频或者导览讲解中重新讲解当前的点。",
        display_name="重播",
        en_display_name="Replay",
        display_names=ACTION_DISPLAY_NAMES.get("REPLAY", {}),
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        action_area=Area.domestic,
        exported=False,
        execute_timeout_limit=0,
        category="guide",
        client_alias="orion.agent.action.COMMON_REPLAY",
    ),
    # 播放视频
    Action(
        name="PLAY",
        name_for_llm="PLAY",
        level="global",
        desc="Start playing any media or interactive content. For example, play a video or start the guided tour at the current point.",
        desc_chinese="开始播放任何媒体或交互内容。例如播放视频或者导览讲解中开始讲解当前的点。",
        display_name="播放",
        en_display_name="Play",
        display_names=ACTION_DISPLAY_NAMES.get("PLAY", {}),
        exported=False,
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.MULTIMEDIA_PLAY",
    ),
    # 恢复动作： 恢复播放、继续讲解
    Action(
        name="RESUME",
        name_for_llm="RESUME",
        level="global",
        desc="Resume or continue the service, such as resuming playback or continuing the guided tour.",
        desc_chinese="恢复或继续服务，例如继续播放、继续讲解等场景。",
        display_name="继续服务",
        en_display_name="Resume Service",
        display_names=ACTION_DISPLAY_NAMES.get("RESUME", {}),
        exported=False,
        execute_timeout_limit=0,
        client_alias="orion.agent.action.MULTIMEDIA_PLAY",
        category="system_control",
    ),
    # 暂停
    Action(
        name="PAUSE",
        name_for_llm="PAUSE",
        level="global",
        desc_chinese="暂定当前服务。用户希望暂停某个动作，例如「暂停播放视频」或者「暂停移动」等。常见表达如：“别走了”“别动了”“等下我”或“等一等”等。",
        desc="Pause the current service. Users may want to pause actions such as video playback or movement. Common phrases include: 'Don’t go', 'Stay still', 'Wait for me', or 'Hold on'.",
        display_name="暂停",
        en_display_name="Pause",
        display_names=ACTION_DISPLAY_NAMES.get("PAUSE", {}),
        exported=False,
        execute_timeout_limit=0,
        category="guide",
        client_alias="orion.agent.action.COMMON_PAUSE",
    ),
    # 导览讲解过程中用户想要提问时触发
    Action(
        name="ASK_QUESTION_IN_GUIDE",
        name_for_llm="ASK_QUESTION_IN_GUIDE",
        level="opk",
        desc_chinese="在导览讲解过程中，用户想提问。与SAY不同，ASK_QUESTION_IN_GUIDE专用于导览讲解场景过程用户提问场景。",
        desc="During the guided tour, the user wants to ask a question. Unlike SAY, ask_question_in_guide is specifically for user questions during the tour.",
        display_name="提问",
        en_display_name="Start Questioning",
        display_names=ACTION_DISPLAY_NAMES.get("ASK_QUESTION_IN_GUIDE", {}),
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        action_area=Area.domestic,
        exported=False,
        execute_timeout_limit=0,
        category="guide",
        client_alias="orion.agent.action.START_QUESTION",
    ),
    # 再讲一次
    Action(
        name="REPEAT_TOUR_EXPLANATION",
        name_for_llm="REPEAT_TOUR_EXPLANATION",
        level="opk",
        desc_chinese="从头开始完全重新进行导览讲解，通常在达到评分/反馈阶段后使用。",
        desc="Completely restart a guided tour explanation from the beginning, typically after reaching the scoring/feedback phase.",
        display_name="重新讲解",
        en_display_name="Repeat",
        display_names=ACTION_DISPLAY_NAMES.get("REPEAT_TOUR_EXPLANATION", {}),
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        exported=False,
        execute_timeout_limit=0,
        category="guide",
        client_alias="orion.agent.action.COMMON_REPEAT",
    ),
    # ROUTES_OTHERS	选择其他路线
    # 当前导览路线彻底结束（已经完成了对之前导览路线的评价）的时候，用户可以接着说，我想继续参观别的路线
    Action(
        name="SWITCH_GUIDE_ROUTE",
        name_for_llm="SWITCH_GUIDE_ROUTE",
        level="opk",
        desc="Switch or choose to a different guided tour route from the current one. Use when user wants to choose or ask for another route.",
        desc_chinese="选择其他导览路线，用户想要选择或者询问其他路线时使用。",
        display_name="选择其他导览路线",
        en_display_name="Choose Another Route",
        display_names=ACTION_DISPLAY_NAMES.get("SWITCH_GUIDE_ROUTE", {}),
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        exported=False,
        execute_timeout_limit=0,
        category="guide",
        client_alias="orion.agent.action.ROUTES_OTHERS",
    ),
    # 导览讲解： 讲解完收集打分
    Action(
        name="RATE_EXPERIENCE",
        name_for_llm="RATE_EXPERIENCE",
        level="opk",
        desc="Rate an experience or service on a scale of 1-5 stars. Used for collecting user feedback after completing a guided tour or interaction.",
        desc_chinese="对体验或服务进行1-5星评分。用于在完成导览或交互后收集用户反馈。",
        display_name="打分",
        en_display_name="Score",
        display_names=ACTION_DISPLAY_NAMES.get("RATE_EXPERIENCE", {}),
        parameters=[
            Action.Parameter(
                name="score",
                type="int",
                min=1,
                max=5,
                desc="Rating value from 1-5, where 5 represents highest satisfaction.",
            )
        ],
        app_ids=(Opk_Guide_App_Id, OverSea_Opk_Guide_App_Id),
        exported=False,
        execute_timeout_limit=0,
        category="guide",
        client_alias="orion.agent.action.SCORE",
    ),
    # 调整移动速度
    Action(
        name="ADJUST_WALKING_SPEED",
        name_for_llm="ADJUST_WALKING_SPEED",
        level="global",
        desc_chinese="调整当前移动速度，范围0.1-1.2m/s，超出请用`{clarify_action_name}`动作拒绝。".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        desc="Adjust the <current walking speed>. range: 0.1-1.2m/s. Note: If the speed exceeds the range, you can use `{clarify_action_name}` tool to reject the user.".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        display_name="调整当前移动速度",
        en_display_name="Adjust Walking Speed",
        display_names=ACTION_DISPLAY_NAMES.get("ADJUST_WALKING_SPEED", {}),
        parameters=[
            Action.Parameter(
                name="adjusted_speed",
                type="float",
                desc="The robot's walking speed, range: 0.1-1.2m/s.",
                min=0.1,
                max=1.2,
            ),
        ],
        execute_timeout_limit=0,
        category="system_control",
        client_alias="orion.agent.action.ADJUST_SPEED",
    ),
    # 招揽语
    Action(
        name="SET_GREETING_MESSAGE",
        name_for_llm="SET_GREETING_MESSAGE",
        level="opk",
        desc="Set up a greeting message that the robot will use when greeting users.",
        desc_chinese="设置机器人见到用户时的问候语/欢迎语。",
        display_name="设置问候语",
        en_display_name="Configure Greeting Message",
        display_names=ACTION_DISPLAY_NAMES.get("SET_GREETING_MESSAGE", {}),
        app_ids=(Opk_Welcome_Message_App_Id,),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="valid_name",
                type="string",
                desc="The user's name, must be a real name, not a pronoun. Leave blank if not provided.",
                is_required=False,
            ),
            Action.Parameter(
                name="one_sentence",
                type="string",
                desc="Greeting message content.",
                is_required=False,
            ),
        ],
        action_area=Area.domestic,
        exported=True,
        category="interaction",
        client_alias="orion.agent.action.CONFIGURE_WELCOME_MESSAGE",
        post_processing=register_action_post_processing,
    ),
    # 说欢迎或者欢送语
    Action(
        name="GENERATE_WELCOME_FAREWELL_MESSAGE",
        name_for_llm="GENERATE_WELCOME_FAREWELL_MESSAGE",
        level="opk",
        desc_chinese="生成欢迎语/欢送语，一般在欢迎来客时使用，或者在送别时使用。",
        desc="Generate welcome/farewell messages. Used when welcoming guests or saying goodbye.",
        display_name="生成欢迎语/欢送语",
        en_display_name="Generate",
        display_names=ACTION_DISPLAY_NAMES.get("GENERATE_WELCOME_FAREWELL_MESSAGE", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=90,
        parameters=[
            # Action.Parameter(
            #     name="goal",
            #     type="string",
            #     desc="Instructions on which type of content to generate: welcome message, farewell, poem, story, or joke.",
            # )
        ],
        execute_function=generate_message,
        execute_side="server",
        pre_execute=True,
        exported=True,
        category="interaction",
        client_alias="orion.agent.action.GENERATE_MESSAGE",
    ),
    # 发送紧急消息
    Action(
        name="SEND_MESSAGE",
        name_for_llm="SEND_MESSAGE",
        level="opk",
        desc_chinese="通过飞书向指定人员发送消息，需提供收件人真实姓名和消息内容，可选紧急程度，适用于找人、留言等。",
        desc="Send a message to a designated person via Feishu. The recipient's real name and message content are required. Message urgency is optional. Suitable for scenarios such as locating someone or leaving a message.",
        display_name="发消息",
        en_display_name="Send Message",
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="recipient_name",
                type="string",
                desc="Must use real names, not pronouns.",
            ),
            Action.Parameter(
                name="message_content",
                type="string",
                desc="Send the message content. Please polish the content to avoid being too direct or stiff.",
            ),
            Action.Parameter(
                name="message_type",
                type="enum",
                enum_constant=["urgent", "normal"],
                desc="Priority level of the message (urgent or normal).",
            ),
            Action.Parameter(
                name="sender_photo_url",
                type="HttpUrl",
                desc="The URL of the sender's photo.",
                is_hidden=True,
                is_required=False,
                generate_side="robot",
            ),
            Action.Parameter(
                name="person_id",
                type="string",
                desc="The unique identifier of the user, provided by the client.",
                is_hidden=True,
                is_required=False,
                generate_side="robot",
            ),
        ],
        execute_function=send_message,
        execute_side="server",
        action_area=Area.domestic,
        exported=True,
        category="interaction",
        client_alias="orion.agent.action.SEND_MESSAGE",
    ),
    # 调用地图搜索或推荐附近的某类地点
    Action(
        name="SEARCH_NEARBY_PLACES",
        name_for_llm="SEARCH_NEARBY_PLACES",
        level="opk",
        desc="Call the map to search for or recommend nearby types of places (such as attractions, hotels, subway stations, food, etc.), but route recommendations are not provided.",
        desc_chinese="调用地图搜索或推荐附近的某类地点（如景点、酒店、地铁站、美食等），不提供路线推荐。",
        display_name="调用地图",
        en_display_name="Open Map",
        display_names=ACTION_DISPLAY_NAMES.get("SEARCH_NEARBY_PLACES", {}),
        execute_timeout_limit=60,
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        parameters=[
            Action.Parameter(
                name="query",
                type="string",
                desc="The user's original query.",
                is_required=True,
            ),
            Action.Parameter(
                name="place",
                type="string",
                desc="Geographic location mentioned in the query (city, province, district, landmark, or any specific place name). Extract the exact place name as mentioned by the user. default '-1' if not found. ",
                is_required=True,
            ),
            Action.Parameter(
                name="target",
                type="string",
                desc="Search target extracted from the query (e.g., hotel, food, subway station, etc.); ",
                is_required=True,
            ),
        ],
        pre_execute=True,
        post_processing=convert_recommend_url,
        client_alias="orion.agent.action.OPEN_WEB_URL",
        action_area=Area.domestic,
        exported=True,
        category="navigation",
    ),
    # 人脸识别
    Action(
        name="FACE_RECOGNITION",
        name_for_llm="FACE_RECOGNITION",
        level="opk",
        desc_chinese="人脸识别，仅识别当前在机器人面前的用户（主要为姓名），无法识别他人。",
        desc="Facial recognition. Identify the face in front of the robot, answer the user's identity (mainly the name), but does not support identifying other people.",
        display_name="人脸识别",
        en_display_name="Face Recognition",
        display_names=ACTION_DISPLAY_NAMES.get("FACE_RECOGNITION", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_timeout_limit=0,
        action_area=Area.domestic,
        exported=True,
        category="user_recognition",
        client_alias="orion.agent.action.FACE_RECOGNITION",
    ),
    # 去充电
    Action(
        name="GO_CHARGING",
        name_for_llm="GO_CHARGING",
        level="admin",
        desc="Instruct the robot to go to the charging station and start charging. Consider the current battery level if needed.",
        desc_chinese="要求机器人前往充电桩进行充电，必要时需结合当前电量判断。",
        display_name="去充电",
        en_display_name="Go to Charging",
        display_names=ACTION_DISPLAY_NAMES.get("GO_CHARGING", {}),
        execute_timeout_limit=0,
        category="system_admin",
        client_alias="orion.agent.action.GO_CHARGING",
    ),
    # 管理员:进入推销APP
    Action(
        name="OPEN_PRODUCT_PROMOTION_APP",
        name_for_llm="OPEN_PRODUCT_PROMOTION_APP",
        level="admin",
        desc="Open the product promotion application for demonstrating and selling products/services.",
        desc_chinese="打开产品推广/推销APP，用于演示和销售产品/服务。",
        display_name="进入推销APP",
        en_display_name="Enter Promotion App",
        display_names=ACTION_DISPLAY_NAMES.get("OPEN_PRODUCT_PROMOTION_APP", {}),
        action_area=Area.domestic,
        category="system_admin",
        client_alias="orion.agent.action.ENTER_PROMOTE_APP",
    ),
    # 管理员:退出推销APP
    Action(
        name="EXIT_PRODUCT_PROMOTION_APP",
        name_for_llm="EXIT_PRODUCT_PROMOTION_APP",
        level="admin",
        desc="Exit the product promotion application.",
        desc_chinese="退出推广/推销APP。",
        display_name="退出推销APP",
        en_display_name="Exit Promotion App",
        display_names=ACTION_DISPLAY_NAMES.get("EXIT_PRODUCT_PROMOTION_APP", {}),
        execute_side="both",
        execute_function=execute_clear_cache_for_key,
        action_area=Area.domestic,
        category="system_admin",
        client_alias="orion.agent.action.EXIT_PROMOTE_APP",
    ),
    # 设置或修改地图点位
    Action(
        name="SET_MAP_LOCATION",
        name_for_llm="SET_MAP_LOCATION",
        level="apk",
        desc="Add or modify a location point on the map for navigation and positioning reference.",
        desc_chinese="新增或修改地图上的位置点(点位)，方便机器人导航和定位。",
        display_name="设置位置",
        en_display_name="Set Location",
        display_names=ACTION_DISPLAY_NAMES.get("SET_MAP_LOCATION", {}),
        parameters=[
            Action.Parameter(
                name="location",
                type="string",
                desc="The name of the location to set.",
            )
        ],
        execute_timeout_limit=0,
        package_names=(Package_Map_Name,),
        category="system_admin",
        client_alias="orion.agent.action.SET_LOCATION",
    ),
    # 知识问答
    Action(
        name=KNOWLEDGE_QA_ACTION_NAME,
        name_for_llm=KNOWLEDGE_QA_ACTION_NAME,
        level="global",
        desc="Professionally answer user questions about company products, employee information by querying the knowledge base.",
        desc_chinese="需要查询知识库来回答用户的问题，如「公司产品」、「员工信息」等。",
        display_name="找答案",
        en_display_name="Find Answer",
        display_names=ACTION_DISPLAY_NAMES.get(KNOWLEDGE_QA_ACTION_NAME, {}),
        execute_timeout_limit=600,
        parameters=[
            Action.Parameter(
                name="question",
                type="string",
                desc="Based on conversation context, summarize the user's specific question that needs knowledge-based answers.",
            ),
        ],
        result_schema=[
            Action.Result(
                name="knowledge_content",
                type="string",
                desc="The knowledge content.",
            ),
        ],
        execute_side="server",
        execute_function=knowledge_qa,
        pre_execute=True,
        category="information",
        client_alias="orion.agent.action.KNOWLEDGE_QA",
    ),
    # 用户要求与机器人进行合影拍照
    Action(
        name="TAKE_PHOTO_WITH_ROBOT",
        name_for_llm="TAKE_PHOTO_WITH_ROBOT",
        level="opk",
        desc="Take a group photo with users. Use when users explicitly request a photo together.",
        desc_chinese="与机器人合影拍照。当用户明确表示要合影或拍照时使用此功能。",
        display_name="合影",
        en_display_name="Group Photo",
        display_names=ACTION_DISPLAY_NAMES.get("TAKE_PHOTO_WITH_ROBOT", {}),
        app_ids=(Opk_TakePicture_App_Id,),
        action_area=Area.domestic,
        execute_timeout_limit=180,
        exported=True,
        category="entertainment",
        client_alias="orion.agent.action.INTERVIEW_START_PHOTO",
    ),
    # 切换表情
    Action(
        name="CHANGE_FACIAL_EXPRESSION",
        name_for_llm="CHANGE_FACIAL_EXPRESSION",
        level="opk",
        desc="Change robot's facial expression or display different emotions on screen.",
        desc_chinese="切换机器人的面部表情。当用户要求机器人改变表情、展示不同情绪时使用。",
        display_name="换脸",
        en_display_name="Change Face",
        display_names=ACTION_DISPLAY_NAMES.get("CHANGE_FACIAL_EXPRESSION", {}),
        app_ids=(Opk_TakePicture_App_Id,),
        exported=False,
        execute_timeout_limit=0,
        category="entertainment",
        client_alias="orion.agent.action.CHANGE_FACE",
    ),
    # 获取实时天气 （已废弃）
    Action(
        name="WEATHER_GET_REALTIME",
        name_for_llm="WEATHER_GET_REALTIME",
        level="opk",
        desc_chinese="当天天气查询。查询「当天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！不支持日历功能！！！",
        desc="Get today's weather.",
        display_name="查询当天天气",
        en_display_name="Query Today Weather",
        display_names=ACTION_DISPLAY_NAMES.get("WEATHER_GET_REALTIME", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_function=get_realtime_weather,
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="city",
                type="string",
                desc="行政区域名称，默认查询`<所在城市>`。当用户没有指明城市，则设置值为'-1'",
            ),
            Action.Parameter(
                name="user_question",
                type="string",
                desc="user's question",
                is_required=False,
                need_summary=True,
                is_hidden=True,
            ),
        ],
        execute_side="server",
        pre_execute=True,
        exported=True,
        category="weather",
    ),
    # click
    Action(
        name="CLICK_WEB_ELEMENT",
        name_for_llm="CLICK_WEB_ELEMENT",
        level="opk",
        desc="Simulate a click action on clickable elements in the current web page being displayed.",
        desc_chinese="模拟在当前显示的网页上点击可点击元素。仅在用户明确要点击网页上某个元素时使用。",
        display_name="点击",
        en_display_name="Click",
        display_names=ACTION_DISPLAY_NAMES.get("CLICK_WEB_ELEMENT", {}),
        parameters=[
            Action.Parameter(
                name="element_tag",
                type="enum",
                desc="可点击元素的标签",
                enum_func=load_clickable_elements_value,
            ),
        ],
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        post_processing=convert_element_id,
        exported=False,
        execute_timeout_limit=0,
        category="web_browser",
        client_alias="orion.agent.action.CLICK",
    ),
    # 根据视觉回答
    Action(
        name="ANSWER_VISUAL_QUESTION",
        name_for_llm="ANSWER_VISUAL_QUESTION",
        level="opk",
        desc_chinese="通过机器人摄像头，仅回答关于人物穿着、表情、性别，周边环境和物体识别的问题，不支持涉及用户关系的话题。",
        desc="Only answer questions about clothing, facial expressions, gender, surroundings, and object recognition through the robot's camera. Topics involving user relationships are not supported.",
        display_name="根据视觉回答",
        en_display_name="Answer from Vision",
        display_names=ACTION_DISPLAY_NAMES.get("ANSWER_VISUAL_QUESTION", {}),
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        execute_function=answer_question_from_vision,
        parameters=[
            Action.Parameter(
                name="image_url",
                type="string",
                desc="The image URL captured by the robot's camera.",
                is_hidden=True,
                is_required=False,
                generate_side="robot",
            ),
            Action.Parameter(
                name="question",
                type="string",
                desc="The user's question, must be summarized as a first-person question, with a length limit of 15 characters.",
            ),
        ],
        result_schema=[
            Action.Result(
                name="answer_text",
                desc="The answer of the question",
                type="string",
            ),
        ],
        execute_side="robot",
        pre_execute=False,
        exported=True,
        category="information",
        client_alias="orion.agent.action.ANSWER_QUESTION_FROM_VISION",
    ),
    # 推销opk下使用: 回到首页，展示产品列表
    Action(
        namespace="orion.app.promote",
        name="SHOW_HOME_PAGE",
        name_for_llm="SHOW_HOME_PAGE",
        level="opk",
        desc="Homepage. Use when the user wants to return to the home page or view an overview of all products.",
        desc_chinese="首页。当用户表达要回到首页，或需要查看所有产品概览时使用。",
        display_name="展示首页",
        en_display_name="Show Home Page",
        app_ids=(Opk_Promote_App_Id,),
        execute_side="both",
        execute_function=home_v3,
        result_schema=[
            Action.Result(name="data", type="dict", desc="home page data"),
        ],
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.HOME",
        display_names=ACTION_DISPLAY_NAMES.get("SHOW_HOME_PAGE", {}),
    ),
    # 推销opk下使用: 展示产品详情，首页用户想了解具体某款产品时触发
    Action(
        namespace="orion.app.promote",
        name="GET_PRODUCT_DETAILS",
        name_for_llm="GET_PRODUCT_DETAILS",
        level="opk",
        desc="Display detailed information about a specific product that user wants to learn more about.",
        desc_chinese="展示具体产品的详细信息。当用户明确表达想了解特定产品时使用。",
        display_name="展示产品详情",
        en_display_name="Show Product Detail",
        app_ids=(Opk_Promote_App_Id,),
        display_names=ACTION_DISPLAY_NAMES.get("GET_PRODUCT_DETAILS", {}),
        parameters=[
            Action.Parameter(
                name="production",
                type="enum",
                enum_func=load_product_list_v2,
                desc="The product the user wants to learn more about. Use explicit user intent if specified; otherwise, refer to `product_name` in Robot Screen Information.",
            )
        ],
        execute_side="both",
        execute_function=production_detail_v2,
        result_schema=[
            Action.Result(
                name="product",
                type="dict",
                desc="The product detail data",
            ),
            Action.Result(
                name="product_detail",
                type="list",
                desc="The media resources of the product",
            ),
            Action.Result(
                name="text_content",
                type="page_info",
                desc="The text content of the product",
            ),
        ],
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.PRODUCT_DETAIL",
    ),
    # 推销opk下使用: 介绍产品具体参数/功能
    Action(
        namespace="orion.app.promote",
        name="GET_PRODUCT_FEATURE_DETAILS",
        name_for_llm="GET_PRODUCT_FEATURE_DETAILS",
        level="opk",
        desc="Get specific features of a product for promotion. Use when the user wants to know about a particular feature.",
        desc_chinese="获取产品的特定功能等，用于产品推销。当用户想了解产品某项具体功能时使用。",
        display_name="介绍产品功能",
        en_display_name="Introduce Product Function",
        parameters=[
            Action.Parameter(
                name="production_function",
                type="enum",
                desc="The specific feature of the product.",
                enum_func=load_product_functions_v2,
            )
        ],
        result_schema=[
            Action.Result(
                name="title",
                type="string",
                desc="The title of the product function",
            ),
            Action.Result(
                name="url",
                type="string",
                desc="The url of the product function",
            ),
            Action.Result(
                name="type",
                type="enum",
                enum_constant=["image", "video"],
                desc="The type of media resource",
            ),
            Action.Result(
                name="text_content",
                type="page_info",
                desc="The text content of the product function",
            ),
        ],
        app_ids=(Opk_Promote_App_Id,),
        execute_function=production_function_v2,
        execute_side="both",
        exported=False,
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.PRODUCT_FUNCTION",
        display_names=ACTION_DISPLAY_NAMES.get("GET_PRODUCT_FEATURE_DETAILS", {}),
    ),
    Action(
        namespace="orion.app.promote",
        name="GET_PRODUCT_PARAMETERS_SPECIFICATIONS",
        name_for_llm="GET_PRODUCT_PARAMETERS_SPECIFICATIONS",
        level="opk",
        desc="Get parameter specifications of a product for promotion. Use when the user wants to know about specification.",
        desc_chinese="获取产品的参数规格，用于产品推销。当用户想了解产品参数规格时使用。",
        display_name="介绍产品参数规格",
        en_display_name="Introduce Product Parameters",
        parameters=[
            Action.Parameter(
                name="production_function",
                type="enum",
                desc="The specific feature or parameter specification of the product.",
                enum_func=load_product_parameters,
            )
        ],
        app_ids=(Opk_Promote_App_Id,),
        execute_function=production_parameters,
        execute_side="both",
        exported=False,
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        display_names=ACTION_DISPLAY_NAMES.get(
            "GET_PRODUCT_PARAMETERS_SPECIFICATIONS", {}
        ),
    ),
    # 收集用户信息字段
    Action(
        namespace="orion.app.promote",
        name="COLLECT_USER_INFO",
        name_for_llm="COLLECT_USER_INFO",
        level="opk",
        desc="Collect user info in promotional scenarios. Triggered only when the user's last dialogue contains specific fields.",
        desc_chinese="用于推销场景中收集用户信息字段。仅当用户最后一轮对话中包含需收集的字段（`user_info_fields` 可选列表中的字段）时触发此技能，用于构建精准用户画像。",
        display_name="收集用户信息",
        en_display_name="Collect User Info",
        parameters=[
            Action.Parameter(
                name="user_info_fields",
                type="enum",
                desc="List of specific user information fields to collect from the predefined enum list. Supports multiple keys when user input contains multiple types of information.",
                enum_func=load_userinfo_collect,
            ),
        ],
        app_ids=(Opk_Promote_App_Id,),
        execute_side="server",
        execute_function=collect_user_info_and_recommend,
        exported=False,
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        display_names=ACTION_DISPLAY_NAMES.get("COLLECT_USER_INFO", {}),
    ),
    # 展示联系方式, 使用此action作为留资的工具
    Action(
        namespace="orion.app.promote",
        name="SHOW_CONTACT_INFORMATION",
        name_for_llm="SHOW_CONTACT_INFORMATION",
        level="opk",
        desc="Display sales or service contact information when users inquire about pricing, purchasing, express intent to leave, or directly request contact details.",
        desc_chinese="展示销售或服务联系方式，适用于用户咨询价格、购买、表达离开意向或直接索要联系方式的场景。",
        display_name="提供联系方式",
        en_display_name="Show Contact Information",
        display_names=ACTION_DISPLAY_NAMES.get("SHOW_CONTACT_INFORMATION", {}),
        app_ids=(Opk_Promote_App_Id,),
        execute_side="both",
        execute_function=show_contact_information,
        exported=False,
        # result_schema=[
        #     Action.Result(
        #         name="qr_code_url",
        #         type="HttpUrl",
        #         desc="The URL of the QR code",
        #     ),
        #     Action.Result(
        #         name="title",
        #         type="string",
        #         desc="The title of the contact information",
        #     ),
        #     Action.Result(
        #         name="subtitle",
        #         type="string",
        #         desc="The subtitle of the contact information",
        #     ),
        # ],
        pre_execute=False,
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.SHOW_CONTACT_INFORMATION",
    ),
    # realtime say: 内部使用Action
    Action(
        name="REALTIME_SAY",
        name_for_llm="REALTIME_SAY",
        level="global",
        desc="Think about it.",
        desc_chinese="思考说",
        display_name="说",
        en_display_name="Say",
        parameters=[
            Action.Parameter(
                name="messages",
                type="list",
                desc="请求内容",
            ),
            Action.Parameter(name="llm_config", type="dict", desc="llm配置"),
        ],
        execute_side="server",
        hidden=True,
        audio_output=True,
        execute_function=stream_say,
        category="interaction",
    ),
    # call llm: 内部使用Action
    Action(
        name="CALL_LLM",
        name_for_llm="CALL_LLM",
        level="global",
        desc="Call llm to get the result.",
        desc_chinese="调用llm获取结果",
        display_name="调用llm",
        en_display_name="Call LLM",
        parameters=[
            Action.Parameter(
                name="messages",
                type="list",
                desc="请求内容",
            ),
            Action.Parameter(name="llm_config", type="dict", desc="llm配置"),
        ],
        result_schema=[
            Action.Result(
                name="llm_response",
                type="dict",
                desc="The response from llm",
            )
        ],
        execute_side="server",
        hidden=True,
        audio_output=False,
        execute_function=call_llm,
        category="interaction",
    ),
    # 推销opk下：用于产品介绍、答疑、演示、推荐等销售话术，适用于一般产品推销、推荐。（推销兜底action）
    Action(
        namespace="orion.app.promote",
        name="GENERAL_SALES_SERVICE",
        name_for_llm="GENERAL_SALES_SERVICE",
        level="opk",
        desc="For product presentations, Q&A, demonstrations, and recommendations in sales mode; suitable for general product promotion or non-specific inquiries.",
        desc_chinese="推销模式下用于产品介绍、答疑、演示、推荐等销售话术，适用于一般产品推销、推荐。",
        display_name="说",
        en_display_name="Say",
        display_names=ACTION_DISPLAY_NAMES.get("GENERAL_SALES_SERVICE", {}),
        app_ids=(Opk_Promote_App_Id,),
        execute_side="both",
        execute_function=sales_pitch_v3,
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.SALES_PITCH",
    ),
    # 竞品信息
    Action(
        namespace="orion.app.promote",
        name="ANSWER_COMPETITOR_INQUIRY",
        name_for_llm="ANSWER_COMPETITOR_INQUIRY",
        level="opk",
        desc="Provide answers to explicit user inquiries regarding competitor products. Use only when the user directly requests information about competitors.",
        desc_chinese="回答用户明确询问的关于竞争对手产品的问题。如：“竞品产品介绍”，“竞品产品参数”，“竞品产品价格”等。",
        parameters=[
            Action.Parameter(
                name="competitor_production_name",
                type="enum",
                desc="The name of the competitor product the user is inquiring about.",
                enum_func=load_competitor_production_name,
            ),
        ],
        display_name="竞品信息",
        en_display_name="Competitive Information",
        display_names=ACTION_DISPLAY_NAMES.get("ANSWER_COMPETITOR_INQUIRY", {}),
        app_ids=(Opk_Promote_App_Id,),
        execute_side="server",
        execute_function=competitors_answer,
        pre_execute=True,
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.COMPETITOR_QUESTION",
    ),
    # 案例介绍
    Action(
        namespace="orion.app.promote",
        name="SHOW_PRODUCT_CASES",
        name_for_llm="SHOW_PRODUCT_CASES",
        level="opk",
        desc="Showcase real-world product application cases through videos or images. Use when users want to learn about actual product usage scenarios.",
        desc_chinese="通过视频或图片展示产品在实际场景中的应用案例。当用户想了解产品实际应用情况时使用。",
        parameters=[
            Action.Parameter(
                name="case",
                type="enum",
                desc="The specific application scenario to be showcased.",
                enum_func=load_case_name_v2,
            ),
        ],
        display_name="案例介绍",
        en_display_name="Case Introduction",
        display_names=ACTION_DISPLAY_NAMES.get("SHOW_PRODUCT_CASES", {}),
        app_ids=(Opk_Promote_App_Id,),
        execute_side="both",
        execute_function=case_introduction_v2,
        exported=False,
        pre_execute=True,
        result_schema=[
            Action.Result(
                name="title",
                type="string",
                desc="The title of the product function",
            ),
            Action.Result(
                name="url",
                type="string",
                desc="The url of the product function",
            ),
            Action.Result(
                name="type",
                type="enum",
                enum_constant=["image", "video"],
                desc="The type of media resource",
            ),
            Action.Result(
                name="text_content",
                type="page_info",
                desc="The text content of the product function",
            ),
        ],
        action_area=Area.domestic,
        category="product",
        client_alias="orion.app.promote.CASE_INTRODUCTION",
    ),
    # 室外导航 海外
    Action(
        name="OUTDOOR_NAVIGATE",
        name_for_llm="OUTDOOR_NAVIGATE",
        level="opk",
        desc="Provide outdoor navigation capability. Select this action when the user wants to know how to reach a place.",
        display_name="室外导航",
        en_display_name="Outdoor Navigation",
        display_names=ACTION_DISPLAY_NAMES.get("OUTDOOR_NAVIGATE", {}),
        client_alias="orion.agent.action.OPEN_WEB_URL",
        execute_timeout_limit=60,
        version="draft",
        parameters=[
            Action.Parameter(
                name="origin",
                type="string",
                desc="""Route origin, provided by the user. Set the value to "-1" if indeterminate.""",
            ),
            Action.Parameter(
                name="destination",
                type="string",
                desc="""Route destination. Provided by the user""",
            ),
            Action.Parameter(
                name="travelmode",
                type="enum",
                enum_constant=["driving", "walking", "bicycling", "transit"],
                desc="""Default to driving if not provided by the user.""",
            ),
        ],
        app_ids=(Launcher_App_Id, OverSea_Opk_Launcher_App_Id),
        action_area=Area.overseas,
        post_processing=overseas_map_dir_url,
        exported=True,
        category="navigation",
    ),
    # 推荐地点 海外
    Action(
        name="RECOMMEND_PLACES",
        name_for_llm="RECOMMEND_PLACES",
        level="opk",
        desc="Recommendations for various places, such as restaurants, attractions, shopping areas, bars, KTVs, subways, etc",
        display_name="推荐",
        en_display_name="Recommend",
        display_names=ACTION_DISPLAY_NAMES.get("RECOMMEND_PLACES", {}),
        execute_timeout_limit=60,
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        client_alias="orion.agent.action.OPEN_WEB_URL",
        version="draft",
        parameters=[
            Action.Parameter(
                name="target",
                type="string",
                desc="The target location that the user wants to go to",
            ),
            Action.Parameter(
                name="ref_location",
                type="string",
                desc="The starting point. Search based on this location. Don't use <Current Indoor Point>. If not specificed by user, set ref_location -1)",
            ),
        ],
        pre_execute=True,
        action_area=Area.overseas,
        post_processing=overseas_map_search_url,
        exported=True,
    ),
    # 地图点位引领： 带领用户前往指定位置
    Action(
        name="GUIDE_INDOOR_NAVIGATION",
        name_for_llm="GUIDE_INDOOR_NAVIGATION",
        level="global",
        desc="Point navigation intent. Users can only be taken to the locations provided below, and outdoor locations are not supported.If the user does not explicitly specify the destination, a list of up to 4 available navigation points is returned and sorted by similarity, with the closest point at the front.",
        desc_chinese="带领用户前往室内特定位置；结合对话历史和用户特征（如性别等），为用户精准推荐最符合其意图的地点。不支持室外导航！ 用户想要去室外时，请用`{clarify_action_name}`拒绝。".format(
            clarify_action_name=CLARIFY_ACTION_NAME.lower()
        ),
        display_name="准备领位",
        en_display_name="Indoor Navigation Recommendation",
        display_names=ACTION_DISPLAY_NAMES.get("GUIDE_INDOOR_NAVIGATION", {}),
        execute_timeout_limit=300,
        parameters=[
            Action.Parameter(
                name="destinations",
                type="String array",
                desc="Navigation points that match user intent. You can choose multiple. only choose from the list of points.",
                is_required=False,
                enum_func=load_robot_support_map_points,
                length=4,
            ),
            Action.Parameter(
                name="guide_text",
                type="string",
                desc="""A natural guiding response that varies based on the number of destinations:
    - For single destination: Direct guidance to that location
    - For multiple destinations: Present options and ask for user's choice
    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in'""",
            ),
        ],
        post_processing=convert_navigation_points,
        action_area=Area.all,
        exported=True,
        category="navigation",
        client_alias="orion.agent.action.NAVIGATE_REC_START",
    ),
    # action干预专用技能
    Action(
        name="OPEN_WEB_URL_DEFINED",
        name_for_llm="OPEN_WEB_URL_DEFINED",
        level="opk",
        display_name="打开轻应用",
        en_display_name="Open Light App",
        display_names=ACTION_DISPLAY_NAMES.get("OPEN_WEB_URL_DEFINED", {}),
        desc="Use a browser or app to browse information. Trigger this action if there's a semantically identical question in the predefined list, as it allows you to directly open the URL or app.",
        desc_chinese="使用浏览器或者APP浏览信息。*只要*预定义问题列表中存在与用户当前问题语义相同的问题，则选择此action。",
        app_ids=(Opk_Small_App_Id, OverSea_Opk_Small_App_Id),
        pre_execute=True,
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="predefined_question",
                type="enum",
                desc="list of predefined question.",
                enum_func=load_light_app_enum,
            ),
        ],
        client_alias="orion.agent.action.OPEN_WEB_URL",
        action_area=Area.all,
        exported=True,
        category="web_browser",
        only_intervention=True,
    ),
    # 获取天气信息
    Action(
        name="GET_WEATHER",
        name_for_llm="GET_WEATHER",
        level="opk",
        desc_chinese="获取天气信息。时间：「今天及未来10天」，地区：「中国及港澳台」，不支持查询国外天气！!!",
        desc="Get weather information for today and the next 10 days. City name must be in English.",
        display_name="获取天气信息",
        en_display_name="Get Weather Information",
        display_names=ACTION_DISPLAY_NAMES.get("GET_WEATHER", {}),
        execute_timeout_limit=180,
        parameters=[
            Action.Parameter(
                name="area_level",
                type="enum",
                enum_constant=["province", "city", "area"],
                desc="The level of the area corresponding to the city. province, city, or area.",
                is_required=False,
            ),
            Action.Parameter(
                name="valid_city_name",
                type="string",
                desc="Enter a valid city name (not a venue); defaults to the current city if not specified.",
                is_required=False,
            ),
        ],
        execute_side="both",
        execute_function=query_weather,
        post_processing=query_weather_post_processing,
        app_ids=(
            Opk_Weather_App_Id,
            OverSea_Opk_Launcher_App_Id,
            OverSea_Opk_Weather_App_Id,
        ),
        pre_execute=True,
        exported=True,
        category="weather",
        client_alias="orion.agent.action.WEATHER",
    ),
)


def singleton(class_):
    """
    A decorator that transforms a class into a singleton.
    The decorated class will only create one instance, always returning the same instance.
    """
    instances = {}

    def getinstance(*args, **kwargs):
        if class_ not in instances:
            instances[class_] = class_(*args, **kwargs)
        return instances[class_]

    return getinstance


@singleton
class ActionLib:
    def __init__(self):
        self._actions = []
        self._session = None

    def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self._session:
            self._session = http_context.http_session()
        return self._session

    def register_actions(self, actions: list[Action]):
        # deduplicate actions: source + name
        if not actions:
            return

        source = actions[0].source
        exist_source_action_names = [
            action.name for action in self._actions if action.source == source
        ]
        for action in actions:
            if action.name not in exist_source_action_names:
                self._actions.append(action)
            else:
                logger.warning(
                    f"[Register Action] Action: {action.name} {action.full_name} already exists in {source} {[action.full_name for action in self._actions if action.source == source]}"
                )

        logger.debug(f"Registered actions: {self._actions}")

    def unregister_actions(self, actions: list[Action]):
        if not actions:
            return

        logger.debug(f"Unregister actions: {actions}")
        for action in actions:
            self._actions.remove(action)
        logger.debug(f"Current actions: {self._actions}")

    def update_builtin_actions(self, builtin_actions: list[Action]):
        current_builtin_actions = [
            action for action in self._actions if action.source == "builtin"
        ]
        self.unregister_actions(current_builtin_actions)
        self.register_actions(builtin_actions)
        logger.debug(f"Updated builtin actions: {self._actions}")

    def update_app_actions(self, app_actions: list[Action]):
        current_app_actions = [
            action
            for action in self._actions
            if action.level == "app" and action.source == "report"
        ]
        self.unregister_actions(current_app_actions)
        self.register_actions(app_actions)
        logger.debug(f"Updated app actions: {self._actions}")

    def update_page_actions(self, page_actions: list[Action]):
        current_page_actions = [
            action
            for action in self._actions
            if action.level == "page" and action.source == "report"
        ]
        self.unregister_actions(current_page_actions)
        self.register_actions(page_actions)
        logger.debug(f"Updated page actions: {self._actions}")

    def update_mcp_actions(self, mcp_actions: list[Action]):
        current_mcp_actions = [
            action for action in self._actions if action.source == "mcp"
        ]
        self.unregister_actions(current_mcp_actions)
        self.register_actions(mcp_actions)
        logger.debug(f"Updated mcp actions: {self._actions}")

    def get_one_action(
        self,
        name: str = "",
        full_name: str = "",
        namespace: str = "",
        agent_id: str = "",
        source: str = "",
    ) -> Optional[Action]:
        logger.debug(
            f"name: {name}, full_name: {full_name}, namespace: {namespace}, agent_id: {agent_id}"
        )
        if name and source:  # 保证 name 在 source 中唯一
            for act in self._actions:
                if act.name.lower() == name.lower() and act.source == source:
                    return act

            for act in self._actions:
                # 匹配 alias, 优先级最后
                if act.client_alias:
                    alias_name = act.client_alias.split(".")[-1]
                    if alias_name.lower() == name.lower() and act.source == source:
                        return act

        for act in self._actions:
            if act.name.lower() in [
                name.lower(),
                full_name.lower(),
            ] or act.full_name.lower() in [
                name.lower(),
                full_name.lower(),
            ]:  # 兼容旧的action
                return act

        for act in self._actions:
            if (
                act.client_alias
                and act.client_alias.lower()
                in [  # 匹配 alias, 优先级最后
                    name.lower(),
                    full_name.lower(),
                ]
            ):
                return act

        logger.warning(
            f"Action namespace:{namespace} name:{name} agent_id:{agent_id} full_name:{full_name} not found"
        )
        return None

    async def load_support_action(
        self,
        parameter,
        use_candidate_actions_directly: bool = False,
        is_admin_mode: bool = False,
    ) -> list[dict]:
        from src.agent_core.models.model import AgentParameter
        from src.utils.language_utils import detect_language

        parameter: AgentParameter = parameter
        support_actions: list[dict] = []

        # 内置Action，必须在规划中包含
        # if (
        #     agent_setting.region_version == Area.domestic
        #     # and agent_setting.clarification_switch   在模型调用的时候控制是否加入澄清tool
        # ):
        # TODO: 在模型调用的时候控制是否加入澄清tool
        action = self.get_one_action(name=CLARIFY_ACTION_NAME)
        if not action:
            logger.error("Clarify action not found")
        else:
            # 对action进行拷贝，避免修改原始action
            action = action.model_copy(deep=True)
            support_actions.append(await action.convert_agent_prompt(parameter))

        logger.info(
            f"Load support action for query: {parameter.query} package_name: {parameter.robot.PACKAGE_NAME} app_id: {parameter.robot.APP_ID} agent_id: {parameter.robot.agent_id} use_candidate_actions_directly: {use_candidate_actions_directly}"
        )

        # 如果开启了admin模式，则只支持admin级别的action
        if is_admin_mode:
            for act in parameter.candidate_actions:
                if act.level == "admin":
                    support_actions.append(await act.convert_agent_prompt(parameter))
            return support_actions

        if use_candidate_actions_directly:  # 适配1.2客户端控制候选action：直接使用candidate_actions, 不再进行agent和opk的过滤
            for act in parameter.candidate_actions:
                support_actions.append(await act.convert_agent_prompt(parameter))
            return support_actions

        agent_config: Optional[AgentConfig] = AGENT_CONFIG.get(parameter.robot.agent_id)
        if not agent_config:
            logger.error(f"Agent config not found for {parameter.robot.agent_id}")
            await send_feishu_alarm(
                f"{parameter.robot.device_id} Failed to find opk_config agent_id: {parameter.robot.agent_id} app_id: {parameter.robot.APP_ID}"
            )

        agent_namespace = (
            agent_config.namespace
            if parameter.robot.agent_id != LAUNCHER_AGENT_ID and agent_config
            else ""
        )
        internal_opk: bool = (
            parameter.robot.APP_ID in agent_config.opks.keys()
            if agent_config
            else False
        )  # 内部OPK

        opk_config = None
        if internal_opk:
            opk_config: OpkConfig = AGENT_CONFIG.get(
                parameter.robot.agent_id, {}
            ).opks.get(parameter.robot.APP_ID)
        else:  # 外部OPK
            for _, agent_config in AGENT_CONFIG.items():
                opk_config: OpkConfig = agent_config.opks.get(parameter.robot.APP_ID)
                if opk_config:
                    break
        for act in parameter.candidate_actions:
            if act.hidden:
                continue

            if act.level == "admin":
                continue  # filter admin action directly

            if act.source in [
                "report",
                "mcp",
            ]:  # TODO: 后续只会存在report和mcp action，不再需要此函数
                support_actions.append(await act.convert_agent_prompt(parameter))
                continue
            # 单独对say action进行是否是多语言的操作，因为在plan阶段多语言设置目前只会对say有影响，其他的都在之后的函数中进行多语言的操作
            if is_multilingual(parameter.robot):
                detect_lang = detect_language(
                    parameter.query,
                    parameter.robot.language,
                    parameter.robot.multilingual,
                )

                if act.name.lower() in [
                    "GUIDE_INDOOR_NAVIGATION".lower(),
                    "NAVIGATE_REC_START".lower(),
                ]:
                    from src.common.constant import (
                        LANGUAGE_CODE_TO_ENGLISH_NAME,
                    )

                    for p in act.parameters:
                        if p.name == "guide_text" and "- Use" not in p.desc:
                            lang = LANGUAGE_CODE_TO_ENGLISH_NAME.get(
                                detect_lang, "Chinese"
                            )
                            p.desc = f"""{p.desc}
                - Use {lang}"""
            if act.level == "global":
                if agent_config:
                    agent_config_exclude_global_actions = [
                        fullname.lower()
                        for fullname in agent_config.exclude_global_actions
                    ]
                    if (
                        act.full_name.lower() in agent_config_exclude_global_actions
                        and internal_opk  # 过滤agent级别的global action， 仅该agent内部OPK过滤global action
                    ):
                        continue

                if opk_config:
                    opk_config_exclude_global_actions = [
                        fullname.lower()
                        for fullname in opk_config.exclude_global_actions
                    ]
                    if (
                        opk_config
                        and act.full_name.lower() in opk_config_exclude_global_actions
                    ):  # 过滤opk级别的global action
                        continue

                support_actions.append(await act.convert_agent_prompt(parameter))
                continue

            if parameter.robot.PACKAGE_NAME in act.package_names:
                if not act.app_ids:
                    support_actions.append(await act.convert_agent_prompt(parameter))
                    continue
                else:
                    if parameter.robot.APP_ID in act.app_ids:
                        support_actions.append(
                            await act.convert_agent_prompt(parameter)
                        )
                        continue

            if not opk_config:
                continue

            if agent_namespace and not internal_opk and opk_config.support_register:
                if (
                    act.namespace == agent_namespace
                ):  # agent模式下，外部opk，如果支持外部注册action，则自动添加agent下所有action
                    support_actions.append(await act.convert_agent_prompt(parameter))
                    continue

            if act.exported:
                agent_config_support_exported_actions = [
                    fullname.lower()
                    for fullname in agent_config.support_exported_actions
                ]
                opk_config_support_exported_actions = [
                    fullname.lower() for fullname in opk_config.support_exported_actions
                ]
                if (
                    agent_config.support_register
                ):  # 支持外部namespace所有注册的外部action
                    if opk_config.support_register:  # 支持跨OPK的所有注册Action
                        support_actions.append(
                            await act.convert_agent_prompt(parameter)
                        )
                    else:
                        if act.full_name.lower() in opk_config_support_exported_actions:
                            support_actions.append(
                                await act.convert_agent_prompt(parameter)
                            )
                elif (
                    act.full_name.lower() in agent_config_support_exported_actions
                ):  # 支持外部action
                    if opk_config.support_register:
                        support_actions.append(
                            await act.convert_agent_prompt(parameter)
                        )
                    else:
                        if act.full_name in opk_config.support_exported_actions:
                            support_actions.append(
                                await act.convert_agent_prompt(parameter)
                            )

        if not support_actions:
            logger.error(
                f"{parameter.robot.device_id} Failed to load actions agent_id: {parameter.robot.agent_id} app_id: {parameter.robot.APP_ID} candidate_actions: {parameter.candidate_actions}"
            )
            await send_feishu_alarm(
                f"{parameter.robot.device_id} Failed to load actions agent_id: {parameter.robot.agent_id} app_id: {parameter.robot.APP_ID} candidate_actions: {parameter.candidate_actions}"
            )
        return support_actions

    @ttl_cache(ttl=60 * 10)
    async def fetch_block_actions_from_studio(self, enterprise_id: str) -> List[Dict]:
        """从studio获取block action"""
        session = self._ensure_session()
        try:
            async with session.get(
                urljoin(
                    agent_setting.aos_studio_host,
                    agent_setting.aos_studio_block_action_url,
                ),
                headers={"x-origws-c-c-ov-corp-id": enterprise_id},
            ) as response:
                if response.status != 200:
                    err_msg = f"Failed to fetch block actions from studio: {response.status} {enterprise_id}"
                    logger.error(err_msg)
                    await send_feishu_alarm(err_msg)
                    return []

                result = await response.json()
                block_actions = result.get("data", [])

                logger.info(
                    f"Fetch block actions from studio: {response.status} {enterprise_id} {block_actions}"
                )
                return [
                    StudioBlockActionResult(**action).model_dump()
                    for action in block_actions
                    if action["is_block"]
                ]
        except asyncio.CancelledError:
            raise
        except Exception as e:
            err_msg = f"Failed to fetch block actions from studio: {e} {enterprise_id}"
            logger.error(err_msg)
            await send_feishu_alarm(err_msg)
            return []


class ActionFunction:
    @classmethod
    def get_execute_function(cls, action_name: str, **kwargs) -> Optional[Callable]:
        if action := ActionLib().get_one_action(full_name=action_name):
            return action.execute_function
        return None


def __reload_action(build_draft_action: bool = True, exclude_actions: list[str] = []):
    import json
    from pathlib import Path

    exclude_actions = set(exclude_actions or [])
    exclude_actions.add("orion.agent.action.WEATHER_GET".lower())
    exclude_actions.add("orion.agent.action.WEATHER_GET_REALTIME".lower())
    exclude_actions.add("orion.agent.action.NOT_MOVE".lower())
    exclude_actions.add(
        "orion.agent.action.NAVIGATE_START".lower(),
    )

    if build_draft_action:
        # Constants
        VERSIONS = {Area.domestic: "draft", Area.overseas: "oversea_draft"}
        current_path = Path(__file__).parent
        resources_path = current_path / "action_version" / "resources"
        resources_path.mkdir(parents=True, exist_ok=True)

        for region_version, version in VERSIONS.items():
            region_actions = []
            for action in ACTIONS:
                # if action.hidden:
                #     continue

                if action.full_name in exclude_actions:
                    continue

                if action.action_area not in (region_version, "all"):
                    continue

                # 创建一个新的字典用于序列化
                action_dict = {}

                # 将函数字段转换为函数名（字符串）
                action_model_dict = action.model_dump(
                    exclude={"execute_function", "post_processing"}
                )
                action_dict.update(action_model_dict)

                if action.execute_function:
                    action_dict["execute_function"] = action.execute_function.__name__

                if action.post_processing:
                    action_dict["post_processing"] = action.post_processing.__name__

                # 处理parameters中的enum_func
                if "parameters" in action_dict and action_dict["parameters"]:
                    for i, parameter in enumerate(action.parameters):
                        if parameter.enum_func:
                            action_dict["parameters"][i]["enum_func"] = (
                                parameter.enum_func.__name__
                            )
                if (
                    action.name == "GO_CHARGING" and region_version == Area.overseas
                ):  # 海外版GO_CHARGING改为global
                    action_dict["level"] = "global"

                action_dict["version"] = version
                region_actions.append(action_dict)

            config_path = resources_path / f"{version}.json"
            action_config = {"version": version, "actions": region_actions}

            with config_path.open("w", encoding="utf-8") as f:
                json.dump(action_config, f, indent=4, ensure_ascii=False)
                logger.info(
                    f"[{version}] Action config saved to {config_path}, Action Count: {len(region_actions)}"
                )
    logger.info("Action functions reloaded successfully")


if __name__ == "__main__":
    __reload_action(
        exclude_actions=[
            "orion.agent.action.WEATHER_GET".lower(),
            "orion.agent.action.WEATHER_GET_REALTIME".lower(),
            "orion.agent.action.NAVIGATE_START".lower(),
            "orion.agent.action.NOT_MOVE".lower(),
        ]
    )
