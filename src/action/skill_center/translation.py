import time
import aiohttp
from typing import Dict, Any
from loguru import logger
from src.settings import agent_setting
from datetime import datetime


class TranslationService:
    """翻译服务类，实现单例模式，支持 access_token 动态获取与自动刷新"""

    _instance = None
    _api_url = agent_setting.baidu_translate_api_url
    _access_token = None
    _token_expire_time = 0  # 时间戳
    _max_text_length = 2000  # 最大文本长度限制
    _api_key = agent_setting.baidu_translate_api_key
    _secret_key = agent_setting.baidu_translate_secret_key

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TranslationService, cls).__new__(cls)
        return cls._instance

    async def _get_access_token(self, force_refresh=False):
        now = int(time.time())
        if (
            not force_refresh
            and self._access_token
            and now < self._token_expire_time - 60
        ):
            return self._access_token
        # 获取新token
        url = (
            f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials"
            f"&client_id={self._api_key}&client_secret={self._secret_key}"
        )
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, timeout=2, ssl=False) as resp:
                    resp.raise_for_status()
                    data = await resp.json()
                    if "access_token" in data and "expires_in" in data:
                        self._access_token = data["access_token"]
                        self._token_expire_time = now + int(data["expires_in"])
                        expire_date = datetime.fromtimestamp(self._token_expire_time)
                        days_remaining = (expire_date - datetime.now()).days
                        logger.info(
                            f"access_token 将在 {expire_date.strftime('%Y-%m-%d %H:%M:%S')} 过期，还有 {days_remaining} 天"
                        )
                        return self._access_token
                    else:
                        logger.error(f"获取 access_token 失败: {data}")
                        return None
        except Exception as e:
            logger.error(f"请求 access_token 失败: {e}")
            return None

    async def translate(self, text: str, dest: str = "zh", src: str = "auto") -> str:
        """
        翻译文本，自动处理 access_token 过期与刷新
        """
        if not text or not isinstance(text, str) or text.strip() == "":
            return text

        # 检查文本长度
        if len(text) > self._max_text_length:
            text = text[: self._max_text_length]

        # 确保 access_token 有效
        token = await self._get_access_token()
        if not token:
            return text

        url = f"{self._api_url}?access_token={token}"
        payload = {"q": text, "from": src, "to": dest}
        logger.debug(f"请求URL: {url}")
        logger.debug(f"请求参数: {payload}")

        async def do_request(request_url):
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    request_url,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=2,
                    ssl=False,
                ) as response:
                    logger.debug(f"响应状态码: {response.status}")
                    response_text = await response.text()
                    logger.debug(f"响应内容: {response_text}")
                    return response

        try:
            response = await do_request(url)
            try:
                result = await response.json()
            except Exception as e:
                logger.error(f"解析API响应失败: {str(e)}")
                return text

            # token 过期处理
            if "error_code" in result and result["error_code"] in [110, 111, 1101]:
                logger.warning(f"access_token 过期或无效，尝试刷新后重试: {result}")
                token = await self._get_access_token(force_refresh=True)
                if not token:
                    logger.error("刷新 access_token 失败")
                    return text
                retry_url = f"{self._api_url}?access_token={token}"
                response = await do_request(retry_url)
                try:
                    result = await response.json()
                except Exception as e:
                    logger.error(f"解析API响应失败: {str(e)}")
                    return text
                if "error_code" in result:
                    logger.error(f"百度翻译API错误: {result}")
                    return text

            if "error_code" in result:
                error_msg = result.get("error_msg", "未知错误")
                error_code = result.get("error_code", "未知错误码")
                logger.error(
                    f"百度翻译API错误: error_code={error_code}, error_msg={error_msg}"
                )
                return text

            translated_text = result["result"]["trans_result"][0]["dst"]
            logger.info(f"翻译结果: {translated_text}")
            return translated_text

        except aiohttp.ClientError as e:
            logger.error(f"翻译请求失败: {str(e)}")
            return text
        except Exception as e:
            logger.error(f"翻译出错: {str(e)}")
            return text


# 创建全局翻译服务实例
translation_service = TranslationService()


async def handle_translation(
    text: str, dest: str = "zh", src: str = "auto"
) -> Dict[str, Any]:
    translated_text = await translation_service.translate(text, dest, src)
    return {
        "text": translated_text,
        "original": text,
        "source_lang": src,
        "target_lang": dest,
    }


if __name__ == "__main__":
    import asyncio

    text = "Hi, I'm planning a two-week trip to Japan in the spring. Could you recommend some must-see destinations and tips for first-time travelers?"
    result = asyncio.run(translation_service.translate(text))
    print(result)
