import asyncio
import datetime
import json
from urllib.parse import urljoin

import aiohttp
from loguru import logger

from src.common.constant import Area, get_weather_lang
from src.common.enums import AreaLevel
from src.settings import agent_setting
from src.action.skill_center.translation import handle_translation
from src.utils.feishu_alarm import send_feishu_alarm
from src.utils.language_utils import detect_language


def get_domestic_weather_req(city, area_level, time):
    today = datetime.datetime.now()
    request_body = {
        "request": {
            "asr": {"text": f"查询{city}的{time}天气"},
            "nlu": {
                "domain": "天气",
                "english_domain": "weather",
                "intent": "get_weather",
                "query": f"查询{city}的{time}天气",
                "slots": {
                    "geo": [
                        {
                            "dict_name": "SEMANTIC_TAG_CHUNK_LOCATION",
                            "slot_id": 0,
                            "slot_type": "GEO",
                            "text": city,
                            "value": [
                                {
                                    "level": area_level,
                                    "poi_text": city,
                                }
                            ],
                        }
                    ],
                    "time": [
                        {
                            "dict_name": "SEMANTIC_TAG_CHUNK_TIME",
                            "slot_name": "time",
                            "slot_type": "time",
                            "text": time,
                            "value": {
                                "begin": {
                                    "abs": today.timestamp(),
                                    "date": {
                                        "day": today.day,
                                        "month": today.month,
                                        "year": today.year,
                                    },
                                },
                                "sub_type": 0,
                                "type": 1,
                            },
                        }
                    ],
                },
            },
            "type": "IntentRequest",
        },
        "session": {
            "application": {"client": {"clientId": "orion.ovs.client.1597807388298"}},
            "sessionId": "ef98487c-ba38-4d5f-81f2-16e2fc63c1e7_orion_test_http_sn",
            "user": {
                "isLogin": True,
                "openId": "b137b951209b65a4e8437d53bcb9a8aa",
                "userId": "127367",
            },
        },
    }
    return request_body


def get_oversea_weather_res(response_json):
    current = response_json["current"]
    location = response_json["location"]
    forecast = response_json["forecast"]["forecastday"]
    localtime_day = forecast[0]["date"]

    # 当前最小温度
    mintemp_c = forecast[0]["day"]["mintemp_c"]
    # 最大温度
    maxtemp_c = forecast[0]["day"]["maxtemp_c"]
    uv = forecast[0]["day"]["uv"]
    daily_chance_of_rain = forecast[0]["day"]["daily_chance_of_rain"]
    wind_degree = current["wind_degree"]

    current_wind_mph = current.get("wind_mph", -1)
    weatherPredicts = []
    for index, element in enumerate(forecast):
        if index == 0:
            continue
        day_element = element["day"]
        predicts = {
            "tempLow": day_element["mintemp_c"],
            "tempHigh": day_element["maxtemp_c"],
            "maxwind_mph": day_element["maxwind_mph"],
            "weatherPic": day_element["condition"]["icon"],
            "weatherCategory": day_element["condition"]["text"],
            "condition_code": day_element["condition"]["code"],
            "uv": day_element["uv"],
            "date": element["date"],
            "daily_chance_of_snow": day_element["daily_chance_of_snow"],
            "wind_mph": day_element["maxwind_mph"],
        }
        weatherPredicts.append(predicts)

    overseas_result = {
        "version": "5.5.5",
        "response": {
            "skillData": {},
            "directives": [],
            "display": {
                "type": "DISPLAY.WEATHER",
                "data": {
                    "queryDate": localtime_day,
                    "location": {
                        "province": location["region"],
                        "city": location["name"],
                        "area": "",
                        "country": location["country"],
                    },
                    "weatherNow": {
                        "windDirection": current["wind_dir"],
                        "tempLow": mintemp_c,
                        "uv": uv,
                        "daily_chance_of_rain": daily_chance_of_rain,
                        "tempHigh": maxtemp_c,
                        "wind_degree": wind_degree,
                        "wind_mph": current_wind_mph,
                        "weatherPic": current["condition"]["icon"],
                        "aqiPic": "",
                        "weatherCategory": current["condition"]["text"],
                        "condition_code": current["condition"]["code"],
                        "humidity": current["humidity"],
                        "tempNow": current["temp_c"],
                        "totalprecip_mm": forecast[0]["day"]["totalprecip_mm"],
                        "vis_km": current["vis_km"],
                        "pm10": current["air_quality"]["pm10"],
                        "pm25": current["air_quality"]["pm2_5"],
                    },
                    "weatherPredicts": weatherPredicts,
                },
            },
            "card": {
                "popup": "",
                "ad": "",
                "text": "",
                "ui": [
                    {
                        "skillIcon": {
                            "image": "http://ovs.ainirobot.com/storge/upload/pic/163ceb95068fc2be41c283a081bfaa59.png",
                            "name": "天气",
                        },
                        "bg": {
                            "color": "",
                            "image": current["condition"]["icon"],
                            "virtual": False,
                        },
                        "type": "CustomWeatherNow",
                        "attr": "",
                        "custom": {
                            "daily_chance_of_rain": forecast[0]["day"][
                                "daily_chance_of_rain"
                            ],
                            "nowTemp": current["temp_c"],
                            "aqiPic": current["condition"]["icon"],
                            "highTemp": maxtemp_c,
                            "lowTemp": mintemp_c,
                            "totalprecip_mm": forecast[0]["day"]["totalprecip_mm"],
                            "desc": "",
                        },
                    }
                ],
                "order": "",
                "linkAccount": "",
            },
            "outSpeech": {"text": "", "textBGM": "", "type": "text"},
        },
    }
    return overseas_result


async def get_future_weather_info(
    city,
    robot_language: str = "zh_CN",
    area_level: str = AreaLevel.CITY.value,
    time_days: str = "",
    env: str = Area.overseas,
):
    """
    获取未来天气信息

    参数:
        city: 城市名称
        area_level: 区域级别，默认为城市级别
        time: 时间描述，例如"未来10天"
        env: 环境，domestic表示国内，overseas表示海外

    返回:
        dict: 天气信息的JSON响应，如果请求失败则返回空字典

    异常:
        不抛出异常，但在请求失败时会记录错误并返回空字典
    """
    try:
        if env == Area.domestic:
            return await _get_domestic_future_weather(city, area_level, time_days)
        else:
            return await _get_overseas_future_weather(city, robot_language)
    except Exception as e:
        logger.error(
            f"获取{'国内' if env == Area.domestic else '海外'}未来天气信息失败: {str(e)}"
        )
        return {}


async def _get_domestic_future_weather(city, area_level, time):
    """获取国内未来天气信息"""
    try:
        # 检测城市名称语言并翻译为中文
        detected_lang = detect_language(city, "zh_CN", 1)
        if detected_lang and detected_lang not in ["zh-CN", "zh_CN"]:
            try:
                translation_result = await handle_translation(
                    city, dest="zh", src="auto"
                )
                city = translation_result["text"]
                logger.info(f"城市名称已翻译为中文: {city}")
            except Exception as e:
                logger.error(f"城市名称翻译失败: {e}")
                # 如果翻译失败，继续使用原始城市名称
                pass
        domestic_req = get_domestic_weather_req(city, area_level, time)
        logger.info(
            f"国内未来天气请求参数: {json.dumps(domestic_req, indent=2, ensure_ascii=False)}"
        )

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=agent_setting.get_weather_url, json=domestic_req
            ) as response:
                if response.status != 200:
                    error_msg = f"weather调用异常，地址: {agent_setting.get_weather_url}, status: {response.status}"
                    await send_feishu_alarm(error_msg)
                    return {}

                return await response.json()
    except Exception as e:
        logger.error(f"获取国内未来天气信息失败: {str(e)}")
        return {}


async def _get_overseas_future_weather(city, robot_language):
    """获取海外未来天气信息"""
    parameters = {
        "q": city,
        "key": agent_setting.overseas_weather_api_key,
        "days": agent_setting.overseas_weather_api_future_days,
        "aqi": "yes",
        "lang": get_weather_lang(robot_language),
    }
    logger.info(f"海外未来天气请求参数: {parameters}")

    url = urljoin(
        agent_setting.overseas_weather_api_url,
        agent_setting.overseas_weather_api_future_path,
    )

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url=url, params=parameters) as response:
                if response.status != 200:
                    error_msg = f"overseas_get_future_weather 调用异常，地址: {url}, status: {response.status}"
                    await send_feishu_alarm(error_msg)
                    return {}

                response_json = await response.json()
                if not response_json or "forecast" not in response_json:
                    logger.error(f"海外未来天气响应格式错误: {response_json}")
                    return {}

                return get_oversea_weather_res(response_json)
    except Exception as e:
        logger.error(f"海外未来天气请求失败: {str(e)}")
        return {}


async def future_weather_chat(
    city: str = "", robot_language: str = "zh_CN", area_level: str = "", time_days: str = "未来10天", env=Area.overseas
):
    result = await get_future_weather_info(city, robot_language, area_level, time_days, env=env)
    return result


async def get_realtime_weather_chat(city: str,robot_language: str,  env: str):
    city_weather = await get_realtime_weather_info(city, robot_language, env)
    return city_weather


async def get_realtime_weather_info(city: str, robot_language: str, env: str):
    """
    获取实时天气信息

    参数:
        city: 城市名称
        env: 环境，domestic表示国内，overseas表示海外

    返回:
        dict: 天气信息的JSON响应，如果请求失败则返回空字典

    异常:
        不抛出异常，但在请求失败时会记录错误并返回空字典
    """
    try:
        if env == Area.overseas:
            return await _get_overseas_realtime_weather(city,robot_language)
        else:
            return await _get_domestic_realtime_weather(city)
    except Exception as e:
        logger.error(
            f"获取{'海外' if env == Area.overseas else '国内'}实时天气信息失败: {str(e)}"
        )
        return {}


async def _get_overseas_realtime_weather(city: str,robot_language: str):
    """获取海外实时天气信息"""
    parameters = {
        "q": city,
        "key": agent_setting.overseas_weather_api_key,
        "lang": get_weather_lang(robot_language),
    }
    url = urljoin(
        agent_setting.overseas_weather_api_url,
        agent_setting.overseas_weather_api_current_path,
    )

    async with aiohttp.ClientSession() as session:
        async with session.get(url=url, params=parameters) as response:
            if response.status != 200:
                error_msg = f"overseas_get_realtime_weather 调用异常，地址: {url}, status: {response.status}"
                await send_feishu_alarm(error_msg)
                return {}

            response_json = await response.json()
            logger.info(
                f"海外实时天气请求参数: {parameters}, 响应状态: {response.status}"
            )

            if "current" not in response_json:
                logger.error(f"海外实时天气响应格式错误: {response_json}")
                return {}

            return response_json["current"]


async def _get_domestic_realtime_weather(city: str):
    """获取国内实时天气信息"""
    try:
        # 检测城市名称语言并翻译为中文
        detected_lang = detect_language(city, "zh_CN", 1)
        if detected_lang and detected_lang not in ["zh-CN", "zh_CN"]:
            try:
                translation_result = await handle_translation(
                    city, dest="zh", src="auto"
                )
                city = translation_result["text"]
                logger.info(f"城市名称已翻译为中文: {city}")
            except Exception as e:
                logger.error(f"城市名称翻译失败: {e}")
                # 如果翻译失败，继续使用原始城市名称
                pass
        parameters = {
            "city": city,
            "extensions": "base",
            "key": "98dad5097ed619cfe5bb71a3287cf27c",
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(
                url=agent_setting.get_weather_info_url, params=parameters
            ) as response:
                if response.status != 200:
                    error_msg = f"get_realtime_weather 调用异常，地址: {agent_setting.get_weather_info_url}, status: {response.status}"
                    await send_feishu_alarm(error_msg)
                    return {}

                response_json = await response.json()
                logger.info(
                    f"国内实时天气请求参数: {parameters}, 响应状态: {response.status}"
                )

                # 检查响应状态和数据有效性
                if response_json.get("status") != "1" or not response_json.get("lives"):
                    logger.warning(f"国内实时天气响应无效数据: {response_json}")
                    return {}

                return response_json["lives"][0]
    except Exception as e:
        logger.error(f"获取国内实时天气信息失败: {str(e)}")
        return {}


if __name__ == "__main__":
    # 运行异步函数
    # # 获得未来的天气信息
    city = "Beijing"
    # city = "北京"
    area_level = "city"
    env = "oversea"
    user_question = "Beijing's weather for the next 3 days"
    # asyncio.run(get_future_weather_info(city=city, area_level=area_level, env=env))
    from src.session_manager.robot import Robot

    root = Robot()
    # root.region_version = "domestic"
    kwargs = {
        "__robot": root,
        # 其他你想传递的参数
    }

    result = asyncio.run(future_weather_chat(city=city, area_level=area_level, env=env))

    # 打印或使用返回结果
    logger.debug(f"===返回结果:{result}===")

    # # 获得实时的天气信息
    # user_question = "今天北京的天气怎么样"
    # # city = "Beijing"
    # # city = "Beijing"
    # env = "oversea"
    # city = "48.8567,2.3508"
    # root.geo_location.latitude = 48.8567
    # root.geo_location.longitude = 2.3508
    # # asyncio.run(get_realtime_weather_info(city, env))
    # # asyncio.run(get_realtime_weather_chat(city=city, __robot=root))
    # # 将asyncio.run的结果赋值给一个变量
    # result = asyncio.run(get_realtime_weather_chat(city=city, env=env))
    #
    # # 打印或使用返回结果
    # logger.debug(f"===返回结果:{result}===")
