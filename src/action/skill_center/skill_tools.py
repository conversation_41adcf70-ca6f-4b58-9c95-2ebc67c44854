import asyncio
import time
from typing import Optional

import aiohttp
from livekit.agents.utils import http_context
from loguru import logger

from src.action.skill_center.calendars import get_calendar_data
from src.action.skill_center.weather import (
    future_weather_chat,
    get_realtime_weather_chat,
)
from src.common.constant import Area
from src.session_manager.robot import Robot


class SkillCenter:
    def __init__(self):
        """初始化技能中心"""
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = http_context.http_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口，确保会话被关闭"""
        if self.session:
            await self.session.close()

    async def get_future_weather(
        self,
        city: str,
        robot_language: str = "zh_CN",
        area_level: str = "",
        time_days: str = "未来10天",
        env: str = Area.overseas,
    ):
        """获取未来天气信息

        Args:
            city: 城市名或经纬度，例如: 城市名:Beijing  经纬度:48.8567,2.3508（48.8567是纬度，2.3508是经度，这个地址是巴黎的经纬度）
            area_level: 地区级别，默认为空字符串  （仅国内环境需要，国外环境不需要）
            time: 时间范围，默认为"未来10天"  （仅国内环境需要，国外环境不需要）
            env: 环境设置，默认为海外环境

        Returns
            dict: 天气信息结果字典，根据区域版本返回不同结构：
                国内环境: 返回国内天气API的原始响应
                海外环境: 返回包含以下结构的字典：
                    {
                        "version": "版本号",
                        "response": {
                            "skillData": {},
                            "directives": [],
                            "display": {
                                "type": "DISPLAY.WEATHER",
                                "data": {
                                    "queryDate": "查询日期",
                                    "location": {
                                        "province": "省/州",
                                        "city": "城市",
                                        "area": "",
                                        "country": "国家"
                                    },
                                    "weatherNow": {
                                        "windDirection": "风向",
                                        "tempLow": "最低温度",
                                        "uv": "紫外线指数",
                                        "daily_chance_of_rain": "降雨概率",
                                        "tempHigh": "最高温度",
                                        "wind_degree": "风向角度",
                                        "weatherPic": "天气图标URL",
                                        "aqiPic": "",
                                        "weatherCategory": "天气类型描述",
                                        "humidity": "湿度",
                                        "tempNow": "当前温度",
                                        "totalprecip_mm": "降水量(mm)",
                                        "vis_km": "能见度(km)"
                                    },
                                    "weatherPredicts": [
                                        {
                                            "tempLow": "最低温度",
                                            "tempHigh": "最高温度",
                                            "maxwind_mph": "最大风速",
                                            "weatherPic": "天气图标URL",
                                            "weatherCategory": "天气类型描述",
                                            "uv": "紫外线指数",
                                            "date": "日期",
                                            "daily_chance_of_snow": "降雪概率"
                                        },
                                        ...
                                    ]
                                }
                            },
                            "card": {...},
                            "outSpeech": {...}
                        }
                    }
        """
        start_time = time.time()
        logger.info(f"开始获取未来天气信息，城市: {city}, 环境: {env}")
        result = await future_weather_chat(
            city=city, robot_language=robot_language, area_level=area_level, time_days=time_days, env=env
        )
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(
            f"获取未来天气信息完成，耗时: {elapsed_time:.3f}秒，城市: {city}, 环境: {env}，结果：{result}"
        )

        return result

    async def get_realtime_weather(self, city: str, robot_language: str, env: str):
        """获取天气信息

        该函数通过调用future_weather_chat获取指定城市的天气预报信息，并结合用户问题生成回答。

        Args:
            city: 城市名或经纬度，例如: 城市名:Beijing  经纬度:48.8567,2.3508（48.8567是纬度，2.3508是经度，这个地址是巴黎的经纬度） 北京经纬度:39.9042,116.4074
            env: 环境设置，可选值为 Area.domestic（国内环境）或 Area.overseas（海外环境）
        Returns:
            dict: 实时天气信息字典，根据区域版本返回不同结构：
                 国内环境: 返回高德地图天气API的响应，结构如下：
                 {
                     "province": "省份",
                     "city": "城市",
                     "adcode": "区域编码",
                     "weather": "天气状况",
                     "temperature": "温度",
                     "winddirection": "风向",
                     "windpower": "风力",
                     "humidity": "湿度",
                     "reporttime": "数据发布时间"
                 }
             海外环境: 返回WeatherAPI的current数据，结构如下：
                 {
                     "last_updated_epoch": 更新时间戳,
                     "last_updated": "最后更新时间",
                     "temp_c": 摄氏温度,
                     "temp_f": 华氏温度,
                     "is_day": 是否白天,
                     "condition": {
                         "text": "天气状况描述",
                         "icon": "天气图标URL",
                         "code": 天气代码
                     },
                     "wind_mph": 风速(英里/小时),
                     "wind_kph": 风速(公里/小时),
                     "wind_degree": 风向角度,
                     "wind_dir": "风向",
                     "pressure_mb": 气压(百帕),
                     "pressure_in": 气压(英寸),
                     "precip_mm": 降水量(毫米),
                     "precip_in": 降水量(英寸),
                     "humidity": 湿度,
                     "cloud": 云量,
                     "feelslike_c": 体感温度(摄氏),
                     "feelslike_f": 体感温度(华氏),
                     "vis_km": 能见度(公里),
                     "vis_miles": 能见度(英里),
                     "uv": 紫外线指数,
                     "gust_mph": 阵风(英里/小时),
                     "gust_kph": 阵风(公里/小时)
                 }
        """

        city_weather = await get_realtime_weather_chat(city=city, robot_language=robot_language, env=env)
        return city_weather

    async def get_calendar(
        self,
        user_question: str,
        env: str,
        timezone: str,
        language: str,
        geo_location: str = "",
    ):
        """获取日历信息

        Args:
            user_question: 用户问题
            **kwargs: 额外参数

        Returns:
            FunctionResult: 包含日历信息的结果
        """
        result = await get_calendar_data(
            user_question=user_question,
            env=env,
            timezone=timezone,
            language=language,
            geo_location=geo_location,
        )

        return result

    @classmethod
    async def create(cls) -> "SkillCenter":
        """创建SkillCenter实例的工厂方法"""
        self = cls()
        self.session = http_context.http_session()
        return self

    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            self.session = None


if __name__ == "__main__":
    sc = SkillCenter()
    # city = "Beijing"
    # # city = "北京"
    # area_level = "city"
    # env = "1"
    # user_question = "Beijing's weather for the next 3 days"
    #
    root = Robot()
    # root.region_version = "domestic"
    kwargs = {
        "__robot": root,
    }
    # result = asyncio.run(
    #     sc.get_future_weather(user_question=user_question, city=city, area_level=area_level, env=env, __robot=root))
    #
    # # 打印或使用返回结果
    # logger.debug(f"返回结果:{result}", )
    # # 可以单独访问text部分
    # logger.debug(f"回答文本:{result['text']}")
    # # 或者访问完整的result部分
    # logger.debug(f"完整结果结构:{result['result']}")

    user_question = "今天北京的天气怎么样"
    city = "Beijing"
    env = "overseas"
    # asyncio.run(get_realtime_weather_info(city, env))
    # asyncio.run(get_realtime_weather_chat(user_question=user_question, city=city, __robot=root))
    # 将asyncio.run的结果赋值给一个变量
    result = asyncio.run(sc.get_realtime_weather(city=city, env=env))

    # 打印或使用返回结果
    logger.debug(f"===返回结果:{result}===")
    # 可以单独访问text部分（模型生成的回答）
    # logger.debug(f"回答文本:{result['text']}")
    # # 或者访问完整的result部分（天气信息）
    # logger.debug(f"天气信息:{result['result']}")
