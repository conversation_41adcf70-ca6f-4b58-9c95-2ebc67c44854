import datetime
import json
import re
import time

import aiohttp
from loguru import logger

from src.action.skill_center.translation import handle_translation
from src.common.constant import Area
from src.settings import agent_setting
from src.utils.feishu_alarm import send_feishu_alarm
from src.utils.i18n import _
from src.utils.language_utils import detect_language
from src.utils.llm import parse_output_to_dict


def parse_llm_response(response_text, model_name="openai"):
    """
    从大模型响应中提取JSON内容
    """
    if model_name == "qwen-max":
        try:
            json_obj = json.loads(response_text)
            return json_obj
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
    else:
        # 使用正则表达式提取JSON部分
        json_pattern = r"```json\n(.*?)\n```"
        match = re.search(json_pattern, response_text, re.DOTALL)

        if match:
            json_str = match.group(1)
            # 解析JSON字符串为Python对象
            try:
                json_obj = json.loads(json_str)
                return json_obj
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                return None
        else:
            print("未找到JSON内容")
            return None


async def get_calendar_data(
    user_question,
    env=Area.overseas,
    timezone="Asia/Shanghai",
    language="en_US",
    geo_location="",
):
    """获取日历数据

    Args:
        user_question: 用户问题
        env: 环境设置，默认为海外环境

    Returns:
        dict: 包含日历信息的响应
    """
    # 国内环境处理逻辑
    if env == Area.domestic:
        try:
            # 检测语言
            detected_lang = detect_language(user_question, "zh_CN", 1)
            # 如果不是中文，则翻译成中文
            if detected_lang and detected_lang not in ["zh-CN", "zh_CN"]:
                try:
                    translation_result = await handle_translation(
                        user_question, dest="zh", src="auto"
                    )
                    user_question = translation_result["text"]
                    logger.debug(f"翻译结果: {user_question}")
                except Exception as e:
                    logger.error(f"翻译错误: {e}")
                    # 如果翻译失败，继续使用原始问题
                    pass
        except Exception as e:
            logger.error(f"Language detection error: {e}")
            # 如果语言检测失败，继续使用原始问题
            pass

        # 调用国内日历接口
        return await _get_domestic_calendar_data(user_question)
    else:
        # 海外环境直接调用海外日历接口
        return await _get_overseas_calendar_data(
            user_question, timezone, language, geo_location=geo_location
        )


async def _get_domestic_calendar_data(user_question):
    """获取国内环境的日历数据"""
    request_body = {"request": {"asr": {"text": user_question}}}

    async with aiohttp.ClientSession() as session:
        async with session.post(
            url=agent_setting.calendar_url, json=request_body
        ) as response:
            if response.status != 200:
                await send_feishu_alarm(
                    f"calendar 接口调用异常，地址：{agent_setting.calendar_url}, status: {response.status}"
                )
                return {
                    "skill_response": {},
                    "version": "",
                    "desc": {
                        "text_output": "抱歉，日历服务返回数据格式异常，请稍后再试。",
                        "display": {},
                        "query": user_question,
                    },
                    "response": {
                        "outSpeech": {
                            "type": "text",
                            "text": "抱歉，日历服务返回数据格式异常，请稍后再试。",
                        },
                        "display": {},
                    },
                }
            try:
                result = await response.json(content_type="text/html")
                logger.debug(
                    f"===国内日历的请求参数:{user_question},返回结果: {result}==="
                )
                return result
            except Exception as e:
                error_msg = f"calendar 接口返回数据解析异常: {str(e)}"
                await send_feishu_alarm(error_msg)
                logger.error(error_msg)
                return {
                    "skill_response": {},
                    "version": "",
                    "desc": {
                        "text_output": "抱歉，日历服务返回数据格式异常，请稍后再试。",
                        "display": {},
                        "query": user_question,
                    },
                    "response": {
                        "outSpeech": {
                            "type": "text",
                            "text": "抱歉，日历服务返回数据格式异常，请稍后再试。",
                        },
                        "display": {},
                    },
                }


async def _get_overseas_calendar_data(
    question, timezone="Asia/Shanghai", language="en_US", geo_location=""
):
    """获取海外环境的日历数据"""
    from src.utils.llm import LLMManager

    # 准备LLM所需的各种数据
    calendar_data = _prepare_calendar_data(timezone, language, geo_location)

    # 构建提示词
    prompt = _build_calendar_prompt(question, calendar_data, language)
    logger.debug(f"===海外日历 prompt:{prompt}===")
    start_time = time.time()
    # 调用LLM获取结果
    result = await LLMManager.invoke_generate_text_model(
        messages=[{"role": "user", "content": prompt}]
    )
    end_time = time.time()
    elapsed_time = end_time - start_time
    logger.debug(
        f"===获取国外日历信息完成，耗时: {elapsed_time:.3f}秒，question: {question}, timezone: {timezone}==="
    )
    logger.debug(
        f"===国外日历的请求参数:{question},返回结果:{result.content}，模型的名字:{result.llm_config.llm_model_name}==="
    )

    parse_result = parse_output_to_dict(result.content)
    # 解析LLM返回结果
    # parse_result = parse_llm_response(response_text=result.content, model_name=result.llm_config.llm_model_name)

    # 构建最终返回结果
    return _build_calendar_response(parse_result[0], question, timezone, language)


def _get_timezone_aware_datetime(timezone=None):
    """获取带时区的datetime对象

    Args:
        timezone (str, optional): 时区名称，例如 'Asia/Shanghai'。如果为None，则使用系统默认时区。

    Returns:
        datetime: 带时区信息的datetime对象
    """
    # 获取当前时间
    now = datetime.datetime.now()

    # 如果提供了时区，则将时间绑定到指定时区
    if timezone:
        try:
            import pytz

            tz = pytz.timezone(timezone)
            # 正确方法：先获取UTC时间，再转换到目标时区
            now = datetime.datetime.now(pytz.UTC).astimezone(tz)
            logger.debug(f"成功设置时区: {timezone}, 当前时间: {now}")
        except Exception as e:
            logger.error(f"设置时区失败: {e}，使用系统默认时区")

    return now


def _prepare_calendar_data(timezone="Asia/Shanghai", language="", geo_location=""):
    """准备日历相关数据"""
    # 获取当前时间及时区
    now = _get_timezone_aware_datetime(timezone)
    logger.debug(f"===当前时区时间:{now}===")

    # 中文星期映射
    weekday_map = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日",
    }

    # 格式化日历信息 - 移除前导空格
    current_calendar_info = f"""current datetime: {now.strftime("%Y-%m-%d %H:%M:%S")}
weekday: {weekday_map[now.weekday()]}
geo_location: {geo_location}"""

    # 目标结果格式 - 移除前导空格
    target_result = """{
    "country":"xxxx",
    "intent": "xxxx",
    "target": "xxxx",
    "target_date": "xxxx",
     "festival": "xx",
    }"""

    # 节日信息 - 移除前导空格
    festival_info = """请根据给定的经纬度判断所在国家或地区，并基于该地区的传统节日习俗回答问题。
注意：不同地区会有其特有的节日传统，请准确识别地区并给出相应的节日信息。"""

    # 约束信息 - 移除前导空格
    constraint_information = """1.country是通过经纬度推导出的国家
2.intent表示用户问题所表达的时间相关意图，是枚举类型，只能是[time,date,week,lunar,festival,holiday,period]其中之一，若意图不明确，默认使用time；如果用户的问题提到的是节日，则intent类型是festival
3.festival:使用英文，比如"Christmas"
"""

    return {
        "current_info": current_calendar_info,
        "target_format": target_result,
        "festival_info": festival_info,
        "constraints": constraint_information,
    }


def _build_calendar_prompt(question, calendar_data, language):
    """构建日历提示词"""
    # 使用 textwrap.dedent 移除共同的前导空格，确保对齐
    import textwrap

    prompt = f"""基于日历信息回答用户问题，答案必须用户指定的格式.
## 用户问题
{question}

## 当前时间和时区信息
{calendar_data["current_info"]}

## 节日信息
{calendar_data["festival_info"]}

## 约束信息
{calendar_data["constraints"]}

预期的输出结果格式:
{calendar_data["target_format"]}"""

    # 确保整个字符串的缩进一致
    return textwrap.dedent(prompt)


# 基准英文星期名称
def get_weekday_name(weekday):
    """
    获取指定语言的星期名称
    """
    WEEKDAYS_DICT = {
        0: _("Monday"),
        1: _("Tuesday"),
        2: _("Wednesday"),
        3: _("Thursday"),
        4: _("Friday"),
        5: _("Saturday"),
        6: _("Sunday"),
    }
    return WEEKDAYS_DICT[weekday]


def get_random_motto(language="en_US"):
    """
    随机获取一条座右铭

    返回:
        str: 随机选择的座右铭
    """
    import random

    return random.choice(
        [  # motto
            _("Never forget why you started, and your mission can be accomplished."),
            _("Never give up."),
            _("The road not taken never leads you astray."),
            _("Everyone has a unique purpose in life."),
            _("Diligence is the mother of success."),
            _("Life is a journey; we are all travelers."),
            _("When you bloom, the breeze will come naturally."),
            _("Hope is the companion of existence."),
            _("Seize the day and enjoy life to the fullest."),
            _("The unexamined life is not worth living."),
        ]
    )


def _build_calendar_response(parse_result, question, timezone, language, env=None):
    """构建日历响应

    Args:
        parse_result (dict): 解析结果，包含基本的日历信息
        question (str): 用户问题
        timezone (str): 时区
        language (str): 语言
        env (str): 环境，domestic表示国内，overseas表示海外

    Returns:
        dict: 标准格式的日历响应
    """
    logger.debug(f"===parse_result:{parse_result}===")

    # 获取当前时区的时间
    from zoneinfo import ZoneInfo

    now = datetime.datetime.now(ZoneInfo(timezone))

    try:
        # 解析目标日期
        target_dt = None
        if parse_result.get("target_date"):
            # 如果是具体日期查询，使用目标日期但保持当前时间
            target_dt = datetime.datetime.strptime(
                parse_result["target_date"], "%Y-%m-%d"
            )
            target_dt = target_dt.replace(
                hour=now.hour,
                minute=now.minute,
                second=now.second,
                microsecond=0,
                tzinfo=ZoneInfo(timezone),
            )
        else:
            # 如果是当前时间查询，使用当前时间
            target_dt = now

        # 计算日期差
        diff = (target_dt.date() - now.date()).days
        # 获取对应语言的星期名称
        week_name = get_weekday_name(target_dt.weekday())

        # 获取年中的第几周
        year_week = int(target_dt.strftime("%V"))

        # 构建标准响应格式
        calendar_data = {
            "type": "DISPLAY.CHAT_TIME",
            "data": {
                "intent": parse_result.get("intent", "date"),
                "target": parse_result.get("target", ""),
                "target_date": target_dt.strftime("%Y-%m-%d"),
                "yearWeek": year_week,
                "date": {
                    "solar": {
                        "year": target_dt.strftime("%Y"),
                        "month": target_dt.strftime("%m"),
                        "day": target_dt.strftime("%d"),
                    }
                },
                "time": {
                    "hour": target_dt.strftime("%H"),
                    "minute": target_dt.strftime("%M"),
                    "second": target_dt.strftime("%S"),
                },
                "week": week_name,
                # 确保 festival 永远不会是 None
                "festival": parse_result.get("festival") or "",
                "diff": diff,
                "motto": get_random_motto(language),
                "zone": "domestic" if env == Area.domestic else "oversea",
                "timezone": timezone,
                "question": question,
                "language": language,
            },
        }

        # 构建完整响应
        target_result = {
            "skill_response": {},
            "version": "",
            "desc": {
                "text_output": "",
                "display": calendar_data,
                "query": question,
            },
            "response": {
                "outSpeech": {"type": "text", "text": ""},
                "display": calendar_data,
            },
        }

        return target_result

    except Exception as e:
        logger.error(f"构建日历响应时发生错误: {str(e)}")
        # 返回一个基本的错误响应
        error_response = {
            "skill_response": {},
            "version": "",
            "desc": {
                "text_output": "抱歉，处理日历数据时出现错误。",
                "display": {},
                "query": question,
            },
            "response": {
                "outSpeech": {
                    "type": "text",
                    "text": _(
                        "Sorry, an error occurred while processing the calendar data."
                    ),
                },
                "display": {},
            },
        }
        return error_response


if __name__ == "__main__":
    json_result = """```json
{
    "display": {
        "type": "DISPLAY.CHAT_TIME",
        "data": {
            "intent": "date",
            "target": "元旦",
            "target_date": "2025-01-01",
            "yearWeek": 1,
            "date": {
                "solar": {
                    "year": "2025",
                    "month": "01",
                    "day": "01"
                }
            },
            "time": {
                "hour": "00",
                "minute": "00",
                "second": "00"
            },
            "week": "星期三",
            "festival": "New Year's Day",
            "diff": "-62"
        }
    }
}
"""  # noqa
    parse_result = parse_llm_response(response_text=json_result)
    print(parse_result)

    res = _prepare_calendar_data(timezone="America/New_York")
    print(res)
