import aiohttp
from src.settings import agent_setting
import urllib.parse
from loguru import logger
from src.utils.async_utils import (
    get_action_ouput_language,
    is_multilingual,
)
from src.common.toolkit import LLMToolKit
from src.utils.llm import LLMManager


class RouteService:
    """路线服务类,处理所有路线相关的API调用"""

    def __init__(self, robot_parameters):
        self.device_id = robot_parameters["device_id"]
        self.enterprise_id = robot_parameters["enterprise_id"]
        self.lang = robot_parameters["lang"]
        self.map_id = robot_parameters["map_id"]
        self.map_name = robot_parameters["map_name"]
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def _make_request(self, url, request_body):
        """统一的API请求方法"""
        try:
            async with self.session.post(
                url=url,
                json=request_body,
                headers={
                    "orionstar-api-key": agent_setting.robot_openapi_key,
                    "Content-Type": "application/json",
                },
                timeout=aiohttp.ClientTimeout(total=10),  # 设置10秒超时
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"API请求失败 device_id:{self.device_id}, url:{url}, status:{response.status}"
                    )
                    return None
                return await response.json()
        except Exception as e:
            logger.error(f"API请求异常: {str(e)}")
            return None

    async def get_all_routes(self):
        """获取所有可用路线"""
        url = urllib.parse.urljoin(
            agent_setting.robot_openapi_host, agent_setting.convert_tour_id_path
        )
        request_body = {
            "get_action": "guide",
            "ov_corp_id": self.enterprise_id,
            "robot_sn": self.device_id,
            "lang": self.lang,
        }
        if self.map_id:
            request_body.update({"map_id": self.map_id})
        if self.map_name:
            request_body.update({"map_name": self.map_name})
        return await self._make_request(url, request_body)

    # async def get_all_routes(self):
    #     data = {
    #         "ret": "0",
    #         "code": 0,
    #         "msg": "",
    #         "stime": "1753065933",
    #         "ptime": "14",
    #         "punit": "",
    #         "data": {
    #             "total_count": "1",
    #             "obj_list": [
    #                 {
    #                     "obj": {
    #                         "id": 3850788,
    #                         "agency_id": "",
    #                         "lang": "zh_CN",
    #                         "config_id": "1752893782",
    #                         "orgid": 20182523,
    #                         "module_code": "module_guide",
    #                         "name": "猎户展厅",
    #                         "prodtype": "bao_mini",
    #                         "status": "publish",
    #                         "utime": "2025-07-19 11:00:57.217736",
    #                         "ptime": "2025-07-19 11:00:55.959214",
    #                         "config_json_path": "orics/down/b014_20250719_94a63119d8e69be34fcce412d2da8f65.json",
    #                         "protocol": 2,
    #                         "version": "version_3850788",
    #                         "publish_changed": 0,
    #                         "nlp_words_list": "orics/down/b014_20250719_d4543808aa56dd282d131fa47c4a8a14.json",
    #                         "obj_corp_uuid": "Fg8jZNNNq_p0DzvGtM9URw",
    #                         "obj_ov_corp_id": "orion.ovs.entprise.4384083697",
    #                         "pubr_orgid": 20182523,
    #                         "pubr_robot_uuid": "qz2V8ZJ1sZqqq-_WoiRXoA",
    #                         "pubr_prodtype": "bao_mini",
    #                         "pubr_lang": "zh_CN",
    #                         "pubr_show_index": 1752894055,
    #                         "pubr_status": "create",
    #                         "pubr_ctime": "2025-07-19 11:00:55.946617",
    #                         "pubr_utime": "2025-07-19 11:00:55.946646",
    #                         "pubrr_robot_sn": "M03SCN1A14024430N038",
    #                         "pubrr_robot_sid": "495977e35a05edee678a519724911528",
    #                         "guide_tour_id": "1752893782",
    #                         "guide_point_list": ["测试部", "会议室", "接待点"],
    #                         "keyword_info": {
    #                             "locations": 3,
    #                             "cover_image_url": "",
    #                             "title": "猎户展厅",
    #                             "describe": "猎户星空通过自研大模型，集成 DeepSeek 技术，升级 AgentOS 机器",
    #                             "duration": 8,
    #                             "is_hot": True,
    #                         },
    #                     }
    #                 }
    #             ],
    #         },
    #         "errdata": {},
    #         "req_id": "aed75d27dff0927be8915f912a186ef9",
    #         "wakeup_id": "",
    #     }
    #     return data

    # async def get_all_routes(self):
    #     data = {
    #         'ret': '0',
    #         'code': 0,
    #         'msg': '',
    #         'stime': '1744947023',
    #         'ptime': '11',
    #         'punit': '',
    #         'data': {
    #             'total_count': '2',
    #             'obj_list': [{
    #                 'obj': {
    #                     'id': 3677331,
    #                     'agency_id': '',
    #                     'lang': 'zh_CN',
    #                     'guide_tour_id': '1744793747',
    #                     'orgid': 20182510,
    #                     'module_code': 'module_guide',
    #                     'name': '测试测试',
    #                     'prodtype': 'bao_mini',
    #                     'status': 'publish',
    #                     'utime': '2025-04-16 16:57:33.234154',
    #                     'ptime': '2025-04-16 16:57:32.220072',
    #                     'config_json_path': 'orics/down/b014_20250416_b775b5d3d51b1c30c731c8ee52b180cd.json',
    #                     'protocol': 2,
    #                     'version': 'version_3677331',
    #                     'publish_changed': 0,
    #                     'obj_corp_uuid': 'xVjuuTwXrjRioRLaMhI9Nw',
    #                     'obj_ov_corp_id': 'orion.ovs.entprise.0335036968',
    #                     'pubr_orgid': 20182510,
    #                     'pubr_robot_uuid': 'gYUXTugr2We4YrnL0J4Gww',
    #                     'pubr_prodtype': 'bao_mini',
    #                     'pubr_lang': 'zh_CN',
    #                     'pubr_show_index': 1744793852,
    #                     'pubr_status': 'create',
    #                     'pubr_ctime': '2025-04-16 16:57:32.192266',
    #                     'pubr_utime': '2025-04-16 16:57:32.192305',
    #                     'pubrr_robot_sn': 'M01BCNA010020142VB27',
    #                     'pubrr_robot_sid': '127ed8ff0687b7d44a99c2affd4d212a',
    #                     'keyword_info': {
    #                         'locations': 5,
    #                         'cover_image_url': '',
    #                         'title': '测试测试',
    #                         'describe': '得到的',
    #                         'duration': 12,
    #                         'is_hot': True
    #                     }
    #                 }
    #             }, {
    #                 'obj': {
    #                     'id': 3677332,
    #                     'agency_id': '',
    #                     'lang': 'zh_CN',
    #                     'guide_tour_id': '1744788670',
    #                     'orgid': 20182510,
    #                     'module_code': 'module_guide',
    #                     'name': '参观办公区域',
    #                     'prodtype': 'bao_mini',
    #                     'status': 'publish',
    #                     'utime': '2025-04-16 16:58:14.867239',
    #                     'ptime': '2025-04-16 16:58:13.085487',
    #                     'config_json_path': 'orics/down/b014_20250416_7a57b0c7868783fad2fa23863d121654.json',
    #                     'protocol': 2,
    #                     'version': 'version_3677332',
    #                     'publish_changed': 0,
    #                     'obj_corp_uuid': 'xVjuuTwXrjRioRLaMhI9Nw',
    #                     'obj_ov_corp_id': 'orion.ovs.entprise.0335036968',
    #                     'pubr_orgid': 20182510,
    #                     'pubr_robot_uuid': 'gYUXTugr2We4YrnL0J4Gww',
    #                     'pubr_prodtype': 'bao_mini',
    #                     'pubr_lang': 'zh_CN',
    #                     'pubr_show_index': 1744793893,
    #                     'pubr_status': 'create',
    #                     'pubr_ctime': '2025-04-16 16:58:13.067304',
    #                     'pubr_utime': '2025-04-16 16:58:13.067335',
    #                     'pubrr_robot_sn': 'M01BCNA010020142VB27',
    #                     'pubrr_robot_sid': '127ed8ff0687b7d44a99c2affd4d212a',
    #                     'keyword_info': {
    #                         'locations': 4,
    #                         'cover_image_url': 'orics/down/b016_20250416_a6c9dec6d5f9c477083ea49090050177.jpeg',
    #                         'title': '参观办公区域',
    #                         'describe': '欢迎来到办公区域，这里是我们团队协作、创新发展的核心地带，也是每一',
    #                         'duration': 10,
    #                         'is_hot': True
    #                     }
    #                 }
    #             }]
    #         },
    #         'errdata': {},
    #         'req_id': 'e6516d724cc98822598321d3e90cf854',
    #         'wakeup_id': ''
    #     }
    #     return data

    async def get_route_details(self, route_id):
        """获取路线详情"""
        routes_data = await self.get_all_routes()
        if not routes_data or not routes_data.get("data", {}).get("obj_list"):
            logger.error("获取路线列表失败或为空")
            return None

        for route_obj in routes_data["data"]["obj_list"]:
            route = route_obj.get("obj", {})
            if str(route.get("id")) == str(route_id):
                keyword_info = route.get("keyword_info", {})
                return {
                    "route_detail": {
                        "id": route.get("id"),
                        "name": route.get("name"),
                        "description": keyword_info.get("describe", ""),
                        "points": [],  # 如果需要景点列表，需要从其他字段获取
                        "duration": str(keyword_info.get("duration", "30-40")),
                        "features": [],  # 如果需要特色列表，需要从其他字段获取
                    }
                }

        logger.error(f"未找到ID为 {route_id} 的路线")
        return None

    async def get_all_route_details(self):
        """并行获取所有路线的详细信息"""
        routes_data = await self.get_all_routes()
        if not routes_data or not routes_data.get("data", {}).get("obj_list"):
            logger.error("获取路线列表失败或为空")
            return []

        route_details = []
        for route_obj in routes_data["data"]["obj_list"]:
            route = route_obj.get("obj", {})
            keyword_info = route.get("keyword_info", {})

            route_details.append(
                {
                    "id": route.get("id"),
                    "name": route.get("name"),
                    "description": keyword_info.get("describe", ""),
                    "points": [],  # 如果需要景点列表，需要从其他字段获取
                    "duration": str(keyword_info.get("duration", "30-40")),
                    "features": [],  # 如果需要特色列表，需要从其他字段获取
                }
            )

        return route_details


async def get_route_info(robot) -> list:
    """获取路线信息。

    Args:
        robot: 机器人对象

    Returns:
        list: 路线列表
    """
    robot_parameters = {
        "device_id": robot.device_id,
        "enterprise_id": robot.enterprise_id,
        "map_id": robot.map_id,
        "map_name": robot.map_name,
        "lang": robot.language,  # 输出语言  zh/en/de
    }

    async with RouteService(robot_parameters) as route_service:
        routes_data = await route_service.get_all_routes()
        # 提取路线列表
        routes = []
        if routes_data and routes_data.get("data", {}).get("obj_list"):
            routes = [item["obj"] for item in routes_data["data"]["obj_list"]]
        return routes


async def generate_route_recommendation_text(
    user_query: str, routes: list, **kwargs
) -> str:
    """
    生成路线推荐播报语
    Args:
        user_query: 用户查询
        routes: 路线列表，每个路线包含name和keyword_info信息
        kwargs: 其他参数，包含robot信息用于多语言支持
    Returns:
        str: 生成的推荐播报语
    """
    # 构建推荐提示词
    if not routes:
        route_info = "当前没有可用的导览路线"
    else:
        route_info = "\n".join(
            [
                f"路线{idx + 1}:{route['name']}\n"
                f"描述：{route['keyword_info']['describe']}\n"
                f"景点数量：{route['keyword_info']['locations']}个\n"
                f"预计时长：{route['keyword_info']['duration']}分钟\n"
                f"是否热门：{'是' if route['keyword_info']['is_hot'] else '否'}"
                for idx, route in enumerate(routes)
            ]
        )

    action_result = get_action_ouput_language(
        user_query, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]
    detect_lang = action_result["detect_lang"]
    prompt = f"""请根据以下可用路线信息，生成一段简洁的推荐文本，并且使用{language}介绍各个路线的特点，并询问用户想参观哪条路线。

可用路线:
{route_info}

要求:
1. 介绍要简洁明了
2. 突出每条路线的特色
3. 最后要询问用户的选择
4. 总字数控制在100字以内
5. 使用{language}中自然的方式表示路线编号
6. 如果没有可用路线，请用{language}礼貌地告知用户当前没有可用的导览路线
7. 避免使用任何markdown格式（如**加粗**）
8. 不要使用表情符号
9. 整个文本必须完全使用{language}，包括数字、标点和所有表述
10. 严格基于提供的{route_info}路线信息，禁止添加、修改或虚构任何路线信息
"""
    messages = []
    if is_multilingual(kwargs.get("__robot")):
        messages.append(
            LLMToolKit.build_multilingual_user_content(
                user_query, detect_lang, robot=kwargs.get("__robot")
            )
        )
    messages.append({"role": "user", "content": prompt})
    # 获取LLM生成的推荐文本
    model_result = await LLMManager.invoke_generate_text_model(messages=messages)
    logger.info(
        f"[recommendation] action llm prompt: {prompt}\n***recommendation llm result***:>>>>{model_result.content}<<<<"
    )
    return model_result.content


async def match_route_with_llm(
    user_query: str, target_route: str, routes: list
) -> dict:
    """
    使用LLM进行智能路线匹配
    Args:
        user_query: 用户的原始查询
        target_route: NLU抽取出的目标路线名
        routes: 可用路线列表
    Returns:
        dict: 匹配到的路线或None
    """
    try:
        # 准备路线信息
        route_info = "\n".join(
            [
                f"路线{idx + 1}: {route.get('name')} (ID: {route.get('guide_tour_id')})"
                for idx, route in enumerate(routes)
            ]
        )
        # 构建提示词
        prompt = f"""请根据目标路线名,从可用路线中选择最匹配的路线。

        用户查询: {user_query}
        目标路线名: {target_route}

        可用路线:
        {route_info}

        请仅返回以下格式之一:
        MATCH: <路线名称> | <tour_id>
        NO_MATCH

        匹配规则:
        1. 序号匹配规则（最高优先级）:
           a) 绝对序号匹配:
               - 如果用户提到"第X条/个路线"或"第X个"，直接匹配列表中的第X个路线
               - 支持以下数字表达方式:
                 * 中文数字: 一、二、三、四、五...
                 * 阿拉伯数字: 1、2、3、4、5...
                 * 汉字数字: 壹、贰、叁、肆、伍...
               - 示例:
                 * "第二条路线" -> 匹配列表中的第2个路线，返回其名称和ID
                 * "第一个路线" -> 匹配列表中的第1个路线，返回其名称和ID
                 * "第3个" -> 匹配列表中的第3个路线，返回其名称和ID
               - *注意：如果用户提到的序号 **超出可用路线数量**（如用户说“第4条路线”，但只有3条），**必须返回 NO_MATCH**，不能尝试匹配最接近的路线。
           b) 相对位置匹配:
              - 支持以下相对位置表达:
                * "最后一条/个路线" -> 匹配列表中的最后一个路线
                * "第一条/个路线" -> 匹配列表中的第一个路线
                * "最后" -> 匹配列表中的最后一个路线
                * "第一个" -> 匹配列表中的第一个路线
              - 示例:
                * "带我参观最后一条路线" -> 匹配列表中的最后一个路线
                * "看看第一条路线" -> 匹配列表中的第一个路线

        2. 路线名称匹配规则（次优先级）:
           - 匹配要求：
             * 路线名称可以有轻微格式差异（如空格、省略冒号等），但主要内容必须一致或高度相关。
             * 不支持明显不一致或模糊匹配。
             - 示例:
             * 目标路线名为"路线1:工厂"，可用路线为" 工厂 (ID: 1742195346)"，视为匹配。
             * 目标路线名为"测试路线1"，可用路线为"测试路线1 (ID: 1742195903)"，视为匹配
        3. 匹配流程:
           - 首先检查是否包含序数词（第X条/个）或相对位置词（最后、第一个等）
           - 如果包含序数词，直接匹配列表中的对应位置
           - 如果不包含序数词，才尝试路线名称匹配
           - 如果都不匹配，返回NO_MATCH

        4. 返回格式说明:
           - 当匹配成功时，返回格式为: MATCH: <路线名称> | <tour_id>
           - 路线名称必须使用路线列表中的原始名称
           - 例如: 如果匹配到"路线2: 测试路线1 (ID: 1742195903)"，应返回: MATCH: 测试路线1 | 1742195903

        5. 特殊情况处理:
           - 如果序号超出路线列表范围，返回NO_MATCH
           - 如果路线名称不匹配任何可用路线，返回NO_MATCH
           - 如果用户明确提到序号或相对位置，必须优先使用序号/位置匹配
           - 如果用户提到"最后"或"第一个"等相对位置词，必须匹配对应的位置
           - 如果路线列表为空，返回NO_MATCH
        """
        # 获取LLM响应
        response = await LLMManager.invoke_generate_text_model(
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                },
            ]
        )
        # 解析响应
        response_text = response.content
        logger.info("route match result:{}".format(response_text))
        if response_text.startswith("MATCH:"):
            route_info = response_text.replace("MATCH:", "").strip()
            name, tour_id = route_info.split("|")
            return {"name": name.strip(), "tour_id": tour_id.strip()}
        return None

    except Exception as e:
        logger.error(f"[match_route_with_llm] LLM匹配失败: {str(e)}")
        return None


async def build_recommendation_text(user_query, route_details, **kwargs):
    """构建推荐提示词"""
    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    text_conversation = await LLMToolKit.get_text_conversation_records(
        chat_context.messages,
        limit=2,
    )
    user_query = kwargs["_USER_QUERY"]
    if user_query and user_query not in text_conversation:
        text_conversation.append(f"<User> said '{user_query}'")

    text_conversation = "\n".join(text_conversation)
    if not route_details:
        routes_info = "当前没有可用的导览路线"
    else:
        routes_info = "\n".join(
            [
                f"路线{i + 1}：{detail['name']}\n"
                f"特点：{detail['description']}\n"
                f"包含景点：{', '.join(detail['points'])}\n"
                f"时长：{detail['duration']}分钟\n"
                for i, detail in enumerate(route_details)
            ]
        )
    action_result = get_action_ouput_language(
        user_query, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]
    detect_lang = action_result["detect_lang"]
    prompt = f"""
            基于用户的需求和可用的导览路线，并且使用{language}生成路线推荐并进行意图确认。

            # 聊天对话
            {text_conversation}

            可用路线：
            {routes_info}

            要求：
            1. 分析用户需求，找出最匹配的1条路线进行推荐
            2. 说明推荐理由，突出路线特色和亮点
            3. 通过提问确认用户意图，引导用户做出选择
            4. 语气友好、专业、热情
            5. 避免使用任何markdown格式（如**加粗**）
            6. 不要使用表情符号
            7. 必须包含路线编号，方便用户选择，使用标准的路线编号格式（如"路线1"）
            8. 控制在100字以内
            9.如果没有可用路线，请用{language}礼貌地告知用户当前没有可用的导览路线
            """
    messages = []
    if is_multilingual(kwargs.get("__robot")):
        messages.append(
            LLMToolKit.build_multilingual_user_content(
                user_query, detect_lang, robot=kwargs.get("__robot")
            )
        )
    messages.append({"role": "user", "content": prompt})
    # 获取LLM生成的推荐文本
    model_result = await LLMManager.invoke_generate_text_model(messages=messages)
    logger.info(
        f"[recommendation] action llm prompt: {prompt}\nrecommendation llm result:>>>>{model_result.content}<<<<"
    )
    return model_result.content
