from typing import Any, Awaitable, Callable, Literal, Optional, Union

from pydantic import BaseModel, Field, model_validator
from loguru import logger

from src.action.function_register import ALL_FUNCTIONS
from src.common.constant import Area, CLARIFY_ACTION_NAME, LanguageEnum
from src.settings import agent_setting

ParameterTypeLiteral = Literal[
    "string",
    "int",
    "float",
    "bool",
    "enum",
    "Integer array",
    "String array",
    "HttpUrl",
    "list",
    "dict",
]

ResultTypeLiteral = Literal[
    "string", "int", "float", "bool", "enum", "list", "dict", "page_info", "HttpUrl"
]

Package_Map_Name = "com.ainirobot.maptool"

Package_Main_Name = "com.ainirobot.moduleapp"

Builtin_Namespaces = ["orion.agent.action", "orion.app.promote"]


class Action(BaseModel):
    class Parameter(BaseModel):
        name: str
        type: ParameterTypeLiteral
        desc: str
        is_required: bool = True
        length: Optional[int] = None
        enum_constant: Optional[list[str]] = None
        enum_func: Optional[Awaitable[Any] | Callable] = Field(None, exclude=True)
        max: Optional[float | int] = None
        min: Optional[float | int] = None

        is_hidden: bool = False
        need_summary: bool = False

        # generate side
        generate_side: Literal["robot", "plan", "inject"] = "plan"

        class Config:
            arbitrary_types_allowed = True

        async def enum(self, parameter=None) -> list[str]:
            return (
                await self.enum_func(parameter)
                if self.enum_func
                else self.enum_constant
            )

        @model_validator(mode="before")
        @classmethod
        def validate_enum_func(cls, values):
            """
            转换函数名到函数对象
            """
            # 验证execute_function
            if enum_func := values.get("enum_func"):
                if isinstance(enum_func, str):
                    values["enum_func"] = ALL_FUNCTIONS[enum_func]

            # 兼容客户端上报类型
            if values.get("type") == "string_array":
                values["type"] = "String array"
            elif values.get("type") == "number_array":
                values["type"] = "Integer array"
            return values

    class Result(BaseModel):
        name: str
        desc: str
        type: ResultTypeLiteral
        enum_constant: Optional[list[str]] = None

    namespace: str = "orion.agent.action"
    level: Literal["apk", "global", "opk", "admin", "app", "page"] = (
        "global"  # app: 二开APP协议
    )
    source: Literal["builtin", "report", "mcp"] = "builtin"
    name: str
    name_for_llm: str
    category: Literal[
        "basic_movement",  # 基础移动控制
        "navigation",  # 导航
        "weather",  # 天气查询
        "interaction",  # 交互对话
        "guide",  # 导览讲解
        "product",  # 产品展示
        "system_control",  # 系统控制
        "user_recognition",  # 用户识别
        "entertainment",  # 娱乐功能
        "information",  # 信息查询
        "transportation",  # 交通票务
        "system_admin",  # 系统管理
        "web_browser",  # 网页浏览
        "visitor_reception",  # 访客接待
        "verification",  # 验证码处理
        "system_config",  # 系统配置
        "other",  # 其他功能
    ] = "other"

    @property
    def full_name(self):
        return f"{self.namespace}.{self.name}".lower()

    display_name: str
    en_display_name: str = ""
    desc: str = ""
    desc_chinese: str = ""
    parameters: list[Parameter] = []
    execute_side: Literal["robot", "server", "both"] = "robot"
    execute_timeout_limit: int = 180
    result_schema: list[Result] = []

    execute_function: Optional[Callable] = None
    post_processing: Optional[Callable] = None  # 后处理函数，参数合并等等

    # for level == "opk" or "launcher"
    app_ids: tuple = tuple()
    package_names: tuple = (Package_Main_Name,)

    client_alias: Optional[str] = (
        None  # 用于客户端调用或执行的Action的别名， 要求接口入参必须和别名Action一致或直接调用别名Action
    )

    version: str = "draft"

    exported: bool = False  # 是否注册到外部

    pre_execute: bool = False  # 是否可以预执行

    hidden: bool = False

    audio_output: bool = False  # 是否是语音输出

    action_area: Literal["all", "domestic", "oversea"] = (
        "all"  # 标识action作用地区，国内，海外还是都适用
    )

    only_intervention: bool = False  # 是否只用于干预阶段
    page_id: str = ""  # 页面ID

    display_names: dict[str, str] = {}

    class Config:
        arbitrary_types_allowed = True

    @model_validator(mode="before")
    @classmethod
    def validate_functions(cls, values):
        """
        转换函数名到函数对象
        """
        # 增加 name_for_llm
        if not values.get("name_for_llm"):
            values["name_for_llm"] = values["name"]

        # 验证execute_function
        if execute_function := values.get("execute_function"):
            if isinstance(execute_function, str):
                # 尝试从ALL_FUNCTIONS中获取函数对象
                if execute_function in ALL_FUNCTIONS:
                    values["execute_function"] = ALL_FUNCTIONS[execute_function]

        # 验证post_processing
        if post_processing := values.get("post_processing"):
            if isinstance(post_processing, str):
                # 尝试从ALL_FUNCTIONS中获取函数对象
                if post_processing in ALL_FUNCTIONS:
                    values["post_processing"] = ALL_FUNCTIONS[post_processing]

        return values

    async def convert_agent_prompt(self, agent_parameter) -> dict:
        if self.source in ["report", "mcp"]:  # 客户端上报的action，直接使用desc
            purpose = self.desc
        else:
            if agent_setting.region_version == Area.overseas:  # 只区分海外和国内
                purpose = self.desc
            else:
                purpose = self.desc_chinese

        define = {
            "name": self.name.lower(),
            "name_for_llm": self.name_for_llm.lower()
            or self.name.lower(),  # 兼容二开无name_for_llm的情况
            "fullname": self.full_name.lower(),
            "namespace": self.namespace.lower(),
            "source": self.source,
            "purpose": purpose,
            "level": self.level,
            "category": self.category,
            "only_intervention": self.only_intervention,
            "client_alias": self.client_alias,
        }
        if self.name == "SAY" or self.name == CLARIFY_ACTION_NAME:
            from src.utils.async_utils import get_action_say_extra_desc, is_multilingual
            from src.utils.language_utils import detect_language

            if is_multilingual(agent_parameter.robot):
                detect_lang = detect_language(
                    agent_parameter.query,
                    agent_parameter.robot.language,
                    agent_parameter.robot.multilingual,
                )
                logger.debug(
                    f"convert_agent_prompt detect_lang: {detect_lang} {agent_parameter.query} {agent_parameter.robot.language} {agent_parameter.robot.multilingual}"
                )

                extra_desc_chinese = get_action_say_extra_desc(
                    lang="en", sentence_language=detect_lang
                )
                for p in self.parameters:
                    if p.name == "text" and extra_desc_chinese:
                        p.desc = f"Speak in the first person, using pure text without emojis. {extra_desc_chinese}"
                    if p.name == "request_clarify_text" and extra_desc_chinese:
                        p.desc = f"{p.desc} {extra_desc_chinese}"

        parameters = []
        for p in self.parameters:
            if p.is_hidden:
                continue

            pp = {
                "name": p.name,
                "desc": p.desc,
                "type": p.type,
                "is_required": p.is_required,
            }
            enum = await p.enum(agent_parameter)
            if enum is not None:  # NOTE: 如果是[]，要赋值
                pp["enum"] = enum

            if (p.max or p.max == 0) and (p.min or p.min == 0):
                # pp["desc"] += f" **(取值范围: {p.min} - {p.max})**"
                pp["min"] = p.min
                pp["max"] = p.max
            else:
                if p.min or p.min == 0:
                    # pp["desc"] += f" **(最小值: {p.min})**"
                    pp["min"] = p.min
                if p.max or p.max == 0:
                    # pp["desc"] += f" **(最大值: {p.max})**"
                    pp["max"] = p.max

            parameters.append(pp)

        if parameters:
            define["parameters"] = parameters

        result_schema = [
            {
                "name": r.name,
                "desc": r.desc,
                "type": r.type,
            }
            for r in self.result_schema
        ]
        if result_schema:
            define["result_schema"] = result_schema

        return define

    def get_display_name(self, language: str = LanguageEnum.en) -> str:
        if self.source == "report":  # 客户端上报的action，直接使用display_name
            return self.display_name

        # 直接使用对象的display_names属性
        if self.display_names and language in self.display_names:
            return self.display_names[language]

        # 回退到原来的逻辑
        if language in (LanguageEnum.zh, LanguageEnum.zh_tw, LanguageEnum.zh_gd):
            return self.display_name
        elif language == LanguageEnum.en:
            return self.en_display_name or self.display_name
        else:  # 其他语言，优先使用英文，如果没有则使用中文
            return self.en_display_name or self.display_name

    @property
    def lower_name(self) -> str:
        return self.name.lower()


class ImageContent(BaseModel):
    file_url: str
    width: int = 500
    height: int = 500


class NonStreamingContent(BaseModel):
    """Base model for non-streaming content"""

    _stream: Literal[False] = False
    text: str

    @property
    def stream(self) -> bool:
        return self._stream

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        data["stream"] = self.stream
        return data


class StreamingContent(BaseModel):
    """Base model for streaming content"""

    _stream: Literal[True] = True
    messages: list[dict]
    llm_config: dict

    @property
    def stream(self) -> bool:
        return self._stream

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        data["stream"] = self.stream
        return data


class AudioRequest(BaseModel):
    """Audio processing request"""

    content: Union[StreamingContent, NonStreamingContent]


class AudioOutput(BaseModel):
    """Direct audio output content"""

    content: Union[StreamingContent, NonStreamingContent]


class FunctionOutput(BaseModel):
    """Function output with regular result"""

    result: dict = {}
    images: Optional[list[ImageContent]] = None
    audio_request: Optional[AudioRequest] = None


class FunctionResult(BaseModel):
    status: Literal["succeeded", "failed"] = "succeeded"
    type: Literal["function", "audio"] = "function"
    content: Union[FunctionOutput, AudioOutput]
    cost_time: float = 0
    elapse_info: dict = {}
    debug: dict = {}


class RetrieveResult(BaseModel):
    query: str
    action_source: Literal["Retrieve", "All"] = "All"
    actions: list[dict] = []
    few_shots: list[dict]
    retrieve_action_elapsed_time: float = 0
    retrieve_few_shot_elapsed_time: float = 0
    embedding_elapsed_time: float = 0
    build_action_elapsed_time: float = 0
    total_elapsed_time: float = 0


class BusinessInfo(BaseModel):
    enterprise_id: str
    client_id: str
    device_id: str
    sid: str


class RequestLLMConfig(BaseModel):
    temperature: float = 0.0
    max_tokens: int | None = None
    timeout: int = 6
    file_search: bool = False
    business_info: BusinessInfo | None = None  # business info for future use
    mode: Literal["turbo", "full"] = "full"


class StudioBlockActionResult(BaseModel):
    action_name: str
    action_name_space: str
    is_block: bool


class LLMResponse(BaseModel):
    token_cost: Optional[dict] = None
    elapsed_time: Optional[float] = None
    return_message: Optional[dict] = None
    error: Optional[str] = None
    status: Literal["succeeded", "failed"] = "succeeded"
