import time
import uuid
from datetime import datetime

import aiohttp
from loguru import logger
from openai import AsyncClient, Client
from openai.types import Embedding
from qdrant_client import AsyncQdrantClient, models

from src.action.few_shot import load_all_few_shot
from src.action.model import Action, RetrieveResult
from src.common.constant import Area, LanguageEnum
from src.settings import agent_setting
from src.utils.date import stats_async_func_cost
from src.utils.feishu_alarm import send_feishu_alarm, send_feishu_alarm_sync


class _MaterialRetriever:
    OpenaiEmbeddingModels = [
        "text-embedding-ada-002",
        "text-embedding-3-small",
        "text-embedding-3-large",
    ]
    HostedEmbeddingModels = [
        "bge",
    ]

    def __init__(
        self,
        version: str,
        env: str = agent_setting.env,
    ):
        self.version = version
        self.env = env
        self.region_version = agent_setting.region_version

        self.embedding_base_url = agent_setting.embedding_model_base_url
        self.embedding_model = agent_setting.embedding_model
        self.embedding_dim = agent_setting.embedding_dim
        self.max_batch_size = agent_setting.max_batch_size
        self.few_shot_score_threshold = agent_setting.few_shot_score_threshold

        self.vector_grpc_client = AsyncQdrantClient(
            host=agent_setting.qdrant_host, prefer_grpc=True
        )

        # 验证embedding_model是否支持
        if (
            agent_setting.embedding_model
            not in self.OpenaiEmbeddingModels + self.HostedEmbeddingModels
        ):
            raise ValueError("embedding_model not supported")

        self.openai_embedding_client = AsyncClient(
            api_key=agent_setting.embedding_api_key
        )
        self.openai_sync_embedding_client = Client(
            api_key=agent_setting.embedding_api_key
        )

    def reload_qdrant_client(self):
        self.vector_grpc_client = AsyncQdrantClient(
            host=agent_setting.qdrant_host, prefer_grpc=True
        )

    def get_few_shot_collection_name(self, region_version: str) -> str:
        return f"few_shot_embeddings_{self.version}_{self.embedding_model}_{self.embedding_dim}_{self.env}_{region_version}"

    def _get_embedding_prompt(self, query: str, model: str, language: str) -> str:
        """根据语言获取适当的embedding提示语

        Args:
            query: 用户输入的查询文本
            language: 语言类型

        Returns:
            str: 处理后的查询文本
        """
        if model == agent_setting.embedding_model and language == LanguageEnum.zh:
            return "为这个句子生成表示以用于检索相关文章：" + query
        return query

    def query_length_format(self, query: str, model_name: str) -> str:
        """
        格式化查询字符串，对BGE模型特殊处理：
        1. 首先按"|"分割，保留最后部分
        2. 如果还是太长，则截取最后的510个字符

        Args:
            query: 查询字符串
            model_name: 模型名称

        Returns:
            格式化后的查询字符串
        """
        if model_name != "bge":
            return query

        MAX_TOKENS = 512
        # 如果原始文本就在限制内，直接返回
        if len(query) <= MAX_TOKENS - 2:  # 减2是为了给特殊token预留空间
            return query

        # 按"|"分割，每次取最后一部分，直到满足长度要求
        while "|" in query and len(query) > MAX_TOKENS - 2:
            parts = query.split("|")
            query = "|".join(parts[1:]).strip()  # 去掉第一段，保留其他所有段

        # 如果分割后还是太长，取最后500个字符
        if len(query) > MAX_TOKENS - 2:
            query = query[-(MAX_TOKENS - 2) :]

        return query

    async def retrieve(
        self,
        query: str,
        filter_action_names: list[str] = [],
        few_shot_limit: int = 2,
        few_shot_score_threshold: float | None = None,
        language: str = LanguageEnum.zh,
        _logger=logger,
    ) -> RetrieveResult:
        start = time.time()

        model = self.embedding_model
        query = self.query_length_format(query=query, model_name=model)
        filter_action_names = [
            a.upper() for a in filter_action_names
        ]  # fewshot的action_name转换为大写

        # 如果外部传入了阈值就用外部的，否则用配置的
        threshold = (
            few_shot_score_threshold
            if few_shot_score_threshold is not None
            else self.few_shot_score_threshold
        )
        search_embedding = await self._embedding(
            [self._get_embedding_prompt(query, model, language)], language
        )

        if not search_embedding:
            logger.warning(
                f"search_embedding is null, query:{query} language:{language}"
            )
            return RetrieveResult(
                query=query,
                action_source="All",
                few_shots=[],
            )

        embedding_end = time.time()

        query_filter = None
        if filter_action_names:
            query_filter = models.Filter(
                must=[
                    models.FieldCondition(
                        key="action_name",
                        match=models.MatchAny(
                            any=filter_action_names,
                        ),
                    )
                ],
            )

        try:
            collection_name = self.get_few_shot_collection_name(self.region_version)
            few_shot_retrieved_result = await self.vector_grpc_client.search(
                collection_name=collection_name,
                query_vector=search_embedding[0].embedding,
                limit=few_shot_limit,
                score_threshold=threshold,
                with_payload=["Input", "Output"],
                query_filter=query_filter,
            )
        except Exception as e:
            logger.error(
                f"Qdrant grpc failed, e:{e}. query:{query}. language:{language}"
            )
            return RetrieveResult(
                query=query,
                action_source="All",
                few_shots=[],
            )

        few_shot_retrieved_end = time.time()

        _logger.info(
            f"retrieved query:{query}. collection_name:{collection_name}\nelapsed_time:{few_shot_retrieved_end - embedding_end}.\nfew_shots: {few_shot_retrieved_result}.\ncount: {len(few_shot_retrieved_result)}"
        )

        few_shots = []
        for point in few_shot_retrieved_result:
            few_shots.append({"score": round(point.score, 3), **point.payload})

        return RetrieveResult(
            query=query,
            # actions=actions,
            action_source="All",
            few_shots=few_shots,
            embedding_elapsed_time=embedding_end - start,
            retrieve_action_elapsed_time=0,
            retrieve_few_shot_elapsed_time=few_shot_retrieved_end - embedding_end,
            build_action_elapsed_time=0,
            total_elapsed_time=few_shot_retrieved_end - start,
        )

    async def init_action_and_few_shot_collection(self):
        """初始化集合

        如果是国内版本仅使用中文，否则为所有非中文语言创建一个统一的collection
        """
        if self.region_version == Area.domestic:
            # 国内版本只初始化中文集合
            languages = [LanguageEnum.zh]
        else:
            # 海外版本获取所有非中文语言
            languages = [
                getattr(LanguageEnum, attr)
                for attr in dir(LanguageEnum)
                if not attr.startswith("_")
                and getattr(LanguageEnum, attr) != LanguageEnum.zh
            ]

        # 获取所有语言的数据
        all_records = []
        for language in languages:
            records = load_all_few_shot(language=language)
            # 为每条记录添加语言标记
            for record in records:
                record["language"] = language
            all_records.extend(records)

        # 创建单个collection包含所有语言的数据
        await self.init_collection_with_alias(
            self.get_few_shot_collection_name(self.region_version), all_records
        )

    async def build_points(
        self, records: list[Action | dict]
    ) -> list[models.PointStruct]:
        if isinstance(records[0], Action):
            return await self.__build_action_points(records)
        else:
            return await self.__build_few_shot_points(records)

    async def __build_action_points(
        self, actions: list[Action]
    ) -> list[models.PointStruct]:
        # TODO：当前只支持中文，后续需要支持多语言进行，不做修改
        actions = [action for action in actions if action.desc_chinese]

        embeddings = await self._embedding(
            [f"{action.desc_chinese}" for action in actions], LanguageEnum.zh
        )
        return [
            models.PointStruct(
                id=uuid.uuid4().hex,
                vector=embedding.embedding,
                payload={
                    "name": action.name,
                    "desc_chinese": action.desc_chinese,
                    "level": action.level,
                    "app_ids": action.app_ids,
                    "package_names": action.package_names,
                    "update_time": datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
                    "language": LanguageEnum.zh,  # 保持原有的语言标记
                },
            )
            for action, embedding in zip(actions, embeddings)
        ]

    async def __build_few_shot_points(
        self, few_shots: list[dict]
    ) -> list[models.PointStruct]:
        # 预处理数据
        processed_shots = []
        for few_shot in few_shots:
            processed = few_shot.copy()
            processed["action_name"] = few_shot["Output"]["action"]
            # processed["input"] = few_shot["Input"]
            # processed["output"] = {
            #     "ACTION": few_shot["Output"]["action"],
            #     "PARAMETERS": few_shot["Output"].get("params", {}),
            # }
            processed_shots.append(processed)

        # 按语言分组处理
        shots_by_language = {}
        for shot in processed_shots:
            language = shot["language"]
            if language not in shots_by_language:
                shots_by_language[language] = []
            shots_by_language[language].append(shot)

        # 为每个语言组批量生成embeddings
        all_points = []
        for language, shots in shots_by_language.items():
            embeddings = await self._embedding(
                [shot["Input"] for shot in shots], language
            )
            # 为每个语言组构建 PointStruct
            points = [
                models.PointStruct(
                    id=uuid.uuid4().hex,
                    vector=embedding.embedding,
                    payload=shot,
                )
                for shot, embedding in zip(shots, embeddings)
            ]
            all_points.extend(points)

        return all_points

    async def rebuild_collection(self, collection_name: str) -> None:
        if await self.vector_grpc_client.collection_exists(collection_name):
            await self.vector_grpc_client.delete_collection(collection_name)
            logger.info(f"Delete collection {collection_name} successful")

        ok = await self.vector_grpc_client.create_collection(
            collection_name=collection_name,
            vectors_config=models.VectorParams(
                size=self.embedding_dim,
                distance=models.Distance.COSINE,
                datatype=models.Datatype.FLOAT32,
            ),
            on_disk_payload=False,  # store payload in memory
        )
        if not ok:
            raise ValueError(f"Create collection {collection_name} failed")

        logger.info(f"Create collection {collection_name} successful")

    @stats_async_func_cost
    async def _embedding(
        self, embedding_input: list[str], language: LanguageEnum
    ) -> list[Embedding]:
        """获取文本的embedding向量

        Args:
            embedding_input: 需要获取embedding的文本列表
            language: 语言类型，用于选择合适的模型

        Returns:
            list[Embedding]: embedding向量列表
        """
        embedding_model = self.embedding_model
        all_embeddings = []

        async def process_batch(batch: list[str], batch_num: int) -> list[Embedding]:
            try:
                if embedding_model in self.OpenaiEmbeddingModels:
                    result = await self.openai_embedding_client.embeddings.create(
                        input=batch,
                        model=embedding_model,
                        dimensions=self.embedding_dim,
                    )
                    embeddings = result.data
                else:
                    embeddings = await self._get_hosted_embeddings(
                        batch, base_index=batch_num * self.max_batch_size
                    )

                return embeddings
            except Exception as e:
                logger.error(f"Error processing batch {batch_num + 1}: {str(e)}")
                raise

        # 串行处理所有批次
        for i in range(0, len(embedding_input), self.max_batch_size):
            batch = embedding_input[i : i + self.max_batch_size]
            batch_embeddings = await process_batch(batch, i // self.max_batch_size)
            all_embeddings.extend(batch_embeddings)

        return all_embeddings

    async def _get_openai_embeddings(self, batch: list[str]) -> list[Embedding]:
        """Get embeddings using OpenAI's API."""
        result = await self.openai_embedding_client.embeddings.create(
            input=batch,
            model=self.embedding_model,
            dimensions=self.embedding_dim,
        )
        return result.data

    async def _get_hosted_embeddings(
        self, batch: list[str], base_index: int = 0
    ) -> list[Embedding]:
        """Get embeddings from self-hosted endpoint."""
        async with aiohttp.ClientSession(
            headers={"Content-Type": "application/json"}
        ) as session:
            response = await session.post(
                url=self.embedding_base_url,
                json={"inputs": batch},
            )

            if response.status != 200:
                error_msg = f"embedding调用异常，地址：{self.embedding_base_url}, status: {response.status}"
                await send_feishu_alarm(error_msg)
                raise RuntimeError(error_msg)

            response_json = await response.json(content_type="application/json")
            return [
                Embedding(
                    embedding=embedding,
                    index=base_index + index,
                    object="embedding",
                )
                for index, embedding in enumerate(response_json)
            ]

    def _embedding_sync(self, embedding_input: list[str]) -> list[Embedding]:
        """Synchronous version of _embedding method"""
        embedding_model = self.embedding_model
        all_embeddings = []

        # Process in batches
        for i in range(0, len(embedding_input), self.max_batch_size):
            batch = embedding_input[i : i + self.max_batch_size]

            try:
                if embedding_model in self.OpenaiEmbeddingModels:
                    batch_embeddings = self._get_openai_embeddings_sync(
                        batch, embedding_model
                    )
                else:
                    batch_embeddings = self._get_hosted_embeddings_sync(
                        batch, base_index=i
                    )
                all_embeddings.extend(batch_embeddings)
            except Exception as e:
                logger.error(f"Embedding generation failed: {str(e)}")
                send_feishu_alarm_sync(
                    f"embedding生成失败。模型：{embedding_model}，错误：{str(e)}"
                )
                raise

        return all_embeddings

    def _get_openai_embeddings_sync(
        self, batch: list[str], embedding_model: str
    ) -> list[Embedding]:
        """Get embeddings using OpenAI's API synchronously."""
        result = self.openai_sync_embedding_client.embeddings.create(
            input=batch,
            model=embedding_model,
            dimensions=self.embedding_dim,
        )
        return result.data

    def _get_hosted_embeddings_sync(
        self, batch: list[str], base_index: int = 0
    ) -> list[Embedding]:
        """Get embeddings from self-hosted endpoint synchronously."""
        import requests

        response = requests.post(
            url=self.embedding_base_url,
            headers={"Content-Type": "application/json"},
            json={"inputs": batch},
        )

        if response.status_code != 200:
            error_msg = f"embedding调用异常，地址：{self.embedding_base_url}, status: {response.status_code}"
            logger.info(error_msg)
            send_feishu_alarm_sync(error_msg)

        response_json = response.json()
        return [
            Embedding(
                embedding=embedding,
                index=base_index + index,
                object="embedding",
            )
            for index, embedding in enumerate(response_json)
        ]

    async def init_collection_with_alias(
        self, collection_name: str, records: list[Action | dict], batch_size: int = 10
    ):
        """使用别名机制平滑初始化collection

        Args:
            collection_name: 集合名称
            records: 要插入的记录列表
            batch_size: 批处理大小

        工作流程：
        1. 创建新的临时collection
        2. 批量写入数据并验证
        3. 更新别名指向新collection，清理旧collection
        """
        timestamp = int(time.time())
        new_collection = f"{collection_name}_{timestamp}"

        logger.info(f"Starting collection initialization: {collection_name}")

        async def create_new_collection():
            """创建临时collection"""
            ok = await self.vector_grpc_client.create_collection(
                collection_name=new_collection,
                vectors_config=models.VectorParams(
                    size=self.embedding_dim,
                    distance=models.Distance.COSINE,
                    datatype=models.Datatype.FLOAT32,
                ),
                on_disk_payload=False,
            )
            if not ok:
                raise ValueError(f"Failed to create collection {new_collection}")
            logger.info(f"Created temporary collection: {new_collection}")

        async def upload_records():
            """批量上传数据"""
            points_uploaded = 0
            for i in range(0, len(records), batch_size):
                batch_records = records[i : i + batch_size]
                points = await self.build_points(batch_records)
                if points:
                    self.vector_grpc_client.upload_points(
                        collection_name=new_collection,
                        points=points,
                        wait=True,
                    )
                    points_uploaded += len(points)
                    logger.info(
                        f"Uploaded batch {i // batch_size + 1}, total points: {points_uploaded}"
                    )
            return points_uploaded

        async def verify_collection():
            """验证collection数据完整性"""
            collection_info = await self.vector_grpc_client.get_collection(
                new_collection
            )
            if not collection_info:
                raise ValueError(f"Failed to get collection: {new_collection}")

            # 检查向量维度是否正确
            vector_params = collection_info.config.params.vectors
            if vector_params.size != self.embedding_dim:
                raise ValueError(
                    f"Collection dimension mismatch. Expected: {self.embedding_dim}, Got: {vector_params.size}"
                )

            # 检查点数是否符合预期
            expected_points = len(records)
            actual_points = collection_info.points_count
            if actual_points != expected_points:
                raise ValueError(
                    f"Points count mismatch. Expected: {expected_points}, Got: {actual_points}"
                )

            logger.info(
                f"Collection verification passed. Points count: {actual_points}"
            )
            return collection_info

        async def update_alias():
            """更新别名指向，并保留最近的3个collection作为备份"""
            # 获取所有collections
            collections = await self.vector_grpc_client.get_collections()

            # 找出与当前collection_name相关的所有collection
            related_collections = [
                c.name
                for c in collections.collections
                if c.name.startswith(f"{collection_name}_")
            ]

            # 按时间戳排序（最新的在前）
            related_collections.sort(reverse=True)

            # 先更新别名指向新collection
            await self.vector_grpc_client.update_collection_aliases(
                change_aliases_operations=[
                    models.CreateAliasOperation(
                        create_alias=models.CreateAlias(
                            collection_name=new_collection, alias_name=collection_name
                        )
                    )
                ]
            )
            logger.info(f"Updated alias: {collection_name} -> {new_collection}")

            # 再删除超过保留限制的旧collection（保留最新的3个）
            if len(related_collections) >= 3:
                for old_collection in related_collections[3:]:  # 从第4个开始删除
                    await self.vector_grpc_client.delete_collection(old_collection)
                    logger.info(f"Deleted old backup collection: {old_collection}")

        try:
            # 1. 创建新collection
            await create_new_collection()

            # 2. 上传并验证数据
            points_uploaded = await upload_records()
            logger.info(f"Successfully uploaded {points_uploaded} points")

            await verify_collection()

            # 3. 更新别名
            await update_alias()

            logger.info(f"Successfully initialized collection: {collection_name}")

        except Exception as e:
            logger.error(f"Error during collection initialization: {str(e)}")
            # 清理临时collection
            try:
                await self.vector_grpc_client.delete_collection(new_collection)
                logger.info(f"Cleaned up temporary collection: {new_collection}")
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {str(cleanup_error)}")
            raise


MaterialRetriever = _MaterialRetriever(version="draft", env=agent_setting.env)
