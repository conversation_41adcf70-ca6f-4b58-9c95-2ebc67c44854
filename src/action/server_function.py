import asyncio
import json
import time
import traceback
import urllib.parse
import uuid
from copy import deepcopy
from typing import Dict, List, Optional, Union

import aiohttp
import yaml
from loguru import logger

from mcp.types import CallToolResult
from src.action.function_register import action_function_register
from src.action.model import (
    AudioOutput,
    AudioRequest,
    BusinessInfo,
    FunctionOutput,
    FunctionResult,
    ImageContent,
    RequestLLMConfig,
    NonStreamingContent,
    StreamingContent,
    LLMResponse,
)
from src.action.resource import (
    get_promote_product_info,
    get_promote_brief_product_info,
)
from src.action.skill_center.router import (
    RouteService,
    build_recommendation_text,
    generate_route_recommendation_text,
    get_route_info,
    match_route_with_llm,
)
from src.action.skill_center.skill_tools import SkillCenter
from src.common.constant import (
    PROMOTE_USER_TEXT,
    USER_ROLE,
    Area,
    SynthesizeType,
    dialogue_strategy_list_general,
    dialogue_strategy_list_trigger,
    dialogue_strategy_list_general_v3,
    dialogue_strategy_list_trigger_v3,
)
from src.common.model import RunStep
from src.common.toolkit import LLMToolKit
from src.settings import agent_setting
from src.utils.async_utils import (
    get_action_ouput_language,
)
from src.utils.date import get_current_date_str
from src.utils.feishu_alarm import send_feishu_alarm
from src.utils.general_environment_sense import (
    get_weather,
)
from src.utils.handle_image import get_image_content
from src.utils.i18n import _
from src.utils.llm import LLMManager, LLMConfig, ModelResult
from src.utils.re_utils import ReUtils
from src.session_manager.memory import Memory


async def put_run_step(
    step_name: str, step_result: dict = None, **kwargs
) -> Optional[asyncio.Queue]:
    logger.info(f"put_run_step: {step_name} {step_result}")
    try:
        run_step_queue: Optional[asyncio.Queue] = kwargs["__run_step_queue"]
        if run_step_queue:
            await run_step_queue.put(
                RunStep(
                    step_name=step_name,
                    step_scope="action_executor",
                    step_result=step_result,
                )
            )

        return run_step_queue
    except Exception as e:
        logger.error(f"put_run_step error: {e}")
        return


@action_function_register
async def say(text: str, **kwargs) -> FunctionResult:
    return FunctionResult(
        type="audio",
        content=AudioOutput(content=NonStreamingContent(text=text), audio=text),
    )


@action_function_register
async def clarify(request_clarify_text: str, **kwargs) -> FunctionResult:
    return FunctionResult(
        type="audio",
        content=AudioOutput(
            content=NonStreamingContent(text=request_clarify_text),
        ),
    )


@action_function_register
async def reject(reason: str, **kwargs) -> FunctionResult:
    return FunctionResult(
        type="audio",
        content=AudioOutput(content=NonStreamingContent(text=reason)),
    )


def compress_weather_info(original_data: dict) -> dict:
    simple_data = {}

    if (
        skill_data := original_data.get("response", {})
        .get("display", {})
        .get("data", {})
    ):
        simple_data["weatherNow"] = skill_data.get("weatherNow", {})
        simple_data["weatherPredicts"] = []
        for predict in skill_data.get("weatherPredicts", []):
            weather_info = predict.copy()
            del weather_info["weatherPic"]
            simple_data["weatherPredicts"].append(weather_info)

    simple_data["card"] = original_data.get("response", {}).get("card", {})

    return simple_data


@action_function_register
async def query_weather(
    city: str, area_level: str = "city", **kwargs
) -> FunctionResult:
    """整合实时和未来天气信息回答用户问题"""
    start_time = time.time()

    sc = SkillCenter()
    start_get_weather = time.time()
    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        robot_language = robot.language
    await put_run_step("fetch_weather_info", **kwargs)
    future_weather, realtime_weather = await asyncio.gather(
        sc.get_future_weather(
            city, robot_language, area_level, "未来10天", agent_setting.region_version
        ),
        sc.get_realtime_weather(city, robot_language, agent_setting.region_version),
    )
    get_weather_cost_time = time.time() - start_get_weather

    logger.info(
        f"[query_weather] fetch weather api cost: {get_weather_cost_time} seconds"
    )

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    text_conversation = await LLMToolKit.get_text_conversation_records(
        chat_context.messages,
        limit=2,
    )

    user_query = kwargs["_USER_QUERY"]
    if user_query and user_query not in text_conversation:
        text_conversation.append(f"<User> said '{user_query}'")

    text_conversation = "\n".join(text_conversation)

    await put_run_step("summarize_answer", **kwargs)

    lang_question = kwargs.get("_USER_QUERY")
    action_result = get_action_ouput_language(
        lang_question, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]

    try:
        weather_info = f"""{json.dumps(compress_weather_info(future_weather), ensure_ascii=False, indent=2)}"""
    except Exception as e:
        logger.error(f"compress_weather_info error: {e}")
        weather_info = ""

    messages = [
        {
            "role": "system",
            "content": "你是一个专业的天气分析助手，综合天气信息和聊天对话，回答用户的问题。",
        },
        {
            "role": "user",
            "content": f"""# 实时天气信息
{json.dumps(realtime_weather, ensure_ascii=False, indent=2)}

# 未来天气信息
{weather_info}

# 聊天对话
{text_conversation}

答案必须纯文本输出，字数在35字以内，语言为{language}。""",
        },
    ]

    logger.info(f"[query_weather] llm prompt: {messages[-1]['content']}")

    return FunctionResult(
        type="function",
        elapse_info={
            "fetch_weather_api_cost_time": get_weather_cost_time,
            "total_time": time.time() - start_time,
        },
        debug={
            "language": language,
            "messages": messages,
        },
        content=FunctionOutput(
            result=future_weather,
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.3,
                        max_tokens=60,
                    ).model_dump(),
                )
            ),
        ),
    )


@action_function_register
async def weather(
    user_question: str, city: str, area_level: str, time: str = "未来10天", **kwargs
) -> FunctionResult:
    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        if agent_setting.region_version == Area.overseas:
            if str(city) == "-1":
                city = f"{robot.geo_location.latitude},{robot.geo_location.longitude}"
        else:
            if str(city) == "-1":
                city = robot.geo_location.city
    sc = SkillCenter()

    await put_run_step("fetch_weather_info", **kwargs)
    result = await sc.get_future_weather(
        city, robot.language, area_level, time, agent_setting.region_version
    )

    if "__memory" in kwargs:
        chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=3)
        conversation_progress = "\n".join(
            LLMToolKit.build_conversation_progress(chat_context.messages)
        )
    else:
        conversation_progress = ""
    lang_question = kwargs.get("_USER_QUERY") or user_question
    action_result = get_action_ouput_language(
        lang_question, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]

    prompt = f"""基于天气信息结合聊天上下文并且使用{language}回答用户问题，答案必须纯文本输出，字数在35字以内.
       ## 天气信息
       {json.dumps(result, ensure_ascii=False, indent=2)}
       ## 聊天上下文
       {conversation_progress}
       ## 用户问题
       {user_question}
       """
    messages = []
    # if is_multilingual(kwargs.get("__robot")):
    #     messages.append(
    #         LLMToolKit.build_multilingual_user_content(lang_question, detect_lang)
    #     )
    messages.append(
        {
            "role": "user",
            "content": prompt,
        }
    )
    await put_run_step("summarize_answer", **kwargs)
    model_result = await LLMManager.invoke_generate_text_model(messages=messages)
    logger.info(f"[weather] prompt: {prompt}. Output:{model_result.content}")
    result["response"]["outSpeech"]["text"] = model_result.content
    result["answer_text"] = model_result.content
    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result=result,
            audio_request=AudioRequest(
                content=NonStreamingContent(text=model_result.content)
            ),
        ),
    )


@action_function_register
async def calendar(user_question: str, **kwargs) -> FunctionResult:
    start_time = time.time()
    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        robot_language = robot.language  # 输出语言  zh/en/de
        timezone = robot.timezone
        geo_coordinates = (
            f"{robot.geo_location.latitude},{robot.geo_location.longitude}"
        )
    sc = SkillCenter()

    await put_run_step("fetch_calendar_info", **kwargs)
    result = await sc.get_calendar(
        user_question=user_question,
        env=agent_setting.region_version,
        timezone=timezone,
        language=robot_language,
        geo_location=geo_coordinates,
    )

    # 保存原始问题
    original_question = user_question

    # 如果display为空且未设置stop_retry，则使用基本日期查询作为fallback
    if not result.get("response", {}).get("display") and not kwargs.get("stop_retry"):
        # 兜底问题
        fallback_query = _("What date is today?")
        fallback_result = await sc.get_calendar(
            user_question=fallback_query,
            env=agent_setting.region_version,
            timezone=timezone,
            language=robot_language,
            geo_location=geo_coordinates,
        )
        # 使用兜底结果中的display信息
        result["response"]["display"] = fallback_result.get("response", {}).get(
            "display", {}
        )
        result["desc"]["display"] = fallback_result.get("desc", {}).get("display", {})

    # 如果经过一次兜底还是没有返回display字典，需要使用 setdefault 确保所有 display 字段都为空字典而不是 None
    result.setdefault("response", {})["display"] = (
        result.get("response", {}).get("display") or {}
    )
    result.setdefault("desc", {})["display"] = (
        result.get("desc", {}).get("display") or {}
    )
    display = result["response"]["display"]

    if "__memory" in kwargs:
        chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
        conversation_progress = "\n".join(
            LLMToolKit.build_conversation_progress(chat_context.messages)
        )
    else:
        conversation_progress = ""
    lang_question = kwargs.get("_USER_QUERY") or user_question
    action_result = get_action_ouput_language(
        lang_question, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]

    prompt = f"""基于日历信息结合聊天上下文并且使用{language}回答用户问题。答案必须纯文本输出，字数在35字以内。
注意：
1. 如果询问是否要放假，结合是否属于所属国家的法定假期进行回答
2. 如果日历信息中没有直接答案，请基于当前日期信息合理推断回答

## 日历信息
当前时间：{get_current_date_str(robot.timezone)}
{display}
## 聊天上下文
{conversation_progress}
## 用户问题
{original_question}
"""
    messages = []
    messages.append(
        {
            "role": "user",
            "content": prompt,
        },
    )
    await put_run_step("summarize_answer", **kwargs)
    model_result = await LLMManager.invoke_generate_text_model(messages=messages)

    logger.info(
        f"[calendar] action llm prompt: {prompt}\n***calendar llm result***:>>>>{model_result.content}<<<<"
    )
    result["response"]["outSpeech"]["text"] = model_result.content
    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result=result,
            audio_request=AudioRequest(
                content=NonStreamingContent(text=model_result.content)
            ),
        ),
        debug={"action_result": result},
        elapse_info={"action_cost_time": time.time() - start_time},
    )


@action_function_register
async def send_message(
    recipient_name: str,
    message_content: str,
    message_type: str,
    sender_photo_url: str,
    person_id: str = "",
    **kwargs,
) -> FunctionResult:
    search_user_url = urllib.parse.urljoin(
        agent_setting.robot_openapi_host, agent_setting.corp_search_user_path
    )
    corp_headers = {
        "orionstar-api-key": agent_setting.robot_openapi_key,
    }
    session = aiohttp.ClientSession()

    await put_run_step("search_user", **kwargs)

    if not person_id:
        request_body = {
            "ov_corp_id": "orion.ovs.entprise.1429922673",
            "get_action": "aios_send_person_messages",
            "full_name": recipient_name,
        }
        async with session.post(
            search_user_url, json=request_body, headers=corp_headers
        ) as response:
            if response.status != 200:
                await send_feishu_alarm(
                    f"send message api调用异常，地址：{search_user_url}, status: {response.status}"
                )
            result = await response.json()
            logger.info(
                f"search user response: {json.dumps(result, indent=2, ensure_ascii=False)}"
            )

        person_count = int(result["data"]["total_count"])
        if person_count > 1:
            logger.warning(
                f"More than one person found: {recipient_name} {person_count}"
            )
            return FunctionResult(
                type="function",
                content=FunctionOutput(
                    result={
                        "status": "failed",
                        "message": "More than one person found",
                    },
                    audio_request=AudioRequest(
                        content=NonStreamingContent(
                            text="哎呀，找到了多个人，我不知道该给谁发消息"
                        )
                    ),
                ),
            )
        elif person_count == 0:
            logger.warning(f"No person found: {recipient_name}")
            return FunctionResult(
                type="function",
                content=FunctionOutput(
                    result={"status": "failed", "message": "No person found"},
                    audio_request=AudioRequest(
                        content=NonStreamingContent(
                            text="哎呀，找不到这个人，我不知道该给谁发消息"
                        )
                    ),
                ),
            )

        person_id = result["data"]["person_list"][0]["person_id"]
        person_fullname = result["data"]["person_list"][0]["person"]["full_name"]
    else:
        person_fullname = recipient_name

    # 美颜
    if agent_setting.meiyan:
        bot_id = "7424484158573625394"
        session = aiohttp.ClientSession()
        try:
            meiyan_result = await _call_coze_bot(
                "", "帮我美颜", bot_id, session, image_url=sender_photo_url
            )
            # 已为你美颜图片：[点击查看](https://s.coze.cn/t/Cnf30AhrPH20p68Q/)。 extract url
            meiyan_content = meiyan_result["data"][2]["content"]
            sender_photo_url = json.loads(meiyan_content)["output"]
        except Exception as e:
            logger.error(f"Failed to call coze meiyan bot: {e} {bot_id}")

    send_message_url = urllib.parse.urljoin(
        agent_setting.robot_openapi_host, agent_setting.corp_send_message_path
    )
    request_body = {
        "msg_type": "aios_text_notice",
        "person_id": person_id,
        "msg_content": message_content,
        "msg_urgent": "1" if message_type == "urgent" else "0",
        "sender_info": {"sender_image": [{"url": sender_photo_url}]},
    }
    await put_run_step("send_message", **kwargs)
    async with session.post(
        send_message_url, json=request_body, headers=corp_headers
    ) as response:
        if response.status != 200:
            await send_feishu_alarm(
                f"send message api调用异常，地址：{send_message_url}, status: {response.status}"
            )
        result = await response.json()
        logger.info(
            f"send message response: {json.dumps(result, indent=2, ensure_ascii=False)}"
        )
        function_result = FunctionResult(
            type="function",
            content=FunctionOutput(
                result=result,
                audio_request=AudioRequest(
                    content=NonStreamingContent(text=f"消息已发送给{person_fullname}")
                ),
            ),
        )

    await session.close()
    return function_result


@action_function_register
async def coze_generate_picture(
    _CONTEXT: str, question: str, **kwargs
) -> FunctionResult:
    bot_id = "7419188910275788834"
    session = aiohttp.ClientSession()
    try:
        result = await _call_coze_bot(_CONTEXT, question, bot_id, session)
    except Exception as e:
        logger.error(f"Failed to call coze bot: {e}")
        await session.close()
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )
    await session.close()
    if not result:
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )

    image_url = ""
    for chat in result["data"]:
        if chat["role"] == "assistant" and chat["type"] == "tool_response":
            image_info = json.loads(chat["content"])
            image_url = image_info["data"]["images"][0]["image_url"]

    if not image_url:
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )

    image = ImageContent(file_url=image_url)
    return FunctionResult(
        type="function",
        content=FunctionOutput(result={"images": [image.model_dump()]}, images=[image]),
    )


@action_function_register
async def coze_talk_to_raiden(_CONTEXT: str, question: str, **kwargs):
    bot_id = "7419190267750334503"
    session = aiohttp.ClientSession()
    try:
        result = await _call_coze_bot(_CONTEXT, question, bot_id, session)
    except Exception as e:
        logger.error(f"Failed to call coze bot: {e}")
        await session.close()
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )
    await session.close()

    if not result:
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )
    # find answer
    corpus = ""
    for chat in result["data"]:
        if chat["role"] == "assistant" and chat["type"] == "answer":
            corpus = chat["content"]
            break

    if not corpus:
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={},
            audio_request=AudioRequest(content=NonStreamingContent(text=corpus)),
        ),
    )


@action_function_register
async def _call_coze_bot(
    _CONTEXT: str,
    question: str,
    bot_id: str,
    session: "aiohttp.ClientSession",
    image_url: str = None,
    **kwargs,
):
    coze_api_url = agent_setting.coze_bot_url
    coze_headers = {
        "Authorization": "Bearer pat_RBV1qjJc5UUKEreQpQNdDI5BCOultd8bqPGquEOXM24oTb9Kq6ev9Rsv0L4DiP8H",
        "Content-Type": "application/json",
    }
    if image_url:
        messages = [
            {
                "role": "user",
                "content": json.dumps(
                    [
                        {
                            "type": "text",
                            "text": "美颜",
                        },
                        {
                            "type": "image",
                            "file_url": image_url,
                        },
                    ]
                ),
                "content_type": "object_string",
            }
        ]
    else:
        messages = [
            {
                "role": "user",
                "content": question,
                "content_type": "text",
            },
        ]

    request_body = {
        "bot_id": bot_id,
        "user_id": uuid.uuid4().hex,
        "stream": False,  # TODO: support stream
        "auto_save_history": True,
        "additional_messages": messages,
    }
    # request coze bot
    async with session.post(
        coze_api_url, json=request_body, headers=coze_headers
    ) as response:
        if response.status != 200:
            await send_feishu_alarm(
                f"_call_coze_bot调用异常，地址：{coze_api_url}, status: {response.status}"
            )
        result = await response.json()
        logger.debug(
            f"coze response: {json.dumps(result, indent=2, ensure_ascii=False)}"
        )

    # fetch chat id and conversation id
    chat_id = result["data"]["id"]
    conversation_id = result["data"]["conversation_id"]

    # loop until get the result, get method, url parameter chat_id, coversation_id
    status_url = agent_setting.coze_bot_retrieve_url
    status = False
    retry_times = 30
    while not status and retry_times > 0:
        async with session.get(
            status_url,
            params={
                "chat_id": chat_id,
                "conversation_id": conversation_id,
            },
            headers=coze_headers,
        ) as response:
            result = await response.json()
            logger.debug(
                f"coze response: {json.dumps(result, indent=2, ensure_ascii=False)}"
            )

            status = result["data"]["status"] == "completed"
            retry_times -= 1
            await asyncio.sleep(2)  # max 1min

    if not status:
        return

    # get chat result
    result_url = agent_setting.coze_bot_get_message_list
    async with session.get(
        result_url,
        params={
            "chat_id": chat_id,
            "conversation_id": conversation_id,
        },
        headers=coze_headers,
    ) as response:
        result = await response.json()
        logger.debug(
            f"coze response: {json.dumps(result, indent=2, ensure_ascii=False)}"
        )
        return result


@action_function_register
async def coze_search_picture(_CONTEXT: str, question: str, **kwargs):
    bot_id = "7419240500814757898"
    session = aiohttp.ClientSession()
    try:
        result = await _call_coze_bot(_CONTEXT, question, bot_id, session)
    except Exception as e:
        logger.error(f"Failed to call coze bot: {e}")
        await session.close()
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )
    await session.close()

    # get the image url
    if not result:
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )

    images = []
    for chat in result["data"]:
        if chat["role"] == "assistant" and chat["type"] == "tool_response":
            images = json.loads(chat["content"])
            break

    if not images:
        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text="请求扣子失败啦，呜呜呜")
                ),
            ),
        )

    image_result = []
    for i, image in enumerate(images):
        image_content = ImageContent(
            file_url=image["picture_info"]["display_url"],
            width=int(image["picture_info"]["size"]["width"]),
            height=int(image["picture_info"]["size"]["height"]),
        )
        image_result.append(image_content)

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={"images": [image.model_dump() for image in image_result]},
            images=image_result,
        ),
    )


@action_function_register
async def get_realtime_weather(user_question: str, city: str, **kwargs):
    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        if agent_setting.region_version == Area.overseas:
            if str(city) == "-1":
                city = f"{robot.geo_location.latitude},{robot.geo_location.longitude}"
        else:
            if str(city) == "-1":
                city = robot.geo_location.city
    sc = SkillCenter()
    await put_run_step("fetch_weather_info", **kwargs)
    city_weather = await sc.get_realtime_weather(city, env=agent_setting.region_version)

    if "__memory" in kwargs:
        chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=3)
        conversation_progress = "\n".join(
            LLMToolKit.build_conversation_progress(chat_context.messages)
        )
    else:
        conversation_progress = ""
    lang_question = kwargs.get("_USER_QUERY") or user_question
    action_result = get_action_ouput_language(
        lang_question, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]

    prompt = f"""基于实时天气信息，结合聊天上下文并且使用{language}回答用户问题。答案必须纯文本输出.
    ## 实时天气信息
    {city_weather if city_weather else "未找到相关天气信息"}
    ## 聊天上下文
    {conversation_progress}
    ## 用户问题
    {user_question}
    """
    messages = []
    # if is_multilingual(kwargs.get("__robot")):
    #     messages.append(
    #         LLMToolKit.build_multilingual_user_content(lang_question, detect_lang)
    #     )
    messages.append(
        {
            "role": "user",
            "content": prompt,
        }
    )
    await put_run_step("summarize_answer", **kwargs)
    model_result = await LLMManager.invoke_generate_text_model(messages=messages)

    logger.info(
        f"[get_realtime_weather] action llm prompt: {prompt}\nOutput{model_result.content}"
    )
    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result=city_weather,
            audio_request=AudioRequest(
                content=NonStreamingContent(text=model_result.content)
            ),
        ),
    )


@action_function_register
async def generate_message(goal: str = "", **kwargs):
    app_id = kwargs["__robot"].interface_state.app_id
    agent_id = kwargs["__robot"].agent_id

    lang_question = kwargs.get("_USER_QUERY") or goal
    action_result = get_action_ouput_language(
        lang_question, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]

    await put_run_step("prepare_answer", **kwargs)

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    conversation_progress = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context.messages)
    )

    if lang_question not in conversation_progress:
        conversation_progress += f"\n{USER_ROLE} just said '{lang_question}'"

    messages = [
        {
            "role": "system",
            "content": LLMToolKit.build_system_prompt(
                agent_id,
                app_id,
                persona=kwargs["__robot"].PERSONA,
                language_style=kwargs["__robot"].LANGUAGE_STYLE,
                objective=kwargs["__robot"].OBJECTIVE,
            ),
        },
        {
            "role": "user",
            "content": f"""根据下面的聊天上下文，使用{language}生成一段文采飞扬的话吧！注意，生成的内容不能包含任何markdown格式的内容，直接给出回答内容，纯文本输出。
## 聊天上下文
{conversation_progress}""",
        },
    ]
    # if is_multilingual(kwargs.get("__robot")):
    #     messages.append(
    #         LLMToolKit.build_multilingual_user_content(lang_question, detect_lang)
    #     )
    # messages.append(
    #     {
    #         "role": "user",
    #         "content": f"""按照用户的目的：{goal}，使用{language}生成一段文采飞扬的话吧！注意，生成的内容不能包含任何markdown格式的内容，直接给出回答内容，纯文本输出.""",
    #     },
    # )
    logger.info(f"generate_message messages is {messages}")
    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.7,
                    ).model_dump(),
                )
            ),
        ),
    )


@action_function_register
async def choice_item_by_llm(choice_list: list[dict], **kwargs) -> tuple[int, str]:
    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    conversation_progress = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context.messages)
    )

    if user_latest_query := kwargs.get("_USER_QUERY"):
        if user_latest_query not in conversation_progress:
            conversation_progress += f"\n{USER_ROLE} SAY '{user_latest_query}'"

    choice_list_str = "\n".join(
        f"ID:{idx}.{item}" for idx, item in enumerate(choice_list)
    )
    messages = [
        {
            "role": "user",
            "content": f"""请根据聊天记录和用户最新的需求，从以下元素列表中选择一个最相关的内容。
# 选择规则
1. 优先匹配用户最新表达的需求或兴趣
2. 如果用户提到具体功能，优先选择该功能的详细介绍
3. 如果用户想了解产品，优先选择产品介绍
4. 如果用户刚看完某个内容，不要重复选择相同内容
# 元素列表
{choice_list_str}
# 聊天记录
{conversation_progress}
# 输出格式(YAML)
```yaml
TEXT: <用完整的陈述句回答用户问题，如“这是XXX的信息”或“XXX如下”，不要反问或重复用户问题，15字以内>
ID: <index of choice_list>
```
""",
        },
    ]

    model_result = await LLMManager.invoke_generate_text_model(messages=messages)
    logger.info(
        f"[choice_item_by_llm] action llm prompt: {messages[-1]['content']}\nOutput{model_result.content}"
    )
    try:
        yaml_result = yaml.safe_load(
            model_result.content.replace("```yaml", "").replace("```", "").strip()
        )
        return int(yaml_result["ID"]), yaml_result["TEXT"]
    except Exception as e:
        logger.error(f"[choice_item_by_llm] error: {e}")
        return 0, ""


@action_function_register
async def case_introduction_v2(**kwargs) -> FunctionResult:
    start_time = time.time()
    debug = {}
    elapse_info = {}
    item_summary = []
    item = []
    await put_run_step("fetch_production_info", **kwargs)
    product_infos = await get_promote_product_info(kwargs["__robot"])
    for case in product_infos:
        product_name = case.get("base_info", {}).get("product_name", "")
        case_list = case.get("detail_config", {}).get("case_list", [])
        for q in case_list:
            name = q.get("name", "")
            file_desc = q.get("file", {}).get("file_desc", "")
            item_summary.append(
                f"产品：{product_name}，案例名称：{name}，案例详细介绍：{file_desc}"
            )
            item.append(
                {
                    "title": f"{product_name}-{name}",
                    "url": [
                        {
                            "file_url": q.get("file", {}).get("file_url", ""),
                            "cover_url": q.get("file", {}).get("cover_url", ""),
                            "type": q.get("file", {}).get("type", ""),
                            "video_duration": float(
                                q.get("file", {}).get("video_duration", -1)
                            ),
                        }
                    ],
                    "text_content": file_desc,
                }
            )
    await put_run_step("select_best_case_introduction", **kwargs)
    choice_id, text = await choice_item_by_llm(item_summary, **kwargs)
    function = item[choice_id]
    debug["case_introduction_id"] = choice_id
    debug["case_introduction_function"] = function
    debug["case_introduction_text"] = text
    elapse_info["case_introduction_time"] = time.time() - start_time
    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result=function,
            audio_request=AudioRequest(content=NonStreamingContent(text=text)),
        ),
    )


@action_function_register
async def production_function_v2(**kwargs) -> FunctionResult:
    start_time = time.time()
    await put_run_step("fetch_production_info", **kwargs)
    product_info = await get_promote_product_info(kwargs["__robot"])
    item_summary = []
    item = []
    debug = {}
    elapse_info = {}
    for p in product_info:
        product_name = p.get("base_info", {}).get("product_name", "")
        # 首页的视频信息
        home_config = p.get("home_config", {})
        feat_list = home_config.get("feat_list", [])
        for temp_data in feat_list:
            name = temp_data.get("name", "")
            file_desc = temp_data.get("jump", {}).get("file_desc", "")
            item_summary.append(
                f"产品名称：{product_name}， 功能名称：{name}， 功能介绍：{file_desc}"
            )
            item.append(
                {
                    "title": f"{product_name}-功能介绍-{name}",
                    "url": [
                        {
                            "file_url": temp_data.get("jump", {}).get("file_url", ""),
                            "cover_url": temp_data.get("img", {}).get("file_url", ""),
                            "type": temp_data.get("jump", {}).get("type", ""),
                            "video_duration": float(
                                temp_data.get("jump", {}).get("video_duration", -1)
                            ),
                            "text_content": file_desc,
                        }
                    ],
                },
            )

        # 详情配置信息
        detail_config = p.get("detail_config", {})
        # 功能介绍
        feature_list = detail_config.get("feature_list", [])
        for temp_data in feature_list:
            name = temp_data.get("name", "")
            file_desc = temp_data.get("file", {}).get("file_desc", "")
            item_summary.append(
                f"产品名称：{product_name}， 功能名称：{name}， 功能介绍：{file_desc}"
            )
            item.append(
                {
                    "title": f"{product_name}-功能介绍-{name}",
                    "url": [
                        {
                            "file_url": temp_data.get("file", {}).get("file_url", ""),
                            "cover_url": temp_data.get("file", {}).get("cover_url", ""),
                            "type": temp_data.get("file", {}).get("type", ""),
                            "video_duration": float(
                                temp_data.get("file", {}).get("video_duration", -1)
                            ),
                            "text_content": file_desc,
                        }
                    ],
                },
            )

        # 成功案例
        # case_list = detail_config.get("case_list", [])
        # for temp_data in case_list:
        #     name = temp_data.get("name", "")
        #     item_summary.append(f"成功案例： {name}")
        #     item.append(
        #         {
        #             "title": f"{product_name}-成功案例-{name}",
        #             "url": [
        #                 {
        #                     "file_url": temp_data.get("file", {}).get("file_url", ""),
        #                     "cover_url": temp_data.get("file", {}).get("cover_url", ""),
        #                     "type": temp_data.get("file", {}).get("type", ""),
        #                 }
        #             ],
        #             "text_content": name,
        #         },
        #     )

        # 产品规格
        params_list = detail_config.get("params_list", [])
        urls = []
        for temp_data in params_list:
            urls.append(
                {
                    "file_url": temp_data.get("file", {}).get("file_url", ""),
                    "cover_url": temp_data.get("file", {}).get("cover_url", ""),
                    "type": temp_data.get("file", {}).get("type", ""),
                    "video_duration": float(
                        temp_data.get("file", {}).get("video_duration", -1)
                    ),
                    "text_content": temp_data.get("file", {}).get("file_desc", ""),
                }
            )
        item_summary.append(f"{product_name}的产品参数规格")
        item.append(
            {
                "title": f"{product_name}-产品参数规格",
                "url": urls,
            },
        )

    await put_run_step("select_best_production_function", **kwargs)
    choice_id, text = await choice_item_by_llm(item_summary, **kwargs)
    function = item[choice_id]
    debug["choice_item_by_llm_id"] = choice_id
    debug["choice_item_by_llm_function"] = function
    debug["choice_item_by_llm_text"] = text
    elapse_info["production_function_time"] = time.time() - start_time
    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse_info,
        content=FunctionOutput(
            result=function,
            audio_request=AudioRequest(content=NonStreamingContent(text=text)),
        ),
    )


@action_function_register
async def production_parameters(**kwargs) -> FunctionResult:
    start_time = time.time()
    debug = {}
    elapse_info = {}
    await put_run_step("fetch_production_info", **kwargs)
    product_info = await get_promote_product_info(kwargs["__robot"])
    item_summary = []
    item = []

    for p in product_info:
        product_name = p.get("base_info", {}).get("product_name", "")

        # 详情配置信息
        detail_config = p.get("detail_config", {})

        # 产品规格
        params_list = detail_config.get("params_list", [])
        urls = []
        for temp_data in params_list:
            urls.append(
                {
                    "file_url": temp_data.get("file", {}).get("file_url", ""),
                    "cover_url": temp_data.get("file", {}).get("cover_url", ""),
                    "type": temp_data.get("file", {}).get("type", ""),
                    "video_duration": float(
                        temp_data.get("file", {}).get("video_duration", -1)
                    ),
                    "text_content": temp_data.get("file", {}).get("file_desc", ""),
                }
            )
        item_summary.append(f"{product_name}的产品参数规格")
        item.append(
            {
                "title": f"{product_name}-产品参数规格",
                "url": urls,
            },
        )

    await put_run_step("select_production_parameters", **kwargs)
    choice_id, text = await choice_item_by_llm(item_summary, **kwargs)
    function = item[choice_id]
    debug["production_parameters_id"] = choice_id
    debug["production_parameters_function"] = function
    debug["production_parameters_text"] = text
    elapse_info["production_parameters_time"] = time.time() - start_time
    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse_info,
        content=FunctionOutput(
            result=function,
            audio_request=AudioRequest(content=NonStreamingContent(text=text)),
        ),
    )


@action_function_register
async def production_detail_v2(production: str, **kwargs):
    text_content = {}
    product_info = await get_promote_product_info(kwargs["__robot"])
    await put_run_step("fetch_production_info", **kwargs)
    product = {}
    product_detail = []
    audio_request = None
    debug = {}
    elapse_info = {}
    for p in product_info:
        if p.get("base_info", {}).get("product_name", "") == production:
            text_content_list = []
            product = {
                "id": 1,
                "image_url": p.get("detail_config", {})
                .get("product_img", {})
                .get("file_url", ""),
                "title": p.get("detail_config", {}).get("title", ""),
                "subtitle": p.get("detail_config", {}).get("desc", ""),
                "description": "",
                "tag_list": p.get("detail_config", {}).get("tag_list", []),
            }
            product_desc = p.get("base_info", {}).get("product_desc", "")
            if product_desc:
                text_content_list.append(f"产品 {production} 描述：{product_desc}")
            # 功能介绍
            temp_urls = []
            for feature_list_item in p.get("detail_config", {}).get("feature_list", []):
                name = feature_list_item.get("name", "")
                temp_urls.append(
                    {
                        "file_url": feature_list_item.get("file", {}).get(
                            "file_url", ""
                        ),  # 视频url
                        "cover_url": feature_list_item.get("file", {}).get(
                            "cover_url", ""
                        ),  # 封面url
                        "type": feature_list_item.get("file", {}).get("type", ""),
                        "name": name,
                        "video_duration": float(
                            feature_list_item.get("file", {}).get("video_duration", -1)
                        ),
                    }
                )
                file_desc = feature_list_item.get("file", {}).get("file_desc", "")
                text_content_list.append(
                    f"产品 {production} 功能：{name}，描述： {file_desc}"
                )
            product_detail.append(
                {
                    "title": "功能介绍",
                    "urls": temp_urls,
                }
            )
            temp_urls = []
            for case_list_item in p.get("detail_config", {}).get("case_list", []):
                name = case_list_item.get("name", "")
                temp_urls.append(
                    {
                        "file_url": case_list_item.get("file", {}).get(
                            "file_url", ""
                        ),  # 视频url
                        "cover_url": case_list_item.get("file", {}).get(
                            "cover_url", ""
                        ),  # 封面url
                        "type": case_list_item.get("file", {}).get("type", ""),
                        "name": name,
                        "video_duration": float(
                            case_list_item.get("file", {}).get("video_duration", -1)
                        ),
                    }
                )
                file_desc = case_list_item.get("file", {}).get("file_desc", "")
                text_content_list.append(
                    f"产品 {production} 成功案例名称：{name}，描述： {file_desc}"
                )
            product_detail.append(
                {
                    "title": "成功案例",
                    "urls": temp_urls,
                }
            )

            temp_urls = []
            params_list_temp = []
            for params_list_item in p.get("detail_config", {}).get("params_list", []):
                temp_urls.append(
                    {
                        "file_url": params_list_item.get("file", {}).get(
                            "file_url", ""
                        ),  # 视频url
                        "cover_url": params_list_item.get("file", {}).get(
                            "cover_url", ""
                        ),  # 封面url
                        "type": params_list_item.get("file", {}).get("type", ""),
                        "name": "",
                        "video_duration": float(
                            params_list_item.get("file", {}).get("video_duration", -1)
                        ),
                    }
                )
                file_desc = params_list_item.get("file", {}).get("file_desc", "")
                params_list_temp.append(file_desc)
            params_list_temp_str = "\n".join(params_list_temp)
            text_content_list.append(
                f"产品 {production} 参数规格细节：\n{params_list_temp_str}\n"
            )
            product_detail.append(
                {
                    "title": "产品规格",
                    "urls": temp_urls,
                }
            )
            text_content = {
                "title": p.get("detail_config", {}).get("title", ""),
                "subtitle": p.get("detail_config", {}).get("desc", ""),
                "description": p.get("detail_config", {}).get("desc", ""),
                "function_list": ["功能介绍", "成功案例", "产品规格"],
                "text_content": "\n".join(text_content_list),
            }
            if kwargs.get("_USER_QUERY"):
                promote_qa_result = await promote_qa(
                    extra_doc=json.dumps(text_content, ensure_ascii=False, indent=2),
                    **kwargs,
                )
                audio_request = promote_qa_result.content.audio_request
                debug["promote_qa"] = promote_qa_result.debug
                elapse_info["promote_qa_time"] = promote_qa_result.elapse_info
            else:
                audio_request = None
            break

    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse_info,
        content=FunctionOutput(
            result={
                "product": product,
                "product_detail": product_detail,
                "text_content": json.dumps({}, ensure_ascii=False),
            },
            audio_request=audio_request,
        ),
    )


@action_function_register
async def select_home_product_by_llm(
    choice_list: list[str], conversation_progress: str, **kwargs
) -> Dict[int, str]:
    start = time.time()
    await put_run_step("select_product_name", **kwargs)
    choice_list_str_dict = {}
    choice_list_str_list = []
    for idx, item in enumerate(choice_list):
        choice_list_str_list.append(f"ID:{idx}. 产品名称: {item}")
        choice_list_str_dict[idx] = item
    choice_list_str = "\n".join(choice_list_str_list)
    user_query = kwargs.get("_USER_QUERY", "")
    messages = [
        {
            "role": "user",
            "content": f"""从给定的「对话历史」中，识别出用户当前感兴趣的产品，根据「产品名称列表」获取相应的产品ID，并以指定格式输出，如果没有具体指明对哪个产品感兴趣，则返回空列表[]。
# 产品名称列表
{choice_list_str}

# 对话历史
{conversation_progress}

# 用户问题
{user_query}

# 输出格式
1. 只输出与用户兴趣匹配的产品ID列表
2. 格式：[<ID1>, <ID2>, ...]""",
        },
    ]

    model_result = await LLMManager.invoke_generate_text_model(
        messages=messages,
    )
    logger_prompt = messages[-1]["content"].replace("\n", "---")
    logger.info(
        f"[select_home_product_by_llm] action llm prompt: {logger_prompt}---Output{model_result.content}"
    )
    logger.info(
        f"[select_home_product_by_llm] elapsed time: {time.time() - start:.2f}s"
    )
    result = {}
    try:
        ids = ReUtils.extract_list_from_text(model_result.content)
    except Exception as _:
        logger.error(traceback.format_exc())
        ids = []
    for product_id in ids:
        try:
            result[int(product_id)] = choice_list_str_dict[product_id]
        except Exception as _:
            logger.error(traceback.format_exc())
    return result


@action_function_register
async def execute_clear_cache_for_key(**kwargs):
    from src.action.resource import get_promote_project_origin_product_info
    from src.action.resource import get_promote_settings_info

    debug = {}
    elapse_info = {}
    star_time = time.time()
    func = get_promote_project_origin_product_info
    if hasattr(func, "cache_delete"):
        await func.cache_delete(kwargs["__robot"])
        debug["get_promote_project_origin_product_info"] = True
    else:
        pass
    func = get_promote_settings_info
    if hasattr(func, "cache_delete"):
        await func.cache_delete(kwargs["__robot"])
        debug["get_promote_settings_info"] = True
    else:
        pass
    elapse_info["total_time"] = time.time() - star_time
    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse_info,
        content=FunctionOutput(
            result={},
            audio_request=AudioRequest(content=NonStreamingContent(text="")),
        ),
    )


@action_function_register
async def home_v2(**kwargs):
    start_time = time.time()
    home_list = []
    text_content = []
    debug = {}
    elapse_info = {}
    product_info = await get_promote_product_info(kwargs["__robot"])
    product_name_list = []
    for idx, pro in enumerate(product_info):
        product_name_list.append(pro.get("base_info", {}).get("product_name", ""))
        feat_list = pro.get("home_config", {}).get("feat_list", [])
        product_feature = []
        for feat_list_item in feat_list:
            feature_name = feat_list_item.get("name", "")
            feature_description = feat_list_item.get("jump", {}).get("file_desc", "")
            product_feature.append(
                f"product_name: {feature_name}\nfeature_desc: {feature_description}"
            )
        text_content.append(
            {
                "id": idx,
                "title": pro.get("home_config", {}).get("title", ""),  # 主标题
                "subtitle": pro.get("home_config", {}).get("desc", ""),  # 副标题
                "product_feature": "\n\n".join(product_feature),
            }
        )
        videos = []
        for item in pro.get("home_config", {}).get("feat_list", []):
            if item.get("img", {}).get("file_url", ""):
                cover_url = item.get("img", {}).get("file_url", "")
            elif item.get("jump", {}).get("cover_url", ""):
                cover_url = item.get("jump", {}).get("cover_url", "")
            else:
                cover_url = ""
            videos.append(
                {
                    "cover_url": cover_url,
                    "file_url": item.get("jump", {}).get("file_url", ""),
                    "type": item.get("jump", {}).get("type", ""),
                    "name": item.get("name", ""),
                    "video_duration": float(
                        item.get("jump", {}).get("video_duration", -1)
                    ),
                }
            )
        home_list.append(
            {
                "id": idx,
                "image_url": pro.get("home_config", {})
                .get("product_img", {})
                .get("file_url", ""),  # 产品图
                "background_image_url": pro.get("home_config", {})
                .get("bg_img", {})
                .get("file_url", ""),  # 背景图
                "title": pro.get("home_config", {}).get("title", ""),
                "home_title": pro.get("home_config", {}).get("home_title", ""),
                "page_title": "",
                "description": "",
                "subtitle": pro.get("home_config", {}).get("desc", ""),
                "query_button": {
                    "text": pro.get("home_config", {}).get(
                        "detail_btn_text", "了解详情"
                    ),
                    "type": "action",
                    "action": "orion.app.promote.PRODUCT_DETAIL",
                    # "query_text": {"production": pro.get("home_config", {}).get("title", "")},
                    "query_text": {
                        "production": pro.get("base_info", {}).get("product_name", "")
                    },
                    "execute_side": "both",
                },
                "contact_btn_text": pro.get("home_config", {}).get(
                    "contact_btn_text", "预约体验"
                ),
                "videos": videos,
                "pic_group": "",
            }
        )

    prepare_product_info_time = time.time()
    elapse_info["prepare_product_info_time"] = prepare_product_info_time - start_time

    try:
        promote_qa_result = await promote_qa(
            extra_doc=json.dumps(text_content, ensure_ascii=False, indent=2), **kwargs
        )
        if promote_qa_result is not None:
            audio_request = promote_qa_result.content.audio_request
        else:
            audio_request = None
        debug["promote_qa_result"] = promote_qa_result.debug
    except Exception:
        logger.error(traceback.format_exc().replace("\n", "---"))
        audio_request = None
    task_finish_time = time.time()
    logger.info(f"home_list is {home_list}")
    logger.info(f"audio_request is {audio_request} and type is {type(audio_request)}")
    elapse_info["task_finish_time"] = task_finish_time - prepare_product_info_time
    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse_info,
        content=FunctionOutput(
            result={
                "data": home_list,
                "text_content": json.dumps({}, ensure_ascii=False),
            },
            audio_request=audio_request,
        ),
    )


@action_function_register
async def home_v3(**kwargs):
    start_time = time.time()
    home_list = []
    text_content = []
    debug = {}
    elapse_info = {}
    product_info = await get_promote_product_info(kwargs["__robot"])
    product_name_list = []
    for idx, pro in enumerate(product_info):
        product_name_list.append(pro.get("base_info", {}).get("product_name", ""))
        feat_list = pro.get("home_config", {}).get("feat_list", [])
        product_feature = []
        for feat_list_item in feat_list:
            feature_name = feat_list_item.get("name", "")
            feature_description = feat_list_item.get("jump", {}).get("file_desc", "")
            product_feature.append(
                f"product_name: {feature_name}\nfeature_desc: {feature_description}"
            )
        text_content.append(
            {
                "id": idx,
                "title": pro.get("home_config", {}).get("title", ""),  # 主标题
                "subtitle": pro.get("home_config", {}).get("desc", ""),  # 副标题
                "product_feature": "\n\n".join(product_feature),
            }
        )
        videos = []
        for item in pro.get("home_config", {}).get("feat_list", []):
            if item.get("jump", {}).get("cover_url", ""):
                cover_url = item.get("jump", {}).get("cover_url", "")
            else:
                cover_url = ""
            videos.append(
                {
                    "cover_url": cover_url,
                    "file_url": item.get("jump", {}).get("file_url", ""),
                    "type": item.get("jump", {}).get("type", ""),
                    "name": item.get("name", ""),
                    "video_duration": float(
                        item.get("jump", {}).get("video_duration", -1)
                    ),
                }
            )
        home_list.append(
            {
                "id": idx,
                "image_url": pro.get("home_config", {})
                .get("product_img", {})
                .get("file_url", ""),  # 产品图
                "background_image_url": pro.get("home_config", {})
                .get("bg_img", {})
                .get("file_url", ""),  # 背景图
                "title": pro.get("home_config", {}).get("title", ""),
                "home_title": pro.get("home_config", {}).get("home_title", ""),
                "page_title": "",
                "description": "",
                "subtitle": pro.get("home_config", {}).get("desc", ""),
                "query_button": {
                    "text": pro.get("home_config", {}).get(
                        "detail_btn_text", "了解详情"
                    ),
                    "type": "action",
                    "action": "orion.app.promote.GET_PRODUCT_DETAILS",
                    # "query_text": {"production": pro.get("home_config", {}).get("title", "")},
                    "query_text": {
                        "production": pro.get("base_info", {}).get("product_name", "")
                    },
                    "execute_side": "both",
                },
                "contact_btn_text": pro.get("home_config", {}).get(
                    "contact_btn_text", "预约体验"
                ),
                "videos": videos,
                "pic_group": "",
            }
        )

    prepare_product_info_time = time.time()
    elapse_info["prepare_product_info_time"] = prepare_product_info_time - start_time
    tasks = [
        asyncio.create_task(
            promote_qa(
                extra_doc=json.dumps(text_content, ensure_ascii=False, indent=2),
                **kwargs,
            )
        )
    ]

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=16)
    conversation_progress_list = await LLMToolKit.get_text_conversation_records(
        chat_context.messages,
        limit=16,
    )
    conversation_progress = "\n".join(conversation_progress_list)
    tasks.append(
        asyncio.create_task(
            select_home_product_by_llm(
                product_name_list,
                conversation_progress,
                **kwargs,
            )
        )
    )
    try:
        promote_qa_result, select_home_product_by_llm_result = await asyncio.gather(
            *tasks
        )
        if promote_qa_result is not None:
            audio_request = promote_qa_result.content.audio_request
        else:
            audio_request = None
        debug["promote_qa_result"] = promote_qa_result.debug
    except Exception:
        logger.error(traceback.format_exc().replace("\n", "---"))
        audio_request = None
        select_home_product_by_llm_result = {}
    task_finish_time = time.time()
    selected_product_debug = {}
    for index, data in enumerate(home_list):
        if index in select_home_product_by_llm_result:
            data["is_selected"] = True
            selected_product_debug[data["title"]] = True
        else:
            data["is_selected"] = False
            selected_product_debug[data["title"]] = False
    debug["selected_product_debug"] = selected_product_debug
    logger.info(f"home_list is {home_list}")
    elapse_info["task_finish_time"] = task_finish_time - prepare_product_info_time
    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse_info,
        content=FunctionOutput(
            result={
                "data": home_list,
                "text_content": json.dumps({}, ensure_ascii=False),
            },
            audio_request=audio_request,
        ),
    )


@action_function_register
async def record_feedback(
    production: str = "", function: str = "", user_review: str = "", **kwargs
):
    # production, function = production_function.split("-")
    async with aiohttp.ClientSession(
        headers={
            "Agentos-Access-Token": "fGAoFAPXVTVOCS0Mp5phytt07d5UQ6Vec9FpytRemPjjt3H4Ey3h6pB9Mwgs2l9v"
        }
    ) as session:
        async with session.post(
            url=agent_setting.agentos_feedback_url,
            json={
                "production": production,
                "function": function,
                "user_review": user_review,
            },
        ) as response:
            if response.status != 200:
                await send_feishu_alarm(
                    f"record_feedback调用异常，地址：{agent_setting.agentos_feedback_url}, status: {response.status}"
                )
                logger.error(f"[record_feedback] error status code:{response.status}")
                return FunctionResult(
                    type="function",
                    content=FunctionOutput(
                        result={"text": "感谢您的反馈"},
                        audio_request=AudioRequest(
                            content=NonStreamingContent(text="感谢您的反馈")
                        ),
                    ),
                )

            response_json = await response.json()
            logger.info(f"[record_feedback] response result:{response_json}")

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={"text": "感谢您的反馈"},
            audio_request=AudioRequest(
                content=NonStreamingContent(text="感谢您的反馈")
            ),
        ),
    )


@action_function_register
async def show_contact_information(**kwargs):
    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    conversation_progress = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context.messages)
    )

    # 生成一段恰当的话术，推出我们的联系方式
    messages = [
        {
            "role": "user",
            "content": f"""基于聊天记录，并使用**聊天记录**的语言生成一段恰当的话术，告诉用户可以扫码添加我们的联系方式。字数限制在20字以内。
# 聊天记录
{conversation_progress}
""",
        },
    ]

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            # result={
            #     "qr_code_url": agent_setting.qr_code_url,
            #     "title": "联系方式",
            #     "subtitle": "这是我的联系方式，欢迎联系我",
            # },
            result={
                "qr_code_url": "",
                "title": "联系方式",
                "subtitle": "这是我的联系方式，欢迎联系我",
            },
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.3,  # 降低生成http url的随机性
                    ).model_dump(),
                )
            ),
        ),
    )


async def _get_knowledge_content(
    query: str,
    knowledge_base_id: str = "",
    enterprise_id: str = "",
    summary_limit: int = -1,
    section_limit: int = -1,
    __run_step_queue: asyncio.Queue = None,
) -> str:
    if not query:
        logger.warning("Knowledge QA action query is empty")
        return ""

    # 限制section和summary的数量
    max_section_limit = 3
    max_summary_limit = 3
    max_chars = 3000

    """Dynamically fetch knowledge content from ChatMax API"""
    summary_limit = (
        max_summary_limit
        if summary_limit == -1 or summary_limit > max_summary_limit
        else summary_limit
    )
    section_limit = (
        max_section_limit
        if section_limit == -1 or section_limit > max_section_limit
        else section_limit
    )

    start_time = time.time()
    try:
        knowledge_content_url = (
            urllib.parse.urljoin(
                agent_setting.robot_openapi_host, agent_setting.knowledge_content_path
            )
            + f"?chatmax_api=ctai_search_ctai_doc&ov_corp_id={enterprise_id}"
        )

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=knowledge_content_url,
                headers={
                    "orionstar-api-key": agent_setting.robot_openapi_key,
                    "Content-Type": "application/json",
                },
                json={
                    "query_text": query,
                    "retrieval_strategy": "v2",
                    "pad_ctai_doc": 0,
                },
            ) as response:
                # async with session.post(
                #         url=agent_setting.get_knowledge_content_url,
                #     headers={
                #         "orionstar-api-key": agent_setting.chatmax_super_key,
                #         "Content-Type": "application/json",
                #     },
                #     json={
                #         "character_id": knowledge_base_id,
                #         "query_text": query,
                #         "retrieval_strategy": "v2",
                #         "pad_ctai_doc": 0,
                #     },
                # ) as response:
                if response.status != 200:
                    logger.error(
                        f"Failed to fetch knowledge content: {response.status}"
                    )
                    await send_feishu_alarm(
                        f"请求知识库 {knowledge_content_url} 异常，status:{response.status}"
                    )
                    return ""

                data = await response.json()
                request_time = time.time() - start_time
                logger.debug(
                    f"ChatMax API request time: {request_time:.2f} seconds {data}"
                )

            knowledge_contents = []

            # Extract content from section_list
            sections = data.get("data", {}).get("section_list", [])
            if section_limit > 0:
                sections = sections[:section_limit]
            for section in sections:
                content = section.get("content", "")
                doc_name = section.get("doc_name")
                if content and doc_name:
                    knowledge_contents.append(f"### {doc_name} ###\n{content}")
                elif content:  # 知识库的QA对，暂不处理
                    pass
                    # try:
                    #     question, answer = content.split("###")
                    #     knowledge_contents.append(
                    #         f"Question: {question} ### Answer: \n{answer}"
                    #     )
                    # except Exception as e:
                    #     logger.error(f"Error when split content: {e} {content}")
                    #     knowledge_contents.append(f"{content}")

            # Extract content from summary_list
            summaries = data.get("data", {}).get("summary_list", [])
            if summary_limit > 0:
                summaries = summaries[:summary_limit]
            if summaries:
                summary_contents = []
                for summary in summaries:
                    content = summary.get("content")
                    if content:
                        summary_contents.append(content)
                if summary_contents:
                    knowledge_contents.append(
                        "### 总结摘要 ###\n" + "\n".join(summary_contents)
                    )

            # remove the knowledge content that is too long
            short_knowledge_contents = []
            accumulated_chars = 0
            for content in knowledge_contents:
                if accumulated_chars + len(content) <= max_chars:
                    short_knowledge_contents.append(content)
                    accumulated_chars += len(content)
                else:
                    break

            total_time = time.time() - start_time
            logger.debug(f"Total knowledge processing time: {total_time:.2f} seconds")
            logger.debug(f"Knowledge contents: {short_knowledge_contents}")
            await put_run_step("fetch_knowledge_doc", __run_step_queue=__run_step_queue)

            return "\n\n---\n\n".join(short_knowledge_contents)

    except Exception as e:
        logger.error(f"Error fetching knowledge content: {e}")
        return ""


@action_function_register
async def convert_turns_to_degree(action_parameters: dict, agent_parameters) -> dict:
    angle = 0
    if turns := action_parameters.get("turns"):
        angle = int(float(turns) * 360)
    elif angle := action_parameters.get("angle"):
        angle = int(angle)

    new_action_parameters = {
        "angle": angle,
        "direction": action_parameters.get("direction", "left"),
    }

    logger.debug(
        f"[convert_turns_to_degree] Convert original action parameters: {action_parameters} to {new_action_parameters}"
    )
    return new_action_parameters


# @action_function_register
# async def call_assistant(question: str = "", **kwargs):
#     context = ""
#     await put_run_step("prepare_answer", **kwargs)
#     clean_screen_info = kwargs["__robot"].interface_state.clean_interface_info()
#     if clean_screen_info:
#         context += f"\n# Current Screen Info\n{clean_screen_info}\n"
#
#     messages = kwargs["__memory"].get_chat_context(max_chat_history=6).messages
#     conversation = "\n".join(
#         f"{msg.role} said '{msg.content}'" for msg in messages if msg.content
#     )
#     user_question = kwargs.get("_USER_QUERY")
#     if user_question not in conversation:
#         # memory 有滞后性
#         conversation += f"\n{USER_ROLE} said '{user_question}'"
#     lang_question = kwargs.get("_USER_QUERY") or user_question
#     action_result = get_action_ouput_language(
#         lang_question, kwargs.get("__robot"), lang_flag="en"
#     )
#     language = action_result["lang"]
#
#     prompt = f"""Answer questions from users in a **chat session**.{context}# Chat Session: {conversation}\n\nOutput language must be {language} for output, and only pure text, periods and commas. You are unable to use any hyperlinks and emojis."""
#
#     messages = [
#         {
#             "role": "system",
#             "content": LLMToolKit.build_system_prompt(
#                 kwargs["__robot"].agent_id,
#                 kwargs["__robot"].interface_state.app_id,
#                 kwargs["__robot"].PERSONA,
#                 kwargs["__robot"].LANGUAGE_STYLE,
#                 objective=kwargs["__robot"].OBJECTIVE,
#             ),
#         },
#         {"role": "user", "content": prompt},
#     ]
#     # if is_multilingual(kwargs.get("__robot")):
#     #     messages.append(
#     #         LLMToolKit.build_multilingual_user_content(lang_question, detect_lang)
#     #     )
#     # messages.append({"role": "user", "content": prompt})
#
#     sid = str(uuid.uuid4())
#
#     return FunctionResult(
#         type="function",
#         debug={"messages": messages, "sid": sid},
#         content=FunctionOutput(
#             audio_request=AudioRequest(
#                 content=StreamingContent(
#                     messages=messages,
#                     llm_config=RequestLLMConfig(
#                         temperature=0.7,
#                         file_search=True,
#                         business_info=BusinessInfo(
#                             enterprise_id=kwargs["__robot"].enterprise_id,
#                             client_id=kwargs["__robot"].client_id,
#                             device_id=kwargs["__robot"].device_id,
#                             sid=sid,
#                         ),
#                     ).model_dump(),
#                 )
#             ),
#         ),
#     )


@action_function_register
async def knowledge_qa(question: str = "", **kwargs):
    question = question or kwargs.get("_USER_QUERY")

    start_at = time.time()
    logger.info(f"Knowledge QA action question: {question}")
    elapse_info = {}

    robot = kwargs["__robot"]

    # Get robot context
    robot_info = robot.robot_status_info()
    if robot.interface_state.interface_info:
        try:
            screen_info = (
                robot.interface_state.interface_info[400:] + "..."
                if len(robot.interface_state.interface_info) > 400
                else robot.interface_state.interface_info
            )
            robot_info["屏幕信息"] = screen_info
        except Exception as e:
            logger.error(f"Error when get screen info: {e}")

    # 获得企业id参数
    enterprise_id = robot.enterprise_id
    robot_info = json.dumps(robot_info, ensure_ascii=False, indent=4)

    knowledge_prompt = ""
    # Use cached knowledge content
    start_get_knowledge_content = time.time()
    generated_knowledge_prompt = kwargs.get("_KNOWLEDGE_PROMPT")

    if generated_knowledge_prompt:  # 预先获取QA对和文档
        await put_run_step("fetch_knowledge_doc", **kwargs)
        knowledge_prompt = generated_knowledge_prompt

    if kwargs.get("extra_doc"):
        extra_doc = kwargs["extra_doc"]
        knowledge_prompt += f"\n{extra_doc}"

    await put_run_step("prepare_answer", **kwargs)

    elapse_info["total_get_knowledge_content"] = (
        time.time() - start_get_knowledge_content
    )

    # TODO: 需要从action中获取
    pco = LLMToolKit.init_persona_core_objective(
        robot.agent_id,
        robot.interface_state.app_id,
        robot.PERSONA,
        robot.LANGUAGE_STYLE,
        robot.OBJECTIVE,
    )

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    conversation_progress = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context.messages)
    )

    # Begin with acknowledgment and use 2-3 sentences.(like "I understand", "That's right", "Let me tell you")
    user_question = kwargs.get("_USER_QUERY") or question
    action_result = get_action_ouput_language(
        user_question, kwargs.get("__robot"), lang_flag="en"
    )  # Get target language
    language = action_result["lang"]

    communication_standards = [
        "You MUST follow the persona and core objective to generate the response.",
        f"Use {language} reply, and only pure text, periods and commas. You are unable to use any hyperlinks and emojis.",
        "Answer user questions **directly**, keep responses under 50 words.",
    ]
    communication_standards_str = "\n".join(communication_standards)

    prompt = f"""{pco.persona}  Language style:{pco.language_style}
# Communication Standards
{communication_standards_str}

# Robot Information:
{robot_info}
{knowledge_prompt}
# Chat History:
{conversation_progress}

# User Query:
{question or kwargs["_USER_QUERY"]}
"""
    messages = []
    # if is_multilingual(kwargs.get("__robot")):
    #     messages.append(
    #         LLMToolKit.build_multilingual_user_content(user_question, detect_lang)
    #     )
    messages.append({"role": "user", "content": prompt})
    logger_prompt = prompt.replace("\n", "---")
    logger.info(f"query：{user_question} Knowledge QA prompt: {logger_prompt}")

    elapse_info["total"] = time.time() - start_at
    llm_config = RequestLLMConfig(
        temperature=0.7,
    )

    llm_stream_uuid = str(uuid.uuid4())
    debug = {
        "messages": messages,
        "model_config": llm_config.model_dump(),
        "llm_stream_uuid": llm_stream_uuid,
    }
    return FunctionResult(
        type="function",
        elapse_info=elapse_info,
        debug=debug,
        content=FunctionOutput(
            result={"knowledge_content": "TODO"},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.7,
                        file_search=True,
                        business_info=BusinessInfo(
                            enterprise_id=enterprise_id,
                            client_id=kwargs["__robot"].client_id,
                            device_id=kwargs["__robot"].device_id,
                            sid=llm_stream_uuid,
                        ),
                    ).model_dump(),
                )
            ),
        ),
    )


async def promote_qa(question: str = "", **kwargs):
    question = question or kwargs.get("_USER_QUERY")
    if agent_setting.region_version == Area.overseas:
        # 海外目前还没有推销模式 time:2025-06-11
        return None

    start_at = time.time()
    logger.info(f"Home QA action question: {question}")
    elapse_info = {}

    robot = kwargs["__robot"]

    # Get robot context
    robot_info = robot.robot_status_info()
    if robot.interface_state.interface_info:
        try:
            screen_info = (
                robot.interface_state.interface_info[400:] + "..."
                if len(robot.interface_state.interface_info) > 400
                else robot.interface_state.interface_info
            )
            robot_info["屏幕信息"] = screen_info
        except Exception as e:
            logger.error(f"Error when get screen info: {e}")

    # 获得企业id参数
    enterprise_id = robot.enterprise_id
    robot_info = json.dumps(robot_info, ensure_ascii=False, indent=4)

    start_get_knowledge_content = time.time()

    if kwargs.get("extra_doc"):
        extra_doc = kwargs["extra_doc"]
        knowledge_prompt = f"\n# Product Information:\n{extra_doc}\n"
    else:
        knowledge_prompt = ""

    elapse_info["total_get_knowledge_content"] = (
        time.time() - start_get_knowledge_content
    )

    # TODO: 需要从action中获取
    pco = LLMToolKit.init_persona_core_objective(
        robot.agent_id,
        robot.interface_state.app_id,
        robot.PERSONA,
        robot.LANGUAGE_STYLE,
        robot.OBJECTIVE,
    )
    try:
        chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
        conversation_progress = "\n".join(
            LLMToolKit.build_conversation_progress(chat_context.messages)
        )
    except Exception as _:
        logger.error(traceback.format_exc())
        conversation_progress = ""

    user_question = kwargs.get("_USER_QUERY") or question
    action_result = get_action_ouput_language(
        user_question, kwargs.get("__robot"), lang_flag="en"
    )  # Get target language
    language = action_result["lang"]

    prompt = f"""{pco.persona}  Language style:{pco.language_style}
# Communication Standards
- You MUST follow the persona and core objective to generate the response.
- Use {language} reply, and only plain text, periods and commas. You are unable to use any hyperlinks and emojis.
- Answer user questions **directly**, keep responses under 50 words.

# Robot Information:
{robot_info}
{knowledge_prompt}
# Chat History:
{conversation_progress}

# User Query:
{question or kwargs.get("_USER_QUERY")}
"""
    messages = [{"role": "user", "content": prompt}]
    logger_prompt = prompt.replace("\n", "---")
    logger.info(f"query：{user_question} Home QA prompt: {logger_prompt}")

    elapse_info["total"] = time.time() - start_at
    llm_config = RequestLLMConfig(
        temperature=0.7,
    )

    llm_stream_uuid = str(uuid.uuid4())
    debug = {
        "messages": messages,
        "model_config": llm_config.model_dump(),
        "llm_stream_uuid": llm_stream_uuid,
        "knowledge_content": knowledge_prompt,
        "intervene_queries": [],
    }
    return FunctionResult(
        type="function",
        elapse_info=elapse_info,
        debug=debug,
        content=FunctionOutput(
            result={"knowledge_content": "TODO"},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.7,
                        file_search=True,
                        business_info=BusinessInfo(
                            enterprise_id=enterprise_id,
                            client_id=kwargs["__robot"].client_id,
                            device_id=kwargs["__robot"].device_id,
                            sid=llm_stream_uuid,
                        ),
                    ).model_dump(),
                )
            ),
        ),
    )


@action_function_register
async def stream_say(messages: List, llm_config: Dict, **kwargs) -> FunctionResult:
    """Request llm and stream the response"""
    llm_config_obj = RequestLLMConfig(**llm_config)
    return FunctionResult(
        type="audio",
        content=AudioOutput(
            content=StreamingContent(
                messages=messages, llm_config=llm_config_obj.model_dump()
            )
        ),
    )


@action_function_register
async def call_llm(messages: List, llm_config: Dict, **kwargs) -> FunctionResult:
    """Request llm and stream the response"""
    request_llm_config = RequestLLMConfig(**llm_config)

    robot = kwargs["__robot"]
    if robot.is_builtin_app:
        llm_config = LLMConfig(
            temperature=request_llm_config.temperature,
            max_tokens=request_llm_config.max_tokens,
            timeout=request_llm_config.timeout,
            base_url=agent_setting.generate_text_model_base_url,
            api_key=agent_setting.generate_text_model_api_key,
            llm_model_name=agent_setting.generate_text_model,
        )
    else:
        llm_config = LLMConfig(
            temperature=request_llm_config.temperature,
            max_tokens=request_llm_config.max_tokens,
            timeout=request_llm_config.timeout,
            base_url=agent_setting.guest_text_model_base_url,
            api_key=agent_setting.guest_text_model_api_key,
            llm_model_name=agent_setting.guest_text_model,
        )

    model_result: ModelResult = await LLMManager.invoke_generate_text_model(
        messages=messages,
        llm_config=llm_config,
    )
    llm_result = LLMResponse(
        token_cost=model_result.token_cost,
        elapsed_time=model_result.elapsed_time,
        return_message=model_result.return_message,
        status="failed" if model_result.error else "succeeded",
        error=model_result.error,
    )

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={"llm_response": llm_result.model_dump()},
        ),
    )


async def _get_brief_production_resources_v2(robot):
    if robot is None:
        return json.dumps([], ensure_ascii=False)
    resources = []
    product_info = await get_promote_product_info(robot)
    for prod in product_info:
        try:
            production_name = prod.get("base_info", {}).get("product_name", "")
            detail_config = prod.get("detail_config", {})
            if production_name:
                for feature_list_item in detail_config.get("feature_list", []):
                    name = feature_list_item["name"]
                    file_data = feature_list_item["file"]
                    file_type = file_data["type"]
                    if file_type == "image":
                        resources.append(f"{production_name}功能介绍：{name}的图片")
                    elif file_type == "video":
                        resources.append(f"{production_name}功能介绍：{name}的功能视频")

                for case_list_item in detail_config.get("case_list", []):
                    name = case_list_item["name"]
                    file_data = case_list_item["file"]
                    file_type = file_data["type"]
                    if file_type == "image":
                        resources.append(f"{production_name}成功案例：{name}的图片")
                    elif file_type == "video":
                        resources.append(f"{production_name}成功案例：{name}的功能视频")

                for params_list_item in detail_config.get("params_list", []):
                    file_data = params_list_item["file"]
                    file_type = file_data["type"]
                    if file_type == "image":
                        resources.append(f"{production_name}产品参数规格的图片")
                    elif file_type == "video":
                        resources.append(f"{production_name}产品参数规格的视频")
                    break

        except Exception:
            logger.error(traceback.format_exc())
    return json.dumps(resources, ensure_ascii=False)


@action_function_register
async def choice_sales_strategy_by_llm_v2(
    choice_list: list[dict], conversation_progress: str, user_query: str = "", **kwargs
) -> int:
    start = time.time()
    # 如果有用户查询，优先选择"回答问题"策略，无法兼顾用户说要离开的情景
    # if user_query:
    #     return 1
    await put_run_step("select_best_sales_strategy", **kwargs)
    choice_list_str = "\n".join(
        f"ID:{idx}. desc:{item}" for idx, item in enumerate(choice_list)
    )
    messages = [
        {
            "role": "user",
            "content": f"""# 对话策略列表
{choice_list_str}

# 对话历史
{conversation_progress}

# 输出要求
1. 只输出策略编号
2. 格式：STRATEGY:<number>
3. 示例：STRATEGY:1
请分析「对话历史」依照「对话策略列表」选择1个最合适的策略。""",
        },
    ]

    model_result = await LLMManager.invoke_generate_text_model(
        messages=messages,
    )
    logger_prompt = messages[-1]["content"].replace("\n", "---")
    logger.info(
        f"[choice_sales_strategy_by_llm_v2] action llm prompt: {logger_prompt}\nOutput{model_result.content}"
    )
    logger.info(
        f"[choice_sales_strategy_by_llm_v2] elapsed time: {time.time() - start:.2f}s"
    )
    try:
        return int(model_result.content.replace("STRATEGY:", ""))
    except Exception as e:
        logger.error(f"[choice_sales_strategy_by_llm_v2] error: {e}")
        return 0


@action_function_register
async def choice_recommend_strategy_by_llm(
    recommend_strategies: list[dict],
    conversation_progress: str,
    user_query: str = "",
    robot=None,
    **kwargs,
) -> int:
    start = time.time()
    choose_list = [
        f"{item['title']}-触发条件：{item['trigger']}" for item in recommend_strategies
    ]
    choose_list_str = "\n".join(
        f"ID:{idx}. desc:{item}" for idx, item in enumerate(choose_list)
    )
    product_material = await _get_brief_production_resources_v2(robot)
    messages = [
        {
            "role": "user",
            "content": f"""基于对话历史和可用的产品推销资料，从推荐策略列表中选择最合适的推荐策略。注意：
1. 分析用户「对话历史」中表达的需求和兴趣点
2. 严格遵循每个策略的触发条件，优先选择最匹配用户当前需求的策略
3. 考虑对话进展：1. 避免重复已展示过的内容；2. 根据用户反馈调整推荐方向
4. 确保选择的策略在产品推销资料中有对应内容可用

# 可用的产品推销资料
{product_material}

# 推荐策略列表
{choose_list_str}

# 对话历史
{conversation_progress}

# 用户问题
{user_query}

# 输出要求
1. 只输出策略编号
2. 格式：RECOMMEND:<number>
3. 示例：RECOMMEND:1""",
        }
    ]

    await put_run_step("select_best_recommend_strategy", **kwargs)

    model_result = await LLMManager.invoke_generate_text_model(messages=messages)
    logger.info(
        f"[choice_recommend_strategy_by_llm] action llm prompt: {messages[-1]['content']}\nOutput{model_result.content}"
    )
    logger.info(
        f"[choice_recommend_strategy_by_llm] elapsed time: {time.time() - start:.2f}s"
    )
    try:
        return int(model_result.content.replace("RECOMMEND:", ""))
    except Exception as e:
        logger.error(f"[choice_recommend_strategy_by_llm] error: {e}")
        return 0


@action_function_register
async def sales_pitch_v2(**kwargs) -> FunctionResult:
    start_time = time.time()
    logger.info(f"Starting sales_pitch function, {kwargs}")
    debug = {}
    elapse = {}

    # 获取对话历史
    conversation_start = time.time()
    # get user turns
    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=16)
    try:
        user_turns = len(
            [msg.role for msg in chat_context.messages if msg.role == "user"]
        )
    except Exception as e:
        logger.error(f"[sales_pitch] error: {e}")
        user_turns = -1

    trigger_active = False
    synthesize_type = kwargs["SYNTHESIZE_TYPE"]
    debug["last_synthesize_type"] = synthesize_type
    logger.info(
        f"sales_pitch synthesize_type is {synthesize_type} and type is {type(synthesize_type)}"
    )
    if synthesize_type in [
        SynthesizeType.ACTION.value,
        SynthesizeType.EVENT.value,
    ]:
        trigger_active = True
    logger.info(f"trigger_active is {trigger_active}")

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=16)
    conversation_progress_list = await LLMToolKit.get_text_conversation_records(
        chat_context.messages,
        limit=16,
    )
    conversation_progress = "\n".join(conversation_progress_list)
    debug["conversation_progress"] = conversation_progress

    if user_query := kwargs.get("_USER_QUERY", ""):
        if user_query not in conversation_progress:
            if user_query == "用户无交互":
                conversation_progress += f"\n{USER_ROLE} SAY '{PROMOTE_USER_TEXT}'"
            else:
                conversation_progress += f"\n{USER_ROLE} SAY '{user_query}'"
        elif trigger_active:
            conversation_progress += f"\n{USER_ROLE} SAY '{PROMOTE_USER_TEXT}'"
    logger.debug(
        f"Conversation processing took: {time.time() - conversation_start:.2f}s"
    )
    elapse["conversation_progress_time"] = time.time() - conversation_start

    # 初始化persona和获取screen信息
    init_start = time.time()
    from src.common.constant import LanguageEnum

    persona = LLMToolKit.init_persona_core_objective(
        kwargs["__robot"].agent_id,
        kwargs["__robot"].interface_state.app_id,
        kwargs["__robot"].PERSONA,
        kwargs["__robot"].LANGUAGE_STYLE,
        kwargs["__robot"].OBJECTIVE,
        language=LanguageEnum.zh,
    )
    screen_info = kwargs["__robot"].interface_state.interface_info  # noqa
    logger.debug(f"Initialization took: {time.time() - init_start:.2f}s")

    # 获取知识库内容
    knowledge_content_atask = None
    if user_query := kwargs.get("_CURRENT_SUMMARY", ""):
        knowledge_content_atask = asyncio.create_task(
            _get_knowledge_content(
                user_query,
                enterprise_id=kwargs["__robot"].enterprise_id,
                summary_limit=2,
                section_limit=2,
                __run_step_queue=kwargs.get("__run_step_queue"),
            )
        )
    product_info = await get_promote_product_info(kwargs["__robot"])
    product_name_list = []
    for idx, pro in enumerate(product_info):
        product_name_list.append(pro.get("base_info", {}).get("product_name", ""))
    # 不管是不是在首页，都先执行这个操作，看看耗时
    select_product_start_time = time.time()
    current_product_id_task = asyncio.create_task(
        select_home_product_by_llm(
            product_name_list,
            conversation_progress,
            **kwargs,
        )
    )

    # 选择策略
    strategy_start = time.time()

    if trigger_active:
        dialogue_strategy_list = deepcopy(dialogue_strategy_list_trigger)
    else:
        dialogue_strategy_list = deepcopy(dialogue_strategy_list_general)
    if user_turns < 0 or user_turns > 3:  # 用户未发言或发言超过3轮，不进行场景引导
        dialogue_strategy_list.pop(0)
    choose_list = [
        f"{item['title']}-触发条件：{item['trigger']}"
        for item in dialogue_strategy_list
    ]
    dialogue_strategy_atask = asyncio.create_task(
        choice_sales_strategy_by_llm_v2(
            choose_list,
            conversation_progress,
            user_query=kwargs.get("_CURRENT_SUMMARY"),
            **kwargs,
        )
    )

    # recommend
    recommend_strategies = [
        {
            "title": "产品详情",
            "trigger": "用户提及了产品名称或场景，但「对话历史」中未出现过该产品的「产品详情」",
            "plan": "推荐用户了解该产品的「产品详情」",
        },
        {
            "title": "产品规格图",
            "trigger": "仅用户需要了解参数细节",
            "plan": "推荐产品规格图",
        },
        {
            "title": "功能视频和成功案例视频",
            "trigger": "用户问题匹配上了「可用的产品推销资料」中的具体视频，或未命中上述其他策略",
            "plan": "基于用户表达的需求和兴趣，从「可用的产品推销资料」中选择一个最相关的视频进行推荐。优先级：1. 用户可能感兴趣场景的案例视频；2. 用户可能关注的功能的功能视频；3. 未被推荐过的视频",
        },
    ]
    recommend_strategy_atask = asyncio.create_task(
        choice_recommend_strategy_by_llm(
            recommend_strategies,
            conversation_progress,
            user_query=kwargs.get("_CURRENT_SUMMARY"),
            robot=kwargs["__robot"],
            **kwargs,
        )
    )

    # wait dialogue strategy
    dialogue_strategy_id = await dialogue_strategy_atask
    elapse["dialogue_strategy_time"] = time.time() - strategy_start
    debug["dialogue_strategy_id"] = dialogue_strategy_id

    if not trigger_active and kwargs.get("_CURRENT_SUMMARY"):
        question_prompt = f"\n# 用户问题\n{kwargs.get('_CURRENT_SUMMARY')}\n"
    else:
        question_prompt = ""
    dialogue_strategy = dialogue_strategy_list[dialogue_strategy_id]
    debug["dialogue_strategy"] = dialogue_strategy

    strategy_prompt = (
        f"""你要使用「{dialogue_strategy["title"]}」策略，{dialogue_strategy["plan"]}"""
    )
    if not dialogue_strategy["should_recommend"]:
        recommend_strategy_atask.cancel()
        recommend_resource_prompt = ""
    else:
        product_material = await _get_brief_production_resources_v2(kwargs["__robot"])
        recommend_resource_prompt = f"""
# 可用的产品推销资料
{product_material}
"""
        recommend_strategy_id = await recommend_strategy_atask
        recommend_strategy = recommend_strategies[recommend_strategy_id]
        recommend_resource_prompt += f"""
# 推荐要求
1. 推荐策略：本次对话你要推荐：{recommend_strategy["title"]}, {recommend_strategy["plan"]}
2. 只推荐用户当下最感兴趣的产品的相关信息
3. 减少推荐重复已展示过的内容
4. 每次只从「可用的产品推销资料」中选择一个最相关的实体通过询问用户的方式来进行推荐，如: '您想了解一下这个功能吗'\n"""
        debug["recommend_strategy_id"] = recommend_strategy_id
        debug["recommned_resource_prompt"] = recommend_resource_prompt

    wait_knowledge_content_time = time.time()
    if knowledge_content_atask:
        knowledge_content = await knowledge_content_atask
    else:
        knowledge_content = ""
    elapse["wait_knowledge_content_time"] = time.time() - wait_knowledge_content_time
    elapse["total_qa_and_strategy_time"] = time.time() - strategy_start

    logger.debug(
        f"Strategy selection and kb retrieval took: {time.time() - strategy_start:.2f}s"
    )
    await put_run_step("fetch_production_info", **kwargs)
    # 产品信息
    product_base_info = []
    for p in product_info:
        product_base_info_item = []
        base_info = p.get("base_info", {})
        if base_info:
            content = base_info.get("product_name", "")
            product_base_info_item.append(f"产品名字: {content}")
            content = base_info.get("product_desc", "")
            product_base_info_item.append(f"产品描述: {content}")
            product_base_info.append("\n".join(product_base_info_item))

    product_base_info_str = "\n---\n".join(product_base_info)
    knowledge_content = "\n\n---\n\n".join([knowledge_content, product_base_info_str])
    knowledge_content = knowledge_content.strip()
    debug["knowledge_content"] = knowledge_content

    # 构建提示词
    prompt_start = time.time()

    await put_run_step("prepare_promote", **kwargs)
    logger.info(f"user_turns is {user_turns}")
    # 如果还存在没有问询的用户信息，则添加上对应的策略
    preferences_empty_field = await kwargs["__memory"].get_preferences_empty_field(
        kwargs["__robot"]
    )
    logger.info(f"get preferences_empty_field is {preferences_empty_field}")
    # 产品需求，大于3轮才开始问询
    if preferences_empty_field and user_turns > 1:
        user_info_prompt = f"用户关注的购买点：{preferences_empty_field}，请基于「对话历史」回答完用户的问题后，选择*一个*合适的购买点去询问用户。"
    else:
        user_info_prompt = ""
    prompt = f"""作为机器人推销员，你需要根据用户最新的对话历史，积极主动地推销当前产品。
绝不说"如果您有任何问题或需要进一步信息，请随时告诉我！"，"想了解更多吗？"等，要求：必须主动引导用户，告诉用户下一步该做什么，而不是被动等待用户提问

# 语言风格
{persona.language_style}

# 屏幕信息
{screen_info}

# 产品信息
{knowledge_content}

{recommend_resource_prompt}
# 对话历史
{conversation_progress}
{question_prompt}
# 回复要求
1. 字数限制：100字内，仅使用句号、逗号、叹号和问号，语言自然流畅，只输出纯文本，不要输出markdown语法
2. 严格按照此对话策略：{strategy_prompt}
3. 人设要求：必须遵循「语言风格」的要求，保持语气和表达方式的一致性
4. 内容要求：禁止重复历史内容，禁止捏造功能参数，严格遵循「产品信息」和「屏幕信息」"""
    if user_info_prompt:
        prompt += f"\n5. 问询用户信息：{user_info_prompt}"
    logger.debug(f"Prompt construction took: {time.time() - prompt_start:.2f}s")
    debug["prompt"] = prompt
    logger_prompt = prompt.replace("\n", "---")
    logger.info(f"sales_pitch_v2 prompt is {logger_prompt}")

    # 构建消息
    messages = [
        {
            "role": "system",
            "content": f"{persona.persona}",
        },
        {
            "role": "user",
            "content": prompt,
        },
    ]

    current_product_id_task_result = await current_product_id_task
    result = []
    for product_id, product_name in current_product_id_task_result.items():
        result.append(
            {
                "product_id": product_id,
                "product_name": product_name,
            }
        )
    select_product_end_time = time.time()
    elapse["select_product_time"] = select_product_end_time - select_product_start_time
    await put_run_step("prepare_answer", **kwargs)
    logger.info(f"sales_pitch final result is {result}")
    total_time = time.time() - start_time
    elapse["total_time"] = total_time
    logger.info(f"Total sales_pitch preparation time: {total_time:.2f}s")
    return FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse,
        content=FunctionOutput(
            result={"result": result},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.6,
                    ).model_dump(),
                )
            ),
        ),
    )


@action_function_register
async def sales_pitch_v3(**kwargs) -> FunctionResult:
    """
    相比较v2版本，进行了流程上的精简
    """
    start_time = time.time()
    logger.info(f"Starting sales_pitch_v3 function, {kwargs}")
    debug = {}
    elapse = {}

    # 获取对话历史
    conversation_start = time.time()
    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=16)
    chat_context = chat_context.messages

    try:
        user_turns = len([msg.role for msg in chat_context if msg.role == "user"])
    except Exception as e:
        logger.error(f"[sales_pitch] error: {e}")
        user_turns = -1

    trigger_active = False
    synthesize_type = kwargs["SYNTHESIZE_TYPE"]
    debug["last_synthesize_type"] = synthesize_type
    logger.info(
        f"sales_pitch synthesize_type is {synthesize_type} and type is {type(synthesize_type)}"
    )
    if synthesize_type in [
        SynthesizeType.ACTION.value,
        SynthesizeType.EVENT.value,
    ]:
        trigger_active = True
    debug["trigger_active"] = trigger_active

    conversation_only_content = await LLMToolKit.get_text_conversation_records(
        chat_context,
        limit=16,
    )
    conversation_progress = "\n".join(conversation_only_content)
    debug["conversation_progress"] = conversation_progress

    if user_query := kwargs.get("_USER_QUERY", ""):
        if user_query not in conversation_progress:
            if user_query == "用户无交互":
                conversation_progress += f"\n{USER_ROLE} SAY '{PROMOTE_USER_TEXT}'"
            else:
                conversation_progress += f"\n{USER_ROLE} SAY '{user_query}'"
        elif trigger_active:
            conversation_progress += f"\n{USER_ROLE} SAY '{PROMOTE_USER_TEXT}'"
    elapse["conversation_progress_time"] = time.time() - conversation_start

    # 初始化persona和获取screen信息
    from src.common.constant import LanguageEnum

    persona = LLMToolKit.init_persona_core_objective(
        kwargs["__robot"].agent_id,
        kwargs["__robot"].interface_state.app_id,
        kwargs["__robot"].PERSONA,
        kwargs["__robot"].LANGUAGE_STYLE,
        kwargs["__robot"].OBJECTIVE,
        language=LanguageEnum.zh,
    )
    screen_info = kwargs["__robot"].interface_state.interface_info  # noqa
    get_promote_product_info_start_time = time.time()
    product_info = await get_promote_product_info(kwargs["__robot"])
    # 产品信息
    product_base_info = []
    for p in product_info:
        product_base_info_item = {}
        base_info = p.get("base_info", {})
        if base_info:
            content = base_info.get("product_name", "")
            product_base_info_item["产品名字"] = content
            content = base_info.get("product_desc", "")
            product_base_info_item["产品描述"] = content
        # 功能介绍
        temp_urls = []
        for feature_list_item in p.get("detail_config", {}).get("feature_list", []):
            name = feature_list_item.get("name", "")
            file_desc = feature_list_item.get("file", {}).get("file_desc", "")
            temp_urls.append({"标题": name, "描述": file_desc})
        if temp_urls:
            product_base_info_item["产品功能"] = temp_urls

        temp_urls = []
        for case_list_item in p.get("detail_config", {}).get("case_list", []):
            name = case_list_item.get("name", "")
            file_desc = case_list_item.get("file", {}).get("file_desc", "")
            temp_urls.append({"标题": name, "描述": file_desc})
        if temp_urls:
            product_base_info_item["成功案例"] = temp_urls
        product_base_info.append(product_base_info_item)

    debug["knowledge_content"] = product_base_info
    await put_run_step("fetch_production_info", **kwargs)
    elapse["get_promote_product_info_time"] = (
        time.time() - get_promote_product_info_start_time
    )
    product_name_list = []
    for idx, pro in enumerate(product_info):
        product_name_list.append(pro.get("base_info", {}).get("product_name", ""))
    # 不管是不是在首页，都先执行这个操作，看看耗时
    select_home_product_by_llm_start_time = time.time()
    current_product_id_task = asyncio.create_task(
        select_home_product_by_llm(
            product_name_list,
            conversation_progress,
            **kwargs,
        )
    )

    if trigger_active:
        dialogue_strategy_list = deepcopy(dialogue_strategy_list_trigger_v3)
    else:
        dialogue_strategy_list = deepcopy(dialogue_strategy_list_general_v3)

    choose_list = [
        f"{item['title']}-触发条件：{item['trigger']}"
        for item in dialogue_strategy_list
    ]
    choice_sales_strategy_by_llm_v2_start_time = time.time()
    dialogue_strategy_atask = asyncio.create_task(
        choice_sales_strategy_by_llm_v2(
            choose_list,
            conversation_progress,
            user_query=kwargs.get("_CURRENT_SUMMARY"),
            **kwargs,
        )
    )

    # wait dialogue strategy
    dialogue_strategy_id = await dialogue_strategy_atask
    elapse["dialogue_strategy_time"] = (
        time.time() - choice_sales_strategy_by_llm_v2_start_time
    )
    debug["dialogue_strategy_id"] = dialogue_strategy_id

    if not trigger_active and kwargs.get("_USER_QUERY"):
        question_prompt = f"\n# 用户问题\n{kwargs.get('_USER_QUERY')}\n"
    else:
        question_prompt = ""

    dialogue_strategy = dialogue_strategy_list[dialogue_strategy_id]
    debug["dialogue_strategy"] = dialogue_strategy

    strategy_prompt = (
        f"""你要使用「{dialogue_strategy["title"]}」策略，{dialogue_strategy["plan"]}"""
    )
    if not dialogue_strategy["should_recommend"]:
        recommend_resource_prompt = ""
    else:
        recommend_resource_prompt = """
# 推荐要求
1. 推荐策略：请根据用户的交互内容，从「产品信息」中的*产品功能*和*成功案例*选择一个用户最可能感兴趣的内容进行推荐
2. 已经推荐过的内容不进行推荐
3. 推荐方式：通过询问用户的方式来进行推荐，如: '您想了解一下这个功能吗'\n"""

    # 构建提示词
    prompt_start = time.time()
    await put_run_step("prepare_promote", **kwargs)
    logger.info(f"user_turns is {user_turns}")
    # 如果还存在没有问询的用户信息，则添加上对应的策略
    preferences_empty_field = await kwargs["__memory"].get_preferences_empty_field(
        kwargs["__robot"]
    )
    logger.info(f"get preferences_empty_field is {preferences_empty_field}")
    # 产品需求，大于3轮才开始问询
    if preferences_empty_field and user_turns > 1:
        user_info_prompt = f"用户关注的购买点：{preferences_empty_field}，请基于「对话历史」回答完用户的问题后，务必选择*一个*合适的购买点去询问用户。"
    else:
        user_info_prompt = ""
    prompt = f"""作为机器人推销员，你需要根据用户最新的对话历史，积极主动地推销当前产品。
绝不说"如果您有任何问题或需要进一步信息，请随时告诉我！"，"想了解更多吗？"等，要求：必须主动引导用户，告诉用户下一步该做什么，而不是被动等待用户提问

# 语言风格
{persona.language_style}

# 屏幕信息
{screen_info}

# 产品信息
{product_base_info}

{recommend_resource_prompt}
# 对话历史
{conversation_progress}
{question_prompt}
# 回复要求
1. 字数限制：100字内，仅使用句号、逗号、叹号和问号，语言自然流畅，只输出纯文本，不要输出markdown语法
2. 严格按照此对话策略：{strategy_prompt}
3. 人设要求：必须遵循「语言风格」的要求，保持语气和表达方式的一致性
4. 内容要求：禁止重复历史内容，禁止捏造功能参数，严格遵循「产品信息」和「屏幕信息」"""
    if user_info_prompt:
        prompt += f"\n5. 问询用户信息：{user_info_prompt}"
    debug["prompt"] = prompt
    elapse["prompt_time"] = time.time() - prompt_start
    logger_prompt = prompt.replace("\n", "---")
    logger.info(f"sales_pitch_v3 prompt is {logger_prompt}")

    # 构建消息
    messages = [
        {
            "role": "system",
            "content": f"{persona.persona}",
        },
        {
            "role": "user",
            "content": prompt,
        },
    ]

    current_product_id_task_result = await current_product_id_task
    elapse["select_home_product_by_llm_time"] = (
        time.time() - select_home_product_by_llm_start_time
    )
    result = []
    for product_id, product_name in current_product_id_task_result.items():
        result.append(
            {
                "product_id": product_id,
                "product_name": product_name,
            }
        )
    await put_run_step("prepare_answer", **kwargs)
    debug["select_home_product_by_llm_result"] = result
    logger.info(f"sales_pitch final result is {result}")
    total_time = time.time() - start_time
    elapse["total_time"] = total_time
    sales_pitch_result = FunctionResult(
        type="function",
        debug=debug,
        elapse_info=elapse,
        content=FunctionOutput(
            result={"result": result},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.6,
                    ).model_dump(),
                )
            ),
        ),
    )
    query = kwargs.get("_USER_QUERY", "")
    logger.info(f"query {query} sales_pitch is {sales_pitch_result}")
    return sales_pitch_result


@action_function_register
async def competitors_answer(**kwargs) -> FunctionResult:
    start_time = time.time()
    logger.info("Starting competitors_answer function")

    # 获取对话历史
    conversation_start = time.time()

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=16)
    conversation_progress_list = await LLMToolKit.get_text_conversation_records(
        chat_context.messages,
        limit=16,
    )
    conversation_progress = "\n".join(conversation_progress_list)

    if user_query := kwargs.get("_USER_QUERY", ""):
        # if user_query not in conversation_progress:
        if len(conversation_progress_list) == 0:
            conversation_progress += f"\n{USER_ROLE} SAY '{user_query}'"
        elif conversation_progress_list[-1].split(" ")[0] != USER_ROLE:
            conversation_progress += f"\n{USER_ROLE} SAY '{user_query}'"
        elif user_query not in conversation_progress:
            conversation_progress += f"\n{USER_ROLE} SAY '{user_query}'"
    logger.debug(
        f"competitors_answer Conversation processing took: {time.time() - conversation_start:.2f}s"
    )

    # 初始化persona和获取screen信息
    init_start = time.time()
    persona = LLMToolKit.init_persona_core_objective(
        kwargs["__robot"].agent_id,
        kwargs["__robot"].interface_state.app_id,
        kwargs["__robot"].PERSONA,
        kwargs["__robot"].LANGUAGE_STYLE,
    )
    logger.debug(f"Initialization took: {time.time() - init_start:.2f}s")

    # 获取知识库内容
    knowledge_content_atask = None
    if user_query := kwargs.get("_CURRENT_SUMMARY", ""):
        knowledge_content_atask = asyncio.create_task(
            _get_knowledge_content(
                user_query,
                enterprise_id=kwargs["__robot"].enterprise_id,
                summary_limit=2,
                section_limit=2,
                __run_step_queue=kwargs.get("__run_step_queue"),
            )
        )

    if knowledge_content_atask:
        knowledge_content = await knowledge_content_atask
    else:
        knowledge_content = ""

    product_info = await get_promote_product_info(kwargs["__robot"])
    # 竞品信息
    await put_run_step("prepare_answer", **kwargs)

    peer_infos = []
    # 产品信息
    product_base_info = []
    for p in product_info:
        product_base_info_item = []
        base_info = p.get("base_info", {})
        if base_info:
            content = base_info.get("product_name", "")
            product_base_info_item.append(f"产品名字: {content}")
            content = base_info.get("product_desc", "")
            product_base_info_item.append(f"产品描述: {content}")
            product_base_info.append("\n".join(product_base_info_item))

        for peer_info_item in p.get("peer_info", {}).get("list", []):
            peer_infos.append(
                {
                    "竞品名称": peer_info_item.get("name", ""),
                    "竞品公司": peer_info_item.get("corp_name", ""),
                    "价格": peer_info_item.get("price", ""),
                    "产品描述": peer_info_item.get("desc", ""),
                    "劣势标签": peer_info_item.get("bad_tag", ""),
                }
            )
    product_base_info_str = "\n---\n".join(product_base_info)
    knowledge_content = "\n\n---\n\n".join([knowledge_content, product_base_info_str])
    # 构建提示词
    prompt_start = time.time()
    question_prompt = (
        f"\n# 用户问题\n{kwargs.get('_CURRENT_SUMMARY')}\n"
        if kwargs.get("_CURRENT_SUMMARY")
        else ""
    )
    screen_info = kwargs["__robot"].interface_state.interface_info
    prompt = f"""请基于「产品信息」和「竞品信息」来回答用户的问题。

# 语言风格
{persona.language_style}

# 屏幕信息
{screen_info}

# 当前产品信息
{knowledge_content}

# 竞品信息
{peer_infos}

# 对话历史
{conversation_progress}
{question_prompt}
# 回复要求
1. 字数限制：100字内，仅使用句号、逗号、叹号和问号，语言自然流畅
2. 人设要求：必须遵循「语言风格」的要求，保持语气和表达方式的一致性
3. 内容要求：禁止重复历史内容，禁止捏造功能参数，严格遵循「产品信息」、「竞品信息」、「屏幕信息」
4. *特别注意*：只回答*当前产品*的优点和*竞品*的缺点。如果用户问竞品的优点和当前产品的缺点，请用一种婉转的方式避免回答此类问题"""
    logger_prompt = prompt.replace("\n", "---")
    logger.info(f"competitors_answer prompt is {logger_prompt}")
    logger.debug(f"Prompt construction took: {time.time() - prompt_start:.2f}s")

    # 构建消息
    messages = [
        {
            "role": "system",
            "content": f"{persona.persona}",
        },
        {
            "role": "user",
            "content": prompt,
        },
    ]

    total_time = time.time() - start_time
    logger.info(f"Total competitors_answer preparation time: {total_time:.2f}s")

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.6,
                    ).model_dump(),
                )
            ),
        ),
    )


@action_function_register
async def answer_question_from_vision(
    image_url: str, question: str, **kwargs
) -> FunctionResult:
    """
    Integrates visual data, question, and conversation context into a single prompt and queries the 7B model once.

    :param image_url: The URL of the image collected.
    :param question: The question posed by the user.
    :param _CONTEXT: Conversation context.
    :return: A FunctionResult containing the answer and audio output.
    """
    # Constructing a concise prompt with image URL, question, and context
    persona = LLMToolKit.init_persona_core_objective(
        agent_id=kwargs["__robot"].agent_id,
        app_id=kwargs["__robot"].interface_state.app_id,
        persona=kwargs["__robot"].PERSONA,
        language_style=kwargs["__robot"].LANGUAGE_STYLE,
        objective=kwargs["__robot"].OBJECTIVE,
    )
    origin_query = kwargs["_USER_QUERY"]
    robot = kwargs["__robot"]
    language = robot.language
    geo_location = (
        robot.geo_location.city
        if agent_setting.region_version == Area.domestic
        else f"{robot.geo_location.latitude},{robot.geo_location.longitude}"
    )

    weather_info = await get_weather(geo_location, language)

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    conversation_progress = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context.messages)
    )

    logger.info(f"[answer_question_from_vision] image_url: {image_url}")
    await put_run_step("visual_analysis", **kwargs)

    image_content = await get_image_content(image_url)

    action_result = get_action_ouput_language(
        origin_query, kwargs.get("__robot"), lang_flag="en"
    )  # Get target language
    language = action_result["lang"]

    zh_prompt = f"""你是一个可以帮助分析图片并回答用户问题的助手。回答必须为纯文本，且不超过35个字。
## 当前天气信息
城市: {kwargs["__robot"].geo_location.city}
天气: {weather_info.weather if weather_info else "未知"}
温度: {weather_info.temperature if weather_info else "未知"}

## 对话历史
{conversation_progress}

## 用户问题
{question}

注意：
1. 如果图片中出现多个人，当问题涉及具体个人信息时，请针对每个人分别回答。
2. 避免使用"在图片中"之类的短语，并以第一人称视角用{language}来回答用户的问题。"""

    en_prompt = f"""You are a helpful assistant that can analyze images and answer user's question. The answer must be plain text and within 35 words.
## Current Weather Information
City: {kwargs["__robot"].geo_location.city}
Weather: {weather_info.weather if weather_info else "unknown"}
Temperature: {weather_info.temperature if weather_info else "unknown"}

## Conversation History
{conversation_progress}

## User Question
{question}

Note:
1. Avoid phrases like "in the image", and use first-person perspective to **answer the user's question** in **{language}**.
2. If multiple people appear in the image, when questions involve specific personal information, please answer separately for each person."""

    if agent_setting.region_version == Area.domestic:
        prompt = zh_prompt
    else:
        prompt = en_prompt

    logger.info(f"answer_question_from_vision prompt:\n {prompt}")

    await put_run_step("prepare_answer", **kwargs)

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {agent_setting.vision_api_key}",
    }
    payload = {
        "model": agent_setting.vision_model,
        "temperature": 0.0,
        "messages": [
            {"role": "system", "content": persona.persona},
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": image_content},
                    },
                    {"text": prompt, "type": "text"},
                ],
            },
        ],
    }

    start_time = time.time()
    debug_info = {"vision_prompt": prompt, "errors": []}

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {agent_setting.vision_api_key}",
    }
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{agent_setting.vision_base_url}/chat/completions",
                headers=headers,
                json=payload,
            ) as response:
                result = await response.json()
                logger.info(f"answer_question_from_vision raw result:\n {result}")
                model_result = (
                    result.get("choices", [{}])[0].get("message", {}).get("content", "")
                )
                logger.info(f"answer_question_from_vision result:\n {model_result}")
    except Exception as e:
        err_msg = f"Failed to get multimodal result: {e} payload:{payload}"
        logger.error(err_msg)
        debug_info["errors"].append(err_msg)
        await send_feishu_alarm(err_msg)
        model_result = ""

    elapsed_time = time.time() - start_time
    debug_info["multimodal_time"] = elapsed_time

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={"answer_text": model_result},
            audio_request=AudioRequest(
                content=NonStreamingContent(text=model_result),
            ),
        ),
        elapse_info={"multimodal_time": elapsed_time},
        debug=debug_info,
    )


@action_function_register
async def introduction_exhibit(exhibit: str, **kwargs) -> FunctionResult:
    """
    Introduction exhibit
    """
    return FunctionResult(
        type="function", content=FunctionOutput(result={"exhibit_detail": {}})
    )


@action_function_register
async def generate_route_introduction(user_query: str, **kwargs) -> FunctionResult:
    """生成路线介绍播报语。

    Args:
        user_query (str): 用户问题
        **kwargs: 额外参数，包含机器人相关信息
    Returns:
        FunctionResult: 包含推荐文本或错误信息的结果对象
    """
    start_time = time.time()
    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        device_id = robot.device_id
        enterprise_id = robot.enterprise_id
        map_id = robot.map_id
        map_name = robot.map_name
        robot_language = robot.language  # 输出语言  zh/en/de
        robot_parameters = {
            "device_id": device_id,
            "enterprise_id": enterprise_id,
            "map_id": map_id,
            "map_name": map_name,
            "lang": robot_language,
        }
    async with RouteService(robot_parameters) as route_service:
        # 获取所有路线信息

        await put_run_step("fetch_route_info", **kwargs)
        routes_data = await route_service.get_all_routes()
        # 提取路线列表
        routes = []
        if routes_data and routes_data.get("data", {}).get("obj_list"):
            routes = [item["obj"] for item in routes_data["data"]["obj_list"]]
        else:
            routes = []

        # 生成推荐文本
        await put_run_step("generate_route_recommendation", **kwargs)
        recommendation_text = await generate_route_recommendation_text(
            user_query, routes, **kwargs
        )

        return FunctionResult(
            type="function",
            content=FunctionOutput(
                result={"recommendation_text": recommendation_text},
                audio_request=AudioRequest(
                    content=NonStreamingContent(text=recommendation_text)
                ),
            ),
            debug={"action_result": {"recommendation_text": recommendation_text}},
            elapse_info={"action_cost_time": time.time() - start_time},
        )


@action_function_register
async def convert_specific_route(
    user_query: str, route_name: str, **kwargs
) -> FunctionResult:
    """转换特定路线信息。
    Args:
        user_query (str): 用户问题
        route_name (str): 路线名称
        **kwargs: 额外参数，包含机器人相关信息
    Returns:
        FunctionResult: 处理后的参数，包含路线ID和名称或推荐文本
    """
    start_time = time.time()

    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        routes = await get_route_info(robot)
        # 使用LLM进行路线匹配
        await put_run_step("match_route_with_llm", **kwargs)
        matched_route = await match_route_with_llm(user_query, route_name, routes)

        # 如果匹配成功，返回路线信息
        if matched_route:
            result = {
                "tour_id": matched_route.get("tour_id"),
                "route_name": matched_route.get("name"),
            }
            return FunctionResult(
                type="function",
                content=FunctionOutput(
                    result=result,
                    audio_request=AudioRequest(
                        content=NonStreamingContent(text=route_name)
                    ),
                ),
                debug={"action_result": result},
                elapse_info={"action_cost_time": time.time() - start_time},
            )
        else:
            # 如果匹配失败，生成推荐文本
            await put_run_step("generate_route_recommendation", **kwargs)
            recommendation_text = await generate_route_recommendation_text(
                user_query, routes, **kwargs
            )
            result = {"recommendation_text": recommendation_text}
            return FunctionResult(
                type="function",
                content=FunctionOutput(
                    result=result,
                    audio_request=AudioRequest(
                        content=NonStreamingContent(text=recommendation_text)
                    ),
                ),
                debug={"action_result": result},
                elapse_info={"action_cost_time": time.time() - start_time},
            )


@action_function_register
async def generate_route_recommendation(user_query, **kwargs) -> FunctionResult:
    """基于用户需求和可用路线生成推荐"""
    start_time = time.time()
    if "__robot" in kwargs:
        robot = kwargs["__robot"]
        device_id = robot.device_id
        enterprise_id = robot.enterprise_id
        map_id = robot.map_id
        map_name = robot.map_name
        robot_language = robot.language  # 输出语言  zh/en/de
        robot_parameters = {
            "device_id": device_id,
            "enterprise_id": enterprise_id,
            "map_id": map_id,
            "map_name": map_name,
            "lang": robot_language,
        }

        async with RouteService(robot_parameters) as route_service:
            route_details = await route_service.get_all_route_details()
            # 生成推荐文本
            recommendation_text = await build_recommendation_text(
                user_query, route_details, **kwargs
            )
            return FunctionResult(
                type="function",
                content=FunctionOutput(
                    result={"recommendation_text": recommendation_text},
                    audio_request=AudioRequest(
                        content=NonStreamingContent(text=recommendation_text)
                    ),
                ),
                debug={"action_result": {"recommendation_text": recommendation_text}},
                elapse_info={"action_cost_time": time.time() - start_time},
            )


@action_function_register
async def mcp_tool_post_process(
    mcp_result: Union[CallToolResult, FunctionResult], **kwargs
) -> FunctionResult:
    """MCP工具后处理"""
    start_time = time.time()

    robot = kwargs.get("__robot")
    if not robot:
        raise ValueError("robot is required")

    await put_run_step("fetch_mcp_tool_result", **kwargs)

    if "__memory" in kwargs:
        chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=3)
        conversation_progress = "\n".join(
            LLMToolKit.build_conversation_progress(chat_context.messages)
        )
    else:
        conversation_progress = ""

    lang_question = kwargs.get("_USER_QUERY")
    if lang_question:
        lang_result = get_action_ouput_language(lang_question, robot, lang_flag="zh")
        language = lang_result["lang"]
        detect_lang = lang_result["detect_lang"]
    else:
        language = "中文"
        detect_lang = "zh_CN"

    is_success = False
    if isinstance(mcp_result, FunctionResult):
        is_success = mcp_result.status == "success"
        tool_result = mcp_result.content.result
    else:
        is_success = not mcp_result.isError
        tool_result = [item.model_dump() for item in mcp_result.content]

    mcp_prompt = ""
    if not is_success:
        mcp_prompt += f"工具执行失败\n执行结果:{tool_result}"
    else:
        mcp_prompt += f"工具执行成功\n执行结果:{tool_result}"

    if len(mcp_prompt) > 1000:  # 限制工具执行结果长度
        mcp_prompt = mcp_prompt[:1000] + "..."

    prompt = f"""根据工具执行结果结合聊天上下文并且使用{language}回答用户问题，回答必须使用纯文本输出，简洁明了。
### 工具执行结果:
{mcp_prompt}

### 聊天上下文:
{conversation_progress}

### 用户问题:
{lang_question}"""
    messages = []
    # if is_multilingual(robot):
    #     messages.append(
    #         LLMToolKit.build_multilingual_user_content(lang_question, detect_lang)
    #     )
    messages.append(
        {
            "role": "user",
            "content": prompt,
        }
    )
    await put_run_step("summarize_answer", **kwargs)

    # Calculate elapsed time
    elapsed_time = time.time() - start_time

    # Create debug info
    debug_info = {
        "lang_question": lang_question,
        "language": language,
        "detect_lang": detect_lang,
        "messages": messages,
    }

    # Get MCP execution time if available
    mcp_execution_time = kwargs.get("__MCP_EXECUTION_TIME", 0)

    return FunctionResult(
        type="function",
        elapse_info={
            "mcp_execution_time": mcp_execution_time,
            "post_process_time": elapsed_time,
            "total_time": mcp_execution_time + elapsed_time,
        },
        debug=debug_info,
        content=FunctionOutput(
            result={},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.7,
                        api_key=agent_setting.generate_text_model_api_key,
                    ).model_dump(),
                ),
            ),
        ),
    )


async def extract_user_info_from_conversation(
    user_info_fields: list, text_conversation: str, user_query: str, **kwargs
) -> dict:
    """从对话历史中提取用户信息"""
    import re

    if not user_info_fields:
        return {}

    # 兼容字符串和列表两种输入格式
    if isinstance(user_info_fields, str):
        user_info_fields = [user_info_fields]

    fields_str = "、".join(user_info_fields)

    prompt = f"""从用户的对话历史中提取以下用户信息字段：{fields_str}

# 对话历史
{text_conversation}

# 当前用户问题
{user_query}

# 提取要求
1. 只提取明确提到的信息，不要推测
2. 如果某个字段没有相关信息，则不包含该字段
3. 输出格式为JSON，JSON的key必须与给定字段名一字不差地完全一致
4. 给定字段：{fields_str}，则JSON的key只能是这些字段名，不能修改、简化或添加
5. 如果没有找到任何相关信息，输出空JSON对象: {{}}

请提取用户信息："""

    messages = [{"role": "user", "content": prompt}]

    def extract_json_block(content: str) -> dict:
        """从模型输出中提取 JSON 对象"""
        matches = re.findall(r"```(?:json)?\s*([\s\S]*?)\s*```", content)
        for match in matches:
            try:
                return json.loads(match.strip())
            except json.JSONDecodeError:
                continue
        match = re.search(r"\{[\s\S]*\}", content)
        if match:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                pass
        return {}

    try:
        model_result = await LLMManager.invoke_generate_text_model(messages=messages)

        # 兼容不同模型返回结构
        if isinstance(model_result, dict) and "choices" in model_result:
            content = model_result["choices"][0]["message"]["content"].strip()
        else:
            content = getattr(model_result, "content", "").strip()

        logger.info(f"[extract_user_info] 模型原始输出内容: {content}")

        extracted_info = extract_json_block(content)

        if not extracted_info:
            logger.error(f"[extract_user_info] 未找到可解析的JSON内容: {content}")
            return {}

        logger.info(f"[extract_user_info] 提取到用户信息: {extracted_info}")
        return extracted_info

    except Exception as e:
        logger.error(f"[extract_user_info] 提取用户信息失败: {e}")
        return {}


async def build_user_recommendation_text(
    user_question: str,
    user_profile: dict,
    product_info: list,
    conversation_progress: str,
    **kwargs,
) -> list:
    """构建用户推荐话术的messages"""
    # 获取输出语言
    lang_question = kwargs.get("_USER_QUERY") or user_question
    action_result = get_action_ouput_language(
        lang_question, kwargs.get("__robot"), lang_flag="zh"
    )
    language = action_result["lang"]
    detect_lang = action_result["detect_lang"]  # noqa

    # 构建产品信息文本
    if not product_info:
        products_info = "当前没有可用的产品信息"
    else:
        products_info = json.dumps(product_info, ensure_ascii=False, indent=2)

    # 构建用户信息文本
    user_info_text = (
        json.dumps(user_profile, ensure_ascii=False, indent=2) if user_profile else ""
    )

    prompt = f"""基于用户问题、聊天对话、用户历史画像信息和产品信息，生成个性化的产品推荐话术。要求：
1. 使用{language}输出，语言自然流畅
2. 字数控制在80字以内
3. 根据用户信息匹配最合适的产品进行推荐
4. 突出匹配产品的优势特点
5. 语气要专业且有说服力
6. 避免使用任何markdown格式
7. 直接给出推荐话术，不要额外的解释

# 聊天对话
{conversation_progress}

## 用户问题
{user_question}

## 用户历史画像信息
{user_info_text}

## 产品信息
{products_info}


请根据以上信息生成合适的推荐话术："""

    messages = []
    messages.append({"role": "user", "content": prompt})

    return messages


@action_function_register
async def collect_user_info_and_recommend(
    user_info_fields: list, **kwargs
) -> FunctionResult:
    """
    收集用户信息并推荐产品
    :param user_info_fields: 要收集的用户信息字段列表，如['budget', 'age', 'preferences']
    :param kwargs: 包含机器人和内存等信息
    :return: FunctionResult 包含推荐话术和用户画像
    """
    start_time = time.time()
    robot = kwargs.get("__robot")

    # 1. 获取对话历史（使用提供的模式）
    memory: Memory = kwargs["__memory"]
    chat_context = await memory.get_chat_context(max_chat_history=6)
    text_conversation = await LLMToolKit.get_text_conversation_records(
        chat_context.messages,
        limit=6,
    )
    user_query = kwargs["_USER_QUERY"]
    if user_query and user_query not in text_conversation:
        text_conversation.append(f"<User> said '{user_query}'")
    text_conversation_str = "\n".join(text_conversation)

    # 2. 获取当前用户画像（从内存中）
    user_profile = {}
    if memory:
        # 从用户偏好中获取已有的用户信息
        user_profile = memory.get_all_preferences()

    extraction_task = asyncio.create_task(
        extract_user_info_from_conversation(
            user_info_fields, text_conversation_str, user_query, **kwargs
        )
    )
    product_task = asyncio.create_task(get_promote_brief_product_info(robot))

    # 等待两个任务完成
    extracted_info, product_info = await asyncio.gather(extraction_task, product_task)

    # 4. 更新用户画像
    user_profile.update(extracted_info)
    logger.info(f"extracted_info is {extracted_info}")
    # 将新提取的用户信息存储到memory中
    if memory and extracted_info:
        for key, value in extracted_info.items():
            memory.add_preference(key, value)

    # 5. 调用构建推荐话术函数，生成messages
    messages = await build_user_recommendation_text(
        user_query, user_profile, product_info, text_conversation_str, **kwargs
    )

    elapsed_time = time.time() - start_time

    # 6. 返回推荐结果
    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={},
            audio_request=AudioRequest(
                content=StreamingContent(
                    messages=messages,
                    llm_config=RequestLLMConfig(
                        temperature=0.6,
                    ).model_dump(),
                )
            ),
        ),
        debug={
            "user_profile": user_profile,
            "products_count": len(product_info) if product_info else 0,
            "extracted_info": extracted_info,
            "prompt": messages[0]["content"],
        },
        elapse_info={"action_cost_time": elapsed_time},
    )


@action_function_register
async def answer_question_from_vision_with_arm(
    image_url: str, question: str, **kwargs
) -> FunctionResult:
    """
    Integrates visual data, question, and conversation context into a single prompt and queries the 7B model once.

    :param image_url: The URL of the image collected.
    :param question: The question posed by the user.
    :param _CONTEXT: Conversation context.
    :return: A FunctionResult containing the answer and audio output.
    """
    # Constructing a concise prompt with image URL, question, and context
    persona = LLMToolKit.init_persona_core_objective(
        agent_id=kwargs["__robot"].agent_id,
        app_id=kwargs["__robot"].interface_state.app_id,
        persona=kwargs["__robot"].PERSONA,
        language_style=kwargs["__robot"].LANGUAGE_STYLE,
        objective=kwargs["__robot"].OBJECTIVE,
    )
    origin_query = kwargs["_USER_QUERY"]
    robot = kwargs["__robot"]
    geo_location = (
        robot.geo_location.city
        if agent_setting.region_version == Area.domestic
        else f"{robot.geo_location.latitude},{robot.geo_location.longitude}"
    )
    language = robot.language

    weather_info = await get_weather(geo_location, language)

    chat_context = await kwargs["__memory"].get_chat_context(max_chat_history=6)
    conversation_progress = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context.messages)
    )

    logger.info(f"[answer_question_from_vision] image_url: {image_url}")
    await put_run_step("visual_analysis", **kwargs)

    image_content = await get_image_content(image_url)

    action_result = get_action_ouput_language(
        origin_query, kwargs.get("__robot"), lang_flag="en"
    )  # Get target language
    language = action_result["lang"]

    zh_prompt = f"""你是一个可以帮助分析图片并回答用户问题的助手。回答必须为纯文本，且不超过35个字。
## 当前天气信息
城市: {kwargs["__robot"].geo_location.city}
天气: {weather_info.weather if weather_info else "未知"}
温度: {weather_info.temperature if weather_info else "未知"}

## 对话历史
{conversation_progress}

## 用户问题
{question}

注意：
1. 如果图片中出现多个人，当问题涉及具体个人信息时，请针对每个人分别回答。
2. 避免使用"在图片中"之类的短语，并以第一人称视角用{language}来回答用户的问题。"""

    en_prompt = f"""You are a helpful assistant that can analyze images and answer user's question. The answer must be plain text and within 35 words.
## Current Weather Information
City: {kwargs["__robot"].geo_location.city}
Weather: {weather_info.weather if weather_info else "unknown"}
Temperature: {weather_info.temperature if weather_info else "unknown"}

## Conversation History
{conversation_progress}

## User Question
{question}

Note:
1. Avoid phrases like "in the image", and use first-person perspective to **answer the user's question** in **{language}**.
2. If multiple people appear in the image, when questions involve specific personal information, please answer separately for each person."""

    if agent_setting.region_version == Area.domestic:
        prompt = zh_prompt
    else:
        prompt = en_prompt

    logger.info(f"answer_question_from_vision prompt:\n {prompt}")

    await put_run_step("prepare_answer", **kwargs)

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {agent_setting.vision_api_key}",
    }
    payload = {
        "model": agent_setting.vision_model,
        "temperature": 0.0,
        "messages": [
            {"role": "system", "content": persona.persona},
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": image_content},
                    },
                    {"text": prompt, "type": "text"},
                ],
            },
        ],
    }

    start_time = time.time()
    debug_info = {"vision_prompt": prompt, "errors": []}

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {agent_setting.vision_api_key}",
    }
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{agent_setting.vision_base_url}/chat/completions",
                headers=headers,
                json=payload,
            ) as response:
                result = await response.json()
                logger.info(f"answer_question_from_vision raw result:\n {result}")
                model_result = (
                    result.get("choices", [{}])[0].get("message", {}).get("content", "")
                )
                logger.info(f"answer_question_from_vision result:\n {model_result}")
    except Exception as e:
        err_msg = f"Failed to get multimodal result: {e} payload:{payload}"
        logger.error(err_msg)
        debug_info["errors"].append(err_msg)
        await send_feishu_alarm(err_msg)
        model_result = ""

    elapsed_time = time.time() - start_time
    debug_info["multimodal_time"] = elapsed_time

    return FunctionResult(
        type="function",
        content=FunctionOutput(
            result={"answer_text": model_result},
            audio_request=AudioRequest(
                content=NonStreamingContent(text=model_result),
            ),
        ),
        elapse_info={"multimodal_time": elapsed_time},
        debug=debug_info,
    )


if __name__ == "__main__":
    import asyncio

    # async def test_download_file():
    #     newest_doc_url = "https://api.chatmax.net/orics/down/ct003_20240905_827e2c838b2708b405388bbdf1e55b47.txt"
    #     async with aiohttp.ClientSession() as session:
    #         async with session.get(newest_doc_url) as response:
    #             content = await response.text()
    #             print(content)

    # async def test():
    #     result = await calendar("", "今天是几号")
    #     print(result)

    # async def test_update_knowledge_base():
    #     content = "公司里最能喝酒的是孙**"
    #     result = await update_knowledge_base(content)
    #     print(result)

    # async def test_coze_generate_picture():
    #     result = await coze_generate_picture("", "我想要一张壁纸")
    #     print(result)

    # async def test_coze_talk_to_raiden():
    #     result = await coze_talk_to_raiden("", "你好")
    #     print(result)

    # async def test_coze_search_picture():
    #     result = await coze_search_picture("", "小猫咪")
    #     print(result)

    # # asyncio.run(test_update_knowledge_base())
    # # asyncio.run(test_coze_generate_picture())
    # # asyncio.run(test_coze_talk_to_raiden())
    # asyncio.run(test_coze_search_picture())

    # asyncio.run(test_send_message())

    # asyncio.run(get_realtime_weather("北京", "今天天气怎么样"))

    # asyncio.run(
    #     _get_knowledge_content(
    #         "豹厂通有几个传感器",
    #         AGENT_CONFIG.get(PROMOTE_AGENT_ID).knowledge_base_id,
    #     )
    # )

    async def test_answer_question_from_vision():
        result = await answer_question_from_vision(
            "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1111/c/4/0/9/c409fca43e7e63591f24e8efa80c30e8.jpeg",
            "我穿成这样去上海冷不冷",
        )
        print(result)

    asyncio.run(test_answer_question_from_vision())

    # async def test_get_knowledge_content():
    #     result = await _get_knowledge_content(
    #         query="test",
    #         knowledge_base_id="",
    #         enterprise_id="orion.ovs.entprise.**********",
    #     )
    #     print(result)
    #
    #
    # asyncio.run(test_get_knowledge_content())
