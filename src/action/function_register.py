import functools
import inspect
from typing import Callable, Dict

# 全局函数字典，键为函数名称，值为函数对象
ALL_FUNCTIONS: Dict[str, Callable] = {}


def action_function_register(func: Callable) -> Callable:
    """
    将函数注册到全局函数字典ALL_FUNCTIONS中的装饰器
    支持同步函数和异步函数

    Args:
        func: 要注册的函数

    Returns:
        原函数或适配的包装函数
    """

    # 检查是否为异步函数
    is_async = inspect.iscoroutinefunction(func)

    # 使用functools.wraps保留原始函数的元数据
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)

    # 为异步函数创建异步包装器
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        return await func(*args, **kwargs)

    # 获取原始函数名称
    original_name = func.__name__

    # 注册原始函数
    ALL_FUNCTIONS[original_name] = func

    # 根据函数类型返回相应的包装器
    return async_wrapper if is_async else wrapper
