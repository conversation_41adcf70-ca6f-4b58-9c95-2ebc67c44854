import json
from copy import deepcopy
from typing import Any, Dict, Literal, Optional, TypeVar

import redis
import regex as re
from livekit.agents import utils
from loguru import logger
from pydantic import BaseModel

from src.settings import agent_setting
from src.settings import global_async_redis_client
from src.action.actions import ActionLib


EventTypes = Literal["update_blackboard",]
T = TypeVar("T")

ACTION_RESULT_REGEX = r"\$(\w+)\.result_parameters\.(\w+)"
ACTION_INPUT_REGEX = r"\$(\w+)\.input_parameters\.(\w+)"


class ActionParameter(BaseModel):
    """
    Reference format:
    * ${action_id}.input_parameters.{parameter_name}
    * ${action_id}.result_parameters.{parameter_name}
    """

    action_id: str
    action_name: str
    input_parameters: Dict = {}
    result_parameters: Dict = {}


class RunningParameter(BaseModel):
    """
    每次运行一棵behavior tree的参数和run_id 一一对应
    先复制plan的action_parameters，然后根据实时运行状态更新
    """

    run_id: str
    action_parameters: Dict[str, ActionParameter]  # action_id -> ActionParameter
    parameters: Dict = {}  # reference format: $running.{parameter_name}, e.g. $running.interviewee_name


class PlanParameter(BaseModel):
    plan_id: str
    action_parameters: Dict[str, ActionParameter] = {}  # action_id -> ActionParameter
    run_parameters: Dict[str, RunningParameter] = {}  # run_id -> ActionParameter

    def init_run_parameters(self, run_id):
        # copy action_parameters
        self.run_parameters = {
            run_id: RunningParameter(
                action_parameters=deepcopy(self.action_parameters), run_id=run_id
            )
        }


class Blackboard(utils.EventEmitter[EventTypes]):
    """
    Blackboard is used to store and manage the parameters of a plan
    TODO: 增加全局预置的参数； reference format: $blackboard.{parameter_name}
    """

    def __init__(
        self,
        redis_client: "redis.Redis",
        plan_parameters: Dict[str, PlanParameter] | None = None,
    ):
        super().__init__()
        self.plan_parameters = plan_parameters if plan_parameters else {}
        self.redis_client = redis_client
        self._device_id = None
        self._key_prefix = f"{agent_setting.env}_blackboard"
        self.expire_duration = 1800  # 30 minutes

    @classmethod
    def is_reference(cls, reference: str) -> bool:
        return bool(
            re.search(ACTION_INPUT_REGEX, reference)
            or re.search(ACTION_RESULT_REGEX, reference)
        )

    @property
    def device_id(self):
        return self._device_id

    @device_id.setter
    def device_id(self, value):
        self._device_id = value

    @property
    def plan_key_prefix(self):
        if not self.device_id:
            raise ValueError("device_id is not set")
        return f"{self._key_prefix}_{self.device_id}_plan"

    @property
    def run_key_prefix(self):
        if not self.device_id:
            raise ValueError("device_id is not set")
        return f"{self._key_prefix}_{self.device_id}_run"

    def get_plan_prefix_key(self, plan_id: str) -> str:
        return f"{self.plan_key_prefix}_plan:{plan_id}"

    def get_run_prefix_key(self, plan_id: str, run_id: str) -> str:
        return f"{self.run_key_prefix}_plan:{plan_id}_run:{run_id}"

    def get_plan_action_key(self, plan_id: str, action_id: str) -> str:
        return f"{self.get_plan_prefix_key(plan_id)}_action:{action_id}"

    def get_run_action_key(self, plan_id: str, run_id: str, action_id: str) -> str:
        return f"{self.get_run_prefix_key(plan_id, run_id)}_action:{action_id}"

    async def _save_run_action_parameters(
        self,
        plan_id: str,
        run_id: str,
        action_id: str,
        action_parameter: ActionParameter,
    ) -> bool:
        action_key = self.get_run_action_key(plan_id, run_id, action_id)
        try:
            ret = await global_async_redis_client.setex(
                action_key,
                self.expire_duration,
                json.dumps(action_parameter.model_dump(), ensure_ascii=False),
            )
            logger.info(f"Save run action parameters: {action_parameter}")
        except Exception as e:
            logger.error(
                f"Failed to set run action parameters: {e} key: {action_key} value: {action_parameter}"
            )
            return False

        if not ret:
            logger.error(
                f"Failed to set run action parameters for key: {action_key} with value: {action_parameter} ret: {ret}"
            )
            return False

        return True

    async def _get_run_action_parameters(
        self, plan_id: str, run_id: str, action_id: str
    ) -> Optional[ActionParameter]:
        action_key = self.get_run_action_key(plan_id, run_id, action_id)
        try:
            raw_value = await global_async_redis_client.get(action_key)
            action_parameter = ActionParameter(**json.loads(raw_value))
            logger.info(f"Get run action parameters: {action_parameter}")
        except Exception as e:
            logger.error(f"Failed to get run action parameters: {e} key: {action_key}")
            return None

        if not raw_value:
            logger.warning(
                f"Failed to get run action parameters: {action_key} {raw_value}. Maybe the key does not exist."
            )
            return None

        return action_parameter

    async def _save_plan_action_parameters(
        self, plan_id: str, action_id: str, action_parameter: ActionParameter
    ) -> bool:
        action_key = self.get_plan_action_key(plan_id, action_id)
        try:
            ret = await global_async_redis_client.setex(
                action_key,
                self.expire_duration,
                json.dumps(action_parameter.model_dump(), ensure_ascii=False),
            )
            logger.info(f"Save plan action parameters: {action_parameter}")
        except Exception as e:
            logger.error(
                f"Failed to set plan action parameters: {e} key: {action_key} value: {action_parameter}"
            )
            return False

        if not ret:
            logger.error(
                f"Failed to set plan action parameters for key: {action_key} with value: {action_parameter} ret: {ret}"
            )
            return False

        return True

    async def _get_plan_action_parameters(
        self, plan_id: str, action_id: str
    ) -> Optional[ActionParameter]:
        action_key = self.get_plan_action_key(plan_id, action_id)
        try:
            raw_value = await global_async_redis_client.get(action_key)
            action_parameter = ActionParameter(**json.loads(raw_value))
            logger.info(f"Get plan action parameters: {action_parameter}")
        except Exception as e:
            logger.error(f"Failed to get plan action parameters: {e} key: {action_key}")
            return None

        if not raw_value:
            logger.warning(
                f"Failed to get plan action parameters: {action_key} {raw_value}. Maybe the key does not exist."
            )
            return None

        return action_parameter

    async def init_plan_parameters(self, plan_id: str, action: dict):
        """
        Parse the behavior tree and set the plan parameters
        无论是否规划成功，都需要在规划前给出plan_id，然后在规划后给出behavior_tree，生成 plan_parameters
        :param plan_id:
        :param plan:
        :return:
        """
        original_input_parameters = (
            action.get("_ORIGINAL_PARAMETERS", action.get("PARAMETERS", {})) or {}
        )
        original_action_full_name = (
            action.get("_ORIGINAL_ACTION_NAME", action.get("ACTION", "")) or ""
        )
        try:
            original_action_name = original_action_full_name.split(".")[-1]
        except Exception as e:
            logger.error(f"Failed to get original action name: {e} action: {action}")
            original_action_name = original_action_full_name

        action_definition = ActionLib().get_one_action(
            full_name=original_action_full_name
        )
        action_definition_parameters = [
            parameter.name
            for parameter in action_definition.parameters
            if not parameter.is_hidden  # filtered hidden parameters
        ]
        input_parameters = {}
        for attr, attr_value in original_input_parameters.items():
            if attr in action_definition_parameters:
                if (
                    "$" not in attr_value
                ):  # TODO: better handle reference;暂时没有引用变量
                    input_parameters[attr] = attr_value

        fake_action_id = "act_0"
        action_parameter = ActionParameter(
            action_id=fake_action_id,  # 目前只有一个动作
            action_name=original_action_name.lower(),
            input_parameters=input_parameters,
        )
        logger.info(f"Init plan parameters: {action_parameter}")
        await self._save_plan_action_parameters(
            plan_id, fake_action_id, action_parameter
        )

    async def init_run_parameters(self, plan_id, run_id):
        # get keys with plan prefix
        plan_prefix_key = self.get_plan_prefix_key(plan_id)
        try:
            plan_keys = await global_async_redis_client.keys(f"{plan_prefix_key}_*")
        except Exception as e:
            logger.error(
                f"[Blackboard]  Failed to get plan keys: {e} {plan_prefix_key}"
            )
            return

        if not plan_keys:
            logger.error(
                f"[Blackboard] Failed to get plan keys with prefix: {plan_prefix_key}"
            )
            return

        # clone plan_parameters to run_parameters
        for plan_key in plan_keys:
            action_id = plan_key.split(":")[-1]
            try:
                action_parameter = await self._get_plan_action_parameters(
                    plan_id, action_id
                )
            except Exception as e:
                logger.error(
                    f"[Blackboard] Failed to get plan action parameters: {e} key: {plan_key}"
                )
                continue

            if not action_parameter:
                logger.error(
                    f"[Blackboard] Failed to get plan action parameters: {plan_key}"
                )
                continue

            try:
                await self._save_run_action_parameters(
                    plan_id, run_id, action_id, action_parameter
                )
            except Exception as e:
                logger.error(
                    f"[Blackboard] Failed to save run action parameters: {e} key: {plan_key} value: {action_parameter}"
                )

        # notify the blackboard has been updated
        await self.async_emit("update_blackboard", plan_id=plan_id, run_id=run_id)

    async def get_running_node_input_parameters(
        self, plan_id: str, run_id: str, action_id: str
    ):
        try:
            action_parameter = await self._get_run_action_parameters(
                plan_id, run_id, action_id
            )
        except Exception as e:
            logger.error(f"Failed to get running node input parameters: {e}")
            return {}

        return action_parameter.input_parameters

    async def get_running_node_parameters(
        self, plan_id: str, run_id: str, action_id: str
    ):
        try:
            action_parameter = await self._get_run_action_parameters(
                plan_id, run_id, action_id
            )
        except Exception as e:
            logger.error(f"Failed to get running node parameters: {e}")
            return {}

        return action_parameter

    async def eval_reference(self, plan_id: str, run_id: str, reference: str):
        """
        reference format:
        * ${action_id}.input_parameters.{parameter_name}
        * ${action_id}.result_parameters.{parameter_name}
        * $running.{parameter_name}
        * $blackboard.{parameter_name}
        """
        logger.info(f"[Blackboard] Evaluating reference: {reference}")
        # Define a list of patterns to match against
        patterns = [ACTION_INPUT_REGEX, ACTION_RESULT_REGEX]

        # Function to evaluate a single match
        async def evaluate_match(action_id, param_name, pattern):
            try:
                if pattern == ACTION_INPUT_REGEX:
                    return (
                        await self._get_run_action_parameters(
                            plan_id, run_id, action_id
                        )
                    ).input_parameters.get(param_name, "")
                elif pattern == ACTION_RESULT_REGEX:
                    return (
                        await self._get_run_action_parameters(
                            plan_id, run_id, action_id
                        )
                    ).result_parameters.get(param_name, "")
            except Exception as e:
                logger.error(f"[Blackboard] Failed to get parameters: {e}")

        # Iterate over each pattern and replace matches in the reference string
        for pattern in patterns:
            matches = re.finditer(pattern, reference)
            for match in matches:
                action_id, param_name = match.groups()
                replacement_value = await evaluate_match(action_id, param_name, pattern)
                if not replacement_value:
                    raise ValueError(f"Cannot find parameter: {reference}")
                reference = reference.replace(match.group(0), replacement_value)

        logger.info(f"[Blackboard] Evaluated reference: {reference}")
        return reference

    async def save_action_result(
        self, plan_id: str, run_id: str, action_id: str, result_parameters: Dict
    ) -> bool:
        """
        Save the result parameters of an action
        :param plan_id:
        :param run_id:
        :param action_id:
        :param result_parameters:
        :return:
        """
        try:
            action_parameter = await self._get_run_action_parameters(
                plan_id, run_id, action_id
            )
        except Exception as e:
            logger.error(
                f"[Blackboard] Failed to get running node input parameters: {e}"
            )
            return False

        if not action_parameter:
            logger.error(
                f"[Blackboard] Failed to get running node input parameters: {action_id}"
            )
            return False

        action_parameter.result_parameters.update(result_parameters)
        try:
            ret = await self._save_run_action_parameters(
                plan_id, run_id, action_id, action_parameter
            )
        except Exception as e:
            logger.error(
                f"[Blackboard] Failed to save running node result parameters: {e}"
            )
            return False

        if not ret:
            logger.error(
                f"[Blackboard] Failed to save running node result parameters: {action_parameter}"
            )
            return False

        # notify the blackboard has been updated
        await self.async_emit("update_blackboard", plan_id=plan_id, run_id=run_id)
        return True

    async def get_running_parameters(self, plan_id: str, run_id: str) -> Dict:
        # get all run action keys
        run_prefix_key = self.get_run_prefix_key(plan_id, run_id)
        try:
            run_keys = await global_async_redis_client.keys(f"{run_prefix_key}_*")
        except Exception as e:
            logger.error(f"[Blackboard]  Failed to get run keys: {e}")
            return {}

        if not run_keys:
            logger.error(
                f"[Blackboard] Failed to get run keys with prefix: {run_prefix_key}"
            )
            return {}

        # get running parameters
        running_parameters = {}
        for run_key in run_keys:
            action_id = run_key.split(":")[-1]
            try:
                action_parameter = await self._get_run_action_parameters(
                    plan_id, run_id, action_id
                )
            except Exception as e:
                logger.error(
                    f"[Blackboard] Failed to get running parameters: {e} key: {run_key}"
                )
                continue

            if not action_parameter:
                logger.error(
                    f"[Blackboard] Failed to get running parameters: {run_key}"
                )
                continue

            running_parameters[action_id] = action_parameter.model_dump(
                exclude={"input_parameters"}
            )

        return {
            "action_parameters": running_parameters,
        }

    async def async_emit(self, event: T, *args: Any, **kwargs: Any) -> None:
        if event in self._events:
            callables = self._events[event].copy()
            for callback in callables:
                await callback(*args, **kwargs)
