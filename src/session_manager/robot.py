from typing import Any, Literal

from loguru import logger
from pydantic import BaseModel

from src.action.model import Action
from src.common.agent_config import (
    LAUNCHER_AGENT_ID,
    Launcher_App_Id,
    APP_IDS,
    AGENT_SDK_COMPATIBLE_APP_IDS,
)
from src.common.constant import Area, LanguageEnum
from src.settings import AgentMode, agent_setting
from src.utils.date import get_current_date_str
from src.utils.text_processor import clean_text
from src.common.agent_config import APP_CONFIG, AppConfig

MODULE_APP_PACKAGE = "com.ainirobot.moduleapp"  # 小豹APP
MAPTOOLS_PACKAGE = "com.ainirobot.maptool"  # 地图工具


class InterfaceState(BaseModel):
    package_name: str = MODULE_APP_PACKAGE  # APK name
    app_id: str = Launcher_App_Id  # 当前机器人屏幕显示的应用对应的id
    page_id: str = ""  # 当前机器人屏幕显示的页面对应的id
    interface_info: str = (
        ""  # 当前机器人屏幕显示的页面的关键信息, 推荐使用 clean_interface_info
    )
    interface_type: str = "default"  # TODO: 支持更多的页面更新类型
    clickable_elements: list = []  # 当前机器人屏幕显示的页面的可点击元素

    # 兼容以前逻辑，已被废弃
    app_objective: str = ""  # 当前应用目标
    page_objective: str = ""  # 当前页面目标

    def clean_interface_info(self) -> str:
        return clean_text(self.interface_info)


class CharacterStatus(BaseModel):
    nickname: str = "小豹"  # 角色别称
    persona: str = "你叫小豹，是个非常聪明的机器人。"  # 角色
    page_persona: str = ""  # 当前页面的角色

    scene: str = ""  # 背景
    language_style: str = "你的语言风格是专业、友好、诙谐的。"  # 语言风格
    page_language_style: str = ""  # 当前页面的语言风格

    app_objective: str = ""  # 当前应用目标
    page_objective: str = ""  # 当前页面目标


class GeoLocation(BaseModel):
    geo_location: str = ""
    country: str = ""
    city: str = ""
    province: str = ""
    district: str = ""
    town: str = ""
    street: str = ""
    describe: str = ""
    latitude: float = 0.0  # 机器人的纬度
    longitude: float = 0.0  # 机器人的经度


def ensure_int(value: Any):
    if isinstance(value, str):
        try:
            return int(value)
        except ValueError:
            pass
    return value


class Robot(BaseModel):
    volume: int = -1  # -1: not set
    brightness: int = -1
    current_speed: float = 1.0
    target_speed: float = 1.0
    indoor_location_history: list[str] = []  # 新增字段，记录最近的室内点位
    battery_level: int = 100
    enterprise_id: str = ""
    device_id: str = "VIRTUAL_DEVICE"
    group_id: str = ""  # 场景 id
    client_id: str = ""  # 业务线 id
    product_id: int = 100010
    product_model: str = ""
    interface_state: InterfaceState = InterfaceState()
    action_version: str = "draft"
    updated_at: float = 0
    face_id: str = ""
    geo_location: GeoLocation = GeoLocation()
    speech_rate: float = 6.0
    agent_id: str = LAUNCHER_AGENT_ID

    character_state: CharacterStatus = CharacterStatus()

    agent_mode: str = AgentMode.turbo

    # localization
    timezone: str = "America/New_York"
    language: str = "zh_CN"
    spokesman: str = "aliyun-loongbella1"
    language_code: int = 1
    # 当前机器人使用的地图name
    map_id: str = ""
    map_name: str = ""

    # Temporary field, only for guide opk
    enable_qa: bool = False

    # 多语言设置, 0关闭，1启用
    multilingual: int = 0

    # 客户端注册action列表
    app_actions: list["Action"] = []
    page_actions: list["Action"] = []
    block_actions: list["Action"] = []
    block_level: Literal["all", "specific"] = (
        "specific"  # specific: 使用指定的block_actions过滤，all: 过滤所有已注册的action，只有当前page上报的action_list生效
    )

    fallback_action: str = ""

    disable_plan: bool = False  # 是否禁用plan

    allow_interrupt: bool = True

    # 澄清，二次确认配置
    turn_on_clarify: bool = True
    turn_on_confirm: bool = True

    def robot_status_info(self) -> dict:
        """Robot status info **prompt**"""
        robot_info = self.robot_base_info
        robot_info.update(self.robot_real_time_info)
        return robot_info

    def robot_debug_info(self) -> dict:
        """
        Robot debug info for **BO**
        """
        robot_info = self.robot_status_info()
        robot_info["agent_id"] = self.agent_id
        robot_info["agent_mode"] = self.agent_mode

        robot_info["action_version"] = self.action_version
        robot_info["is_builtin_app"] = self.is_builtin_app
        robot_info["is_agent_sdk_compatible"] = self.is_agent_sdk_compatible

        robot_info["face_id"] = self.face_id
        robot_info["timezone"] = self.timezone
        robot_info["language"] = self.language
        robot_info["spokesman"] = self.spokesman
        robot_info["speech_rate"] = self.speech_rate
        robot_info["language_code"] = self.language_code
        robot_info["map_id"] = self.map_id
        robot_info["map_name"] = self.map_name
        robot_info["enable_qa"] = self.enable_qa
        robot_info["multilingual"] = self.multilingual
        robot_info["app_id"] = self.APP_ID
        robot_info["package_name"] = self.PACKAGE_NAME
        robot_info["block_level"] = self.block_level
        robot_info["fallback_action"] = self.fallback_action
        robot_info["disable_plan"] = self.disable_plan
        robot_info["character_state"] = self.character_state.model_dump()
        robot_info["interface_state"] = self.interface_state.model_dump()

        # business info
        robot_info["enterprise_id"] = self.enterprise_id
        robot_info["device_id"] = self.device_id
        robot_info["group_id"] = self.group_id
        robot_info["client_id"] = self.client_id
        robot_info["product_id"] = self.product_id
        robot_info["product_model"] = self.product_model

        robot_info["turn_on_clarify"] = self.turn_on_clarify
        robot_info["turn_on_confirm"] = self.turn_on_confirm

        robot_info["app_config"] = self.app_config.model_dump()
        return robot_info

    @property
    def app_config(self):
        if self.is_builtin_app:
            return APP_CONFIG.get(self.APP_ID, AppConfig())
        else:
            # TODO: 从aos管理后台拉取二开APP配置
            return AppConfig(
                enable_mcp=False,
                enable_action_command=False,
                enable_action_intervention=False,
            )

    @property
    def robot_base_info(self) -> dict:
        from src.settings import agent_setting

        self.geo_location.describe = (
            self.geo_location.describe.removesuffix("附近")
            .removeprefix("在")
            .removesuffix("(总部)")
        )

        if agent_setting.region_version == Area.overseas:
            return {
                "Current Latitude and Longitude": f"{self.geo_location.latitude},{self.geo_location.longitude}",
                "Current Country": self.geo_location.country,
                "Current Province": self.geo_location.province,
                "Current City": self.geo_location.city,
                "Current District": self.geo_location.district,
                "Current Street": self.geo_location.street,
                "Current Location": self.geo_location.describe,
            }
        else:
            return {
                "当前位置经纬度": f"{self.geo_location.latitude},{self.geo_location.longitude}",
                "所在国家": self.geo_location.country,
                "所在省份": self.geo_location.province,
                "所在城市": self.geo_location.city,
                "所在地区": self.geo_location.district,
                "所在街道": self.geo_location.street,
                "所在地点": self.geo_location.describe,
            }

    @property
    def robot_real_time_info(self) -> dict:
        robot_real_time_info_dict = {
            "当前音量": {Area.overseas: "Current Volume", Area.domestic: "当前音量"},
            "电池电量": {Area.overseas: "Battery Level", Area.domestic: "电池电量"},
            "当前时间": {Area.overseas: "Current Time", Area.domestic: "当前时间"},
            "当前移动速度": {
                Area.overseas: "Current Movement Speed",
                Area.domestic: "当前移动速度",
            },
            "目标最大平稳速度": {
                Area.overseas: "Target Maximum Stable Speed",
                Area.domestic: "目标最大平稳速度",
            },
            "当前室内点位": {
                Area.overseas: "Current Indoor Point",
                Area.domestic: "当前室内点位",
            },
            "上一次室内点位": {
                Area.overseas: "Previous Indoor Location",
                Area.domestic: "上一次室内点位",
            },
        }
        from src.settings import agent_setting

        region_version = agent_setting.region_version
        robot_info = {
            robot_real_time_info_dict["当前音量"].get(
                region_version, "当前音量"
            ): self.volume,
            robot_real_time_info_dict["电池电量"].get(
                region_version, "电池电量"
            ): self.battery_level,
            robot_real_time_info_dict["当前时间"].get(
                region_version, "当前时间"
            ): get_current_date_str(self.timezone),
            robot_real_time_info_dict["当前移动速度"].get(
                region_version, "当前移动速度"
            ): self.current_speed,
        }
        if self.target_speed:
            robot_info[
                robot_real_time_info_dict["目标最大平稳速度"].get(
                    region_version, "目标最大平稳速度"
                )
            ] = self.target_speed
        if self.indoor_location_history:
            robot_info[
                robot_real_time_info_dict["当前室内点位"].get(
                    region_version, "当前室内点位"
                )
            ] = self.indoor_location_history[-1]
        if len(self.indoor_location_history) > 1:
            robot_info[
                robot_real_time_info_dict["上一次室内点位"].get(
                    region_version, "上一次室内点位"
                )
            ] = self.indoor_location_history[-2]

        return robot_info

    def update_indoor_location(self, new_location: str, max_history: int = 3):
        """
        更新机器人室内点位，并维护点位历史记录
        """
        current_location = (
            self.indoor_location_history[-1] if self.indoor_location_history else ""
        )
        # TODO(@wangxiaoxi): 校验点位是否在地图上
        if new_location != current_location:
            logger.info(f"更新机器人室内点位: {current_location} -> {new_location}")
            self.indoor_location_history.append(new_location)
            if len(self.indoor_location_history) > max_history:
                self.indoor_location_history = self.indoor_location_history[
                    -max_history:
                ]

    @classmethod
    def _update(
        cls,
        original_instance: "Robot",
        origin_data: dict,
        nested_keys_with_cls: dict,
    ) -> "Robot":
        """
        Maintain single instance of Robot
        TODO: Singleton pattern
        """
        instance = original_instance.model_dump()  # original instance
        instance.update(origin_data)  # update instance
        original_instance.model_validate(instance)  # only validate; no modifying

        for k, v in origin_data.items():
            if k in nested_keys_with_cls:
                # recursively update
                sub_instance = cls._update(getattr(original_instance, k), v, {})
                setattr(original_instance, k, sub_instance)  # use instance to update
            elif (
                hasattr(original_instance, k)
                and getattr(original_instance, k, None) != v
            ):
                logger.debug(
                    f"updating value of '{k}' from '{getattr(original_instance, k, None)}' to '{v}'"
                )
                setattr(original_instance, k, v)

        return original_instance

    def update(self, data: dict) -> "Robot":
        return self._update(
            self,
            data,
            {
                "interface_state": InterfaceState,
                "geo_location": GeoLocation,
                "character_state": CharacterStatus,
            },
        )

    @property
    def latitude_longitude(self):
        return f"{self.geo_location.latitude},{self.geo_location.longitude}"

    def is_multilingual(self):
        from src.common.constant import Multilingual

        if not self.multilingual:
            if agent_setting.region_version == Area.overseas:
                return False

            if self.language == LanguageEnum.zh:
                return True

        return str(self.multilingual) == Multilingual.Open.value

    @property
    def actions(self) -> list["Action"]:
        if self.block_level == "all":  # only page actions are effective
            return self.page_actions

        elif self.block_level == "specific":  # only filter block actions
            total_actions = self.app_actions + self.page_actions
            block_actions_fullname = [a.full_name for a in self.block_actions]

            candidate_actions = []
            for action in total_actions:
                if action.full_name not in block_actions_fullname:
                    candidate_actions.append(action)

            return candidate_actions
        else:
            raise ValueError(f"Invalid block_level: {self.block_level}")

    @property
    def PERSONA(self):
        return self.character_state.page_persona or self.character_state.persona

    @property
    def LANGUAGE_STYLE(self):
        return (
            self.character_state.page_language_style
            or self.character_state.language_style
        )

    @property
    def APP_ID(self) -> str:
        return self.interface_state.app_id

    @property
    def PAGE_ID(self) -> str:
        return self.interface_state.page_id

    @property
    def PACKAGE_NAME(self) -> str:
        return self.interface_state.package_name

    @property
    def OBJECTIVE(self) -> str:
        return (
            self.character_state.page_objective
            or self.character_state.app_objective
            or self.interface_state.page_objective
            or self.interface_state.app_objective
            or ""
        )

    @property
    def is_builtin_app(self):
        if not self.APP_ID:  # maptools无app_id,目前也算builtin
            return self.PACKAGE_NAME in [MODULE_APP_PACKAGE, MAPTOOLS_PACKAGE]

        return self.APP_ID in APP_IDS

    @property
    def is_agent_sdk_compatible(self):
        """
        Check if the app is compatible with Agent SDK.

        Agent SDK compatible apps must have APP_ID, and the APP_ID must not be in
        APP_IDS or must be in AGENT_SDK_COMPATIBLE_APP_IDS.

        Returns:
            bool: Whether the app is compatible with Agent SDK.
        """
        # Agent SDK compatible apps must have APP_ID
        if not self.APP_ID:
            return self.PACKAGE_NAME not in [MODULE_APP_PACKAGE, MAPTOOLS_PACKAGE]

        # The APP_ID must not be in APP_IDS or must be in AGENT_SDK_COMPATIBLE_APP_IDS
        return self.APP_ID not in APP_IDS or self.APP_ID in AGENT_SDK_COMPATIBLE_APP_IDS
