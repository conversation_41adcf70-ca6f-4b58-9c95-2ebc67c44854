from typing import Any, Literal, Dict, List, Optional
import json
from datetime import datetime, timedelta

from pydantic import BaseModel
import xml.etree.ElementTree as ET
from loguru import logger
import redis

from src.settings import agent_setting, global_async_redis_client

NODE_TYPES = ["Action", "Fallback", "Sequence"]

RunningStatus = Literal[
    "pending", "running", "succeeded", "failed", "rejected", "recalled", "interrupted"
]
TerminalRunningStatus = ["succeeded", "failed", "rejected", "recalled", "interrupted"]


class NodeRunningStatus(BaseModel):
    node_id: str
    request_id: str = ""
    result: Any = None
    exception: Any = None
    updated_at: float = 0.0
    created_at: float = datetime.now().timestamp()
    info: Dict = {}
    status: RunningStatus = "pending"


class PlanRunningStatus(BaseModel):
    """记录plan此次运行的状态
    * 可能同时多个节点处于running状态
    """

    plan_id: str
    run_id: str  # 用于区分不同次运行
    plan_xml: str
    updated_at: float = 0.0
    created_at: float = datetime.now().timestamp()
    status: RunningStatus = "pending"


class RuntimeStatusManager:
    def __init__(self, redis_client: "redis.Redis"):
        self._device_id = None
        self.redis_client = redis_client
        self.key_prefix = f"{agent_setting.env}_action_result"
        self.expire_duration = int(timedelta(minutes=10).total_seconds())

    @property
    def device_id(self):
        return self._device_id

    @device_id.setter
    def device_id(self, value):
        self._device_id = value

    @property
    def plan_key_prefix(self):
        if not self.device_id:
            raise ValueError("device_id is not set")
        return f"{self.key_prefix}_{self.device_id}_plan"

    @property
    def node_key_prefix(self):
        return f"{self.key_prefix}_{self.device_id}_node"

    def get_plan_prefix_key(self, run_id: str):
        return f"{self.plan_key_prefix}_run:{run_id}"

    def get_node_prefix_key(self, node_id: str, run_id: str):
        return f"{self.node_key_prefix}_run:{run_id}_node:{node_id}"

    async def set_run_failed(self, run_id, reason: str):
        logger.warning(f"Set run failed: {reason} for run_id: {run_id}")
        if plan_running_status := await self.get_plan_running_status(run_id):
            plan_running_status.status = "failed"
            await self.save_plan_running_status(run_id, plan_running_status)

    async def save_plan_running_status(
        self, run_id: str, plan_running_status: PlanRunningStatus
    ) -> bool:
        key = self.get_plan_prefix_key(run_id)
        try:
            ret = await global_async_redis_client.setex(
                key, self.expire_duration, json.dumps(plan_running_status.model_dump())
            )
            logger.info(f"Save plan running status: {plan_running_status}")
        except Exception as e:
            logger.error(f"Failed to save plan running status: {e}")
            return False

        if not ret:
            logger.error(f"Failed to save plan running status for key: {key}")
            return False

        return True

    async def _save_node_running_status(
        self,
        run_id: str,
        node_id: str,
        node_running_status: NodeRunningStatus,
    ) -> bool:
        key = self.get_node_prefix_key(node_id, run_id)
        try:
            ret = await global_async_redis_client.setex(
                key, self.expire_duration, json.dumps(node_running_status.model_dump())
            )
            logger.info(f"Save node running status: {node_running_status}")
        except Exception as e:
            logger.error(f"Failed to save node running status: {e}")
            return False

        if not ret:
            logger.error(f"Failed to save node running status for key: {key}")
            return False

        return True

    async def init_plan(self, run_id: str, plan_xml: str, plan_id: str):
        plan_running_status = PlanRunningStatus(
            plan_id=plan_id, run_id=run_id, plan_xml=plan_xml
        )
        try:
            ret = await self.save_plan_running_status(run_id, plan_running_status)
        except Exception as e:
            logger.error(f"Failed to init node running status: {e}")
            return

        if not ret:
            logger.error("Failed to init node running status")
            return

        try:
            root = ET.fromstring(plan_xml)
        except ET.ParseError:
            raise ValueError("Invalid behavior tree format")

        # get all nodes
        for node in root.findall(".//*[@ID]"):
            node_id = node.attrib["ID"]
            node_tag = node.tag
            if node_tag not in NODE_TYPES:
                continue

            node_running_status = NodeRunningStatus(node_id=node_id)
            # get all attrib
            for attr, attr_value in node.attrib.items():
                if attr not in ["ID"]:
                    node_running_status.info[attr] = attr_value

            try:
                ret = await self._save_node_running_status(
                    run_id, node_id, node_running_status
                )
            except Exception as e:
                logger.error(f"Failed to init node running status: {e}")
                continue

            if not ret:
                logger.error("Failed to init node running status")
                continue

        logger.info(f"Init plan running status: plan:{plan_id} run:{run_id}")

    async def get_one_node_running_status(
        self, run_id: str, node_id: str
    ) -> Optional[NodeRunningStatus]:
        key = self.get_node_prefix_key(node_id, run_id)
        try:
            raw_value = await global_async_redis_client.get(key)
        except Exception as e:
            logger.error(f"Failed to get node running status: {e}")
            return None

        if not raw_value:
            return None
        try:
            node_running_status = NodeRunningStatus(**json.loads(raw_value))
            logger.info(
                f"[run_id: {run_id}] Get node running status: {node_id} {node_running_status.status}"
            )
            return node_running_status
        except Exception as e:
            logger.error(
                f"Failed to build node running status: {e} key: {key} value: {raw_value}"
            )
            return None

    async def update_node_status(self, run_id: str, node_id: str, content: Dict):
        logger.info(f"Update node status: {run_id} {node_id} {content}")
        if "updated_at" not in content:
            content["updated_at"] = datetime.now().timestamp()

        origin_node_status = await self.get_one_node_running_status(run_id, node_id)
        if not origin_node_status:
            raise ValueError(
                f"Failed to update node status, node_id: {node_id} not found"
            )
        try:
            ret = await self._save_node_running_status(
                run_id, node_id, origin_node_status.model_copy(update=content)
            )
        except Exception as e:
            logger.error(
                f"Failed to update node: {run_id} {origin_node_status.node_id} status: {e}"
            )
            return

        if not ret:
            logger.error("Failed to update node status")
            return

    async def get_plan_running_status(
        self,
        run_id: str,
    ) -> Optional[PlanRunningStatus]:
        key = self.get_plan_prefix_key(run_id)
        try:
            raw_value = await global_async_redis_client.get(key)
        except Exception as e:
            logger.error(f"Failed to get plan running status: {e}")
            return None

        if not raw_value:
            return None
        try:
            plan_running_status = PlanRunningStatus(**json.loads(raw_value))
            logger.info(
                f"[run_id: {run_id}] Get plan running status: {plan_running_status.plan_id} {plan_running_status.status}"
            )
            return plan_running_status
        except Exception as e:
            logger.error(
                f"Failed to build plan running status: {e} key: {key} value: {raw_value}"
            )
            return None

    async def get_running_node_status(
        self,
        run_id: str,
    ) -> List[NodeRunningStatus]:
        # get plan
        try:
            plan_running_status = await self.get_plan_running_status(run_id)
        except Exception as e:
            logger.error(f"Failed to get running nodes: {e}")
            return []

        if not plan_running_status:
            return []

        plan_xml = plan_running_status.plan_xml
        root = ET.fromstring(plan_xml)
        running_nodes = []
        for node in root.findall(".//*[@ID]"):
            node_id = node.attrib["ID"]
            try:
                node_status = await self.get_one_node_running_status(run_id, node_id)
            except Exception as e:
                logger.error(
                    f"Failed to get node:run:{run_id} node: {node_id} status: {e}"
                )
                continue

            if node_status and node_status.status == "running":
                running_nodes.append(node_status)

        return running_nodes

    async def get_all_node_status(self, run_id: str) -> List[NodeRunningStatus]:
        try:
            plan_running_status = await self.get_plan_running_status(run_id)
        except Exception as e:
            logger.error(f"Failed to get all nodes: {e}")
            return []

        if not plan_running_status:
            return []

        plan_xml = plan_running_status.plan_xml
        root = ET.fromstring(plan_xml)
        all_nodes = []
        for node in root.findall(".//*[@ID]"):
            node_id = node.attrib["ID"]
            try:
                node_status = await self.get_one_node_running_status(run_id, node_id)
            except Exception as e:
                logger.error(
                    f"Failed to get node: run:{run_id} node: {node_id} status: {e}"
                )
                continue

            all_nodes.append(node_status)

        return all_nodes

    async def get_children_status(
        self, run_id: str, node_id: str
    ) -> List[NodeRunningStatus]:
        try:
            plan_running_status = await self.get_plan_running_status(run_id)
        except Exception as e:
            logger.error(f"Failed to get children status: {e}")
            return []

        if not plan_running_status:
            return []

        plan_xml = plan_running_status.plan_xml
        root = ET.fromstring(plan_xml)
        node = root.find(f".//*[@ID='{node_id}']")
        if node is None:
            raise ValueError(f"Node {node_id} not found in plan xml")

        children = []
        for child in node:
            if child.tag in NODE_TYPES:
                children.append(child.attrib["ID"])

        children_status = []
        for child in children:
            try:
                child_status = await self.get_one_node_running_status(run_id, child)
            except Exception as e:
                logger.error(
                    f"Failed to get node: run:{run_id} node: {child} status: {e}"
                )
                continue

            children_status.append(child_status)

        return children_status
