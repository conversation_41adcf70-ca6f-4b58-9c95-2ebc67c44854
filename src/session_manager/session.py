import time
import traceback
import uuid
from typing import Optional
from urllib.parse import urljoin

import aiohttp
from loguru import logger

from src.action.resource import get_promote_settings_info
from src.agent_core.models.agent_core import RunAction
from src.common.agent_config import Opk_Promote_App_Id
from src.session_manager.robot import Robot
from src.settings import agent_setting
from src.utils.feishu_alarm import send_feishu_alarm


class AosSession:
    """用户与机器人的会话类，记录会话内容和状态"""

    def __init__(self, robot: Robot):
        """初始化一个新的会话

        Args:
            robot: 机器人实例
        """
        self.start_time = int(time.time())
        self.end_time = None
        self.robot = robot
        self.session_id = robot.device_id[-4:] + str(uuid.uuid4())  # 生成唯一会话ID
        self.confirmed_action: Optional[RunAction] = None
        logger.info(
            f"新建session， device_id: {robot.device_id}， session_id: {self.session_id}"
        )

    @property
    def timezone(self):
        if self.robot is None:
            return ""
        return self.robot.timezone

    @property
    def enterprise_id(self):
        if self.robot is None:
            return ""
        return self.robot.enterprise_id

    @property
    def agent_id(self):
        if self.robot is None:
            return ""
        return self.robot.agent_id

    @property
    def device_id(self):
        if self.robot is None:
            return ""
        return self.robot.device_id

    @property
    def group_id(self):
        if self.robot is None:
            return ""
        return self.robot.group_id

    @property
    def client_id(self):
        if self.robot is None:
            return ""
        return self.robot.client_id

    @property
    def product_id(self):
        if self.robot is None:
            return ""
        return self.robot.product_id

    @property
    def product_model(self):
        if self.robot is None:
            return ""
        return self.robot.product_model

    @property
    def face_id(self):
        if self.robot is None:
            return ""
        return self.robot.face_id

    @property
    def app_id(self):
        if self.robot is None:
            return ""
        return self.robot.APP_ID

    @property
    def page_id(self):
        if self.robot is None:
            return ""
        return self.robot.PAGE_ID

    async def get_project_id(self, robot):
        if robot is None:
            return ""
        data = await get_promote_settings_info(robot)
        project_id = data.get("config_id", "")
        return project_id

    async def _construct_request_body(self, memory):
        request_body = {
            "session_id": self.session_id,
            "enterprise_id": self.enterprise_id,
            "device_id": self.device_id,
            "group_id": self.group_id,
            "client_id": self.client_id,
            "project_id": await self.get_project_id(self.robot),
            "product_id": self.product_id,
            "product_model": self.product_model,
            "timezone": self.timezone,
            "session_start_time": self.start_time,
            "session_end_time": self.end_time,
            "user_preferences": memory.get_all_preferences(),
        }
        history_message = await memory.get_chat_context()
        messages = history_message.messages
        # TODO: logger info --> debug
        logger.info(
            f"[aos session end] request_body: {request_body} history_messages: {messages}"
        )
        # TODO 临时的策略，通过这种方式来判断此次会话是否使用了推销模式
        app_id = self.app_id
        for m in messages:
            if m.app_id == Opk_Promote_App_Id:
                app_id = Opk_Promote_App_Id
                break
        request_body["app_id"] = app_id
        history_message_dict = history_message.model_dump()
        request_body["messages"] = history_message_dict["messages"]
        return request_body

    async def _report_aos_backend(self, memory):
        """
        上报数据
        """
        from src.utils.feishu_alarm import send_feishu_alarm

        enterprise_id = self.enterprise_id
        try:
            async with aiohttp.ClientSession() as session:
                url = urljoin(
                    agent_setting.aos_studio_host, agent_setting.aos_report_url
                )
                request_body = await self._construct_request_body(memory)

                # 检查是否为空数据，如果是则跳过发送
                if (
                    not request_body.get("enterprise_id")
                    or not request_body.get("device_id")
                    or not request_body.get("session_id")
                    or not request_body.get("product_id")
                    or request_body.get("session_start_time") is None
                    or request_body.get("session_end_time") is None
                ):
                    logger.warning(
                        f"检测到空数据，跳过上报 - enterprise_id: {request_body.get('enterprise_id')}, device_id: {request_body.get('device_id')}, session_id: {request_body.get('session_id')}, product_id: {request_body.get('product_id')}, session_start_time: {request_body.get('session_start_time')}, session_end_time: {request_body.get('session_end_time')}"
                    )
                    return False

                logger.info(f"aos request_body is {request_body}")
                async with session.post(
                    url=url,
                    json=request_body,
                    headers={
                        "Content-Type": "application/json",
                    },
                    timeout=30,
                ) as response:
                    result = await response.json()
                    logger.info(f"session report data is {result}")
                    if response.status >= 400:
                        await send_feishu_alarm(
                            f"上报：{enterprise_id} 推销模式中的会话数据到aos平台api调用异常，请求体：{request_body}， 地址：{url}, status: {response.status}, result is {result}"
                        )
                        return False
                return True

        except Exception as _:
            print(traceback.format_exc())
            return False

    async def end(self, memory=None) -> None:
        """结束当前会话"""
        logger.info(
            f"结束session， device_id: {self.device_id}, session_id: {self.session_id}"
        )
        self.end_time = int(time.time())
        # 手动加1秒
        if self.end_time == self.start_time:
            self.end_time += 1

        try:
            await self._report_aos_backend(memory)
        except Exception as _:
            logger.error(f"Failed to report aos backend: {traceback.format_exc()}")
            await send_feishu_alarm(
                f"Failed to report aos backend: {traceback.format_exc()}"
            )

        # 重置相关字段
        self.session_id = ""
        self.start_time = None
        self.end_time = None
        self.robot = None


async def get_setting_user_preferences(robot):
    data = await get_promote_settings_info(robot)
    user_info = data.get("user_info", [])
    return user_info


class UserPreferences:
    """
    用户偏好信息管理类，支持基于 session_id 添加、删除、覆盖、清空、获取等操作。
    每个 session_id 下的偏好信息独立管理。
    """

    def __init__(self):
        self._preferences: dict[str, str] = {}

    def add_preference(self, key: str, value: any) -> None:
        """添加/更新一个偏好"""
        self._preferences[key] = value

    def remove_preference(self, key: str) -> bool:
        """删除指定某个偏好，返回是否成功"""
        if key in self._preferences:
            del self._preferences[key]
            return True
        return False

    def update_preference(self, key: str, value: any) -> None:
        """覆盖（更新）特定偏好"""
        self.add_preference(key, value)

    def clear_preferences(self) -> None:
        """清空偏好"""
        self._preferences.clear()

    def get_preference(self, key: str) -> any:
        """获取指定偏好"""
        return self._preferences.get(key)

    def get_all_preferences(self) -> dict:
        """获取偏好"""
        return self._preferences

    async def is_full(self, robot):
        user_info = await get_setting_user_preferences(robot)
        for item in user_info:
            try:
                key = item["name"]
                if key not in self._preferences:
                    return False
            except Exception as _:
                print(traceback.format_exc())
        return True

    async def empty_field(self, robot):
        result = []
        user_info = await get_setting_user_preferences(robot)
        for item in user_info:
            try:
                key = item["name"]
                if key not in self._preferences:
                    result.append(key)
            except Exception as _:
                print(traceback.format_exc())
        return result
