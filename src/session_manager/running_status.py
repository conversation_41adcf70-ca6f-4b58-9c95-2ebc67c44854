from typing import Literal, Any, Optional
from datetime import datetime, timedelta
import json
import redis

from pydantic import BaseModel
from loguru import logger

from src.settings import agent_setting
from src.action.model import FunctionResult


class ActionStatus(BaseModel):
    """

    Represents the status of an action with details on its execution.

    Attributes:
    - status (Literal["running", "succeeded", "failed", "pending"]): The current status of the action. Defaults to "running".
    - created_at (float): The timestamp when the action was created. Defaults to the current time in seconds since the Unix epoch.
    - expired_at (float): The timestamp when the action is considered expired. Defaults to 10 minutes after creation but can be manually set.
    - running_result (Optional[FunctionResult]): The result of the function when it has executed. Null if the action is not yet completed.

    Note:
    - The `expired_at` field automatically sets the expiration to 10 minutes from creation unless overridden.
    - The `FunctionResult` type represents the outcome of the executed function and is optional, indicating the action may not have finished yet.

    Example Usage:
    ```python
    # Instantiate an ActionStatus with default values
    action_status = ActionStatus()

    # Update the status manually
    action_status.status = "succeeded"
    action_status.running_result = my_function_result

    # Manually setting the expiration time
    action_status.expired_at = datetime.now().timestamp() + timedelta(hours=1).total_seconds()
    ```

    """

    status: Literal["running", "succeeded", "failed", "pending"] = "running"
    created_at: float = datetime.now().timestamp()
    expired_at: float = (
        datetime.now().timestamp() + timedelta(minutes=10).total_seconds()
    )  # expired after 10 minutes or manually set the expired time
    running_result: Optional[FunctionResult] = None


class ActionResultCache:
    """
    ActionResultCache is a class that manages the status of actions in the system.
    """

    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.key_prefix = f"{agent_setting.env}_action_result"
        self.expire_duration = int(timedelta(minutes=10).total_seconds())

    @property
    def device_id(self):
        return self._device_id

    @device_id.setter
    def device_id(self, value):
        self._device_id = value

    @staticmethod
    def generate_action_running_id(run_id: str, node_id: str) -> str:
        return f"{run_id}_{node_id}"

    def get_action_status(self, action_running_id: str) -> Optional[ActionStatus]:
        key = self.get_action_result_key(action_running_id)
        raw_value = self.redis_client.get(key)
        if not raw_value:
            return None
        try:
            action_status = ActionStatus(**json.loads(raw_value))
            logger.info(
                f"[action_running_id: {action_running_id}] Get action status: {action_status}"
            )
        except Exception as e:
            logger.error(
                f"Failed to build action status: {e} key: {key} value: {raw_value}"
            )
            return None
        return action_status

    def get_action_result_key(self, action_running_id: str) -> str:
        return f"{self.key_prefix}_{self.device_id}_{action_running_id}"

    def set_action_status(self, action_running_id: str, action_status: ActionStatus):
        key = self.get_action_result_key(action_running_id)
        try:
            ret = self.redis_client.setex(
                key, self.expire_duration, json.dumps(action_status.model_dump())
            )
            logger.info(
                f"[action_running_id: {action_running_id}] save action status: {action_status}"
            )
        except Exception as e:
            logger.error(
                f"Failed to set action status: {e} key: {key} value: {action_status}"
            )
            return

        if not ret:
            logger.error(
                f"Failed to set action status for key: {key} with value: {action_status} ret: {ret}"
            )
