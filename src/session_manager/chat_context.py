import time
from typing import Any, List, Literal

from pydantic import BaseModel, Field

from src.action.model import ResultTypeLiteral

ChatRole = Literal["system", "user", "assistant"]


class ChatImage(BaseModel):
    image: str
    inference_width: int | None = None
    inference_height: int | None = None


class ChatParameter(BaseModel):
    """
    Parameter for chat history
    """

    name: str
    desc: str = ""
    value: Any


class ChatResult(BaseModel):
    """
    Result for chat history
    """

    name: str
    desc: str = ""
    type: ResultTypeLiteral
    value: Any


class ChatAction(BaseModel):
    """
    Action for chat history
    """

    name: str
    display_name: str = ""
    parameters: List[ChatParameter] = []
    result: List[ChatResult] = []


class ChatEvent(BaseModel):
    """
    Event for chat history
    """

    desc: str


class ChatMessage(BaseModel):
    role: ChatRole
    content: str | list[str | ChatImage] | None = None
    action: ChatAction | None = None
    event: ChatEvent | None = None
    app_id: str = ""
    page_id: str = ""
    agent_id: str = ""  # 待废弃, 但推销模式需要等改造后才能用
    face_id: str = ""# 可能会非常不准确
    sid: str = ""
    message_timestamp: str = Field(default_factory=lambda: str(int(time.time())))


class ChatContext(BaseModel):
    messages: list[ChatMessage] = Field(default_factory=list)

    def append(
        self,
        *,
        text: str = "",
        images: list[ChatImage] = [],
        role: ChatRole = "system",
        action: ChatAction | None = None,
        event: ChatEvent | None = None,
        app_id: str = "",
        page_id: str = "",
        agent_id: str = "",
        face_id: str = "",
        sid: str = "",
        message_timestamp: str = ""
    ) -> "ChatContext":
        if not images:
            content = text
        else:
            content: list[str | ChatImage] = []
            if text:
                content.append(text)
            content.extend(images)

        msg_kwargs = {
            "content": content,
            "role": role,
            "action": action,
            "event": event,
            "app_id": app_id,
            "page_id": page_id,
            "agent_id": agent_id,
            "face_id": face_id,
            "sid": sid,
        }
        if message_timestamp:
            msg_kwargs["message_timestamp"] = message_timestamp
        self.messages.append(ChatMessage(**msg_kwargs))
        return self
