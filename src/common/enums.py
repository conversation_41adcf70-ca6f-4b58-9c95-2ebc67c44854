"""
枚举类模块

此模块包含项目中使用的所有枚举类定义，集中管理所有枚举值，
使代码更加清晰和易于维护。
"""

from enum import Enum, auto
from typing import Union, Optional


class TimeZone(Enum):
    """时区枚举类"""
    SHANGHAI = "Asia/Shanghai"
    NEW_YORK = "America/New_York"
    LONDON = "Europe/London"
    TOKYO = "Asia/Tokyo"
    SYDNEY = "Australia/Sydney"
    
    @classmethod
    def from_string(cls, value: Union[str, None]) -> Optional['TimeZone']:
        """从字符串转换为枚举值
        
        Args:
            value: 字符串值，如 "Asia/Shanghai", "shanghai" 等
                  如果为None，则返回None
            
        Returns:
            TimeZone: 对应的枚举值，如果输入为None则返回None
            
        Raises:
            ValueError: 如果无法转换为有效的时区枚举值
        """
        if value is None:
            return None
            
        value = value.lower() if isinstance(value, str) else value
        
        # 映射常见的时区名称变体
        mapping = {
            "shanghai": cls.SHANGHAI,
            "asia/shanghai": cls.SHANGHAI,
            "china": cls.SHANGHAI,
            "beijing": cls.SHANGHAI,
            
            "new_york": cls.NEW_YORK,
            "america/new_york": cls.NEW_YORK,
            "newyork": cls.NEW_YORK,
            "usa": cls.NEW_YORK,
            "us": cls.NEW_YORK,
            
            "london": cls.LONDON,
            "europe/london": cls.LONDON,
            "uk": cls.LONDON,
            "britain": cls.LONDON,
            
            "tokyo": cls.TOKYO,
            "asia/tokyo": cls.TOKYO,
            "japan": cls.TOKYO,
            
            "sydney": cls.SYDNEY,
            "australia/sydney": cls.SYDNEY,
            "australia": cls.SYDNEY,
        }
        
        if value in mapping:
            return mapping[value]
        
        # 尝试直接匹配枚举值
        for timezone in cls:
            if value == timezone.value.lower():
                return timezone
        
        raise ValueError(f"无法将 '{value}' 转换为有效的时区枚举值")
    
    def __str__(self) -> str:
        """返回枚举值的字符串表示"""
        return self.value


class AreaLevel(Enum):
    """区域级别枚举类，用于区分省份、城市和地区"""
    PROVINCE = "province"  # 省份级别
    CITY = "city"          # 城市级别
    AREA = "area"          # 区域级别
    
    @classmethod
    def from_string(cls, value: Union[str, None]) -> Optional['AreaLevel']:
        """从字符串转换为枚举值
        
        Args:
            value: 字符串值，如 "province", "city", "area" 等
                  如果为None，则返回None
            
        Returns:
            AreaLevel: 对应的枚举值，如果输入为None则返回None
            
        Raises:
            ValueError: 如果无法转换为有效的区域级别枚举值
        """
        if value is None:
            return None
            
        value = value.lower() if isinstance(value, str) else value
        
        # 映射常见的区域级别名称变体
        mapping = {
            "province": cls.PROVINCE,
            "省": cls.PROVINCE,
            "省份": cls.PROVINCE,
            
            "city": cls.CITY,
            "城市": cls.CITY,
            "市": cls.CITY,
            
            "area": cls.AREA,
            "district": cls.AREA,
            "区域": cls.AREA,
            "区": cls.AREA,
            "县": cls.AREA,
        }
        
        if value in mapping:
            return mapping[value]
            
        # 尝试直接匹配枚举值
        for level in cls:
            if value == level.value:
                return level
                
        raise ValueError(f"无法将 '{value}' 转换为有效的区域级别枚举值")
    
    def __str__(self) -> str:
        """返回枚举值的字符串表示"""
        return self.value


# 可以根据需要添加更多枚举类

class LightAppType(str, Enum):
    WEB = "0"
    APP = "1"
