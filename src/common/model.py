import time
from typing import Dict, Literal, Optional

from pydantic import BaseModel, Field, field_serializer

from src.agent_core.models.agent_core import (
    LLMConfig as AgentCoreLLMConfig,
)
from src.agent_core.models.agent_core import (
    RunAction,
    RunAgentResponse,
)
from src.common.constant import LanguageEnum
from src.settings import AgentMode
from src.utils.llm import LLMConfig


class Debug(BaseModel):
    agent_messages: list[dict[str, str | list[dict]]] = []
    tools: list[dict] = []
    tool_short_name_map: dict = {}
    few_shot: list[dict] = []
    summary_messages: list[dict[str, str]] = []
    final_plan: str = ""
    error_msg: Optional[str] = None
    topk_actions: Optional[list[dict]] = None

    agent_token_cost: dict = {}
    summary_token_cost: dict = {}
    run_agent_response: Optional[RunAgentResponse] = None

    retry: int = 0

    llm_config: LLMConfig | AgentCoreLLMConfig = None

    select_few_shot_cost_time: float = 0
    summary_cost_time: float = 0
    embedded_cost_time: float = 0
    retrieval_cost_time: float = 0
    agent_call_cost_time: float = 0
    user_profile_cost_time: float = 0
    load_context_cost_time: float = 0
    strategy_check_cost_time: float = 0
    output_check_cost_time: float = 0
    action_post_process_cost_time: float = 0
    convert_action_to_plan_cost_time: float = 0
    strategy_action_cost_time: float = 0
    total_elapsed_time: float = 0
    end_timestamp: float = 0
    get_action_resource_cost_time: float = 0
    fetch_intervention_action_cost_time: float = 0
    call_agent_core_cost_time: float = 0
    inner_agent_core_cost_time: float = 0

    # 动作标识，是策略还是llm
    action_flag: Literal["strategy", "llm", "intervention"] = "strategy"

    # 极速和满血模式
    agent_mode: str = AgentMode.turbo
    summary_mode: str = ""
    search_query: str = ""

    action_intervention_info: dict = {}
    context_messages: list[dict] = []

    # 并行知识问答
    parallelize_knowledge_qa: bool = False
    knowledge_documents: list[dict] = []
    intervention_documents: list[dict] = []
    total_knowledge_documents: list[dict] = []
    total_intervention_documents: list[dict] = []

    knowledge_debug_info: dict = {}

    confirmed_action: Optional[RunAction] = None

    @field_serializer("llm_config")
    def serialize_llm_config(self, llm_config: LLMConfig | AgentCoreLLMConfig, _info):
        return llm_config.model_dump(exclude={"api_key"})


class PersonaCoreObjective(BaseModel):
    persona: str
    # 语言风格
    language_style: str = "your language style is professional, friendly, humorous."
    objective: list[str] | str = []


class OpkConfig(BaseModel):
    support_register: bool = True  # 同namespace级别，是否支持跨OPK的Action
    support_exported_actions: list = []  # 同namespace级别，支持指定的外部OPK的Action，如果已经支持了所有的外部注册action，不要再填此字段
    exclude_global_actions: list = []  # 不参与规划的global action列表


class AgentConfig(BaseModel):
    """
    AgentConfig is the configuration of an agent.
    """

    namespace: str = "orion.agent.action"
    persona_core_objective: Dict[str, PersonaCoreObjective] = {
        "en": PersonaCoreObjective(
            persona="Your name is '小豹', you are a very smart robot.",
        ),
        "zh_CN": PersonaCoreObjective(
            persona="Your name is '小豹', you are a very smart robot.",
        ),
    }
    support_register: bool = True  # namespace 级别是否支持所有外部注册的Action
    support_exported_actions: list = []  # namespace 级别支持指定的外部Action，如果已经支持了所有的外部注册action，不要再填此字段
    auto_generate_action_follow_up: bool = False  # 是否启用生成action执行结果触发的规划
    follow_up_trigger_actions: list = []  # 可触发的后续action列表
    auto_generate_recommend_follow_up: bool = True  # 是否启用action执行结果触发的推荐语，如果已经开启auto_generate_action_follow_up，则此字段为False
    recommend_prompt: Dict = {}  # 多模态打招呼配置
    exclude_global_actions: list = []  # 不参与规划的action列表
    auto_handle_event: bool = False
    exclude_history_actions: list = []  # 不参与历史记录的action列表
    opks: Dict[str, OpkConfig] = {}
    knowledge_base_id: str = "7e9e0e1f6c73491e514d4ef69f064572"


class SceneChangeContext(BaseModel):
    agent_description: str
    original_app_description: str
    changed_app_description: str


Multilingual_Step_Display_Mapping = {
    LanguageEnum.zh: {
        "thinking": "思考中",
        "analyze_context": "分析上下文",
        "select_tool": "选择工具",
        "reflect": "反思",
        "execute_tool": "执行工具",
        "fetch_weather_info": "获取天气信息",
        "fetch_calendar_info": "获取日历信息",
        "search_user": "搜索用户",
        "send_message": "发送消息",
        "prepare_answer": "准备答案",
        "select_best_case_introduction": "选择最佳案例介绍",
        "select_best_production_function": "选择最佳产品功能",
        "select_best_sales_strategy": "选择最佳销售策略",
        "select_best_recommend_strategy": "选择最佳推荐策略",
        "fetch_production_info": "获取产品信息",
        "fetch_knowledge_doc": "获取知识文档",
        "visual_analysis": "视觉分析",
        "fetch_route_info": "获取路线信息",
        "generate_route_recommendation": "生成路线推荐",
        "match_route_with_llm": "匹配路线",
        "summarize_answer": "总结答案",
        "fetch_mcp_tool_result": "获取MCP工具结果",
        "prepare_promote": "推销产品",
        "select_product_name": "选择产品",
        "select_production_parameters": "选择产品参数",
    },
    LanguageEnum.zh_gd: {
        "thinking": "諗緊",
        "analyze_context": "分析上下文",
        "select_tool": "揀工具",
        "reflect": "反思",
        "execute_tool": "執行工具",
        "fetch_weather_info": "攞天氣資訊",
        "fetch_calendar_info": "攞日曆資訊",
        "search_user": "搵用戶",
        "send_message": "發訊息",
        "prepare_answer": "準備答案",
        "select_best_case_introduction": "揀最好嘅案例介紹",
        "select_best_production_function": "揀最好嘅產品功能",
        "select_best_sales_strategy": "揀最好嘅銷售策略",
        "select_best_recommend_strategy": "揀最好嘅推薦策略",
        "fetch_production_info": "攞產品資訊",
        "fetch_knowledge_doc": "攞知識文檔",
        "visual_analysis": "視覺分析",
        "fetch_route_info": "攞路線資訊",
        "generate_route_recommendation": "生成路線推薦",
        "match_route_with_llm": "配對路線",
        "summarize_answer": "總結答案",
        "fetch_mcp_tool_result": "攞MCP工具結果",
    },
    LanguageEnum.en: {
        "thinking": "Thinking",
        "analyze_context": "Analyze Context",
        "select_tool": "Select Tool",
        "reflect": "Reflect",
        "execute_tool": "Execute Tool",
        "fetch_weather_info": "Fetch Weather Info",
        "fetch_calendar_info": "Fetch Calendar Info",
        "search_user": "Search User",
        "send_message": "Send Message",
        "prepare_answer": "Prepare Answer",
        "select_best_case_introduction": "Select Best Case Introduction",
        "select_best_production_function": "Select Best Production Function",
        "select_best_sales_strategy": "Select Best Sales Strategy",
        "select_best_recommend_strategy": "Select Best Recommend Strategy",
        "fetch_production_info": "Fetch Production Info",
        "fetch_knowledge_doc": "Fetch Knowledge Doc",
        "visual_analysis": "Visual Analysis",
        "fetch_route_info": "Fetch Route Info",
        "generate_route_recommendation": "Generate Route Recommendation",
        "match_route_with_llm": "Match Route",
        "summarize_answer": "Summarize Answer",
        "fetch_mcp_tool_result": "Fetch MCP Tool Result",
        "prepare_promote": "Promote The Sale of Product",
        "select_product_name": "Select Product",
        "select_production_parameters": "Select Product Parameters",
    },
    LanguageEnum.zh_tw: {
        "thinking": "思考中",
        "analyze_context": "分析上下文",
        "select_tool": "選擇工具",
        "reflect": "反思",
        "execute_tool": "執行工具",
        "fetch_weather_info": "獲取天氣信息",
        "fetch_calendar_info": "獲取日曆信息",
        "search_user": "搜索用戶",
        "send_message": "發送消息",
        "prepare_answer": "準備答案",
        "select_best_case_introduction": "選擇最佳案例介紹",
        "select_best_production_function": "選擇最佳產品功能",
        "select_best_sales_strategy": "選擇最佳銷售策略",
        "select_best_recommend_strategy": "選擇最佳推薦策略",
        "fetch_production_info": "獲取產品信息",
        "fetch_knowledge_doc": "獲取知識文檔",
        "visual_analysis": "視覺分析",
        "fetch_route_info": "獲取路線信息",
        "generate_route_recommendation": "生成路線推薦",
        "match_route_with_llm": "匹配路線",
        "summarize_answer": "總結答案",
        "fetch_mcp_tool_result": "獲取MCP工具結果",
        "prepare_promote": "推銷產品",
        "select_product_name": "選擇產品",
        "select_production_parameters": "選擇產品參數",
    },
    LanguageEnum.ja_jp: {
        "thinking": "考え中",
        "analyze_context": "コンテキストを分析",
        "select_tool": "ツールを選択",
        "reflect": "反省",
        "execute_tool": "ツールを実行",
        "fetch_weather_info": "天気情報を取得",
        "fetch_calendar_info": "カレンダー情報を取得",
        "search_user": "ユーザーを検索",
        "send_message": "メッセージを送信",
        "prepare_answer": "回答を準備",
        "select_best_case_introduction": "最適な事例紹介を選択",
        "select_best_production_function": "最適な製品機能を選択",
        "select_best_sales_strategy": "最適な販売戦略を選択",
        "select_best_recommend_strategy": "最適な推奨戦略を選択",
        "fetch_production_info": "製品情報を取得",
        "fetch_knowledge_doc": "知識文書を取得",
        "visual_analysis": "視覚分析",
        "fetch_route_info": "ルート情報を取得",
        "generate_route_recommendation": "ルート推奨を生成",
        "match_route_with_llm": "ルートをマッチング",
        "summarize_answer": "回答を要約",
        "fetch_mcp_tool_result": "MCPツール結果を取得",
        "prepare_promote": "製品を宣伝",
        "select_product_name": "製品を選択",
        "select_production_parameters": "製品パラメータを選択",
    },
    LanguageEnum.ko_kr: {
        "thinking": "생각 중",
        "analyze_context": "컨텍스트 분석",
        "select_tool": "도구 선택",
        "reflect": "반성",
        "execute_tool": "도구 실행",
        "fetch_weather_info": "날씨 정보 가져오기",
        "fetch_calendar_info": "캘린더 정보 가져오기",
        "search_user": "사용자 검색",
        "send_message": "메시지 전송",
        "prepare_answer": "답변 준비",
        "select_best_case_introduction": "최적의 사례 소개 선택",
        "select_best_production_function": "최적의 제품 기능 선택",
        "select_best_sales_strategy": "최적의 영업 전략 선택",
        "select_best_recommend_strategy": "최적의 추천 전략 선택",
        "fetch_production_info": "제품 정보 가져오기",
        "fetch_knowledge_doc": "지식 문서 가져오기",
        "visual_analysis": "시각적 분석",
        "fetch_route_info": "경로 정보 가져오기",
        "generate_route_recommendation": "경로 추천 생성",
        "match_route_with_llm": "경로 매칭",
        "summarize_answer": "답변 요약",
        "fetch_mcp_tool_result": "MCP 도구 결과 가져오기",
        "prepare_promote": "제품 홍보",
        "select_product_name": "제품 선택",
        "select_production_parameters": "제품 매개변수 선택",
    },
    LanguageEnum.fr_fr: {
        "thinking": "Réflexion",
        "analyze_context": "Analyser le contexte",
        "select_tool": "Sélectionner l'outil",
        "reflect": "Réfléchir",
        "execute_tool": "Exécuter l'outil",
        "fetch_weather_info": "Récupérer les informations météo",
        "fetch_calendar_info": "Récupérer les informations du calendrier",
        "search_user": "Rechercher un utilisateur",
        "send_message": "Envoyer un message",
        "prepare_answer": "Préparer la réponse",
        "select_best_case_introduction": "Sélectionner la meilleure présentation de cas",
        "select_best_production_function": "Sélectionner la meilleure fonction de production",
        "select_best_sales_strategy": "Sélectionner la meilleure stratégie de vente",
        "select_best_recommend_strategy": "Sélectionner la meilleure stratégie de recommandation",
        "fetch_production_info": "Récupérer les informations de production",
        "fetch_knowledge_doc": "Récupérer le document de connaissance",
        "visual_analysis": "Analyse visuelle",
        "fetch_route_info": "Récupérer les informations d'itinéraire",
        "generate_route_recommendation": "Générer une recommandation d'itinéraire",
        "match_route_with_llm": "Correspondre à l'itinéraire",
        "summarize_answer": "Résumer la réponse",
        "fetch_mcp_tool_result": "Récupérer le résultat de l'outil MCP",
        "prepare_promote": "Promouvoir le produit",
        "select_product_name": "Sélectionner le produit",
        "select_production_parameters": "Sélectionner les paramètres du produit",
    },
    LanguageEnum.de: {
        "thinking": "Denken",
        "analyze_context": "Kontext analysieren",
        "select_tool": "Werkzeug auswählen",
        "reflect": "Reflektieren",
        "execute_tool": "Werkzeug ausführen",
        "fetch_weather_info": "Wetterinformationen abrufen",
        "fetch_calendar_info": "Kalenderinformationen abrufen",
        "search_user": "Benutzer suchen",
        "send_message": "Nachricht senden",
        "prepare_answer": "Antwort vorbereiten",
        "select_best_case_introduction": "Beste Fallpräsentation auswählen",
        "select_best_production_function": "Beste Produktionsfunktion auswählen",
        "select_best_sales_strategy": "Beste Verkaufsstrategie auswählen",
        "select_best_recommend_strategy": "Beste Empfehlungsstrategie auswählen",
        "fetch_production_info": "Produktionsinformationen abrufen",
        "fetch_knowledge_doc": "Wissensdokument abrufen",
        "visual_analysis": "Visuelle Analyse",
        "fetch_route_info": "Routeninformationen abrufen",
        "generate_route_recommendation": "Routenempfehlung generieren",
        "match_route_with_llm": "Route abgleichen",
        "summarize_answer": "Antwort zusammenfassen",
        "fetch_mcp_tool_result": "MCP-Tool-Ergebnis abrufen",
        "prepare_promote": "Produkt bewerben",
        "select_product_name": "Produkt auswählen",
        "select_production_parameters": "Produktparameter auswählen",
    },
    LanguageEnum.es_es: {
        "thinking": "Pensando",
        "analyze_context": "Analizar contexto",
        "select_tool": "Seleccionar herramienta",
        "reflect": "Reflexionar",
        "execute_tool": "Ejecutar herramienta",
        "fetch_weather_info": "Obtener información meteorológica",
        "fetch_calendar_info": "Obtener información del calendario",
        "search_user": "Buscar usuario",
        "send_message": "Enviar mensaje",
        "prepare_answer": "Preparar respuesta",
        "select_best_case_introduction": "Seleccionar mejor presentación de caso",
        "select_best_production_function": "Seleccionar mejor función de producción",
        "select_best_sales_strategy": "Seleccionar mejor estrategia de ventas",
        "select_best_recommend_strategy": "Seleccionar mejor estrategia de recomendación",
        "fetch_production_info": "Obtener información de producción",
        "fetch_knowledge_doc": "Obtener documento de conocimiento",
        "visual_analysis": "Análisis visual",
        "fetch_route_info": "Obtener información de ruta",
        "generate_route_recommendation": "Generar recomendación de ruta",
        "match_route_with_llm": "Coincidir ruta",
        "summarize_answer": "Resumir respuesta",
        "fetch_mcp_tool_result": "Obtener resultado de herramienta MCP",
        "prepare_promote": "Promocionar el producto",
        "select_product_name": "Seleccionar producto",
        "select_production_parameters": "Seleccionar parámetros del producto",
    },
    LanguageEnum.it_it: {
        "thinking": "Pensando",
        "analyze_context": "Analizzare il contesto",
        "select_tool": "Selezionare strumento",
        "reflect": "Riflettere",
        "execute_tool": "Eseguire strumento",
        "fetch_weather_info": "Recuperare informazioni meteo",
        "fetch_calendar_info": "Recuperare informazioni calendario",
        "search_user": "Cercare utente",
        "send_message": "Inviare messaggio",
        "prepare_answer": "Preparare risposta",
        "select_best_case_introduction": "Selezionare migliore presentazione caso",
        "select_best_production_function": "Selezionare migliore funzione produzione",
        "select_best_sales_strategy": "Selezionare migliore strategia vendite",
        "select_best_recommend_strategy": "Selezionare migliore strategia raccomandazione",
        "fetch_production_info": "Recuperare informazioni produzione",
        "fetch_knowledge_doc": "Recuperare documento conoscenza",
        "visual_analysis": "Analisi visiva",
        "fetch_route_info": "Recuperare informazioni percorso",
        "generate_route_recommendation": "Generare raccomandazione percorso",
        "match_route_with_llm": "Abbinare percorso",
        "summarize_answer": "Riassumere risposta",
        "fetch_mcp_tool_result": "Recuperare risultato strumento MCP",
        "prepare_promote": "Promuovere il prodotto",
        "select_product_name": "Selezionare prodotto",
        "select_production_parameters": "Selezionare parametri del prodotto",
    },
    LanguageEnum.ru_ru: {
        "thinking": "Размышление",
        "analyze_context": "Анализ контекста",
        "select_tool": "Выбор инструмента",
        "reflect": "Рефлексия",
        "execute_tool": "Выполнение инструмента",
        "fetch_weather_info": "Получение информации о погоде",
        "fetch_calendar_info": "Получение информации календаря",
        "search_user": "Поиск пользователя",
        "send_message": "Отправка сообщения",
        "prepare_answer": "Подготовка ответа",
        "select_best_case_introduction": "Выбор лучшего представления случая",
        "select_best_production_function": "Выбор лучшей производственной функции",
        "select_best_sales_strategy": "Выбор лучшей стратегии продаж",
        "select_best_recommend_strategy": "Выбор лучшей стратегии рекомендаций",
        "fetch_production_info": "Получение информации о производстве",
        "fetch_knowledge_doc": "Получение документа знаний",
        "visual_analysis": "Визуальный анализ",
        "fetch_route_info": "Получение информации о маршруте",
        "generate_route_recommendation": "Генерация рекомендации маршрута",
        "match_route_with_llm": "Сопоставление маршрута",
        "summarize_answer": "Суммирование ответа",
        "fetch_mcp_tool_result": "Получение результата инструмента MCP",
        "prepare_promote": "Продвигать продукт",
        "select_product_name": "Выбрать продукт",
        "select_production_parameters": "Выбрать параметры продукта",
    },
    LanguageEnum.pt_br: {
        "thinking": "Pensando",
        "analyze_context": "Analisar contexto",
        "select_tool": "Selecionar ferramenta",
        "reflect": "Refletir",
        "execute_tool": "Executar ferramenta",
        "fetch_weather_info": "Buscar informações do tempo",
        "fetch_calendar_info": "Buscar informações do calendário",
        "search_user": "Procurar usuário",
        "send_message": "Enviar mensagem",
        "prepare_answer": "Preparar resposta",
        "select_best_case_introduction": "Selecionar melhor apresentação de caso",
        "select_best_production_function": "Selecionar melhor função de produção",
        "select_best_sales_strategy": "Selecionar melhor estratégia de vendas",
        "select_best_recommend_strategy": "Selecionar melhor estratégia de recomendação",
        "fetch_production_info": "Buscar informações de produção",
        "fetch_knowledge_doc": "Buscar documento de conhecimento",
        "visual_analysis": "Análise visual",
        "fetch_route_info": "Buscar informações de rota",
        "generate_route_recommendation": "Gerar recomendação de rota",
        "match_route_with_llm": "Combinar rota",
        "summarize_answer": "Resumir resposta",
        "fetch_mcp_tool_result": "Buscar resultado da ferramenta MCP",
        "prepare_promote": "Promover o produto",
        "select_product_name": "Selecionar produto",
        "select_production_parameters": "Selecionar parâmetros do produto",
    },
    LanguageEnum.nl_nl: {
        "thinking": "Nadenken",
        "analyze_context": "Context analyseren",
        "select_tool": "Tool selecteren",
        "reflect": "Reflecteren",
        "execute_tool": "Tool uitvoeren",
        "fetch_weather_info": "Weerinformatie ophalen",
        "fetch_calendar_info": "Kalenderinformatie ophalen",
        "search_user": "Gebruiker zoeken",
        "send_message": "Bericht verzenden",
        "prepare_answer": "Antwoord voorbereiden",
        "select_best_case_introduction": "Beste casuspresentatie selecteren",
        "select_best_production_function": "Beste productiefunctie selecteren",
        "select_best_sales_strategy": "Beste verkoopstrategie selecteren",
        "select_best_recommend_strategy": "Beste aanbevelingsstrategie selecteren",
        "fetch_production_info": "Productie-informatie ophalen",
        "fetch_knowledge_doc": "Kennisdocument ophalen",
        "visual_analysis": "Visuele analyse",
        "fetch_route_info": "Route-informatie ophalen",
        "generate_route_recommendation": "Route-aanbeveling genereren",
        "match_route_with_llm": "Route matchen",
        "summarize_answer": "Antwoord samenvatten",
        "fetch_mcp_tool_result": "MCP-tool resultaat ophalen",
        "prepare_promote": "Product promoten",
        "select_product_name": "Product selecteren",
        "select_production_parameters": "Productparameters selecteren",
    },
    LanguageEnum.sv_se: {
        "thinking": "Tänker",
        "analyze_context": "Analysera kontext",
        "select_tool": "Välj verktyg",
        "reflect": "Reflektera",
        "execute_tool": "Utför verktyg",
        "fetch_weather_info": "Hämta väderinformation",
        "fetch_calendar_info": "Hämta kalenderinformation",
        "search_user": "Sök användare",
        "send_message": "Skicka meddelande",
        "prepare_answer": "Förbered svar",
        "select_best_case_introduction": "Välj bästa fallpresentation",
        "select_best_production_function": "Välj bästa produktionsfunktion",
        "select_best_sales_strategy": "Välj bästa försäljningsstrategi",
        "select_best_recommend_strategy": "Välj bästa rekommendationsstrategi",
        "fetch_production_info": "Hämta produktionsinformation",
        "fetch_knowledge_doc": "Hämta kunskapsdokument",
        "visual_analysis": "Visuell analys",
        "fetch_route_info": "Hämta ruttinformation",
        "generate_route_recommendation": "Generera ruttrekommendation",
        "match_route_with_llm": "Matcha rutt",
        "summarize_answer": "Sammanfatta svar",
        "fetch_mcp_tool_result": "Hämta MCP-verktygsresultat",
        "prepare_promote": "Marknadsför produkten",
        "select_product_name": "Välj produkt",
        "select_production_parameters": "Välj produktparametrar",
    },
    LanguageEnum.da_dk: {
        "thinking": "Tænker",
        "analyze_context": "Analyser kontekst",
        "select_tool": "Vælg værktøj",
        "reflect": "Reflektere",
        "execute_tool": "Udfør værktøj",
        "fetch_weather_info": "Hent vejrinformation",
        "fetch_calendar_info": "Hent kalenderinformation",
        "search_user": "Søg bruger",
        "send_message": "Send besked",
        "prepare_answer": "Forbered svar",
        "select_best_case_introduction": "Vælg bedste sagspræsentation",
        "select_best_production_function": "Vælg bedste produktionsfunktion",
        "select_best_sales_strategy": "Vælg bedste salgsstrategi",
        "select_best_recommend_strategy": "Vælg bedste anbefalingsstrategi",
        "fetch_production_info": "Hent produktionsinformation",
        "fetch_knowledge_doc": "Hent vidensdokument",
        "visual_analysis": "Visuel analyse",
        "fetch_route_info": "Hent ruteinformation",
        "generate_route_recommendation": "Generer ruteanbefaling",
        "match_route_with_llm": "Match rute",
        "summarize_answer": "Opsummer svar",
        "fetch_mcp_tool_result": "Hent MCP-værktøjsresultat",
        "prepare_promote": "Promover produktet",
        "select_product_name": "Vælg produkt",
        "select_production_parameters": "Vælg produktparametre",
    },
    LanguageEnum.nb_no: {
        "thinking": "Tenker",
        "analyze_context": "Analyser kontekst",
        "select_tool": "Velg verktøy",
        "reflect": "Reflektere",
        "execute_tool": "Utfør verktøy",
        "fetch_weather_info": "Hent værinformasjon",
        "fetch_calendar_info": "Hent kalenderinformasjon",
        "search_user": "Søk bruker",
        "send_message": "Send melding",
        "prepare_answer": "Forbered svar",
        "select_best_case_introduction": "Velg beste casepresentasjon",
        "select_best_production_function": "Velg beste produksjonsfunksjon",
        "select_best_sales_strategy": "Velg beste salgsstrategi",
        "select_best_recommend_strategy": "Velg beste anbefalingsstrategi",
        "fetch_production_info": "Hent produksjonsinformasjon",
        "fetch_knowledge_doc": "Hent kunnskapsdokument",
        "visual_analysis": "Visuell analyse",
        "fetch_route_info": "Hent ruteinformasjon",
        "generate_route_recommendation": "Generer ruteanbefaling",
        "match_route_with_llm": "Match rute",
        "summarize_answer": "Sammendrag svar",
        "fetch_mcp_tool_result": "Hent MCP-verktøyresultat",
        "prepare_promote": "Promoter produktet",
        "select_product_name": "Velg produkt",
        "select_production_parameters": "Velg produktparametre",
    },
    LanguageEnum.fi_fi: {
        "thinking": "Ajattelee",
        "analyze_context": "Analysoi konteksti",
        "select_tool": "Valitse työkalu",
        "reflect": "Reflektoi",
        "execute_tool": "Suorita työkalu",
        "fetch_weather_info": "Hae säätiedot",
        "fetch_calendar_info": "Hae kalenteritiedot",
        "search_user": "Etsi käyttäjä",
        "send_message": "Lähetä viesti",
        "prepare_answer": "Valmistele vastaus",
        "select_best_case_introduction": "Valitse paras tapausesittely",
        "select_best_production_function": "Valitse paras tuotantofunktio",
        "select_best_sales_strategy": "Valitse paras myyntistrategia",
        "select_best_recommend_strategy": "Valitse paras suositusstrategia",
        "fetch_production_info": "Hae tuotantotiedot",
        "fetch_knowledge_doc": "Hae tietodokumentti",
        "visual_analysis": "Visuaalinen analyysi",
        "fetch_route_info": "Hae reittitieto",
        "generate_route_recommendation": "Luo reittisuositus",
        "match_route_with_llm": "Täsmää reitti",
        "summarize_answer": "Tiivistä vastaus",
        "fetch_mcp_tool_result": "Hae MCP-työkalutulos",
        "prepare_promote": "Mainosta tuotetta",
        "select_product_name": "Valitse tuote",
        "select_production_parameters": "Valitse tuotteen parametrit",
    },
    LanguageEnum.pl_pl: {
        "thinking": "Myślenie",
        "analyze_context": "Analizuj kontekst",
        "select_tool": "Wybierz narzędzie",
        "reflect": "Refleksja",
        "execute_tool": "Wykonaj narzędzie",
        "fetch_weather_info": "Pobierz informacje pogodowe",
        "fetch_calendar_info": "Pobierz informacje kalendarzowe",
        "search_user": "Szukaj użytkownika",
        "send_message": "Wyślij wiadomość",
        "prepare_answer": "Przygotuj odpowiedź",
        "select_best_case_introduction": "Wybierz najlepszą prezentację przypadku",
        "select_best_production_function": "Wybierz najlepszą funkcję produkcji",
        "select_best_sales_strategy": "Wybierz najlepszą strategię sprzedaży",
        "select_best_recommend_strategy": "Wybierz najlepszą strategię rekomendacji",
        "fetch_production_info": "Pobierz informacje o produkcji",
        "fetch_knowledge_doc": "Pobierz dokument wiedzy",
        "visual_analysis": "Analiza wizualna",
        "fetch_route_info": "Pobierz informacje o trasie",
        "generate_route_recommendation": "Generuj rekomendację trasy",
        "match_route_with_llm": "Dopasuj trasę",
        "summarize_answer": "Podsumuj odpowiedź",
        "fetch_mcp_tool_result": "Pobierz wynik narzędzia MCP",
        "prepare_promote": "Promuj produkt",
        "select_product_name": "Wybierz produkt",
        "select_production_parameters": "Wybierz parametry produktu",
    },
    LanguageEnum.cs_cz: {
        "thinking": "Přemýšlení",
        "analyze_context": "Analyzovat kontext",
        "select_tool": "Vybrat nástroj",
        "reflect": "Reflektovat",
        "execute_tool": "Spustit nástroj",
        "fetch_weather_info": "Získat informace o počasí",
        "fetch_calendar_info": "Získat informace kalendáře",
        "search_user": "Hledat uživatele",
        "send_message": "Odeslat zprávu",
        "prepare_answer": "Připravit odpověď",
        "select_best_case_introduction": "Vybrat nejlepší prezentaci případu",
        "select_best_production_function": "Vybrat nejlepší produkční funkci",
        "select_best_sales_strategy": "Vybrat nejlepší prodejní strategii",
        "select_best_recommend_strategy": "Vybrat nejlepší doporučovací strategii",
        "fetch_production_info": "Získat informace o výrobě",
        "fetch_knowledge_doc": "Získat dokument znalostí",
        "visual_analysis": "Vizuální analýza",
        "fetch_route_info": "Získat informace o trase",
        "generate_route_recommendation": "Generovat doporučení trasy",
        "match_route_with_llm": "Spárovat trasu",
        "summarize_answer": "Shrnout odpověď",
        "fetch_mcp_tool_result": "Získat výsledek nástroje MCP",
        "prepare_promote": "Propagovat produkt",
        "select_product_name": "Vybrat produkt",
        "select_production_parameters": "Vybrat parametry produktu",
    },
    LanguageEnum.hu_hu: {
        "thinking": "Gondolkodás",
        "analyze_context": "Kontextus elemzése",
        "select_tool": "Eszköz kiválasztása",
        "reflect": "Reflexió",
        "execute_tool": "Eszköz végrehajtása",
        "fetch_weather_info": "Időjárási információk lekérése",
        "fetch_calendar_info": "Naptári információk lekérése",
        "search_user": "Felhasználó keresése",
        "send_message": "Üzenet küldése",
        "prepare_answer": "Válasz előkészítése",
        "select_best_case_introduction": "Legjobb eset bemutatás kiválasztása",
        "select_best_production_function": "Legjobb termelési funkció kiválasztása",
        "select_best_sales_strategy": "Legjobb értékesítési stratégia kiválasztása",
        "select_best_recommend_strategy": "Legjobb ajánlási stratégia kiválasztása",
        "fetch_production_info": "Termelési információk lekérése",
        "fetch_knowledge_doc": "Tudás dokumentum lekérése",
        "visual_analysis": "Vizuális elemzés",
        "fetch_route_info": "Útvonal információk lekérése",
        "generate_route_recommendation": "Útvonal ajánlás generálása",
        "match_route_with_llm": "Útvonal egyeztetése",
        "summarize_answer": "Válasz összefoglalása",
        "fetch_mcp_tool_result": "MCP eszköz eredmény lekérése",
        "prepare_promote": "Termék népszerűsítése",
        "select_product_name": "Termék kiválasztása",
        "select_production_parameters": "Termék paramétereinek kiválasztása",
    },
    LanguageEnum.sk_sk: {
        "thinking": "Premýšľanie",
        "analyze_context": "Analyzovať kontext",
        "select_tool": "Vybrať nástroj",
        "reflect": "Reflektovať",
        "execute_tool": "Spustiť nástroj",
        "fetch_weather_info": "Získať informácie o počasí",
        "fetch_calendar_info": "Získať informácie kalendára",
        "search_user": "Hľadať používateľa",
        "send_message": "Odoslať správu",
        "prepare_answer": "Pripraviť odpoveď",
        "select_best_case_introduction": "Vybrať najlepšiu prezentáciu prípadu",
        "select_best_production_function": "Vybrať najlepšiu produkčnú funkciu",
        "select_best_sales_strategy": "Vybrať najlepšiu predajnú stratégiu",
        "select_best_recommend_strategy": "Vybrať najlepšiu odporúčaciu stratégiu",
        "fetch_production_info": "Získať informácie o výrobe",
        "fetch_knowledge_doc": "Získať dokument znalostí",
        "visual_analysis": "Vizuálna analýza",
        "fetch_route_info": "Získať informácie o trase",
        "generate_route_recommendation": "Generovať odporúčanie trasy",
        "match_route_with_llm": "Spárovať trasu",
        "summarize_answer": "Zhrnutie odpovede",
        "fetch_mcp_tool_result": "Získať výsledok nástroja MCP",
        "prepare_promote": "Propagovať produkt",
        "select_product_name": "Vybrať produkt",
        "select_production_parameters": "Vybrať parametre produktu",
    },
    LanguageEnum.ro_ro: {
        "thinking": "Gândesc",
        "analyze_context": "Analizez contextul",
        "select_tool": "Selectez instrumentul",
        "reflect": "Reflectez",
        "execute_tool": "Execut instrumentul",
        "fetch_weather_info": "Preiau informații meteo",
        "fetch_calendar_info": "Preiau informații calendar",
        "search_user": "Caut utilizator",
        "send_message": "Trimit mesaj",
        "prepare_answer": "Pregătesc răspuns",
        "select_best_case_introduction": "Selectez cea mai bună prezentare de caz",
        "select_best_production_function": "Selectez cea mai bună funcție de producție",
        "select_best_sales_strategy": "Selectez cea mai bună strategie de vânzări",
        "select_best_recommend_strategy": "Selectez cea mai bună strategie de recomandare",
        "fetch_production_info": "Preiau informații despre producție",
        "fetch_knowledge_doc": "Preiau document de cunoștințe",
        "visual_analysis": "Analiza vizuală",
        "fetch_route_info": "Preiau informații despre rută",
        "generate_route_recommendation": "Generez recomandare de rută",
        "match_route_with_llm": "Potrivesc ruta",
        "summarize_answer": "Sumarizez răspunsul",
        "fetch_mcp_tool_result": "Preiau rezultatul instrumentului MCP",
        "prepare_promote": "Promovează produsul",
        "select_product_name": "Selectează produsul",
        "select_production_parameters": "Selectează parametrii produsului",
    },
    LanguageEnum.el_gr: {
        "thinking": "Σκέπτομαι",
        "analyze_context": "Αναλύω το περιβάλλον",
        "select_tool": "Επιλέγω εργαλείο",
        "reflect": "Αναστοχάζομαι",
        "execute_tool": "Εκτελώ εργαλείο",
        "fetch_weather_info": "Λαμβάνω πληροφορίες καιρού",
        "fetch_calendar_info": "Λαμβάνω πληροφορίες ημερολογίου",
        "search_user": "Αναζητώ χρήστη",
        "send_message": "Στέλνω μήνυμα",
        "prepare_answer": "Προετοιμάζω απάντηση",
        "select_best_case_introduction": "Επιλέγω καλύτερη παρουσίαση περίπτωσης",
        "select_best_production_function": "Επιλέγω καλύτερη λειτουργία παραγωγής",
        "select_best_sales_strategy": "Επιλέγω καλύτερη στρατηγική πωλήσεων",
        "select_best_recommend_strategy": "Επιλέγω καλύτερη στρατηγική συστάσεων",
        "fetch_production_info": "Λαμβάνω πληροφορίες παραγωγής",
        "fetch_knowledge_doc": "Λαμβάνω έγγραφο γνώσης",
        "visual_analysis": "Οπτική ανάλυση",
        "fetch_route_info": "Λαμβάνω πληροφορίες διαδρομής",
        "generate_route_recommendation": "Δημιουργώ σύσταση διαδρομής",
        "match_route_with_llm": "Ταιριάζω διαδρομή",
        "summarize_answer": "Συνοψίζω απάντηση",
        "fetch_mcp_tool_result": "Λαμβάνω αποτέλεσμα εργαλείου MCP",
        "prepare_promote": "Προώθηση προϊόντος",
        "select_product_name": "Επιλογή προϊόντος",
        "select_production_parameters": "Επιλογή παραμέτρων προϊόντος",
    },
    LanguageEnum.tr_tr: {
        "thinking": "Düşünüyorum",
        "analyze_context": "Bağlamı analiz ediyorum",
        "select_tool": "Araç seçiyorum",
        "reflect": "Yansıtıyorum",
        "execute_tool": "Araç çalıştırıyorum",
        "fetch_weather_info": "Hava durumu bilgisi alıyorum",
        "fetch_calendar_info": "Takvim bilgisi alıyorum",
        "search_user": "Kullanıcı arıyorum",
        "send_message": "Mesaj gönderiyorum",
        "prepare_answer": "Cevap hazırlıyorum",
        "select_best_case_introduction": "En iyi durum sunumunu seçiyorum",
        "select_best_production_function": "En iyi üretim fonksiyonunu seçiyorum",
        "select_best_sales_strategy": "En iyi satış stratejisini seçiyorum",
        "select_best_recommend_strategy": "En iyi tavsiye stratejisini seçiyorum",
        "fetch_production_info": "Üretim bilgisi alıyorum",
        "fetch_knowledge_doc": "Bilgi dokümanı alıyorum",
        "visual_analysis": "Görsel analiz",
        "fetch_route_info": "Rota bilgisi alıyorum",
        "generate_route_recommendation": "Rota tavsiyesi üretiyorum",
        "match_route_with_llm": "Rotayı eşleştiriyorum",
        "summarize_answer": "Cevabı özetliyorum",
        "fetch_mcp_tool_result": "MCP araç sonucunu alıyorum",
        "prepare_promote": "Ürünü tanıt",
        "select_product_name": "Ürün seç",
        "select_production_parameters": "Ürün parametrelerini seç",
    },
    LanguageEnum.th_th: {
        "thinking": "กำลังคิด",
        "analyze_context": "วิเคราะห์บริบท",
        "select_tool": "เลือกเครื่องมือ",
        "reflect": "สะท้อน",
        "execute_tool": "ดำเนินการเครื่องมือ",
        "fetch_weather_info": "ดึงข้อมูลสภาพอากาศ",
        "fetch_calendar_info": "ดึงข้อมูลปฏิทิน",
        "search_user": "ค้นหาผู้ใช้",
        "send_message": "ส่งข้อความ",
        "prepare_answer": "เตรียมคำตอบ",
        "select_best_case_introduction": "เลือกการนำเสนอกรณีที่ดีที่สุด",
        "select_best_production_function": "เลือกฟังก์ชันการผลิตที่ดีที่สุด",
        "select_best_sales_strategy": "เลือกกลยุทธ์การขายที่ดีที่สุด",
        "select_best_recommend_strategy": "เลือกกลยุทธ์การแนะนำที่ดีที่สุด",
        "fetch_production_info": "ดึงข้อมูลการผลิต",
        "fetch_knowledge_doc": "ดึงเอกสารความรู้",
        "visual_analysis": "การวิเคราะห์ภาพ",
        "fetch_route_info": "ดึงข้อมูลเส้นทาง",
        "generate_route_recommendation": "สร้างคำแนะนำเส้นทาง",
        "match_route_with_llm": "จับคู่เส้นทาง",
        "summarize_answer": "สรุปคำตอบ",
        "fetch_mcp_tool_result": "ดึงผลลัพธ์เครื่องมือ MCP",
        "prepare_promote": "โปรโมทสินค้า",
        "select_product_name": "เลือกสินค้า",
        "select_production_parameters": "เลือกพารามิเตอร์สินค้า",
    },
    LanguageEnum.vi_vn: {
        "thinking": "Đang suy nghĩ",
        "analyze_context": "Phân tích ngữ cảnh",
        "select_tool": "Chọn công cụ",
        "reflect": "Phản ánh",
        "execute_tool": "Thực hiện công cụ",
        "fetch_weather_info": "Lấy thông tin thời tiết",
        "fetch_calendar_info": "Lấy thông tin lịch",
        "search_user": "Tìm kiếm người dùng",
        "send_message": "Gửi tin nhắn",
        "prepare_answer": "Chuẩn bị câu trả lời",
        "select_best_case_introduction": "Chọn bài giới thiệu trường hợp tốt nhất",
        "select_best_production_function": "Chọn chức năng sản xuất tốt nhất",
        "select_best_sales_strategy": "Chọn chiến lược bán hàng tốt nhất",
        "select_best_recommend_strategy": "Chọn chiến lược đề xuất tốt nhất",
        "fetch_production_info": "Lấy thông tin sản xuất",
        "fetch_knowledge_doc": "Lấy tài liệu kiến thức",
        "visual_analysis": "Phân tích hình ảnh",
        "fetch_route_info": "Lấy thông tin tuyến đường",
        "generate_route_recommendation": "Tạo đề xuất tuyến đường",
        "match_route_with_llm": "Khớp tuyến đường",
        "summarize_answer": "Tóm tắt câu trả lời",
        "fetch_mcp_tool_result": "Lấy kết quả công cụ MCP",
        "prepare_promote": "Quảng bá sản phẩm",
        "select_product_name": "Chọn sản phẩm",
        "select_production_parameters": "Chọn tham số sản phẩm",
    },
    LanguageEnum.id_id: {
        "thinking": "Berpikir",
        "analyze_context": "Menganalisis konteks",
        "select_tool": "Memilih alat",
        "reflect": "Merefleksikan",
        "execute_tool": "Menjalankan alat",
        "fetch_weather_info": "Mengambil informasi cuaca",
        "fetch_calendar_info": "Mengambil informasi kalender",
        "search_user": "Mencari pengguna",
        "send_message": "Mengirim pesan",
        "prepare_answer": "Menyiapkan jawaban",
        "select_best_case_introduction": "Memilih presentasi kasus terbaik",
        "select_best_production_function": "Memilih fungsi produksi terbaik",
        "select_best_sales_strategy": "Memilih strategi penjualan terbaik",
        "select_best_recommend_strategy": "Memilih strategi rekomendasi terbaik",
        "fetch_production_info": "Mengambil informasi produksi",
        "fetch_knowledge_doc": "Mengambil dokumen pengetahuan",
        "visual_analysis": "Analisis visual",
        "fetch_route_info": "Mengambil informasi rute",
        "generate_route_recommendation": "Menghasilkan rekomendasi rute",
        "match_route_with_llm": "Mencocokkan rute",
        "summarize_answer": "Merangkum jawaban",
        "fetch_mcp_tool_result": "Mengambil hasil alat MCP",
        "prepare_promote": "Promosikan produk",
        "select_product_name": "Pilih produk",
        "select_production_parameters": "Pilih parameter produk",
    },
    LanguageEnum.ms_my: {
        "thinking": "Berfikir",
        "analyze_context": "Menganalisis konteks",
        "select_tool": "Memilih alat",
        "reflect": "Merenung",
        "execute_tool": "Melaksanakan alat",
        "fetch_weather_info": "Mengambil maklumat cuaca",
        "fetch_calendar_info": "Mengambil maklumat kalendar",
        "search_user": "Mencari pengguna",
        "send_message": "Menghantar mesej",
        "prepare_answer": "Menyediakan jawapan",
        "select_best_case_introduction": "Memilih persembahan kes terbaik",
        "select_best_production_function": "Memilih fungsi pengeluaran terbaik",
        "select_best_sales_strategy": "Memilih strategi jualan terbaik",
        "select_best_recommend_strategy": "Memilih strategi cadangan terbaik",
        "fetch_production_info": "Mengambil maklumat pengeluaran",
        "fetch_knowledge_doc": "Mengambil dokumen pengetahuan",
        "visual_analysis": "Analisis visual",
        "fetch_route_info": "Mengambil maklumat laluan",
        "generate_route_recommendation": "Menjana cadangan laluan",
        "match_route_with_llm": "Memadankan laluan",
        "summarize_answer": "Merumuskan jawapan",
        "fetch_mcp_tool_result": "Mengambil hasil alat MCP",
        "prepare_promote": "Promosikan produk",
        "select_product_name": "Pilih produk",
        "select_production_parameters": "Pilih parameter produk",
    },
    LanguageEnum.fil_ph: {
        "thinking": "Nag-iisip",
        "analyze_context": "Susuriin ang konteksto",
        "select_tool": "Pumili ng kasangkapan",
        "reflect": "Mag-reflect",
        "execute_tool": "Isagawa ang kasangkapan",
        "fetch_weather_info": "Kunin ang impormasyon ng panahon",
        "fetch_calendar_info": "Kunin ang impormasyon ng kalendaryo",
        "search_user": "Maghanap ng user",
        "send_message": "Magpadala ng mensahe",
        "prepare_answer": "Ihanda ang sagot",
        "select_best_case_introduction": "Pumili ng pinakamahusay na pagpapakilala ng kaso",
        "select_best_production_function": "Pumili ng pinakamahusay na function ng produksyon",
        "select_best_sales_strategy": "Pumili ng pinakamahusay na diskarte sa pagbebenta",
        "select_best_recommend_strategy": "Pumili ng pinakamahusay na diskarte sa pag-recommend",
        "fetch_production_info": "Kunin ang impormasyon ng produksyon",
        "fetch_knowledge_doc": "Kunin ang dokumento ng kaalaman",
        "visual_analysis": "Visual na pagsusuri",
        "fetch_route_info": "Kunin ang impormasyon ng ruta",
        "generate_route_recommendation": "Lumikha ng rekomendasyon ng ruta",
        "match_route_with_llm": "Itugma ang ruta",
        "summarize_answer": "Buuin ang sagot",
        "fetch_mcp_tool_result": "Kunin ang resulta ng MCP tool",
        "prepare_promote": "I-promote ang produkto",
        "select_product_name": "Pumili ng produkto",
        "select_production_parameters": "Pumili ng mga parameter ng produkto",
    },
    LanguageEnum.pt_pt: {
        "thinking": "A pensar",
        "analyze_context": "Analisar contexto",
        "select_tool": "Selecionar ferramenta",
        "reflect": "Reflectir",
        "execute_tool": "Executar ferramenta",
        "fetch_weather_info": "Obter informações meteorológicas",
        "fetch_calendar_info": "Obter informações do calendário",
        "search_user": "Procurar utilizador",
        "send_message": "Enviar mensagem",
        "prepare_answer": "Preparar resposta",
        "select_best_case_introduction": "Seleccionar melhor apresentação de caso",
        "select_best_production_function": "Seleccionar melhor função de produção",
        "select_best_sales_strategy": "Seleccionar melhor estratégia de vendas",
        "select_best_recommend_strategy": "Seleccionar melhor estratégia de recomendação",
        "fetch_production_info": "Obter informações de produção",
        "fetch_knowledge_doc": "Obter documento de conhecimento",
        "visual_analysis": "Análise visual",
        "fetch_route_info": "Obter informações da rota",
        "generate_route_recommendation": "Gerar recomendação de rota",
        "match_route_with_llm": "Combinar rota",
        "summarize_answer": "Resumir resposta",
        "fetch_mcp_tool_result": "Obter resultado da ferramenta MCP",
        "prepare_promote": "Promover produto",
        "select_product_name": "Selecionar produto",
        "select_production_parameters": "Selecionar parâmetros do produto",
    },
    LanguageEnum.ar_sa: {
        "thinking": "جاري التفكير",
        "analyze_context": "تحليل السياق",
        "select_tool": "اختيار الأداة",
        "reflect": "تأمل",
        "execute_tool": "تنفيذ الأداة",
        "fetch_weather_info": "جلب معلومات الطقس",
        "fetch_calendar_info": "جلب معلومات التقويم",
        "search_user": "البحث عن المستخدم",
        "send_message": "إرسال رسالة",
        "prepare_answer": "تحضير الإجابة",
        "select_best_case_introduction": "اختيار أفضل عرض للحالة",
        "select_best_production_function": "اختيار أفضل وظيفة إنتاج",
        "select_best_sales_strategy": "اختيار أفضل استراتيجية مبيعات",
        "select_best_recommend_strategy": "اختيار أفضل استراتيجية توصية",
        "fetch_production_info": "جلب معلومات الإنتاج",
        "fetch_knowledge_doc": "جلب وثيقة المعرفة",
        "visual_analysis": "التحليل البصري",
        "fetch_route_info": "جلب معلومات المسار",
        "generate_route_recommendation": "إنشاء توصية المسار",
        "match_route_with_llm": "مطابقة المسار",
        "summarize_answer": "تلخيص الإجابة",
        "fetch_mcp_tool_result": "جلب نتيجة أداة MCP",
        "prepare_promote": "الترويج للمنتج",
        "select_product_name": "اختيار المنتج",
        "select_production_parameters": "اختيار معايير المنتج",
    },
}


class RunStep(BaseModel):
    step_name: str
    step_result: Optional[dict] = None
    status: Literal["running", "failed", "succeeded"] = "running"
    step_scope: Literal["plan", "action_executor"] = "plan"
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000))
    error_msg: Optional[str] = None
    is_start: bool = False


def get_step_display_name(language: str, step_name: str) -> Optional[str]:
    if language not in Multilingual_Step_Display_Mapping:
        language = LanguageEnum.en

    # Define prefix for languages that need it
    prefix_languages = {
        LanguageEnum.zh: "正在",
        LanguageEnum.zh_tw: "正在",
        LanguageEnum.zh_gd: "正在",
        LanguageEnum.en: "Running ",
        LanguageEnum.ja_jp: "実行中",
        LanguageEnum.ko_kr: "진행 중",
        LanguageEnum.th_th: "กำลัง",
        LanguageEnum.vi_vn: "Đang",
        LanguageEnum.fr_fr: "En cours de ",
        LanguageEnum.de: "Wird gerade ",
        LanguageEnum.es_es: "Ejecutando ",
        LanguageEnum.it_it: "In corso di ",
        LanguageEnum.ru_ru: "Выполняется ",
        LanguageEnum.pt_br: "Executando ",
        LanguageEnum.pt_pt: "A executar ",
        LanguageEnum.nl_nl: "Bezig met ",
        LanguageEnum.pl_pl: "Wykonywanie ",
        LanguageEnum.cs_cz: "Probíhá ",
        LanguageEnum.hu_hu: "Végrehajtás ",
        LanguageEnum.sk_sk: "Vykonáva sa ",
        LanguageEnum.ro_ro: "Se execută ",
        LanguageEnum.tr_tr: "Şu anda ",
        LanguageEnum.el_gr: "Εκτελείται ",
        LanguageEnum.da_dk: "Udfører ",
        LanguageEnum.sv_se: "Utför ",
        LanguageEnum.nb_no: "Utfører ",
        LanguageEnum.fi_fi: "Suorittaa ",
        LanguageEnum.id_id: "Sedang ",
        LanguageEnum.ms_my: "Sedang ",
        LanguageEnum.fil_ph: "Ginagawa ang ",
        LanguageEnum.ar_sa: "جاري ",
    }

    prefix = prefix_languages.get(language, "")
    display_name = Multilingual_Step_Display_Mapping[language].get(step_name, step_name)
    return f"{prefix}{display_name}..."


class AppConfig(BaseModel):
    enable_mcp: bool = True  # 是否启用MCP
    enable_action_intervention: bool = True  # 是否启用动作干预
    enable_action_command: bool = True  # 是否启用快指令
