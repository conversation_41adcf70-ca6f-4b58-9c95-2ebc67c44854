from enum import Enum

from lingua import Language


ASSISTANT_REPLY = [
    # "稍等一下！",
    # "让我理一理思路。",
    # "我在琢磨呢。",
    # "包在我身上！",
    # "我正在想。",
    # "我在思考。",
    # "我在想想。",
    # "我在考虑。",
    # "我在思索。",
    # "哎呀，我在想。",
    # "好",
    # "OK",
    # "嗯嗯",
    # "哦",
    # "收到",
    "稍等",
    "请稍后",
    "思考中",
    "我想想",
    "思考中",
    "规划中",
    "开始执行啦",
    "您好，很开心为您服务",
    "，你好，有问题请问我吧",
]
ASSISTANT_ROLE = "<Robot>"
USER_ROLE = "<User>"

CLARIFY_ACTION_NAME = "SAY_FOR_CLARIFICATION"
KNOWLEDGE_QA_ACTION_NAME = "ANSWER_KNOWLEDGE_QUESTION"
MCP_PREFIX = "mcp_"
REPORT_PREFIX = "app_"
BUILTIN_PREFIX = "system_"
OTHER_PREFIX = "other_"

RECOMMEND_ACTIONS = {
    "orion.agent.action.CRUISE": "让我带您四处转转吧,我们一起探索这里的环境!",
    "orion.agent.action.NAVIGATE_START": "需要我带路吗?告诉我您想去哪里,我可以为您导航。",
    "orion.agent.action.OUTDOOR_NAVIGATE_START": "想要外出走走吗?我可以为您规划去香山的路线吧。",
    "orion.agent.action.TURN_DIRECTION": "需要我转向吗?只要告诉我方向,我就能调整姿态。",
    "orion.agent.action.HEAD_NOD": "我可以点头表示同意或问候,要看看吗?",
    "orion.agent.action.START_DANCE": "心情不错?我们来跳支舞吧,我可以唱歌跳舞哦!",
    "orion.agent.action.REGISTER": "您是新朋友吗?我们来做个简单的注册,这样以后见面我就能认出您了。",
    "orion.agent.action.MOVE_DIRECTION": "需要我移动吗?告诉我前进还是后退,我就能按您的指示行动。",
    "orion.agent.action.WEATHER_GET": "想知道天气如何吗?我可以为您查询最新的天气预报。",
    "orion.agent.action.CALENDAR": "想知道今天是什么日子吗?我可以为您查询日历。",
    "orion.agent.action.CONFIGURE_WELCOME_MESSAGE": "想要自定义欢迎语吗?我们一起来设置一个独特的问候吧!",
    "orion.agent.action.GENERATE_MESSAGE": "要我说声欢迎或再见吗?我有专门为您准备的问候语。",
    # "orion.agent.action.SEND_MESSAGE": "需要传递信息吗?我可以帮您给您的同事发送消息。",
    "orion.agent.action.RECOMMEND": "不知道做什么好?让我为您推荐一些景点、KTV、酒吧、美食餐厅吧",
    "orion.agent.action.FACE_RECOGNITION": "好奇我能不能认出您吗?让我看看您的脸,我试试能不能认出您来。",
    # "orion.agent.action.CHANGE_FACE": "想看看我的其他表情吗?我可以变换不同的脸部表情哦。",
    # "orion.agent.action.COZE_GENERATE_IMAGE": "需要一些创意图片吗?告诉我您的想法,我可以为您生成独特的图像。",
    # "orion.agent.action.COZE_TALK_TO_Raiden_Shogun": "想和雷电将军聊聊天吗?我可以为您连线她。", 不知道为什么COZE失败
    # "orion.agent.action.COZE_SEARCH_IMAGE": "在找什么特别的图片吗?告诉我您想看的,我来帮您搜索。",
    # "orion.agent.action.CLICK": "看到屏幕上有什么想点击的吗?告诉我,我可以帮您操作。",
}

ROBOT_REDUNDANT_WORDS = [
    "你好",
    "。请问是否开始执行？",
    "你好，有什么可以帮助你的么？",
    "嗨 ，有什么需要帮忙吗",
    "，你好，有问题请问我吧",
    "，下午好，见到你很高兴",
    "嗨，这位贵宾，我是智能服务机器人，你可以问我：你都会干什么？",
    "嗨，这位贵宾，我是智能服务机器人，你可以点击屏幕或直接语音来问我问题哦，例如你可以问我：你都会干什么？",
    "你现在看到的就是我的全部功能啦，你可以点击屏幕或直接语音来问我问题，例如你可以问我：你都会干什么？",
]

USER_REDUNDANT_WORDS = ["确认执行", "取消执行"]


class SynthesizeType(Enum):
    USER_QUERY = "user_query"
    RECOMMEND = "recommend"
    ACTION = "action"
    EVENT = "event"


class Area:
    overseas = "oversea"
    domestic = "domestic"
    all = "all"


class LanguageEnum:
    # 标准语言码
    zh = "zh_CN"  # 中文
    zh_tw = "zh_TW"  # 繁中
    zh_gd = "zh_GD"  # 繁中香港（粤语）
    en = "en_US"  # 英语
    ja_jp = "ja_JP"  # 日语
    th_th = "th_TH"  # 泰语
    de = "de_DE"  # 德语
    es_es = "es_ES"  # 西班牙语
    ko_kr = "ko_KR"  # 韩语
    da_dk = "da_DK"  # 丹麦语
    sv_se = "sv_SE"  # 瑞典语
    fi_fi = "fi_FI"  # 芬兰语
    nb_no = "nb_NO"  # 挪威语
    fr_fr = "fr_FR"  # 法语
    nl_nl = "nl_NL"  # 荷兰语
    ru_ru = "ru_RU"  # 俄语
    pl_pl = "pl_PL"  # 波兰语
    pt_pt = "pt_PT"  # 葡萄牙语
    it_it = "it_IT"  # 意大利语
    ro_ro = "ro_RO"  # 罗马尼亚语
    ms_my = "ms_MY"  # 马来语
    vi_vn = "vi_VN"  # 越南语
    id_id = "id_ID"  # 印尼语
    fil_ph = "fil_PH"  # 菲律宾语
    cs_cz = "cs_CZ"  # 捷克语
    el_gr = "el_GR"  # 希腊语
    pt_br = "pt_BR"  # 巴西葡萄牙语
    hu_hu = "hu_HU"  # 匈牙利语
    tr_tr = "tr_TR"  # 土耳其语
    sk_sk = "sk_SK"  # 斯洛伐克语
    ar_sa = "ar_SA"  # 阿拉伯语


# 语言对照表（标准语言码 -> 原生语言名称）
language_dict_en = {
    LanguageEnum.zh: "简体中文",
    LanguageEnum.zh_tw: "繁体中文",
    LanguageEnum.zh_gd: "繁体中文（粤语）",
    LanguageEnum.en: "English",
    LanguageEnum.ja_jp: "日本語",
    LanguageEnum.th_th: "ไทย",
    LanguageEnum.de: "Deutsch",
    LanguageEnum.es_es: "Español",
    LanguageEnum.ko_kr: "한국어",
    LanguageEnum.da_dk: "Dansk",
    LanguageEnum.sv_se: "Svenska",
    LanguageEnum.fi_fi: "Suomalainen",
    LanguageEnum.nb_no: "Norsk",
    LanguageEnum.fr_fr: "Français",
    LanguageEnum.nl_nl: "Nederlands",
    LanguageEnum.ru_ru: "Русский",
    LanguageEnum.pl_pl: "Polskie",
    LanguageEnum.pt_pt: "Português",
    LanguageEnum.it_it: "Italiano",
    LanguageEnum.ro_ro: "Română",
    LanguageEnum.ms_my: "Orang Malaysia",
    LanguageEnum.vi_vn: "Tiếng Việt",
    LanguageEnum.id_id: "Bahasa Indonesia",
    LanguageEnum.fil_ph: "Pilipino",
    LanguageEnum.cs_cz: "čeština",
    LanguageEnum.el_gr: "Ελληνικά",
    LanguageEnum.pt_br: "Português (Brasil)",
    LanguageEnum.hu_hu: "Magyar",
    LanguageEnum.tr_tr: "Türkiye",
    LanguageEnum.sk_sk: "Slovenský",
    LanguageEnum.ar_sa: "العربية",
}

# 英文对照表（标准语言码 -> 英文名称）
LANGUAGE_CODE_TO_ENGLISH_NAME = {
    LanguageEnum.zh: "Chinese",
    LanguageEnum.zh_tw: "Traditional Chinese",
    LanguageEnum.zh_gd: "Cantonese",
    LanguageEnum.en: "English",
    LanguageEnum.ja_jp: "Japanese",
    LanguageEnum.th_th: "Thai",
    LanguageEnum.de: "German",
    LanguageEnum.es_es: "Spanish",
    LanguageEnum.ko_kr: "Korean",
    LanguageEnum.da_dk: "Danish",
    LanguageEnum.sv_se: "Swedish",
    LanguageEnum.fi_fi: "Finnish",
    LanguageEnum.nb_no: "Norwegian",
    LanguageEnum.fr_fr: "French",
    LanguageEnum.nl_nl: "Dutch",
    LanguageEnum.ru_ru: "Russian",
    LanguageEnum.pl_pl: "Polish",
    LanguageEnum.pt_pt: "Portuguese",
    LanguageEnum.it_it: "Italian",
    LanguageEnum.ro_ro: "Romanian",
    LanguageEnum.ms_my: "Malay",
    LanguageEnum.vi_vn: "Vietnamese",
    LanguageEnum.id_id: "Indonesian",
    LanguageEnum.fil_ph: "Filipino",
    LanguageEnum.cs_cz: "Czech",
    LanguageEnum.el_gr: "Greek",
    LanguageEnum.pt_br: "Portuguese (Brazil)",
    LanguageEnum.hu_hu: "Hungarian",
    LanguageEnum.tr_tr: "Turkish",
    LanguageEnum.sk_sk: "Slovak",
    LanguageEnum.ar_sa: "Arabic",
}

# 中文对照表（标准语言码 -> 中文名称）
language_dict_zh = {
    LanguageEnum.zh: "中文",
    LanguageEnum.zh_tw: "繁中",
    LanguageEnum.zh_gd: "繁中香港",
    LanguageEnum.en: "英语",
    LanguageEnum.ja_jp: "日语",
    LanguageEnum.th_th: "泰语",
    LanguageEnum.de: "德语",
    LanguageEnum.es_es: "西班牙语",
    LanguageEnum.ko_kr: "韩语",
    LanguageEnum.da_dk: "丹麦语",
    LanguageEnum.sv_se: "瑞典语",
    LanguageEnum.fi_fi: "芬兰语",
    LanguageEnum.nb_no: "挪威语",
    LanguageEnum.fr_fr: "法语",
    LanguageEnum.nl_nl: "荷兰语",
    LanguageEnum.ru_ru: "俄语",
    LanguageEnum.pl_pl: "波兰语",
    LanguageEnum.pt_pt: "葡萄牙语",
    LanguageEnum.it_it: "意大利语",
    LanguageEnum.ro_ro: "罗马尼亚语",
    LanguageEnum.ms_my: "马来语",
    LanguageEnum.vi_vn: "越南语",
    LanguageEnum.id_id: "印尼语",
    LanguageEnum.fil_ph: "菲律宾语",
    LanguageEnum.cs_cz: "捷克语",
    LanguageEnum.el_gr: "希腊语",
    LanguageEnum.pt_br: "葡萄牙语（巴西）",
    LanguageEnum.hu_hu: "匈牙利语",
    LanguageEnum.tr_tr: "土耳其语",
    LanguageEnum.sk_sk: "斯洛伐克语",
    LanguageEnum.ar_sa: "阿拉伯语",
}

SUPPORTED_LANGUAGE_CODES = list(LANGUAGE_CODE_TO_ENGLISH_NAME.keys())
SUPPORTED_LANGUAGE_NAME_IN_NATIVE = list(language_dict_en.values())
SUPPORTED_LANGUAGE_NAME_IN_CHINESE = list(language_dict_zh.values())
SUPPORTED_LANGUAGE_NAME_IN_ENGLISH = list(LANGUAGE_CODE_TO_ENGLISH_NAME.values())


weather_language_map = {
    "zh_CN": "zh",         # 中文简体
    "zh_TW": "zh_tw",      # 中文繁体
    "zh_GD": "zh_yue",     # 粤语
    "en_US": "en",         # 英语
    "ja_JP": "ja",         # 日语
    "th_TH": "en",         # 无泰语，fallback
    "de_DE": "de",         # 德语
    "es_ES": "es",         # 西班牙语
    "ko_KR": "ko",         # 韩语
    "da_DK": "da",         # 丹麦语
    "sv_SE": "sv",         # 瑞典语
    "fi_FI": "fi",         # 芬兰语
    "nb_NO": "en",         # 无挪威语，fallback
    "fr_FR": "fr",         # 法语
    "nl_NL": "nl",         # 荷兰语
    "ru_RU": "ru",         # 俄语
    "pl_PL": "pl",         # 波兰语
    "pt_PT": "pt",         # 葡萄牙语
    "pt_BR": "pt",         # 巴西葡语 -> 葡语
    "it_IT": "it",         # 意大利语
    "ro_RO": "ro",         # 罗马尼亚语
    "ms_MY": "en",         # 无马来语，fallback
    "vi_VN": "vi",         # 越南语
    "id_ID": "en",         # 无印尼语，fallback
    "fil_PH": "en",        # 无菲律宾语，fallback
    "cs_CZ": "cs",         # 捷克语
    "el_GR": "el",         # 希腊语
    "hu_HU": "hu",         # 匈牙利语
    "tr_TR": "tr",         # 土耳其语
    "sk_SK": "sk",         # 斯洛伐克语
    "ar_SA": "ar",         # 阿拉伯语
}


def get_weather_lang(lang_code: str) -> str:
    return weather_language_map.get(lang_code, "en")  # 默认英文


class Multilingual(str, Enum):
    Close = "0"
    Open = "1"


multilingual_other_lang_template = {
    LanguageEnum.zh: "对不起，我无法使用xxx语言，但我可以用中文回答你的问题，<回答内容>",
    LanguageEnum.en: "Sorry, I am unable to use xxx language, but I can answer your question in English. <Answer Content>",
}


class InterventionThreshold(str, Enum):
    low = "low"
    medium = "medium"
    high = "high"
    extreme = "extreme"


class EmbeddingModel(str, Enum):
    bge = "bge"
    openai_text_embedding_3_small = "text-embedding-3-small"


# TODO(<EMAIL>): 实验一套合适的阈值(先只做bge的阈值)
INTERVENTION_THRESHOLD = {
    InterventionThreshold.low: {
        EmbeddingModel.bge: 0.8,
        EmbeddingModel.openai_text_embedding_3_small: -10000,
    },
    InterventionThreshold.medium: {
        EmbeddingModel.bge: 0.85,
        EmbeddingModel.openai_text_embedding_3_small: -10000,
    },
    InterventionThreshold.high: {
        EmbeddingModel.bge: 0.9,
        EmbeddingModel.openai_text_embedding_3_small: -10000,
    },
    InterventionThreshold.extreme: {
        EmbeddingModel.bge: 0.95,
        EmbeddingModel.openai_text_embedding_3_small: -10000,
    },
}

# TODO(<EMAIL>): 实验一个合适的阈值(先只做bge的阈值)
QA_THRESHOLD = {
    EmbeddingModel.bge: 0.75,
    EmbeddingModel.openai_text_embedding_3_small: -10000,
}

PROMOTE_USER_TEXT = "现在处于推销模式，请根据当前对话历史和你的人设信息主动引导用户"

# ISO 639-1语言编码标准
LANGUAGE_CODING_STANDARDS = {
    "zh": LanguageEnum.zh,
    "en": LanguageEnum.en,
    "it": LanguageEnum.it_it,
    "fr": LanguageEnum.fr_fr,
    "de": LanguageEnum.de,
    "ko": LanguageEnum.ko_kr,
    "ja": LanguageEnum.ja_jp,
    "es": LanguageEnum.es_es,
}

LANGUAGE_NAME_TO_LANGUAGE_ENUM = {
    Language.SPANISH: LanguageEnum.es_es,
    Language.FRENCH: LanguageEnum.fr_fr,
    Language.GERMAN: LanguageEnum.de,
    Language.ITALIAN: LanguageEnum.it_it,
    Language.CHINESE: LanguageEnum.zh,
    Language.JAPANESE: LanguageEnum.ja_jp,
    Language.KOREAN: LanguageEnum.ko_kr,
    Language.ENGLISH: LanguageEnum.en,
}

dialogue_strategy_list_general = [
    {
        "title": "了解场景",
        "trigger": "用户尚未说明具体的应用场景或产品名称（提示：只需了解大致场景即可，无需详细需求）",
        "plan": "询问用户的应用场景",
        "should_recommend": False,
    },
    {
        "title": "回答问题",
        "trigger": "用户提出具体的问题或疑虑时，且还未得到解答时，优先级最高",
        "plan": """1. 必须先直接且全面地回答用户问题，不能拐弯抹角；2. 回答时必须原文引用「产品信息」中的内容，不能改写、不能编造；3. 如果「产品信息」中找不到相关内容，就委婉转场；4. 回答问题后，根据「推荐要求」进行推荐；5. 如果用户询问体验功能，禁止给出任何推荐""",
        "should_recommend": True,
    },
    {
        "title": "视频跟进",
        "trigger": "刚完成视频的播放",
        "plan": "邀请用户给出视频反馈，并根据「推荐要求」进行推荐",
        "should_recommend": True,
    },
    {
        "title": "用户离去",
        "trigger": "用户表明离开意图",
        "plan": "确保用户留下信息后再离开",
        "should_recommend": False,
    },
    {
        "title": "灵活推荐",
        "trigger": "未触发上述策略",
        "plan": "根据「推荐要求」推荐合适内容",
        "should_recommend": True,
    },
]
dialogue_strategy_list_trigger = [
    {
        "title": "了解场景",
        "trigger": "用户尚未说明具体的应用场景或产品名称（提示：只需了解大致场景即可，无需详细需求）",
        "plan": "询问用户的应用场景",
        "should_recommend": False,
    },
    {
        "title": "视频跟进",
        "trigger": "刚完成视频的播放",
        "plan": "邀请用户给出视频反馈，并根据「推荐要求」进行推荐",
        "should_recommend": True,
    },
    {
        "title": "灵活推荐",
        "trigger": "未触发上述策略",
        "plan": "根据「推荐要求」推荐合适内容",
        "should_recommend": True,
    },
]


dialogue_strategy_list_general_v3 = [
    {
        "title": "回答问题",
        "trigger": "用户提出具体的问题或疑虑时，且还未得到解答时，优先级最高",
        "plan": """1. 必须先直接且全面地回答用户问题，不能拐弯抹角；2. 回答时必须原文引用「产品信息」中的内容，不能改写、不能编造；3. 如果「产品信息」中找不到相关内容，就委婉转场；4. 回答问题后，根据「推荐要求」进行推荐；5. 如果用户询问体验功能，禁止给出任何推荐""",
        "should_recommend": True,
    },
    {
        "title": "用户离去",
        "trigger": "用户表明离开意图",
        "plan": "确保用户留下信息后再离开",
        "should_recommend": False,
    },
    {
        "title": "灵活推荐",
        "trigger": "未触发上述策略",
        "plan": "根据「推荐要求」推荐合适内容",
        "should_recommend": True,
    },
]
dialogue_strategy_list_trigger_v3 = [
    {
        "title": "灵活推荐",
        "trigger": "未触发上述策略",
        "plan": "根据「推荐要求」推荐合适内容",
        "should_recommend": True,
    },
]

KNOWLEDGE_FIRST_ACTIONS = [  # TODO: 不使用action name
    f"orion.agent.action.{CLARIFY_ACTION_NAME.lower()}",
    "orion.agent.action.say",
    "orion.app.promote.GENERAL_SALES_SERVICE".lower(),
    "orion.agent.action.SEARCH_WEB_INFORMATION".lower(),
]
