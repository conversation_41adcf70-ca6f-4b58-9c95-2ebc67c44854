import json
import re
import time
from typing import Any, Dict, List

import aiohttp
import yaml
from loguru import logger
from pydantic import BaseModel

from src.common.constant import (
    ASSISTANT_ROLE,
    USER_ROLE,
    LanguageEnum,
    multilingual_other_lang_template,
)
from src.common.model import PersonaCoreObjective
from src.session_manager.chat_context import ChatMessage
from src.settings import agent_setting
from src.utils.llm import LLMManager


class SummaryResult(BaseModel):
    content: str
    elapsed_time: float
    token_cost: dict = {}
    messages: list[dict[str, str]] = []


class LLMToolKit:
    message_prefix_mapping = {
        "assistant": ASSISTANT_ROLE,
        "user": USER_ROLE,
        "system": "<System>",
    }
    #     SummaryPrompt = """你的任务首先是逐步分析思考，基于`聊天会话`，对`用户的最新输入`中的相关代词（这、那、这些、那些等）的进行实体替换，确保输出最新、最完整的用户问题，决不能直接回答用户的问题。
    # # 聊天会话
    # {chat_history}
    # 用户的最新输入:{follow_up_input}

    # # 输出格式 (YAML)
    # ```yaml
    # Thought: <推理过程，字数限制在30字以内>
    # FinalAnswer: <完整的用户问题>
    # ```
    # """

    SummaryPrompt = """Your task is to analyze step by step, based on the `Chat History`, replace pronouns (this, that, these, those, etc.) in the `User's Latest Input` with their actual entities, ensuring the output is the most up-to-date and complete user question. Do not directly answer the user's question.

# Chat History
{chat_history}
User's Latest Input: {follow_up_input}

# Output Format (YAML)
```yaml
Thought: <reasoning process, within 30 words>
FinalAnswer: <complete user question>
```
"""

    #     DialogueSummaryPrompt = """请简要总结以下对话的核心主题。要求：直接、精确、避免赘述。

    # 对话记录：
    # {conversation}

    # 仅输出：
    # ```yaml
    # Topic: <核心话题，30字以内>
    # ```
    # """
    DialogueSummaryPrompt = """Please analyze the language of the following conversation and provide a summary in the SAME LANGUAGE as the input conversation. The summary should be direct, precise, and avoid redundancy.

Conversation:
{conversation}

Output only:
```yaml
Topic: <core topic, within 50 words/characters, in the same language as the conversation>
```

Note: If the conversation is in Chinese, respond in Chinese. If it's in English, respond in English. For other languages, respond in that language while maintaining the same format.
"""

    @classmethod
    def parse_yaml(cls, yaml_str: str) -> dict:
        """解析YAML字符串为字典"""
        try:
            # 使用 正则获取```yaml 和 ```之间的内容
            yaml_str = re.search(r"```yaml(.*)```", yaml_str, re.DOTALL).group(1)
            return yaml.safe_load(yaml_str) if yaml_str else {}
        except Exception as e:
            logger.error(f"Failed to parse YAML: {e}, yaml_str: {yaml_str}")
            return {}

    @classmethod
    async def summary(
        cls,
        chat_history: list[str] | str,
        follow_up_input: str,
        language: str = "zh_CN",
        _logger=logger,
    ) -> SummaryResult:
        if not chat_history and len(follow_up_input) <= 15:
            return SummaryResult(content=follow_up_input, elapsed_time=0, token_cost={})

        start = time.time()

        if isinstance(chat_history, str):
            chat_history = [chat_history]

        messages = [
            {
                "role": "user",
                "content": cls.SummaryPrompt.format(
                    chat_history="\n".join(chat_history),
                    follow_up_input=follow_up_input,
                    language=language,
                ),
            },
        ]

        model_result = await LLMManager.invoke_summary_model(
            messages,
            _logger=_logger,
        )

        _logger.debug(
            f"[LLMToolKit] Summary Prompt: {messages[0]['content']}\nOriginal Output: {model_result.content}\nElapsed Time: {time.time() - start:.2f}s"
        )
        summaries_result = SummaryResult(
            messages=messages,
            content=str(cls.parse_yaml(model_result.content).get("FinalAnswer", "")),
            elapsed_time=time.time() - start,
            token_cost=model_result.token_cost,
        )

        return summaries_result

    @classmethod
    def init_persona_core_objective(
        cls,
        agent_id: str,
        app_id: str,
        persona: str = "",
        language_style: str = "",
        objective: list[str] | str = "",
        language: LanguageEnum = LanguageEnum.en,
    ) -> PersonaCoreObjective:
        return PersonaCoreObjective(
            persona=persona,
            language_style=language_style,
            objective=objective,
        )

    @classmethod
    def build_system_prompt(
        cls,
        agent_id: str,
        app_id: str,
        persona: str = "",
        language_style: str = "",
        language: LanguageEnum = LanguageEnum.en,
        objective: list[str] | str = "",
    ) -> str:
        pco = cls.init_persona_core_objective(
            agent_id=agent_id,
            app_id=app_id,
            persona=persona,
            language_style=language_style,
            language=language,
            objective=objective,
        )

        period = "." if language == LanguageEnum.en else "。"
        period_suffix = ". " if language == LanguageEnum.en else "。"  # 英文结尾有空格

        if isinstance(pco.objective, str):
            core_objectives = pco.objective
        else:
            core_objectives = "".join(pco.objective)

        if core_objectives and not core_objectives.endswith(period):
            core_objectives += period_suffix

        if language == LanguageEnum.zh:
            ability = "你记忆力超群、极其擅长工具选择。"
        else:
            ability = "You have an exceptional memory and are extremely skilled at tool selection."

        return "{persona}{core_objective}{ability} {language_style}".format_map(
            {
                "persona": pco.persona
                if pco.persona.endswith(period)
                else pco.persona + period_suffix,
                "core_objective": core_objectives,
                "language_style": language_style
                if language_style.endswith(period)
                else language_style + period_suffix,
                "ability": ability,
            }
        )

    @classmethod
    def build_conversation_progress(
        cls,
        original_messages: list[ChatMessage],
    ) -> list[str]:
        conversation_progress: list[str] = []
        logger.info(
            f"original_messages count: {len(original_messages)}\noriginal_messages:{original_messages}"
        )

        for msg in original_messages:
            if msg.content:
                current_msg = (
                    f"{cls.message_prefix_mapping[msg.role]} said '{msg.content}'"
                )
                if conversation_progress and current_msg in conversation_progress[-1]:
                    # 相邻重复过滤
                    continue
                conversation_progress.append(current_msg)

            elif msg.action:
                result = {}
                for r in msg.action.result:
                    value = r.value
                    if not value:
                        continue
                    if isinstance(value, str) and len(value) > 10:
                        value = value[:20] + "..."
                        result[r.name] = value
                    elif r.type in ["list", "dict"]:
                        result[r.name] = json.dumps(value, ensure_ascii=False)[:20]
                    else:
                        result[r.name] = value

                parameters = {}
                for p in msg.action.parameters:
                    value = p.value
                    if isinstance(value, str) and len(value) > 20:
                        value = value[:20] + "..."
                    parameters[p.name] = value

                conversation_progress.append(
                    f"{cls.message_prefix_mapping[msg.role]} Executed Action. "
                    f"{msg.action.name.split('.')[-1]}({','.join([f'{p}={v}' for p, v in parameters.items()])}) "
                    f"Result:{result} "
                )

            elif msg.event:
                conversation_progress.append(
                    f"{cls.message_prefix_mapping[msg.role]} Triggered Event [{msg.event}]"
                )
            else:
                raise ValueError(f"Invalid message: {msg}")

        return conversation_progress

    @classmethod
    def build_conversation_progress_for_function_call(
        cls,
        original_messages: list[ChatMessage],
    ) -> list[str]:
        conversation_progress: list[str] = []
        logger.debug(
            f"original_messages count: {len(original_messages)}\noriginal_messages:{original_messages}"
        )

        latest_sense_switch = None
        robot_index = 0

        for msg in original_messages:
            role = (
                cls.message_prefix_mapping[msg.role].replace("<", "").replace(">", "")
            )
            if role.lower().find("robot") != -1:
                robot_index = len(conversation_progress)

            if msg.content:
                if (
                    role.lower() == "user"
                    and conversation_progress
                    and conversation_progress[-1].startswith(role)
                ):
                    conversation_progress[-1] += f" {msg.content}"
                else:
                    conversation_progress.append(f"{role}: {msg.content}")

            elif msg.action:
                result = {}
                for r in msg.action.result:
                    value = r.value
                    if not value:
                        continue
                    if isinstance(value, str) and len(value) > 10:
                        value = value[:20] + "..."
                        result[r.name] = value
                    elif r.type in ["list", "dict"]:
                        result[r.name] = json.dumps(value, ensure_ascii=False)[:20]
                    else:
                        result[r.name] = value

                parameters = {}
                for p in msg.action.parameters:
                    value = p.value
                    if isinstance(value, str) and len(value) > 20:
                        value = value[:20] + "..."
                    parameters[p.name] = value

                conversation_progress.append(
                    f"{role}: Executed Action. "
                    f"[{msg.action.display_name}], Parameters:({','.join([f'{p}={v}' for p, v in parameters.items()])}) "
                    f"Result:{result} "
                )

            elif msg.event:
                if "The scene has switched" in msg.event.desc:
                    latest_sense_switch = msg.event.desc
                else:
                    conversation_progress.append(
                        f"{role}: Triggered Event [{msg.event}]"
                    )
            else:
                raise ValueError(f"Invalid message: {msg}")

        if latest_sense_switch and robot_index:
            # 只保留一份最新的app sense,在机器消息后
            conversation_progress.insert(
                robot_index + 1,
                latest_sense_switch,
            )

        return conversation_progress

    @classmethod
    def build_conversation_progress_for_agent_core(
        cls,
        original_messages: list[ChatMessage],
    ) -> List[Dict[str, Any]]:
        conversation_progress: List[Dict[str, Any]] = []
        logger.debug(
            f"original_messages count: {len(original_messages)}\noriginal_messages:{original_messages}"
        )

        latest_sense_switch = None
        robot_index = 0

        for msg in original_messages:
            logger.debug(f"msg: {msg} role: {msg.role}")
            role = (
                cls.message_prefix_mapping[msg.role].replace("<", "").replace(">", "")
            )
            logger.debug(f"After mapping role: {role}")
            if role.lower().find("robot") != -1:
                robot_index = len(conversation_progress)

            if msg.content:
                if (
                    role.lower() == "user"
                    and conversation_progress
                    and conversation_progress[-1].get("role").startswith(role)
                ):
                    conversation_progress[-1]["content"] += f" {msg.content}"
                else:
                    conversation_progress.append(
                        {
                            "role": role,
                            "content": msg.content,
                        }
                    )

            elif msg.action:
                result = {}
                for r in msg.action.result:
                    value = r.value
                    if not value:
                        continue
                    if isinstance(value, str) and len(value) > 20:
                        value = value[:20] + "..."
                        result[r.name] = value
                    elif r.type in ["list", "dict"]:
                        result[r.name] = json.dumps(value, ensure_ascii=False)[:20]
                    else:
                        result[r.name] = value

                parameters = {}
                for p in msg.action.parameters:
                    value = p.value
                    if isinstance(value, str) and len(value) > 20:
                        value = value[:20] + "..."
                    parameters[p.name] = value

                conversation_progress.append(
                    {
                        "role": role,
                        "content": f"Executed Action. "
                        f"[{msg.action.name}], Parameters:({','.join([f'{p}={v}' for p, v in parameters.items()])}) "
                        f"Result:{result} ",
                    }
                )

            elif msg.event:
                if "The scene has switched" in msg.event.desc:
                    latest_sense_switch = msg.event.desc
                else:
                    conversation_progress.append(
                        {
                            "role": role,
                            "content": f"Triggered Event [{msg.event}]",
                        }
                    )
            else:
                raise ValueError(f"Invalid message: {msg}")

        if latest_sense_switch and robot_index:
            # 只保留一份最新的app sense,在机器消息后
            conversation_progress.insert(
                robot_index + 1,
                {
                    "role": "robot",
                    "content": latest_sense_switch,
                },
            )

        logger.info(
            f"After build_conversation_progress_for_agent_core: {conversation_progress}"
        )
        return conversation_progress

    @classmethod
    async def get_text_conversation_records(
        cls,
        original_messages: list[ChatMessage],
        limit: int = 3,
    ) -> list[str]:
        """
        获取最近的对话记录，只保留文本内容
        """
        conversation_progress: list[str] = []

        for msg in reversed(original_messages):
            if msg.content:
                conversation_progress.append(
                    f"{cls.message_prefix_mapping[msg.role]} said '{msg.content}'"
                )
            if len(conversation_progress) >= limit:
                break

        return conversation_progress[::-1]

    @classmethod
    def get_latest_sentence(
        cls,
        original_messages: list[ChatMessage],
    ) -> str:
        """
        在多语言自适应的情况下调用，和上面的区别在于会再返回用户最近一次说的内容，用来特别指示模型需要使用的语言，经过简单测试，这样效果很好
        """
        latest_user_sentence = ""
        for msg in original_messages:
            if msg.content:
                if msg.role == "user":
                    latest_user_sentence = msg.content
        return latest_user_sentence

    @classmethod
    def build_multilingual_user_content(cls, user_question, detect_lang, robot=None):
        # 优先使用robot的language设置
        default_lang = robot.language if robot and hasattr(robot, 'language') else LanguageEnum.zh

        template = multilingual_other_lang_template.get(
            detect_lang,
            multilingual_other_lang_template.get(
                default_lang,
                multilingual_other_lang_template[LanguageEnum.zh],
            ),
        )
        return {
            "role": "user",
            "content": f"""你需要结合用户当前问题：'{user_question}'来选择使用中文/英语来进行回答。如果用户指定了使用中文或者英文回答，则使用对应的语言进行回复。如果用户指定的语言不是中文以及英语，则采用如下模板回复：{template}。 如果用户没有指定语言，则使用和'{user_question}'本身相同的语言进行回答。""",
        }

    @classmethod
    async def summarize_discussion_intent(
        cls,
        messages: list[ChatMessage],
        history_turns: int = 3,
        _logger=logger,
    ) -> SummaryResult:
        """
        通过分析对话历史，生成完整的讨论意图

        Args:
            messages: 对话消息列表
            history_turns: 要分析的最近对话轮数，默认为3轮
            _logger: 日志记录器
        Returns:
            SummaryResult: 包含完整讨论意图的总结
        """
        start = time.time()

        if not messages:
            return SummaryResult(
                content="",
                elapsed_time=0,
                token_cost={},
            )

        # 只获取最近n轮的对话记录
        text_conversation_records = await cls.get_text_conversation_records(
            messages, limit=history_turns
        )

        prompt_messages = [
            {
                "role": "user",
                "content": cls.DialogueSummaryPrompt.format(
                    conversation="\n".join(text_conversation_records),
                ),
            },
        ]

        session = aiohttp.ClientSession()
        try:
            model_result = await LLMManager.invoke_summary_model(
                prompt_messages,
                _logger=_logger,
                session=session,
            )
        except Exception as e:
            logger.error(
                f"Error when invoke summary model: {e}. prompt_messages: {prompt_messages}"
            )
            return SummaryResult(
                content="",
                elapsed_time=0,
                token_cost={},
            )
        finally:
            await session.close()

        _logger.debug(
            f"[LLMToolKit] Discussion Intent Summary:\n"
            f"Original Output: {model_result.content}\n"
            f"Elapsed Time: {time.time() - start:.2f}s"
        )

        # 解析YAML结果
        yaml_result = cls.parse_yaml(model_result.content)

        # 直接返回整合后的完整讨论意图
        return SummaryResult(
            messages=prompt_messages,
            content=yaml_result.get("Topic", ""),
            elapsed_time=time.time() - start,
            token_cost=model_result.token_cost,
        )

    # 补全用户意图（使用summary+current_question），暂时不使用
    CompleteIntentPrompt = """请分析用户的最新问题，结合之前的对话总结，判断是否需要补全用户的真实意图。

# 之前对话总结
{summary}

# 用户最新问题
{current_question}

请注意：
1. 如果用户问题表述不清晰或包含指代词，需要结合对话总结补全完整意图
2. 如果用户问题已经很清晰，不要过度解读，但仍要保证问题的完整性，避免再使用代词进行推导
3. 确保输出的问题符合用户的原始意图，不要添加额外的解释

请按照以下YAML格式输出：
```yaml
Analysis: <简要分析用户问题是否需要补全>
CompleteQuestion: <补全后的问题或原始问题>
```
"""

    @classmethod
    async def complete_user_intent(
        cls,
        summary: str,
        current_question: str,
        _logger=logger,
    ) -> SummaryResult:
        """
        基于对话总结补全用户的真实意图

        Args:
            summary: 之前对话的总结
            current_question: 用户的最新问题
            _logger: 日志记录器
        Returns:
            SummaryResult: 包含补全后问题的结果
        """
        start = time.time()

        if not summary or not current_question:
            return SummaryResult(
                content=current_question,
                elapsed_time=0,
                token_cost={},
            )

        prompt_messages = [
            {
                "role": "user",
                "content": cls.CompleteIntentPrompt.format(
                    summary=summary,
                    current_question=current_question,
                ),
            },
        ]

        model_result = await LLMManager.invoke_summary_model(
            prompt_messages,
            _logger,
        )

        _logger.debug(
            f"[LLMToolKit] Complete User Intent:\n"
            f"Original Output: {model_result.content}\n"
            f"Elapsed Time: {time.time() - start:.2f}s"
        )

        yaml_result = cls.parse_yaml(model_result.content)

        return SummaryResult(
            messages=prompt_messages,
            content=yaml_result.get("CompleteQuestion", current_question),
            elapsed_time=time.time() - start,
            token_cost=model_result.token_cost,
        )
