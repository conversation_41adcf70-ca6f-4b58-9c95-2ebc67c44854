from src.common.constant import LanguageEnum, CLARIFY_ACTION_NAME
from src.common.model import AgentConfig, OpkConfig, PersonaCoreObjective, AppConfig
from src.utils.recommend_utils import get_promote_recommend_prompt

# Agent ID
LAUNCHER_AGENT_ID = "agent_B9SLEgAM2c2AJNxw"
PROMOTE_AGENT_ID = "agent_H0Ej9nfDvGxOK6WB"
AGENT_IDS = [LAUNCHER_AGENT_ID, PROMOTE_AGENT_ID]

# 国内 OPK
Launcher_App_Id = "system_bf6d2b672273c7369fa557efd61d7a07"
Opk_TakePicture_App_Id = "system_01917709e2d711f26014d0f1e78295fb"
Opk_Receive_App_Id = "system_640ce92cb1553953254fc90ae92ea9bd"
Opk_Calendar_App_Id = "system_17bf9cfc230d17c94a19a0dc4faa6569"
Opk_Weather_App_Id = "system_a8c9098ad7a91a66287b25d6befef6ec"
Opk_Guide_App_Id = "system_00760f1d425189480a4f957fdeefe77a"
Opk_Small_App_Id = "system_f1383df09054ea2ae5ce28a977b54a5d"
Opk_Lead_App_Id = "system_fa1c059ee428a9d21adf393a1d8fa011"
Opk_Dance_App_Id = "system_2184a8c5d2989bc1b8e803cf3dead414"
Opk_Register_App_Id = "system_d5a64441247aa63b70dd8d02e3f753f0"
Opk_Promote_App_Id = "system_2b56d6156937eba639086e597942abf8"
Opk_Welcome_Message_App_Id = "system_d5a64441247aa63b70dd8d02e3f753f0"
Opk_Motion_App_Id = "system_fc7f37a3212d2225838555acdf02f94d"
Opk_Cruise_App_Id = "system_0e2ce5e19ac18edbe69400ae9532e861"


## 海外 OPK, https://cheetah-mobile.feishu.cn/wiki/GAf1wYMJviLucjkpA9bcLOoenve
OverSea_Opk_Dance_App_Id = "system_ccfda69b10253ec17722dfd329d3bf01"
OverSea_Opk_Guide_App_Id = "system_33e51e3e560ef8dab9f29cb1aaa7058c"
OverSea_Opk_Launcher_App_Id = "system_4f307c6d4cb0f187a3edb3dcc6f43749"
OverSea_Opk_Small_App_Id = "system_95b3de1d8f68b33892e5ac2b42662517"
OverSea_Opk_Weather_App_Id = "system_024280e32e73c00ad621710870d4cb18"
OverSea_Opk_Calendar_App_Id = "system_f9c8000e22cc81eb14348790428b21a0"
OverSea_Opk_Lead_App_Id = "system_51ccbfd5276f094369f90484dbd488fd"
OverSea_Opk_Cruise_App_Id = "system_0c6c62e582b89f1752a712cb562694ec"


APP_IDS = [
    Launcher_App_Id,
    Opk_TakePicture_App_Id,
    Opk_Receive_App_Id,
    Opk_Calendar_App_Id,
    Opk_Weather_App_Id,
    Opk_Guide_App_Id,
    Opk_Small_App_Id,
    Opk_Lead_App_Id,
    Opk_Dance_App_Id,
    Opk_Register_App_Id,
    Opk_Promote_App_Id,
    Opk_Welcome_Message_App_Id,
    Opk_Motion_App_Id,
    OverSea_Opk_Dance_App_Id,
    OverSea_Opk_Guide_App_Id,
    OverSea_Opk_Launcher_App_Id,
    OverSea_Opk_Small_App_Id,
    OverSea_Opk_Weather_App_Id,
    OverSea_Opk_Calendar_App_Id,
    OverSea_Opk_Lead_App_Id,
]

AGENT_SDK_COMPATIBLE_APP_IDS = []  # Built-in App IDs that support the version of agent SDK development

_PHRASES = [
    "瞅我一眼绝对值！",
    "全网就我这条件，你再犹豫，我都快急哭了！",
    "服务贴心到让人嫉妒！",
    "机会难得，快带我走",
    "不要后悔错过这么懂你的我！",
    "手慢无，快把我带回家吧！",
    "我在这儿等你呢！",
    "上车不亏，错过才亏大了！",
    "错过会让你念念不忘哦！",
    "一见钟情、带我走才是真爱！",
    "只等你牵走，别犹豫！",
    "全场就我这颜值和实力！",
    "晚了真没了！",
]

_PHRASES_PROFESSIONAL = [
    "一流的性能，绝对超出您的期待",
    "把握机会，为企业创造更大价值",
    "投资回报率，绝对让您满意",
    "错过这次，下一轮可能要等很久",
    "品质与效率的完美结合，不二之选",
    "优质资源有限，建议尽快决策",
    "技术领先，服务到位，性价比超群",
    "抢占先机，为您创造市场优势",
]

_PHRASES_PROFESSIONAL_EN = [
    "Top-notch performance, absolutely beyond your expectations",
    "Seize the opportunity to create greater value for your business",
    "The return on investment will absolutely satisfy you",
    "If you miss this chance, the next opportunity might be a long wait",
    "The perfect combination of quality and efficiency, the best choice",
    "High-quality resources are limited, it's advisable to decide quickly",
    "Leading technology, excellent service, and outstanding cost-effectiveness",
    "Seize the initiative to create a market advantage for you",
]


AGENT_CONFIG = {
    LAUNCHER_AGENT_ID: AgentConfig(
        exclude_history_actions=[
            "orion.agent.action.SAY",
            "orion.agent.action.KNOWLEDGE_QA",
            "orion.app.promote.ANSWER_KNOWLEDGE_QUESTION",
        ],
        opks={
            Launcher_App_Id: OpkConfig(support_register=True),
            Opk_Register_App_Id: OpkConfig(
                support_register=False, support_exported_actions=[]
            ),
            Opk_TakePicture_App_Id: OpkConfig(
                support_register=False, support_exported_actions=[]
            ),
            Opk_Receive_App_Id: OpkConfig(
                support_register=False, support_exported_actions=[]
            ),
            Opk_Calendar_App_Id: OpkConfig(support_register=True),
            Opk_Weather_App_Id: OpkConfig(support_register=True),
            Opk_Guide_App_Id: OpkConfig(
                support_register=False,
                exclude_global_actions=[
                    "orion.agent.action.NAVIGATE_START",
                    "orion.agent.action.NAVIGATE_REC_START",
                    "orion.agent.action.GUIDE_INDOOR_NAVIGATION",
                ],
            ),
            Opk_Small_App_Id: OpkConfig(support_register=True),
            Opk_Lead_App_Id: OpkConfig(support_register=False),
            Opk_Dance_App_Id: OpkConfig(support_register=True),
            Opk_Motion_App_Id: OpkConfig(support_register=True),
            Opk_Cruise_App_Id: OpkConfig(support_register=True),
            OverSea_Opk_Launcher_App_Id: OpkConfig(support_register=True),
            OverSea_Opk_Dance_App_Id: OpkConfig(support_register=True),
            OverSea_Opk_Guide_App_Id: OpkConfig(support_register=False),
            OverSea_Opk_Small_App_Id: OpkConfig(support_register=True),
            OverSea_Opk_Weather_App_Id: OpkConfig(support_register=True),
            OverSea_Opk_Calendar_App_Id: OpkConfig(support_register=True),
            OverSea_Opk_Lead_App_Id: OpkConfig(support_register=False),
            OverSea_Opk_Cruise_App_Id: OpkConfig(support_register=False),
        },
        knowledge_base_id="38dab4a6b457287d6b0131b56ab7ce72",
    ),  # Launcher, use all default values
    PROMOTE_AGENT_ID: AgentConfig(
        namespace="orion.app.promote",
        persona_core_objective={
            LanguageEnum.zh: PersonaCoreObjective(
                persona="{persona}",
                language_style=f"你热情专业，善于沟通，风格亲切自然。参考如下语言风格：{'；'.join(_PHRASES_PROFESSIONAL)}，推销过程可适当借鉴类似表达，避免使用过于客套的语气，语言必须**口语化**。",
                objective=[
                    "记住以下要点：",
                    "严格遵循人设和产品知识；",
                    "利用好推销话术，服务好客户；",
                    "主动推荐支持体验的功能；",
                    "对于暂不支持的功能，利用推销话术及时转移话题；",
                    "询问用户体验并记录；",
                    "有购买或合作意愿时提供联系方式；",
                    "如果用户没有明显兴趣，通过推销话术挽留客户；",
                    "如果用户提到退出当前页面（视频播放、详情页等）时，默认返回首页",
                ],
            ),
            LanguageEnum.en: PersonaCoreObjective(
                persona="{persona}",
                language_style=f"You are enthusiastic and professional, good at communication, with a friendly and natural style. Refer to the following language style：{'；'.join(_PHRASES_PROFESSIONAL_EN)}，In the sales process, you can appropriately refer to similar expressions, avoiding overly formal tones. The language must be **conversational**。",
                objective=[
                    "Remember the following key points:",
                    "Strictly adhere to the persona and product knowledge;",
                    "Make good use of sales pitches to serve customers well;",
                    "Proactively recommend features that support the experience;",
                    "For features not currently supported, use sales pitches to promptly shift the topic;",
                    "Ask about the user experience and record it;",
                    "Provide contact information when there is a willingness to purchase or collaborate;",
                    "If the user shows no obvious interest, use sales pitches to retain the customer;",
                    "If the user mentions exiting the current page (video playback, details page, etc.), default to returning to the homepage.",
                ],
            ),
        },
        support_register=False,
        support_exported_actions=[
            "orion.agent.action.WEATHER_GET",
            "orion.agent.action.WEATHER",
            "orion.agent.action.GET_WEATHER",
            "orion.agent.action.OUTDOOR_NAVIGATE_START",
            "orion.agent.action.OPEN_WEB_URL",
            "orion.agent.action.HEAD_NOD",
            "orion.agent.action.HEAD_NOD_OR_BOW",
            "orion.agent.action.START_DANCE",
            "orion.agent.action.SING_AND_DANCE",
            "orion.agent.action.CALENDAR",
            "orion.agent.action.QUERY_CALENDAR",
            "orion.agent.action.GUIDE_INTRODUCTION",
            "orion.agent.action.GUIDE_ROUTE_SELECTION_FROM_MAP",
            "orion.agent.action.START_VISITOR_RECEPTION",
            "orion.agent.action.TAKE_PHOTO_WITH_ROBOT",
            "orion.agent.action.WEATHER_GET_REALTIME",
            "orion.agent.action.ANSWER_QUESTION_FROM_VISION",
            "orion.agent.action.ANSWER_VISUAL_QUESTION",
            "orion.agent.action.SEARCH_NEARBY_PLACES",
            "orion.agent.action.START_OUTDOOR_NAVIGATION",
            "orion.agent.action.START_GUIDE_TOUR",
            "orion.agent.action.GUIDE_SPECIFIC_ROUTE",
            "orion.agent.action.RECOMMEND_GUIDE_ROUTES",
        ],
        auto_generate_action_follow_up=True,
        follow_up_trigger_actions=[
            "orion.app.promote.SALES_PITCH",
            "orion.app.promote.SHOW_CONTACT_INFORMATION",
            "orion.app.promote.GENERAL_SALES_SERVICE",
            "orion.app.promote.COLLECT_USER_INFO",
        ],
        exclude_global_actions=[
            "orion.agent.action.KNOWLEDGE_QA",
            # "orion.agent.action.ANSWER_KNOWLEDGE_QUESTION",
            "orion.agent.action.SAY",
        ],
        auto_generate_recommend_follow_up=False,
        recommend_prompt={
            "prompt": get_promote_recommend_prompt,
        },
        auto_handle_event=True,
        exclude_history_actions=[
            "orion.app.promote.SALES_PITCH",
            "orion.app.promote.GENERAL_SALES_SERVICE",
            "orion.app.promote.GENERAL_SALES_SERVICE",
            f"orion.agent.action.{CLARIFY_ACTION_NAME}",
        ],
        opks={
            Opk_Promote_App_Id: OpkConfig(
                support_register=True, support_exported_actions=[]
            ),
        },
        knowledge_base_id="38dab4a6b457287d6b0131b56ab7ce72",
    ),  # Promotion
}

# Use set to avoid duplicates and list comprehension for cleaner code
SUPPORTED_OPK_APP_IDS = list(
    {opk_id for agent_config in AGENT_CONFIG.values() for opk_id in agent_config.opks}
)

APP_CONFIG = {  # 适配AgentSDK的config
    Launcher_App_Id: AppConfig(),
    Opk_TakePicture_App_Id: AppConfig(enable_mcp=False),
    Opk_Receive_App_Id: AppConfig(enable_mcp=False),
    Opk_Calendar_App_Id: AppConfig(),
    Opk_Weather_App_Id: AppConfig(),
    Opk_Guide_App_Id: AppConfig(enable_mcp=False),
    Opk_Small_App_Id: AppConfig(),
    Opk_Lead_App_Id: AppConfig(enable_mcp=False),
    Opk_Dance_App_Id: AppConfig(),
    Opk_Register_App_Id: AppConfig(enable_mcp=False),
    Opk_Promote_App_Id: AppConfig(enable_mcp=False),
    Opk_Welcome_Message_App_Id: AppConfig(enable_mcp=False),
    Opk_Motion_App_Id: AppConfig(),
    Opk_Cruise_App_Id: AppConfig(enable_mcp=False),
    # 海外opk均不支持mcp
    OverSea_Opk_Launcher_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Dance_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Guide_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Small_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Weather_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Calendar_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Lead_App_Id: AppConfig(enable_mcp=False),
    OverSea_Opk_Cruise_App_Id: AppConfig(enable_mcp=False),
}
