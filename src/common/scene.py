from typing import Optional

from loguru import logger

from src.common.agent_config import (
    LAUNCHER_AGENT_ID,
    PROMOTE_AGENT_ID,
    Launcher_App_Id,
    Opk_Calendar_App_Id,
    Opk_Dance_App_Id,
    Opk_Guide_App_Id,
    Opk_Lead_App_Id,
    Opk_Promote_App_Id,
    Opk_Receive_App_Id,
    Opk_Register_App_Id,
    Opk_Small_App_Id,
    Opk_TakePicture_App_Id,
    Opk_Weather_App_Id,
    Opk_Welcome_Message_App_Id,
    OverSea_Opk_Calendar_App_Id,
    OverSea_Opk_Dance_App_Id,
    OverSea_Opk_Guide_App_Id,
    OverSea_Opk_Launcher_App_Id,
    OverSea_Opk_Small_App_Id,
    OverSea_Opk_Weather_App_Id,
    OverSea_Opk_Lead_App_Id,
)
from src.common.constant import Area, LanguageEnum
from src.common.model import SceneChangeContext
from src.settings import agent_setting


class SceneManager:
    _agent_mapping = {
        LanguageEnum.zh: {
            LAUNCHER_AGENT_ID: {
                "description": "主应用",
                "app_mapping": {
                    Launcher_App_Id: {
                        "description": "通用场景",
                    },
                    Opk_TakePicture_App_Id: {
                        "description": "拍照场景",
                    },
                    Opk_Receive_App_Id: {
                        "description": "前台接待场景（人脸、手机号/验证码验证）",
                    },
                    Opk_Calendar_App_Id: {
                        "description": "日历场景",
                    },
                    Opk_Weather_App_Id: {
                        "description": "天气查询场景",
                    },
                    Opk_Guide_App_Id: {
                        "description": "导览讲解场景",
                    },
                    Opk_Small_App_Id: {
                        "description": "浏览器场景",
                    },
                    Opk_Lead_App_Id: {
                        "description": "问路引领场景",
                    },
                    Opk_Welcome_Message_App_Id: {
                        "description": "欢迎语配置场景",
                    },
                    Opk_Register_App_Id: {
                        "description": "人员信息注册场景",
                    },
                    Opk_Promote_App_Id: {
                        "description": "推销场景",
                    },
                    Opk_Dance_App_Id: {
                        "description": "娱乐场景",
                    },
                },
            },
            PROMOTE_AGENT_ID: {
                "description": "推销应用",
                "app_mapping": {
                    Opk_Promote_App_Id: {
                        "description": "推销场景",
                    },
                },
            },
        },
        LanguageEnum.en: {
            LAUNCHER_AGENT_ID: {
                "description": "Home Application",
                "app_mapping": {
                    OverSea_Opk_Launcher_App_Id: {
                        "description": "General Scene",
                    },
                    OverSea_Opk_Calendar_App_Id: {
                        "description": "Calendar Scene",
                    },
                    OverSea_Opk_Weather_App_Id: {
                        "description": "Weather Query Scene",
                    },
                    OverSea_Opk_Guide_App_Id: {
                        "description": "Guided Tour Scene",
                    },
                    OverSea_Opk_Small_App_Id: {
                        "description": "Browser Scene",
                    },
                    OverSea_Opk_Dance_App_Id: {
                        "description": "Entertainment Scene",
                    },
                    OverSea_Opk_Lead_App_Id: {
                        "description": "Wayfinding and Navigation Scene",
                    },
                },
            },
            PROMOTE_AGENT_ID: {
                "description": "Promotion Application",
                "app_mapping": {
                    Opk_Promote_App_Id: {
                        "description": "Promotion Scene",
                    },
                },
            },
        },
    }

    @classmethod
    def get_scene_change_context(
        cls,
        original_app_id: str,
        changed_app_id: str,
        agent_id: str = LAUNCHER_AGENT_ID,
    ) -> Optional[SceneChangeContext]:
        if agent_setting.region_version == Area.domestic:
            _agent_mapping = cls._agent_mapping[LanguageEnum.zh]
        else:
            _agent_mapping = cls._agent_mapping[LanguageEnum.en]

        if agent_id not in _agent_mapping:
            logger.error(f"Unsupported agent_id: {agent_id}")
            return None

        original_app = _agent_mapping[agent_id]["app_mapping"].get(original_app_id)
        if not original_app:
            logger.warning(f"Unsupported original_app_id: {original_app_id}, It may be a third-party app.")
            return None

        changed_app = _agent_mapping[agent_id]["app_mapping"].get(changed_app_id)
        if not changed_app:
            logger.warning(f"Unsupported changed_app_id: {changed_app_id}, It may be a third-party app.")
            return None

        logger.info(
            f"Scene change context: {agent_id}:{_agent_mapping[agent_id]['description']}\n{original_app_id}:{original_app['description']} Changed to {changed_app_id}:{changed_app['description']}"
        )
        return SceneChangeContext(
            agent_description=_agent_mapping[agent_id]["description"],
            original_app_description=original_app["description"],
            changed_app_description=changed_app["description"],
        )


if __name__ == "__main__":
    print(
        SceneManager.get_scene_change_context(
            original_app_id=Launcher_App_Id,
            changed_app_id=Opk_TakePicture_App_Id,
            agent_id=LAUNCHER_AGENT_ID,
        )
    )
