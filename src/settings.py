import os
from enum import Enum

import redis
from google import genai
from google.genai import types
from pydantic_settings import BaseSettings, SettingsConfigDict


# import vertexai
# import boto3


class SummaryMode(Enum):
    DISABLED = "disabled"  # 完全关闭总结功能
    BASIC = "basic"  # 满血模式
    ADVANCED = "advanced"  # 极速模式


class LivekitSettings(BaseSettings):
    """
    Load environment variables automatically
    * Case insensitive
    """

    env: str = "dev"
    region_version: str = "domestic"

    # 百度翻译配置
    baidu_translate_api_url: str = "https://aip.baidubce.com/rpc/2.0/mt/texttrans/v1"
    baidu_translate_api_key: str = "gALlgj33XTFUH2gX0qo03LnY"
    baidu_translate_secret_key: str = "j5AO07pR3fTqPYaU5viYcwjTngLzPeUd"

    livekit_url: str = "wss://test-kt1s0oba.livekit.cloud"
    livekit_api_key: str = "APIeQDefFQuZucR"
    livekit_api_secret: str = "UBeEXjoDKJ5Pnd8LcMz55Dfw66DmA5siHsXobFEu20L"

    ######  EMBEDDING MODELS CONFIG ######
    embedding_api_key: str = "***************************************************"
    embedding_model_base_url: str = "http://*************:8080/embed"
    embedding_model: str = "bge"
    embedding_dim: int = 1024
    embedding_timeout: int = 2
    max_batch_size: int = 32
    few_shot_score_threshold: float = 0.4

    ######  OPENAI MODELS CONFIG ######

    ######  ALIYUN MODELS CONFIG ######

    plan_model_base_url: str = "https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
    plan_model_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"
    plan_model: str = "qwen-max"
    # plan_model_base_url: str = "http://proxy-ai.smartsales.vip/v1"
    # plan_model_api_key: str = "***************************************************"
    # plan_model: str = "gpt-4o-2024-11-20"
    # plan_model_base_url: str = "https://api.openai.com/v1"
    # plan_model_api_key: str = "***************************************************"
    # plan_model: str = "gpt-4o-2024-11-20"
    # plan_model_base_url: str = "https://api.openai.com/v1"
    # plan_model_api_key: str = "***************************************************"
    # plan_model: str = "gpt-4.1"

    summary_model_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    summary_model_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"
    summary_model: str = "qwen-max"

    generate_text_model_base_url: str = (
        "https://dashscope.aliyuncs.com/compatible-mode/v1"
    )
    generate_text_model_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"
    generate_text_model: str = "qwen-max"

    ######  AGENT MODE CONFIG ######

    # 满血模式下相关的信息
    full_power_plan_model_api_key: str = (
        "***************************************************"
    )
    full_power_plan_model_base_url: str = "http://proxy-ai.smartsales.vip/v1"
    full_power_plan_model: str = "gpt-4o-2024-11-20"
    full_power_generate_text_model: str = "gpt-4o-2024-11-20"

    # 极速模式下相关的信息
    turbo_plan_model_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"
    turbo_plan_model_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    turbo_plan_model: str = "qwen-max"
    turbo_generate_text_model: str = "qwen-max"

    guest_text_model_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"
    guest_text_model_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    guest_text_model: str = "qwen-max"

    chat_base_url: str = (
        "https://api.chatmax.net/proxyapi/gateway_iapi/iapi/v1/ctai/ovs/query_text_chat"
    )
    chat_api_key: str = "65cef20ebb64b75b10f25c4481e2c769"
    asr_base_url: str = (
        "http://speech-test.ainirobot.com/asr-client/h1/asr/text?version=v3"
    )
    asr_api_key: str = "f073b065d3bcc8682e424a2bb359099d"
    asr_ws_url: str = "wss://speech-test.ainirobot.com/ws/streaming-asr"

    admin_forspoken: str = (
        "我是管理员"  # 古英语"Forspeak"的过去分词，此词有着"施展魔法"的意思
    )
    chatmax_super_key: str = "bz5f5f458d2c811b86573b2d4ce323a9f9z3fbb114fac4c8ae3fdb1351e03024c01"  # 超级密钥，用于特殊操作
    cos_secret_id: str = "AKID4M0PyaFgIsgGkjiS6d4PqS2YI1e8lSKH"
    cos_secret_key: str = "1DczIWh1OkI2tszDYyP91xssYJFGUtKu"
    cos_prefix: str = "agentos"
    cos_bucket: str = "nlp-test-1256573505"
    cos_region: str = "ap-beijing"
    test_corp_api_key: str = "7ef1f8909e3da64c67f3e7e839070b6b"  # 测试环境知识库api_key

    # 机器人平台openAPI
    robot_openapi_host: str = "https://test-openapi.orionstar.com"
    robot_openapi_key: str = "7ef1f8909e3da64c67f3e7e839070b6b"
    corp_send_message_path: str = "/v1/aios/corp/send_person_im_message"
    corp_search_user_path: str = "/v1/aios/corp/person_list"
    knowledge_content_path: str = "/v1/aios/corp/chatmax_api_proxy"
    guide_whole_point_path: str = "/v1/aios/mc/robot_mc_obj_list"
    convert_tour_id_path: str = "/v1/aios/mc/robot_mc_obj_list"
    light_app_path: str = "/v1/aios/corp/webapp_list"
    support_map_points_path: str = "/v1/aios/map/robot_position_list"

    example_selector_k: int = 2  # 选择几个例子

    qdrant_host: str = "*************"
    meiyan: bool = False  # 25s
    redis_host: str = "embodied-ai-test.b6uduc.ng.0001.cnw1.cache.amazonaws.com.cn"
    redis_port: int = 6379
    redis_db: int = 0
    face_id_cache_db: int = 1
    test_case_redis_db: int = 5

    # TTS
    tts_endpoint: str = "/tts/v1/text2audio"
    tts_base_url: str = "https://speech-bxm.ainirobot.com"
    tts_token: str = "eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Im9yaW9uLm92cy5jbGllbnQuMTUxNDI1OTUxMjQ3MSIsIm9wZW5JZCI6IjNhMWY3OWMzMzlkZmI5MWNjMmQ3OTMwODcxMjljZDY1Iiwic2NvcGUiOiJvdnM6YXBwIiwiaXNzIjoib3Jpb24iLCJleHAiOjE2NDUwOTUxNzMsInR5cGUiOiJhY2Nlc3NfdG9rZW4iLCJpYXQiOjE2NDUwODc5NzMsInZlcnNpb24iOiIxLjAifQ.1wGORkN5Ik2SC2pi1F_yRaHlFYQ-6UqiBxpOiUItZ2k"
    tts_type: str = "aliyun-loongstella"
    # multimodal
    multimodal_url: str = "http://*********:8000/v1/chat/completions"
    multimodal_model: str = "Qwen2-VL-7B-Instruct"

    mem0_api_key: str = "m0-yYWCNOvFhWpnBVLjOJen03zqNc0WYS8DtHFrvIjG"
    feishu_alarm_url: str = "https://open.feishu.cn/open-apis/bot/v2/hook/7b357c0d-02e3-4273-a68e-f95ecb713085"

    # BO
    diagnostic_host: str = "https://speech-test.orionstar.com"
    diagnostic_path: str = "/speech-bridge/bo/history_record"
    diagnostic_secret_key: str = "d6487560941d4d2c8d58975d9e47c15c"
    asr_file_path_cache_ttl: int = 3600  # 1 hour
    user_memory_limit: int = 50  # 50条
    summary_mode: SummaryMode = SummaryMode.ADVANCED  # 替换原来的 summary_switch
    agent_retry_times: int = 3  # 重试次数
    delay_sync_plan_status: float = 0.5  # 同步计划状态的延迟时间

    # get_knowledge_content_url: str = "https://openapi.chatmax.net/v1/ctai/search_ctai_doc"  # 知识库旧的url

    get_weather_url: str = (
        "http://dspyskil-bxm.ainirobot.net:9607/skill/weather"  # 天气
    )
    calendar_url: str = "http://chat-robot.ainirobot.net:8000/chat/"  # 日历
    update_knowledge_base_url: str = "https://openapi.chatmax.net/v1"  # 知识库

    coze_bot_url: str = "https://api.coze.cn/v3/chat"  # coze_bot chat
    coze_bot_retrieve_url: str = (
        "https://api.coze.cn/v3/chat/retrieve"  # coze_bot retrieve
    )
    coze_bot_get_message_list: str = (
        "https://api.coze.cn/v3/chat/message/list"  # coze_bot message list
    )
    get_weather_info_url: str = (
        "https://restapi.amap.com/v3/weather/weatherInfo"  # weather info
    )
    agentos_feedback_url: str = (
        "https://speech.orionstar.com/speech-robot/agentos/feedback"  # agentos_feedback
    )
    qr_code_url: str = "https://test-jiedai.ainirobot.com/orics/down/k005_20250122_48f82df45fb06badb3bca380ae4a1b1b.png"  # qr_cod

    # azure config
    azure_endpoint: str = "https://orionai-east-us.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview"

    azure_subscription_key: str = "7dda87fe65bc448185c6ea231c1ec65b"

    robot_support_map_points_url: str = (
        "http://resource-readonly-online.ainirobot.com/open/v1/resource/dict"
    )

    overseas_weather_api_key: str = "0270cb7603514ad885641807251103"

    overseas_weather_api_url: str = "http://api.weatherapi.com"
    overseas_weather_api_current_path: str = "/v1/current.json"
    overseas_weather_api_future_path: str = "/v1/forecast.json"
    overseas_weather_api_future_days: int = 14

    map_dir_url: str = "https://www.google.com/maps/dir/"

    map_search_url: str = "https://www.google.com/maps/search/"

    vision_model: str = "qwen-vl-plus"
    vision_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    vision_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"

    # 临时使用自己部署的模型
    # vision_model: str = "Qwen2-VL-7B-Instruct"
    # vision_base_url: str = "http://*********:8000/v1"
    # vision_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"

    # 双模型规划
    dual_model_planning: bool = True

    model_config = SettingsConfigDict(
        env_file=os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "..", ".env.template"
        ),
        env_file_encoding="utf-8",
        # 环境变量前缀和映射
        env_prefix="",
        # 将字段名转换为大写以匹配环境变量名
        case_sensitive=False,
    )

    speech_bridge_sse_url: str = "http://speech-test.orionstar.com/speech-bridge/events"
    speech_bridge_enterprise_validator_url: str = (
        "https://speech-test.orionstar.com/speech-bridge/assistant/status"
    )

    # intervene
    intervene_qa_pair_url: str = (
        "http://resource-online-test.ainirobot.com/open/v1/resource/qa_pair"
    )
    intervene_num_retries_for_qa_pair: int = 3
    intervene_polish_answer_model_base_url: str = (
        "https://prem.dashscope.aliyuncs.com/compatible-mode/v1"
    )
    intervene_polish_answer_model_api_key: str = "sk-2f99e60920ee4e22bde8fda877567b99"
    intervene_polish_answer_model_name: str = "qwen-max"
    intervene_polish_answer_model_max_completion_tokens: int = 512
    intervene_polish_answer_model_temperature: float = 0.0
    intervene_polish_answer_model_repetition_penalty: float = 1.0
    intervene_num_retries_for_polish_answer: int = 1
    intervene_embedding_enable_redis: bool = False

    # single action fewshot
    enable_single_action_fewshot: bool = True
    single_action_fewshot_embedding_api_key: str = (
        "***************************************************"
    )
    single_action_fewshot_embedding_model_base_url: str = (
        "http://*************:8080/embed"
    )
    single_action_fewshot_embedding_model: str = "bge"
    single_action_fewshot_embedding_dim: int = 1024
    single_action_fewshot_embedding_batch_size: int = 16
    single_action_fewshot_embedding_enable_redis: bool = False
    single_action_fewshot_qdrant_host: str = "*************"
    single_action_fewshot_qdrant_insert_batch_size: int = 16

    log_level: str = "INFO"
    map_tool_base_url: str = "https://test-agentpoi.orionstar.com/amap/"

    # speech wakeup result report url
    speech_wakeup_result_report_url: str = (
        "https://speech.orionstar.com/speech-ai-robot/speech/oversea_asr_vad"
    )

    aos_studio_host: str = (
        "http://aos-backend:80/"  # 集群内使用coreDNS访问，本地开发配置env
    )
    aos_studio_timeout: int = 8
    aos_studio_webapp_resource_url: str = "/capi/v1/corp/gateway_agentos/app_webapp/"
    aos_studio_intervene_action_enterprise_level_threshold_url: str = (
        "/capi/v1/corp/gateway_agentos/intervention-thresholds/action/mapped-value"
    )
    aos_studio_block_action_url: str = (
        "/capi/v1/corp/gateway_agentos/block-actions/expanded"
    )
    aos_studio_mcp_url: str = "/capi/v1/corp/gateway_agentos/mcp-servers"
    aos_report_url: str = "/capi/v1/corp/gateway_agentos/conversations/sessions/report"

    agent_core_base_url: str = "http://aos-core-dev.orionstar.com/"
    agent_core_path: str = "/api/v1/agent/run"
    agent_core_timeout: int = 8

    knowledge_score_threshold: float = 0.8639
    intervention_threshold: float = 0.85

    gemini_project_id: str = "orionstar-ai-gemini"
    gemini_auth_path: str = "orionstar-ai-gemini-488de8d2fe77.json"

    assistant_redis_host: str = (
        "robot-jiedai-hwdev.b6uduc.ng.0001.cnw1.cache.amazonaws.com.cn"
    )
    assistant_redis_port: int = 6379
    assistant_redis_db: int = 14

    vector_api_url: str = "https://api.openai.com/v1/vector_stores/"

    turn_on_transition: bool = True

    min_sentence_len: int = 5


agent_setting = LivekitSettings()

face_id_redis_client = redis.Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.face_id_cache_db,
    charset="utf-8",
    decode_responses=True,
)  # a connection pool is created on redis.Redis()

# async redis client
global_async_redis_client = redis.asyncio.Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.redis_db,
    decode_responses=True,
)

# get current directory
current_dir = os.path.dirname(os.path.abspath(__file__))
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = os.path.join(
    current_dir, agent_setting.gemini_auth_path
)

# boto3_client = boto3.client(
#     "bedrock-runtime",
#     region_name="us-west-2",
#     aws_access_key_id="********************",
#     aws_secret_access_key="jpDQmnQ6Wq3TS6EMvqpiGEs1AAsAgQDUmWN2uum7",
# )

gemini_client = genai.Client(
    vertexai=True,
    project=agent_setting.gemini_project_id,
    location="global",
)


class AgentMode:
    turbo = "turbo"
    full_power = "full_power"


# 使用示例
if __name__ == "__main__":
    # 创建设置实例
    settings = LivekitSettings()

    # 访问设置
    print(f"API Key: {settings.generate_text_model_base_url}")
    print(f"API Secret: {settings.generate_text_model_api_key}")
    print(f"Host: {settings.generate_text_model}")
