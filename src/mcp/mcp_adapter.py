from typing import Any, Callable, Awaitable
from loguru import logger

from mcp_use import <PERSON><PERSON><PERSON>
from mcp.types import Tool

from src.action.model import Action


MCP_ACTION_NAMESPACE_PREFIX = "orion.mcp"


class MCPToolAdapter:
    """
    MCP 工具适配器
    """

    def __init__(self, server_name: str, tool: Any, mcp_client: MCPClient):
        self.server_name = server_name
        self.tool: "Tool" = tool
        self.mcp_client = mcp_client

    def to_action(self) -> Action:
        input_schema = self.tool.inputSchema
        properties = input_schema.get("properties", {})
        parameters = []
        required_parameters = input_schema.get("required", [])
        for parameter_name, parameter_info in properties.items():
            required = parameter_name in required_parameters
            parameters.append(
                self._convert_param(
                    parameter_name, parameter_info, is_required=required
                )
            )

        display_name = input_schema.get("title", self.tool.name)
        return Action(
            name=self.tool.name,
            display_name=f"调用MCP工具: {display_name}",
            en_display_name=f"Call MCP Tool: {display_name}",
            desc=self.tool.description,
            parameters=parameters,
            result_schema=[],
            execute_function=self._build_executor(),
            execute_side="server",
            exported=True,
            namespace=f"{MCP_ACTION_NAMESPACE_PREFIX}.{self.server_name}",
            source="mcp",
        )

    def _convert_param(
        self, param_name: str, param_info: dict, is_required: bool = True
    ) -> Action.Parameter:
        """Convert MCP param to Action param
        https://modelcontextprotocol.io/docs/concepts/tools

        TODO: 支持更多MCP Tool类型
        """
        if param_info["type"] == "array":
            items_def = param_info["items"]
            items_type = items_def["type"]
            if items_type == "string":
                param_type = "String array"
            elif items_type == "number":
                param_type = "Integer array"
            else:
                param_type = "list"

        elif param_info["type"] == "object":
            param_type = "dict"
        elif param_info["type"] == "integer":
            param_type = "int"
        elif param_info["type"] == "number":
            param_type = "float"
        elif param_info["type"] == "boolean":
            param_type = "bool"
        else:
            param_type = param_info["type"]

        logger.info(
            f"MCPToolAdapter _convert_param: {param_name} {param_info} type: {param_type}"
        )

        description = param_info.get("description", "")
        if "default" in param_info:
            is_required = True
            description += f"\nDefault value: {param_info['default']}"

        return Action.Parameter(
            name=param_name,
            type=param_type,
            desc=description,
            is_required=is_required,
        )

    def _build_executor(self) -> Callable[[dict], Awaitable[Any]]:
        async def executor(**kwargs: Any) -> Any:
            # 调用 MCP server 的接口
            session = self.mcp_client.get_session(self.server_name)
            return await session.call_tool(self.tool.name, kwargs)

        return executor


def load_mcp_actions(mcp_client: MCPClient) -> list[Action]:
    mcp_actions = []
    for server_name, session in mcp_client.sessions.items():
        for tool in session.tools:
            adapter = MCPToolAdapter(server_name, tool, mcp_client)
            mcp_action = adapter.to_action()
            mcp_actions.append(mcp_action)
            logger.info(
                f"Loaded MCP action: {server_name} {mcp_action.name} {mcp_action.namespace}"
            )

    return mcp_actions
