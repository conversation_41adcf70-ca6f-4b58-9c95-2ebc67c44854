import json
import os
import time
from typing import Literal, Dict
import asyncio

from mcp_use import MCPClient
from loguru import logger
from pydantic import BaseModel, model_validator
from livekit.agents.utils.http_context import http_session
import aiohttp
from urllib.parse import urljoin

from src.settings import agent_setting
from src.utils.feishu_alarm import send_feishu_alarm
from src.common.constant import Area
from src.utils.double_cache.double_cache import ttl_cache

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
MCP_CONFIG_PATH = os.path.join(CURRENT_DIR, "mcp.json")


class McpResult(BaseModel):
    server_name: str
    is_active: bool
    source: Literal["external", "built-in"]
    namespace: str
    sse_config: dict | None = None

    @model_validator(mode="before")
    def extract_values(cls, values):
        """{
          "id": 1,
          "name": "Fetch网页内容抓取",
          "desc": "该服务器使大型语言模型能够检索和处理网页内容，将HTML转换为markdown格式，以便于更轻松地使用。",
          "enterprise_id": "orion.ovs.entprise.9945420568",
          "operator": "SSOUID.bo.ca.368",
          "mcp_servers": {
            "mcpServers": {
              "fetch": {
                "url": "https://mcp.api-inference.modelscope.cn/sse/b367b26b26b944",
                "type": "sse"
              }
            }
          },
          "source": "external",
          "is_active": true,
          "namespace": "orion.ovs.entprise.9945420568.mcp.Fetch网页内容抓取"
        }"""
        new_values = {}

        servers = values.pop("mcp_servers", {}).get("mcpServers", {})
        for server_name, server_config in servers.items():
            new_values["server_name"] = server_name
            if values["source"] == "external":
                new_values["sse_config"] = server_config

        new_values.update(values)
        return new_values


class MCPManager:
    def __init__(
        self,
        mcp_client: MCPClient,
        session: aiohttp.ClientSession | None = None,
    ):
        self.mcp_client = mcp_client
        self.session = session if session else http_session()

        try:
            with open(MCP_CONFIG_PATH, "r") as f:
                self.builtin_mcp_server_config = json.load(f)
                logger.info(
                    f"Builtin MCP server config: {self.builtin_mcp_server_config}"
                )
        except Exception as e:
            logger.error(
                f"Failed to load builtin MCP server config: {e} {MCP_CONFIG_PATH}"
            )
            self.builtin_mcp_server_config = {}

    @ttl_cache(ttl=60 * 2)
    async def fetch_mcp_server_config(
        self, enterprise_id: str
    ) -> Dict[str, "McpResult"]:
        logger.info(f"Fetch MCP server config: {enterprise_id}")
        headers = {
            "x-origws-c-c-ov-corp-id": enterprise_id,
        }
        try:
            async with self.session.get(
                urljoin(
                    agent_setting.aos_studio_host,
                    agent_setting.aos_studio_mcp_url,
                ),
                headers=headers,
            ) as response:
                response.raise_for_status()
                result = await response.json()
                logger.info(f"Fetched MCP server config: {result}")
                data = result.get("data", [])
                if not data:
                    return {}
        except Exception as e:
            err_msg = (
                f"Failed to fetch MCP server config: {e} enterprise_id: {enterprise_id}"
            )
            logger.error(err_msg)
            await send_feishu_alarm(err_msg)
            return {}

        mcp_server_results = {}
        for server in data:
            try:
                mcp_result = McpResult(**server)
                if mcp_result.is_active:
                    mcp_server_results[mcp_result.server_name] = mcp_result.model_dump()
            except Exception as e:
                err_msg = f"Failed to parse MCP server config: {e} server: {server}"
                logger.error(err_msg)
                await send_feishu_alarm(err_msg)

        logger.debug(f"Fetched MCP server config: {mcp_server_results}")

        return mcp_server_results

    @ttl_cache(ttl=60 * 3)
    async def update_mcp_servers(self, enterprise_id: str):
        if agent_setting.region_version == Area.overseas:
            return

        start_at = time.time()
        active_server_names = self.mcp_client.get_all_active_sessions().keys()
        logger.info(f"Active MCP servers: {active_server_names}")

        mcp_servers: Dict[str, Dict] = await self.fetch_mcp_server_config(enterprise_id)

        to_deleted_server_names = set(active_server_names) - set(mcp_servers.keys())
        logger.info(f"To delete MCP servers: {to_deleted_server_names}")
        for server_name in to_deleted_server_names:
            await self.mcp_client.close_session(server_name)

        new_mcp_server_names = set(mcp_servers.keys()) - set(active_server_names)
        logger.info(f"New MCP servers: {new_mcp_server_names}")

        # update builtin mcp servers first
        new_mcp_builtin_cmd_config = {}
        for server_name in new_mcp_server_names:
            if bultin_config := self.builtin_mcp_server_config["mcpServers"].get(
                server_name
            ):
                if bultin_config.get("type") != "sse":
                    new_mcp_builtin_cmd_config[server_name] = bultin_config

        for server_name, cmd_config in new_mcp_builtin_cmd_config.items():
            logger.debug(
                f"Create new MCP server: {server_name} {cmd_config} from builtin config"
            )
            try:
                self.mcp_client.add_server(server_name, cmd_config)
                await self.mcp_client.create_session(server_name)
            except Exception as e:
                logger.error(
                    f"Failed to create new MCP server: {e} {e.__class__.__name__} {server_name} {cmd_config}"
                )

        for server_name in new_mcp_server_names:
            if server_name in new_mcp_builtin_cmd_config:
                continue

            if server_name in self.builtin_mcp_server_config["mcpServers"]:
                logger.debug(
                    f"Create new MCP server: {server_name} {(self.builtin_mcp_server_config['mcpServers'][server_name],)} from builtin config"
                )
                try:
                    self.mcp_client.add_server(
                        server_name,
                        self.builtin_mcp_server_config["mcpServers"][server_name],
                    )
                    async with asyncio.timeout(5):
                        await self.mcp_client.create_session(
                            server_name,
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to create new MCP server: {e} {e.__class__.__name__} {server_name} {self.builtin_mcp_server_config['mcpServers'][server_name]}"
                    )
            else:
                logger.debug(
                    f"Create new MCP server: {server_name} {mcp_servers[server_name]}"
                )
                try:
                    self.mcp_client.add_server(
                        server_name,
                        mcp_servers[server_name].get("sse_config", {}),
                    )
                    async with asyncio.timeout(5):
                        await self.mcp_client.create_session(
                            server_name,
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to create new MCP server: {e} {e.__class__.__name__} {server_name} {mcp_servers[server_name]}"
                    )

        logger.info(f"Active MCP sessions: {self.mcp_client.active_sessions}")
        logger.info(f"Updated MCP servers in {time.time() - start_at} seconds")


if __name__ == "__main__":
    import asyncio

    async def main():
        mcp_client = MCPClient()
        async with aiohttp.ClientSession() as session:
            mcp_manager = MCPManager(mcp_client, session)
            await mcp_manager.update_mcp_servers("orion.ovs.entprise.9945420568")
            await mcp_manager.update_mcp_servers("orion.ovs.entprise.9945420568")

            await asyncio.sleep(5)

            await mcp_manager.update_mcp_servers("orion.ovs.entprise.9945420568")

    asyncio.run(main())
