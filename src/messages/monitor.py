from datetime import datetime
from typing import Dict, Any, Literal, Optional

from pydantic import BaseModel


class MonitorHeartbeatContent(BaseModel):
    request_msg_id: str
    timestamp: str = datetime.now().isoformat()
    client: Dict[str, Any] = {}
    server: Dict[str, Any] = {}

    def get_server_info(self, assistant):
        server_subscribed_tracks = []
        if assistant._human_input._subscribed_track:
            server_subscribed_tracks.append(
                {
                    "sid": assistant._human_input._subscribed_track.sid,
                    "state": assistant._human_input._subscribed_track.stream_state,
                }
            )

        server_published_tracks = []
        if assistant._agent_publication and assistant._agent_publication.track:
            server_published_tracks.append(
                {
                    "sid": assistant._agent_publication.track.sid,
                    "state": assistant._agent_publication.track.stream_state,
                }
            )
        self.server = {
            "subscribed_tracks": server_subscribed_tracks,
            "published_tracks": server_published_tracks,
        }


class MonitorMessage(BaseModel):
    device_id: str
    msg_type: str = "monitor.heartbeat"
    content: Optional[MonitorHeartbeatContent] = None
