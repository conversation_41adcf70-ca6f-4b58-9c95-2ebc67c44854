from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel

from src.session_manager.plan_running_status import RunningStatus


class ActionResultContent(BaseModel):
    plan_id: str
    run_id: str
    node_id: str
    request_msg_id: str
    result_type: Literal["server_sent", "client_requested", "server_preprocessed"]
    action_name: str
    status: bool
    result: Dict = {}
    message: str = ""
    result_id: str = ""
    push_audio: bool = False


class ActionResultMessage(BaseModel):
    device_id: str
    session_id: str = ""
    msg_type: str = "run.action_result"
    content: Optional[ActionResultContent] = None
    diagnostic_info: Dict[str, Any] = {}


class RunActionContent(BaseModel):
    action_name: str
    run_id: str
    plan_id: str | None = None
    node_id: str | None = None
    parameters: dict | None = {}


class RunParameterContent(BaseModel):
    parameters: Dict[str, Any]
    plan_id: str
    run_id: str


class RunStatusContent(BaseModel):
    run_id: str
    plan_id: str
    status: RunningStatus
    nodes: List = []


class RunStepContent(BaseModel):
    step_id: int
    step_name: str
    step_display_name: str
    timestamp: int
    step_result: Optional[dict] = None
    status: RunningStatus
    error_msg: str | None = None
    step_scope: Literal["plan", "action_executor"] = "plan"


class InterruptSpeechContent(BaseModel):
    message: str
    request_msg_id: str


class RunMessage(BaseModel):
    msg_type: Literal[
        "run.run_action",
        "run.parameters",
        "run.state",
        "run.step",
        "run.interrupt_speech",
    ]
    content: (
        RunActionContent
        | RunStatusContent
        | RunParameterContent
        | RunStepContent
        | InterruptSpeechContent
        | None
    ) = None
    device_id: str | None = None
    session_id: str | None = None
    diagnostic_info: dict | None = None
