from typing import Dict, Any, Optional

from pydantic import BaseModel


class ChatAnswerContent(BaseModel):
    question: str
    user_query: str
    answer: Optional[str] = None
    image_info: str = ""
    video_info: str = ""
    radio_info: str = ""
    uuid: str = ""
    pair_id: int = 0


class ChatAnswerMessage(BaseModel):
    device_id: str
    session_id: str = ""
    msg_type: str = "chat.qa_answer"
    content: Optional[ChatAnswerContent] = None
    diagnostic_info: Dict[str, Any] = {}
