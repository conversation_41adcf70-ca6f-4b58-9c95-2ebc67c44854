from typing import Dict, Any, Literal, Optional, Union

from pydantic import BaseModel


class PlanContent(BaseModel):
    status: str
    plan_id: str
    plan_type: Literal["plan", "single_action"]
    plan_xml: str
    page_id: str = ""
    app_id: str = ""
    message: str = ""
    # scope: Literal["app", "global"] = "global"
    run_id: str = ""  # 如果execute_immediately为True，则run_id不为空
    execute_immediately: bool = False


class PlanMessage(BaseModel):
    device_id: str
    query_id: str = ""
    user_query: str = ""
    session_id: str = ""
    msg_type: str = "plan.plan"
    action_version: str = "draft"
    content: Optional[PlanContent] = None
    diagnostic_info: Dict[str, Any] = {}
    elapse_info: Dict[str, Union[float, str]] = {}


class PlanStatusContent(BaseModel):
    status: Literal["running", "pending", "succeeded", "failed", "rejected"] = "pending"
    message: str = "规划中"


class PlanStatusMessage(BaseModel):
    device_id: str
    query_id: str = ""
    session_id: str = ""
    msg_type: str = "plan.state"
    action_version: str = "draft"
    content: PlanStatusContent
    diagnostic_info: Dict[str, Any] = {}
    elapse_info: Dict[str, Union[float, str]] = {}
