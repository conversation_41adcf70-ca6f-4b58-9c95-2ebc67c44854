import asyncio
import inspect
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Literal
import traceback

from loguru import logger

from src.action.actions import ActionFunction, ActionLib
from src.action.server_function import mcp_tool_post_process
from src.session_manager.blackboard import Blackboard
from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.action.model import FunctionResult, FunctionOutput


@dataclass
class _ActionInfo:
    action_name: str
    node_id: str
    parameters: Dict[str, Any]
    run_id: str
    plan_id: str
    request_msg_id: str
    query_id: str
    result_type: Literal["server_sent", "client_requested", "server_preprocessed"]
    version: str = "draft"
    add_to_chat_ctx: bool = True
    commit_user_msg: bool = False  # 当前action是否需要尝试commit user msg
    user_question: str = ""  # 当前action是否是对用户query的回答


class ActionExecutor:
    """
    ActionExecutor is used to execute actions
    Future Enhancement: The system should be able to handle reference parameters.
    For instance, a function call like func1($var1, $var2) should be possible, where $var1 and $var2 are references to actual parameters.

    Action input parameters:
    * system parameters（系统注入参数）:
        - __memory: Memory
        - __robot: Robot
    * action hidden parameters(Action的隐藏参数):
        - _APP_ID: str
        - _SUMMARY: str
        ....
    """

    def __init__(
        self,
        blackboard: "Blackboard",
        memory: "Memory",
        robot: "Robot",
    ):
        self.blackboard = blackboard
        self.memory = memory
        self.robot = robot

    async def execute(
        self,
        action_name: str,
        parameters: Dict,
        plan_id: str,
        run_id: str,
        node_id: str,
        run_step_queue: asyncio.Queue,
    ) -> Any:
        _logger = logger.bind(
            plan_id=plan_id,
            run_id=run_id,
        )
        _logger.info(f"Start to execute action: {action_name}")
        # check if parameters is a reference to another parameter
        for parameter, parameter_value in parameters.items():
            _logger.info(
                f"ActionExecutor: parameter: {parameter}, parameter_value: {parameter_value}"
            )
            if isinstance(parameter_value, str):  # not a reference
                parameters[parameter] = await self._eval_reference(
                    parameter_value, plan_id, run_id
                )
            elif isinstance(parameter_value, List):
                for i, item in enumerate(parameter_value):
                    if isinstance(item, str):
                        parameters[parameter][i] = await self._eval_reference(
                            item, plan_id, run_id
                        )

        _logger.info(f"After evaluating the references: {parameters}")

        _logger.info(f"Action {action_name} start to execute")
        # inject system parameters
        parameters["__memory"] = self.memory
        parameters["__robot"] = self.robot
        parameters["__run_step_queue"] = run_step_queue
        action = ActionLib().get_one_action(full_name=action_name)

        func = ActionFunction.get_execute_function(action_name=action_name)
        if not func:
            raise ValueError(f"Action {action_name} not found")

        if action.source == "mcp":
            # 严格限制mcp action的参数
            strict_parameters = {}
            for parameter in action.parameters:
                strict_parameters[parameter.name] = parameters.get(parameter.name, "")
        else:
            strict_parameters = parameters

        # inspect if action_name's function is async or not
        start_at = time.time()
        try:
            if inspect.iscoroutinefunction(func):
                result = await func(**strict_parameters)
            else:
                result = func(**strict_parameters)
        except Exception as e:
            logger.error(
                f"ActionExecutor: {action_name} error: {e} {traceback.format_exc()}"
            )
            result = FunctionResult(
                status="failed",
                type="function",
                content=FunctionOutput(result={"error": str(e)}),
            )

        elapsed_time = time.time() - start_at
        _logger.info(
            f"ActionExecutor: {action_name} elapsed time: {elapsed_time} seconds"
        )

        # check if action's source is mcp
        if action.source == "mcp":
            parameters["__MCP_EXECUTION_TIME"] = elapsed_time
            result = await mcp_tool_post_process(result, **parameters)

        # update parameters
        action = ActionLib().get_one_action(full_name=action_name)
        result_parameters = {}
        if result.type == "function":
            for return_schema in action.result_schema:
                result_value = result.content.result.get(return_schema.name, "")
                # TODO: validate result type; now we just save it
                result_parameters[return_schema.name] = result_value
        logger.info(f"ActionExecutor: result_parameters: {result_parameters}")

        if result_parameters:
            logger.info(
                f"ActionExecutor: save action result: plan_id: {plan_id}, run_id: {run_id}, node_id: {node_id}, result_parameters: {result_parameters}"
            )
            await self.blackboard.save_action_result(
                plan_id, run_id, node_id, result_parameters
            )

        return result

    async def _eval_reference(self, parameter_value, plan_id, run_id):
        if not Blackboard.is_reference(parameter_value):
            return parameter_value

        logger.info(f"[Action Parameter Reference] parameter_value: {parameter_value}")
        try:
            return await self.blackboard.eval_reference(
                plan_id, run_id, parameter_value
            )
        except ValueError as e:
            raise ValueError(
                f"Failed to evaluate reference: {parameter_value}. Error: {e}"
            )
