"""_summary
Run action task
"""

import asyncio
import traceback
from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Optional

import redis
from loguru import logger

from src.action.model import FunctionOutput, FunctionResult
from src.session_manager.blackboard import Blackboard
from src.session_manager.memory import Memory
from src.session_manager.plan_running_status import RuntimeStatusManager
from src.session_manager.robot import Robot
from src.session_manager.running_status import ActionResultCache, ActionStatus
from src.utils.diagnostic_client import DiagnosticClient

from .executor import ActionExecutor

if TYPE_CHECKING:
    pass


CHAT_MAX_TURNS = 8


class ActionRunner:
    """
    Class ActionRunner

    This class serves as the primary executor for actions within a system, coordinating the execution of various actions,
    handling caching logic, and managing the state of ongoing actions. It integrates with components like memory,
    cloud object storage (COS), a robot interface, and a Redis client for caching action results.

    Attributes:
    - `memory` (Memory): A storage component for retaining and retrieving information"""

    def __init__(
        self,
        memory: Memory,
        cos_client,
        robot: Robot,
        runtime_manager: "RuntimeStatusManager",
        redis_client: "redis.Redis",
        blackboard: "Blackboard",
        diagnostic_client: "DiagnosticClient",
        run_step_queue: asyncio.Queue,
    ):
        self.running_actions_status: dict[str, ActionStatus] = {}
        self.action_executor = ActionExecutor(
            blackboard=blackboard,
            memory=memory,
            robot=robot,
        )
        self.memory = memory
        self._cos_client = cos_client
        self.robot = robot
        self.runtime_manager = runtime_manager
        self.action_result_cache = ActionResultCache(redis_client)
        self.max_retries = 10
        self.diagnostic_client = diagnostic_client
        self.run_step_queue = run_step_queue

    async def run(self, action_info) -> FunctionResult:
        query_id = action_info.query_id
        action_running_id = f"{action_info.run_id}_{action_info.node_id}"
        # logger.debug(
        #     f"[query_id: {query_id}] current running status {self.running_actions_status} current action_id {action_running_id}"
        # )

        logger.debug(
            f"[query_id: {query_id}] current action {action_info.action_name} {action_info.parameters}"
        )

        use_cache, result = await self._check_and_handle_cache(
            action_running_id, query_id
        )

        if not use_cache:
            result = await self._execute_action(
                action_info, action_running_id, query_id, self.run_step_queue
            )

        # update runtime status； ！！！ we should not update runtime status here, wait for the caller to update
        # self.update_runtime_status(action_info, final_function_result)

        # save run result in diagnostic info
        sid = query_id or action_running_id
        debug = result.debug
        debug["elapse_info"] = result.elapse_info

        try:
            debug["action_result"] = result.model_dump(exclude_unset=True)
        except Exception as e:
            logger.error(f"Failed to get action result: {e}")
            debug["action_result"] = {}

        if result.status == "failed":
            answer = "执行失败"
        else:
            try:
                if result.type == "function":
                    if not result.content.audio_request.content.stream:
                        answer = result.content.audio_request.content.text
                    else:
                        answer = "流式Answer"
                else:
                    if not result.content.content.stream:
                        answer = result.content.content.text
                    else:
                        answer = "流式Answer"
            except Exception as e:
                logger.error(f"Failed to get answer: {e}")
                answer = ""

        asyncio.create_task(
            self.diagnostic_client.send_diagnostic(
                sid=sid,
                client_id=self.robot.client_id,
                enterprise_id=self.robot.enterprise_id,
                device_id=self.robot.device_id,
                group_id=self.robot.group_id,
                query=f"执行Action: {action_info.action_name.split('.')[-1]}",
                answer=answer,
                model=self.robot.product_model,
                lang_str=self.robot.language,
                speaker_id=0,
                extra_info={},
                debug_info=debug,
                plan_info="",
                audio_path_list=[],
            )
        )
        return result

    async def _check_and_handle_cache(self, action_running_id, query_id):
        action_running_status = self.action_result_cache.get_action_status(
            action_running_id
        )
        use_cache = False
        result = None

        if not action_running_status:
            logger.info(f"Failed to find action status for {action_running_id}")
            return use_cache, result

        logger.info(
            f"[query_id: {query_id}] {action_running_id} action running status: {action_running_status.status}"
        )
        now = datetime.now().timestamp()
        logger.info(
            f"[query_id: {query_id}] Action {action_running_id} expired at {action_running_status.expired_at} now: {now}"
        )
        if (
            now < action_running_status.expired_at
        ):  # If the action is still running, we try to wait for it
            if action_running_status.status == "running":
                action_running_status = await self._wait_for_action_completion(
                    action_running_id, action_running_status
                )

            if action_running_status and (action_running_status.status == "succeeded"):
                result = action_running_status.running_result
                use_cache = True

        return use_cache, result

    async def _wait_for_action_completion(
        self, action_running_id, action_running_status
    ) -> Optional[ActionStatus]:
        retry = 0
        while action_running_status.status not in ["succeeded", "failed"]:
            await asyncio.sleep(0.5)
            action_running_status = self.action_result_cache.get_action_status(
                action_running_id
            )
            if datetime.now().timestamp() > action_running_status.expired_at:
                logger.error(
                    f"[action_running_id: {action_running_id}] Action running status is not succeeded or failed, expired"
                )
                return

            retry += 1
            if retry > self.max_retries:
                logger.error(
                    f"[action_running_id: {action_running_id}] Action running status is not succeeded or failed, retry limit reached"
                )
                return
        return action_running_status

    async def _execute_action(
        self,
        action_info,
        action_running_id,
        query_id,
        run_step_queue: asyncio.Queue,
    ) -> FunctionResult:
        logger.info(f"It's going to execute action {action_info.action_name}")
        cache_action_result = self._should_cache_action_result(action_info)
        if not cache_action_result:
            action_status = None
        else:
            logger.info(
                f"[query_id: {query_id}] Action {action_running_id} {action_info.action_name} is not in running status, start to execute"
            )
            action_status = ActionStatus(
                status="running",
                expired_at=(datetime.now() + timedelta(seconds=10)).timestamp(),
            )
            self.action_result_cache.set_action_status(action_running_id, action_status)

        try:
            result: FunctionResult = await self.action_executor.execute(
                action_info.action_name,
                action_info.parameters,
                plan_id=action_info.plan_id,
                run_id=action_info.run_id,
                node_id=action_info.node_id,
                run_step_queue=run_step_queue,
            )
        except Exception as e:
            logger.error(
                f"[query_id: {query_id}] Error when execute action: {e} {traceback.format_exc()}"
            )
            result = FunctionResult(
                status="failed",
                type="function",
                content=FunctionOutput(),
            )

        if action_status:
            action_status.running_result = result
            action_status.status = result.status
            self.action_result_cache.set_action_status(action_running_id, action_status)

        return result

    def _should_cache_action_result(self, action_info):
        return (
            action_info.run_id
            and action_info.run_id != "GGGGGGGGG"
            and action_info.node_id
            and action_info.node_id != "GGGGGGGGG"
            and action_info.action_name
        )
