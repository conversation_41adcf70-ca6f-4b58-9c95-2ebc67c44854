from typing import Optional

import aiohttp
from livekit.agents import llm, utils
from loguru import logger

from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.utils.feishu_alarm import send_feishu_alarm
from src.utils.llm import async_stream_generator
from src.action.model import RequestLLMConfig


class LLM(llm.LLM):
    def __init__(
        self,
        *,
        model_name: str | None = None,
        base_url: str | None = None,
        api_key: str | None = None,
        session: aiohttp.ClientSession | None = None,
        memory: Memory | None = None,
        robot: Robot | None = None,
    ) -> None:
        self._session = session or utils.http_context.http_session()
        self.request_url = base_url
        self.api_key = api_key
        self.memory = memory
        self.robot = robot
        self.model_name = model_name

    def chat(self, messages: list, llm_config: Optional[RequestLLMConfig] = None):
        try:
            return async_stream_generator(
                request_url=self.request_url,
                api_key=self.api_key,
                model_name=self.model_name,
                messages=messages,
                llm_stream_config=llm_config,
            )
        except Exception as e:
            err_msg = f"[Realtime say] Failed to generate messages: error: {e} {e.__class__.__name__}"
            logger.error(err_msg)
            send_feishu_alarm(text=err_msg)
