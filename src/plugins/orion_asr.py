import dataclasses
import io
import os
import time
import wave
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from dataclasses import dataclass, field
from typing import TYPE_CHECKING, List
import traceback
import asyncio

import aiohttp
import librosa
import shortuuid
import soundfile
from livekit import agents
from livekit.agents import Plugin, stt, utils
from livekit.agents.log import logger
from livekit.agents.utils import AudioBuffer

from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.utils.cos_client import CosClient
from src.utils.async_utils import callback
from src.settings import agent_setting

if TYPE_CHECKING:
    from src.utils.cos_client import CosClient


__all__ = [
    "STT",
]

TARGET_SAMPLE_RATE = 16000
RESPONSE_SUCCESS_CODE = 0
EMPTY_TRANSCRIPTION_CODE = 100002
REQUEST_TIMEOUT = 60  # seconds


class OrionPlugin(Plugin):
    def __init__(self) -> None:
        super().__init__(__name__, "0.1.0", __package__)

    def download_files(self) -> None:
        pass


Plugin.register_plugin(OrionPlugin())


@dataclass
class SpeechEvent:
    type: stt.SpeechEventType
    alternatives: List[stt.SpeechData] = field(default_factory=list)
    audio_id: str = ""
    elapsed: float = 0.0


@dataclass
class _STTOptions:
    language: str
    api_key: str
    endpoint: str


class STT(stt.STT):
    def __init__(
        self,
        *,
        language: str = "zh_CN",
        api_key: str | None = None,
        base_url: str | None = None,
        http_session: aiohttp.ClientSession | None = None,
        cos_client: CosClient | None = None,
        memory: Memory | None = None,
        robot: Robot | None = None,
    ):
        super().__init__(
            capabilities=stt.STTCapabilities(streaming=False, interim_results=False)
        )
        api_key = api_key or os.environ.get("ORION_ASR_API_KEY")
        if not api_key:
            raise ValueError("ORION_ASR_API_KEY must be set")

        self._opts = _STTOptions(
            language=language,
            api_key=api_key,
            endpoint=base_url,
        )
        self._session = http_session
        self._cos_client = cos_client
        self._memory = memory
        self._robot = robot
        self._executor = ThreadPoolExecutor(max_workers=4)

    def _ensure_session(self) -> aiohttp.ClientSession:
        if not self._session:
            self._session = utils.http_context.http_session()

        return self._session

    def _sanitize_options(self, *, language: str | None = None) -> _STTOptions:
        config = dataclasses.replace(self._opts)
        config.language = language or config.language
        return config

    async def recognize(
        self,
        buffer: AudioBuffer,
        *,
        language: str | None = None,
        audio_id: str | None = None,
    ) -> SpeechEvent:
        start = time.time()
        config = self._sanitize_options(language=language)

        buffer = agents.utils.merge_frames(buffer)
        logger.info(f"{audio_id} Orion STT Merge frames elapsed {time.time() - start}")

        # Use the new _process_audio method
        if not audio_id:
            audio_id = shortuuid.ShortUUID().random(length=8)
            logger.warning(f"Audio ID is not provided, generating a new one {audio_id}")

        wav_data = await self._process_audio(buffer, audio_id)
        logger.info(
            f"{audio_id} Orion STT audio processing elapsed {time.time() - start}"
        )

        form = aiohttp.FormData()
        form.add_field("audio_file", wav_data, filename="audio.wav")
        form.add_field("lang", config.language)
        # form.add_field("client_id", "orion.ovs.client.1597807388298")
        form.add_field("enterprise_id", self._robot.enterprise_id)

        # form.add_field("group_id", "ovs.group.158623137963780")
        # form.add_field("pid", "100010")
        form.add_field("pid", self._robot.product_id)
        form.add_field("device_id", self._robot.device_id)
        form.add_field("extra_id", audio_id)
        logger.info(f"{audio_id} Before post Orion STT elapsed {time.time() - start}")
        logger.info(
            f"""{audio_id} Orion STT post {config.endpoint} parameters:
lang: {config.language}
enterprise_id: {self._robot.enterprise_id}
device_id: {self._robot.device_id}
extra_id: {audio_id}"""
        )

        # save audio file to COS
        env = agent_setting.env
        today = time.strftime("%Y-%m-%d", time.localtime())
        file_path = f"{env}/{self._robot.device_id}/{today}/{audio_id}.wav"
        future = self._executor.submit(
            self._cos_client.upload_file, wav_data, file_path
        )
        future.add_done_callback(callback)

        # cache file path with audio_id
        future = self._executor.submit(self._cache_file_path, audio_id, file_path)
        future.add_done_callback(callback)

        try:
            async with self._ensure_session().post(
                self._opts.endpoint,
                headers={"asr-secret-key": f"{config.api_key}"},
                data=form,
                timeout=aiohttp.ClientTimeout(total=REQUEST_TIMEOUT),
            ) as resp:
                if resp.status != 200:
                    raise ValueError(
                        f"AudioID: {audio_id} Unexpected response: status:{resp.status} {await resp.text()}"
                    )

                try:
                    data = await resp.json()
                except Exception as e:
                    logger.error(
                        f"AudioID: {audio_id} Error in parsing Orion STT response: {e} {traceback.format_exc()} {await resp.text()}"
                    )
                    raise ValueError(f"{audio_id} Failed to parse response to json")

                if not data:
                    raise ValueError(
                        f"AudioID: {audio_id} Received empty response from asr server {self._opts.endpoint}"
                    )

                status = data.get("status", 100000)
                if status == EMPTY_TRANSCRIPTION_CODE:
                    logger.info(
                        f"AudioID: {audio_id} Orion STT received {EMPTY_TRANSCRIPTION_CODE}, empty transcription Elapsed: {time.time() - start}"
                    )
                    raise ValueError(
                        f"AudioID:  {audio_id} Unexpected response: {data} {data.get('status')}"
                    )

                if data.get("status", 100000) != RESPONSE_SUCCESS_CODE:
                    raise ValueError(
                        f"AudioID: {audio_id} Unexpected response: {data} {data.get('status')}"
                    )

                logger.info(
                    f"AudioID: {audio_id} Orion STT elapsed {time.time() - start} {data}"
                )
                speech_event = _transcription_to_speech_event(
                    data, config.language, audio_id
                )

                speech_event.elapsed = time.time() - start
                logger.info(
                    f"AudioID: {audio_id} Orion STT elapsed total: {speech_event.elapsed} {data}"
                )
                return speech_event
        except asyncio.TimeoutError:
            logger.error(
                f"AudioID: {audio_id} Timeout error while calling Orion STT API (>{REQUEST_TIMEOUT}s)"
            )
            return SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(
                        text=f"/[ASR ERROR, Audio ID: {audio_id}, Error: Request timeout after {REQUEST_TIMEOUT}s]",
                        language=config.language,
                    )
                ],
                audio_id=audio_id,
            )
        except Exception as e:
            logger.error(
                f"AudioID: {audio_id} Error in Orion STT: {e} {traceback.format_exc()}"
            )
            return SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[
                    stt.SpeechData(
                        text=f"/[ASR ERROR, Audio ID: {audio_id}, Error: {str(e)}]",
                        language=config.language,
                    )
                ],
                audio_id=audio_id,
            )

    def _cache_file_path(self, audio_id: str, file_path: str) -> None:
        """Cache file path in Redis"""
        logger.info(f"AudioID: {audio_id} Caching file path {file_path}")
        try:
            self._memory.redis_client.setex(
                f"asr_file_path_{agent_setting.env}_{audio_id}",
                agent_setting.asr_file_path_cache_ttl,
                file_path,
            )
        except Exception as e:
            logger.error(f"AudioID: {audio_id} Error caching file path: {e}")

    @classmethod
    async def _process_audio(cls, buffer: AudioBuffer, audio_id: str, remove_header: bool = False) -> bytes:
        """Process audio buffer and return WAV data and generated audio ID."""
        # Convert to WAV
        io_buffer = io.BytesIO()
        with wave.open(io_buffer, "wb") as wav:
            wav.setnchannels(buffer.num_channels)
            wav.setsampwidth(2)  # 16-bit
            wav.setframerate(buffer.sample_rate)
            wav.writeframes(buffer.data)

        # Resample to 16kHz
        y, sr = librosa.load(io.BytesIO(io_buffer.getvalue()), sr=TARGET_SAMPLE_RATE)

        # Convert back to WAV
        output_buffer = io.BytesIO()
        output_buffer.name = "audio.wav"  # Required for FormData
        soundfile.write(data=y, samplerate=sr, file=output_buffer)

        if remove_header:
            return output_buffer.getvalue()[44:]  # SKIP wav head
        return output_buffer.getvalue()


def _transcription_to_speech_event(
    transcription: dict, language: str, audio_id: str
) -> SpeechEvent:
    if (
        transcription["data"]["asr_content"]["nbest"] == None
        or transcription["data"]["asr_param"][
            "err_no"
        ]  # filtered noise: 'nbest': ['嗯'],  'err_no': -2020022, 'error': 'decoder server: 语音识别为噪音
    ):
        return SpeechEvent(
            type=stt.SpeechEventType.FINAL_TRANSCRIPT,
            alternatives=[stt.SpeechData(text="", language=language)],
        )

    try:
        paraformer_asr = transcription["data"]["asr_content"]["nbest"][0]
    except Exception:
        paraformer_asr = ""
        logger.error(
            f"AudioID: {audio_id} Error in parsing paraformer_asr transcription {transcription}"
        )

    try:
        wenet_asr = transcription["data"]["asr_content"]["wenet_result"][0]
    except Exception:
        logger.error(
            f"AudioID: {audio_id} Error in parsing wenet_asr transcription {transcription}"
        )
        wenet_asr = ""

    if not paraformer_asr and not wenet_asr:
        combined_asr_result = ""

    elif not paraformer_asr:
        combined_asr_result = f"""{wenet_asr}"""

    elif not wenet_asr:
        combined_asr_result = f"""{paraformer_asr}"""

    else:
        combined_asr_result = f"""{paraformer_asr}"""

    return SpeechEvent(
        type=stt.SpeechEventType.FINAL_TRANSCRIPT,
        alternatives=[stt.SpeechData(text=combined_asr_result, language=language)],
        audio_id=audio_id,
    )
