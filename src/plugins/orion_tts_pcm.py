from __future__ import annotations

import os
import time
import uuid
import urllib.parse
from dataclasses import dataclass

import aiohttp
from yarl import URL
from livekit.agents import tts, utils
from livekit import rtc
from loguru import logger

from src.session_manager.robot import Robot


ORION_TTS_SAMPLE_RATE = 16000
ORION_TTS_CHANNELS = 1


@dataclass
class _TTSOptions:
    type: str
    endpoint: str
    speed: float
    lan: str


class OrionTTS(tts.TTS):
    def __init__(
        self,
        *,
        robot: Robot,
        type: str = "xiaoya-sweet",
        speed: float = 6.0,
        base_url: str | None = None,
        client: aiohttp.ClientSession | None = None,
        endpoint: str | None = None,
    ) -> None:
        super().__init__(
            capabilities=tts.TTSCapabilities(
                streaming=False,
            ),
            sample_rate=ORION_TTS_SAMPLE_RATE,
            num_channels=ORION_TTS_CHANNELS,
        )

        self._client = client or aiohttp.ClientSession(base_url=base_url)
        self._opts = _TTSOptions(
            type=type,
            endpoint=os.path.join(base_url, endpoint or "tts/v1/text2audio"),
            speed=speed,
            lan=robot.language,
        )
        self._robot = robot

    def synthesize(self, text: str) -> "ChunkedStream":
        """
        :param text:
        :return:
        """
        logger.info(f"synthesize text: {text}")
        return ChunkedStream(self._client, text, self._opts, self._robot)


class ChunkedStream(tts.ChunkedStream):
    def __init__(
        self,
        client: aiohttp.ClientSession,
        text: str,
        opts: _TTSOptions,
        robot: Robot,
    ) -> None:
        super().__init__()
        self._client, self._opts, self._text, self._robot = client, opts, text, robot

    @utils.log_exceptions(logger=logger)
    async def _main_task(self):
        request_id = utils.shortuuid()
        segment_id = utils.shortuuid()

        logger.debug(f"[TTS] synthesize text: {self._text}")
        start_time = time.time()

        sn = str(uuid.uuid4())

        params = {
            "tex": self._text,
            "aue": 4,  # PCM格式
            "sn": sn,
            "spd": int(self._robot.speech_rate),
            "lan": self._robot.language,
            "type": self._robot.spokesman,
            "sty": 1,
            "vol": 30,
            "ctp": 1,
            "idx": 1,
            "phonemes": 0,
            "pdt": self._robot.product_id,
            "usevoc": 0,
            "cuid": self._robot.device_id,
            "fmt": "chunked",
            "RetryTimes": 0,
            "rate": 2,
            # "token": agent_setting.tts_token,  TODO: temporary disable
            "enterprise_id": self._robot.enterprise_id,
            "client_id": self._robot.client_id,
            "source": 100,  # 100: agentos
        }

        encoded_text = urllib.parse.urlencode(params, quote_via=urllib.parse.quote)
        url = f"{self._opts.endpoint}?{encoded_text}"

        async with self._client.get(URL(url, encoded=True)) as resp:
            # if not resp.ok:
            #     await send_feishu_alarm(f"环境： {agent_setting.env} TTS调用异常，地址：{resp.request_info.real_url}, error: {str(resp.reason)}")
            count = 0
            buffer = bytearray()  # 累加音频数据的缓冲区
            min_chunk_size = 1024 * 2  # 最小chunk大小（1024样本 * 2字节）

            logger.info(f"[TTS: {sn}] request {resp.request_info.real_url}")
            async for data in resp.content.iter_chunked(
                4096
            ):  # Use iter_chunked instead of iter_bytes
                if count % 10 == 0:
                    logger.info(
                        f"[TTS: {sn}] chunked data: {count} elapsed: {time.time() - start_time:.2f}s"
                    )

                count += 1
                buffer.extend(data)  # 累加数据到缓冲区

                # 当缓冲区有足够数据时，发送音频帧
                while len(buffer) >= min_chunk_size:
                    # 计算PCM样本数：字节数 / (声道数 * 每样本字节数)
                    # 假设16-bit PCM，每个样本2字节
                    samples_per_channel = min_chunk_size // (ORION_TTS_CHANNELS * 2)

                    # 提取一个完整的音频帧数据
                    frame_data = bytes(buffer[:min_chunk_size])
                    buffer = buffer[min_chunk_size:]  # 移除已处理的数据

                    audio_frame = rtc.AudioFrame(
                        sample_rate=ORION_TTS_SAMPLE_RATE,
                        num_channels=ORION_TTS_CHANNELS,
                        samples_per_channel=samples_per_channel,
                        data=frame_data,
                    )

                    self._event_ch.send_nowait(
                        tts.SynthesizedAudio(
                            request_id=request_id,
                            segment_id=segment_id,
                            frame=audio_frame,
                            delta_text=self._text,
                        )
                    )

            # 处理缓冲区中剩余的数据
            if len(buffer) > 0:
                samples_per_channel = len(buffer) // (ORION_TTS_CHANNELS * 2)
                if samples_per_channel > 0:
                    audio_frame = rtc.AudioFrame(
                        sample_rate=ORION_TTS_SAMPLE_RATE,
                        num_channels=ORION_TTS_CHANNELS,
                        samples_per_channel=samples_per_channel,
                        data=bytes(buffer),
                    )

                    self._event_ch.send_nowait(
                        tts.SynthesizedAudio(
                            request_id=request_id,
                            segment_id=segment_id,
                            frame=audio_frame,
                            delta_text=self._text,
                        )
                    )
