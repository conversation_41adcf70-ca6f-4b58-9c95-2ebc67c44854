"""
Orion VAD v2

This is a simple VAD implementation that detects speech by events from the client.
"""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import AsyncGenerator, List
import queue

import numpy as np
from livekit import agents, rtc
from livekit.agents.vad import VADEventType
from livekit.agents import utils
from loguru import logger


@dataclass
class _VADOptions:
    min_speech_duration: float
    min_silence_duration: float
    padding_duration: float
    max_buffered_speech: float
    activation_threshold: float
    sample_rate: int


@dataclass
class VADEvent:
    type: VADEventType
    """type of the event"""
    samples_index: int
    """index of the samples when the event was fired"""
    speech_duration: float
    """duration of the speech in seconds"""
    silence_duration: float
    """duration of the silence in seconds"""
    frames: List[rtc.AudioFrame] = field(default_factory=list)
    """list of audio frames of the speech"""
    probability: float = 0.0
    """smoothed probability of the speech (only for INFERENCE_DONE event)"""
    inference_duration: float = 0.0
    """duration of the inference in seconds (only for INFERENCE_DONE event)"""
    speaking: bool = False
    """whether speech was detected in the frames"""
    audio_id: str = ""
    """audio id from client"""


class OrionVAD(agents.vad.VAD):
    @classmethod
    def load(
        cls,
        *,
        min_speech_duration: float = 0.05,
        min_silence_duration: float = 0.25,
        padding_duration: float = 0.1,
        max_buffered_speech: float = 60.0,
        activation_threshold: float = 0.5,
        sample_rate: int = 16000,
        vad_event_queue: queue.Queue,
    ) -> "VAD":
        opts = _VADOptions(
            min_speech_duration=min_speech_duration,
            min_silence_duration=min_silence_duration,
            padding_duration=padding_duration,
            max_buffered_speech=max_buffered_speech,
            activation_threshold=activation_threshold,
            sample_rate=sample_rate,
        )
        return cls(opts=opts, vad_event_queue=vad_event_queue)

    def __init__(self, *, opts: _VADOptions, vad_event_queue: queue.Queue) -> None:
        super().__init__(capabilities=agents.vad.VADCapabilities(update_interval=0.032))
        self._opts = opts
        self._vad_event_queue = vad_event_queue

    def stream(self) -> "VADStream":
        return VADStream(self._opts, self._vad_event_queue)


class VADStream(agents.vad.VADStream):
    def __init__(self, opts: _VADOptions, vad_event_queue: queue.Queue) -> None:
        super().__init__()
        self._opts = opts

        # Speech detection state
        self._speaking = False
        self._speech_duration = 0.0
        self._silence_duration = 0.0
        self._current_sample = 0
        self._speech_frames = 0
        self._silence_frames = 0

        # Use list for dynamic buffer
        self._speech_buffer: list = []
        self._og_sample_rate = 0
        # Use list for temp buffer, save the last N frames
        self._temp_buffer: list = []
        self._temp_buffer_size = 200
        self._network_delay_frame_count = 40

        self._vad_event_queue = vad_event_queue
        self._sid = ""

    def _initialize_buffer(self, sample_rate: int) -> None:
        """Initialize the speech buffer with the given sample rate"""
        self._og_sample_rate = sample_rate
        self._speech_buffer = []

    def _copy_speech_buffer(self) -> rtc.AudioFrame:
        """Copy the speech buffer to an AudioFrame"""
        if not self._speech_buffer:
            logger.critical("[Vad] Failed to copy speech buffer, buffer is empty")
            return rtc.AudioFrame(
                sample_rate=self._og_sample_rate,
                num_channels=1,
                samples_per_channel=0,
                data=b"",
            )

        logger.info(
            f"[Vad] {self._sid} Copy speech buffer, size: {len(self._speech_buffer)}"
        )

        # Convert list of samples to numpy array then to bytes
        speech_data = np.array(self._speech_buffer, dtype=np.int16).tobytes()
        return rtc.AudioFrame(
            sample_rate=self._og_sample_rate,
            num_channels=1,
            samples_per_channel=len(self._speech_buffer),
            data=speech_data,
        )

    def _copy_frame_to_speech_buffer(self, frame: rtc.AudioFrame) -> None:
        """Copy frame data to speech buffer"""
        if not self._og_sample_rate:
            self._initialize_buffer(frame.sample_rate)

        frame_data = np.frombuffer(frame.data, dtype=np.int16)
        # Simply extend the list with new samples
        self._speech_buffer.extend(frame_data)
        # logger.info(f"[Vad] Frame data: {frame_data}")

    def _copy_frame_to_temp_buffer(self, frame: rtc.AudioFrame) -> None:
        """Copy frame data to temp buffer"""
        if not self._og_sample_rate:
            self._initialize_buffer(frame.sample_rate)

        frame_data = np.frombuffer(frame.data, dtype=np.int16)
        # logger.debug(f"[Vad] Frame data size: {len(frame_data)}")  # 480

        # 根据每帧的采样点数, 计算temp buffer的大小
        max_temp_buffer_len = self._temp_buffer_size * len(frame_data)

        # 如果temp buffer已满，移除前面的数据，保持固定大小
        if len(self._temp_buffer) >= max_temp_buffer_len:
            self._temp_buffer = self._temp_buffer[
                -max_temp_buffer_len + len(frame_data) :
            ]  # 只移除一个frame data的位置
        self._temp_buffer.extend(frame_data)
        # logger.debug(f"[Vad] Temp buffer size: {len(self._temp_buffer)}")

    def _copy_temp_buffer_to_speech_buffer(self, copy_size: int) -> None:
        """Copy temp buffer to speech buffer"""
        self._speech_buffer = []
        if copy_size > len(self._temp_buffer):
            copy_size = len(self._temp_buffer)
        # copy temp buffer to speech buffer
        self._speech_buffer.extend(self._temp_buffer[-copy_size:])
        self._reset_temp_buffer()

    def _reset_speech_buffer(self) -> None:
        """Reset the buffer by clearing the list"""
        self._speech_buffer = []

    def _reset_temp_buffer(self) -> None:
        """Reset the temp buffer by clearing the list"""
        self._temp_buffer = []

    def _reset_all(self) -> None:
        """Reset all buffers and variables"""
        self._reset_speech_buffer()
        self._reset_temp_buffer()
        self._sid = ""
        self._speech_duration = 0.0
        self._silence_duration = 0.0

    def handle_speaking_vad_event(self, vad_event: dict, frame: rtc.AudioFrame) -> None:
        logger.info(f"[Vad] {self._sid} Vad event: {vad_event}")
        if vad_event.get("type") == "vad_end":  # vad end event
            if vad_event.get("sid") == self._sid:  # correct event
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.END_OF_SPEECH,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        frames=[self._copy_speech_buffer()],
                        speaking=False,
                        audio_id=self._sid,
                    )
                )
                self._speaking = False
                self._reset_all()
                logger.info(f"[Vad] {self._sid} Vad end event, finish current speech")
            else:  # wrong event, ignore
                logger.error(f"[Vad] Wrong event, ignore {vad_event}")
                self._copy_frame_to_speech_buffer(frame)
                return
        elif vad_event.get("type") == "vad_start":  # wrong event
            if vad_event.get("sid") == self._sid:  # duplicate event, ignore
                self._copy_frame_to_speech_buffer(frame)
                logger.error(f"[Vad] Duplicate event, ignore {vad_event}")
                return
            else:  # send current speech and start new one
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.END_OF_SPEECH,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        frames=[self._copy_speech_buffer()],
                        speaking=False,
                        audio_id=self._sid,
                    )
                )
                self._reset_all()

                # start new speech
                self._sid = vad_event.get("sid")
                self._speaking = True
                self._copy_frame_to_speech_buffer(frame)  # skip copy temp buffer
                logger.info(
                    f"[Vad] {self._sid} Encountered re-start event, start new speech"
                )
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.START_OF_SPEECH,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        frames=[self._copy_speech_buffer()],
                        speaking=True,
                        audio_id=self._sid,
                    )
                )

    def handle_non_speaking_vad_event(
        self, vad_event: dict, frame: rtc.AudioFrame
    ) -> None:
        logger.info(f"[Vad] {self._sid} Vad event: {vad_event}")
        if vad_event.get("type") == "vad_end":  # wrong event, ignore
            logger.error(f"[Vad Non-speaking] Get wrong event, ignore {vad_event}")
            return
        else:  # vad start event
            self._sid = vad_event.get("sid")
            required_frame_count = vad_event.get("frames", 140)
            copy_frame_count = required_frame_count + self._network_delay_frame_count

            if copy_frame_count > self._temp_buffer_size:
                logger.warning(
                    f"[Vad] Copy frame count exceeds temp buffer size, use temp buffer size instead: {copy_frame_count} > {self._temp_buffer_size}, required: {required_frame_count}, network delay: {self._network_delay_frame_count}"
                )
                copy_frame_count = self._temp_buffer_size

            self._reset_speech_buffer()
            self._copy_temp_buffer_to_speech_buffer(copy_frame_count * 480)  # fixed 480
            self._speech_duration = 0.0
            self._silence_duration = 0.0
            self._speaking = True
            self._copy_frame_to_speech_buffer(frame)
            logger.info(f"[Vad] {self._sid} vad start event, start new speech")
            self._event_ch.send_nowait(
                VADEvent(
                    type=VADEventType.START_OF_SPEECH,
                    samples_index=self._current_sample,
                    silence_duration=self._silence_duration,
                    speech_duration=self._speech_duration,
                    frames=[self._copy_speech_buffer()],
                    speaking=True,
                    audio_id=self._sid,
                )
            )

    @utils.log_exceptions(logger=logger)
    async def _main_task(self) -> AsyncGenerator:
        frame_duration = 0.01  # 10ms per frame
        count = 0
        async for frame in self._input_ch:
            if not isinstance(frame, rtc.AudioFrame):
                continue

            count += 1

            if self._speaking:  # currently speaking, check vad event queue
                if self._vad_event_queue.qsize() > 0:
                    vad_event = self._vad_event_queue.get()
                    self.handle_speaking_vad_event(vad_event, frame)
                else:  # if there is no vad event, continue with normal processing
                    self._copy_frame_to_speech_buffer(frame)
                    self._current_sample += frame.samples_per_channel
                    self._speech_duration += frame_duration

                    # check if speech buffer is too long
                    if self._speech_duration >= self._opts.max_buffered_speech:
                        # terminate current speech and set speaking to false
                        self._copy_frame_to_speech_buffer(frame)
                        logger.info(
                            f"[Vad] {self._sid} Encountered overflow, terminate current speech"
                        )
                        self._event_ch.send_nowait(
                            VADEvent(
                                type=VADEventType.END_OF_SPEECH,
                                samples_index=self._current_sample,
                                silence_duration=self._silence_duration,
                                speech_duration=self._speech_duration,
                                frames=[self._copy_speech_buffer()],
                                speaking=True,
                                audio_id=self._sid,
                            )
                        )
                        self._reset_all()
                        self._speaking = False
            else:
                if self._vad_event_queue.qsize() > 0:
                    vad_event = self._vad_event_queue.get()
                    self.handle_non_speaking_vad_event(vad_event, frame)
                else:  # if there is no vad event, continue with normal processing
                    self._copy_frame_to_temp_buffer(frame)
                    self._silence_duration += frame_duration

            # Send inference event for every 20 frames (200ms)
            if count != 0 and count % 20 == 0:
                # logger.info(f"[Vad] Audio id: {self._sid} Send inference event, speaking: {self._speaking}")
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.INFERENCE_DONE,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        probability=int(self._speaking),
                        inference_duration=0.0,  # Not tracking actual inference time
                        speaking=self._speaking,
                        audio_id=self._sid,
                    )
                )
