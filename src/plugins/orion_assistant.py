# 废弃
# import json
# import time
# from typing import AsyncIterable, Optional
#
# import aiohttp
# from livekit.agents import llm, utils
# from loguru import logger
#
# from src.action.model import RequestLLMConfig
# from src.session_manager.memory import Memory
# from src.session_manager.robot import Robot
# from src.settings import agent_setting
# from src.utils.aio_sse_client import aiosseclient
# from src.utils.feishu_alarm import send_feishu_alarm
# from src.utils.llm import async_stream_generator
#
#
# class OpenAssistant(llm.LLM):
#     def __init__(
#         self,
#         *,
#         model_name: str | None = None,
#         base_url: str | None = None,
#         api_key: str | None = None,
#         session: aiohttp.ClientSession | None = None,
#         memory: Memory | None = None,
#         robot: Robot | None = None,
#     ) -> None:
#         self._session = session or utils.http_context.http_session()
#         self.request_url = base_url
#         self.api_key = api_key
#         self.memory = memory
#         self.robot = robot
#         self.model_name = model_name
#         self.assistant_id = None
#
#     async def _assistant_stream_generator(
#         self, messages: list, llm_stream_config: Optional[RequestLLMConfig] = None
#     ) -> AsyncIterable[bytes]:
#         # replace role from system to user
#         new_messages = []
#         for message in messages:
#             if message.get("role") == "system":
#                 new_messages.append({"role": "user", "content": message["content"]})
#             else:
#                 new_messages.append(message)
#         logger.info(f"Assistant messages: {messages}")
#
#         count = 0
#         start_time = time.time()
#         async for event in aiosseclient(
#             url=agent_setting.speech_bridge_sse_url,
#             session=self._session,
#             data={
#                 "messages": new_messages,
#                 "temperature": llm_stream_config.temperature,
#                 "sid": llm_stream_config.business_info.sid,
#                 "project": "agentos",  # fixed value
#             },
#             headers={
#                 "enterprise_id": llm_stream_config.business_info.enterprise_id,
#                 "sid": llm_stream_config.business_info.sid,
#                 "client_id": llm_stream_config.business_info.client_id,
#                 "device_id": llm_stream_config.business_info.device_id,
#                 "Content-Type": "application/json",
#             },
#             raise_for_status=False,
#         ):
#             try:
#                 data = event.data
#                 data = json.loads(data)
#                 result = (
#                     data.get("data", {})
#                     .get("data", {})
#                     .get("text", {})
#                     .get("content", "")
#                 )
#                 if "platform is empty" in result:
#                     # 不抛出异常，而是返回一个特殊的标记
#                     logger.warning("Platform is empty, returning special marker")
#                     yield b"__PLATFORM_EMPTY__"
#                     return
#             except json.JSONDecodeError:
#                 logger.error(f"Failed to parse data: {data}")
#                 continue
#
#             if count == 0:
#                 logger.info(
#                     f"[Orion Assistant] First chunk of speech bridge sse elapsed time: {time.time() - start_time} seconds"
#                 )
#             count += 1
#
#             if not result:
#                 continue
#
#             yield result.encode("utf-8")
#
#     async def check_enterprise_permission(self, enterprise_id: str) -> bool:
#         try:
#             async with self._session.get(
#                 agent_setting.speech_bridge_enterprise_validator_url,
#                 params={
#                     "enterprise_id": enterprise_id,
#                     "project": "agentos",
#                 },
#                 headers={
#                     "speech-secret-key": agent_setting.diagnostic_secret_key,
#                     "Accept": "application/json",  # 明确指定接受JSON响应
#                     "Content-Type": "application/json",
#                 },
#             ) as response:
#                 if response.status != 200:
#                     logger.error(
#                         f"Enterprise permission check failed: HTTP {response.status}"
#                     )
#                     return False
#
#                 # 检查内容类型
#                 content_type = response.headers.get("Content-Type", "")
#                 if "application/json" not in content_type:
#                     logger.warning(f"Expected JSON response but got {content_type}")
#                     text_content = await response.text()
#                     try:
#                         # 尝试手动解析JSON，即使内容类型不是JSON
#                         data = json.loads(text_content)
#                     except json.JSONDecodeError:
#                         logger.error("Failed to parse response as JSON")
#                         return False
#                 else:
#                     data = await response.json()
#
#                 status = data.get("status")
#                 if status != 0:
#                     logger.error(
#                         f"Enterprise permission check failed, msg:{data.get('msg', '')}"
#                     )
#                     return False
#
#                 assistant_data = data.get("data", {})
#                 assistant_status = assistant_data.get("status")
#                 if assistant_status != "active":
#                     logger.error(
#                         f"Enterprise assistant status abnormal: {assistant_status}"
#                     )
#                     return False
#
#                 self.assistant_id = assistant_data.get("assistant_id")
#                 return True
#
#         except Exception as e:
#             logger.error(f"Enterprise permission check error: {str(e)}")
#             return False
#
#     async def chat(self, messages: list, llm_config: Optional[RequestLLMConfig] = None):
#         if llm_config.file_search and await self.check_enterprise_permission(
#             llm_config.business_info.enterprise_id
#         ):
#             try:
#                 # 创建一个包装生成器，用于检测特殊标记
#                 async def wrapper_generator():
#                     async for chunk in self._assistant_stream_generator(
#                         messages, llm_stream_config=llm_config
#                     ):
#                         if chunk == b"__PLATFORM_EMPTY__":
#                             logger.info(
#                                 "Detected platform empty marker, switching to async_stream_generator"
#                             )
#                             # 当检测到特殊标记时，切换到async_stream_generator
#                             async for fallback_chunk in async_stream_generator(
#                                 request_url=self.request_url,
#                                 api_key=self.api_key,
#                                 model_name=self.model_name,
#                                 messages=messages,
#                                 llm_stream_config=llm_config,
#                             ):
#                                 yield fallback_chunk
#                             return
#                         yield chunk
#
#                 return wrapper_generator()
#             except Exception as e:
#                 err_msg = f"[Realtime say - Assistant._assistant_stream_generator] Failed to generate messages: error: {e} {e.__class__.__name__}"
#                 logger.error(err_msg)
#                 send_feishu_alarm(text=err_msg)
#                 # 当_assistant_stream_generator失败时，继续执行async_stream_generator
#                 logger.info("Falling back to async_stream_generator")
#
#         try:
#             return async_stream_generator(
#                 request_url=self.request_url,
#                 api_key=self.api_key,
#                 model_name=self.model_name,
#                 messages=messages,
#                 llm_stream_config=llm_config,
#             )
#         except Exception as e:
#             err_msg = f"[Realtime say - Assistant.async_stream_generator] Failed to generate messages: error: {e} {e.__class__.__name__}"
#             logger.error(err_msg)
#             send_feishu_alarm(text=err_msg)
#
#
# if __name__ == "__main__":
#     import asyncio
#     from dataclasses import dataclass
#
#     @dataclass
#     class BusinessInfo:
#         enterprise_id: str = "orion.ovs.entprise.2465488082"
#         client_id: str = "orion.ovs.client.1514259512471"
#         device_id: str = "MC1BCN2L1102473143DF"
#         sid: str = "07839ca5-0ee4-4db9-bb7a-25029a10e61c"
#
#     @dataclass
#     class TestLLMStreamConfig:
#         business_info: BusinessInfo
#         file_search: bool = True
#         temperature: float = 0.7
#
#     async def test_assistant():
#         # 创建 aiohttp session
#         async with aiohttp.ClientSession() as session:
#             # 创建测试消息
#             test_messages = [
#                 {"role": "system", "content": "You are a helpful assistant"},
#                 {"role": "user", "content": "Hello"},
#                 {"role": "assistant", "content": "Hi there!"},
#                 {"role": "user", "content": "介绍下家乐福"},
#             ]
#
#             # 创建 OpenAssistant 实例，传入 session
#             assistant = OpenAssistant(session=session)
#             ret = await assistant.check_enterprise_permission("xxx")
#             print(f"ret: {ret}")
#
#             # 创建测试配置
#             config = TestLLMStreamConfig(
#                 business_info=BusinessInfo(),
#                 file_search=True,
#             )
#
#             # 测试流式生成
#             async_generator = assistant._assistant_stream_generator(
#                 messages=test_messages, llm_stream_config=config
#             )
#
#             print("Starting stream test...")
#             try:
#                 async for chunk in async_generator:
#                     print(f"Received chunk: {chunk}")
#             except Exception as e:
#                 print(f"Error occurred: {e}")
#
#     # 运行测试
#     asyncio.run(test_assistant())
