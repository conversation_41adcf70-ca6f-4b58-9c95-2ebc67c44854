"""
Orion VAD v2

This is a simple VAD implementation for stream ASR.
"""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import AsyncGenerator, List, Literal
from enum import Enum, unique
import queue

from livekit import agents, rtc
from livekit.agents.vad import VADEventType
from livekit.agents import utils
from loguru import logger


@dataclass
class _VADOptions:
    min_speech_duration: float
    min_silence_duration: float
    padding_duration: float
    max_buffered_speech: float
    activation_threshold: float
    sample_rate: int


@dataclass
class VADEvent:
    type: VADEventType
    """type of the event"""
    samples_index: int
    """index of the samples when the event was fired"""
    speech_duration: float
    """duration of the speech in seconds"""
    silence_duration: float
    """duration of the silence in seconds"""
    frames: List[rtc.AudioFrame] = field(default_factory=list)
    """list of audio frames of the speech"""
    probability: float = 0.0
    """smoothed probability of the speech (only for INFERENCE_DONE event)"""
    inference_duration: float = 0.0
    """duration of the inference in seconds (only for INFERENCE_DONE event)"""
    speaking: bool = False
    """whether speech was detected in the frames"""
    audio_id: str = ""
    """audio id from client"""


@unique
class ASRStreamEventType(str, Enum):
    START_OF_SPEECH = "start_of_speech"
    END_OF_SPEECH = "end_of_speech"
    SPEAKING = "speaking"


@dataclass
class ASRStreamEvent:
    type: ASRStreamEventType
    """type of the event"""
    samples_index: int
    """index of the samples when the event was fired"""
    frame: rtc.AudioFrame
    audio_id: str = ""
    """audio id from client"""


class OrionVADV3(agents.vad.VAD):
    """
    适配Orion流式ASR的VAD
    """

    @classmethod
    def load(
        cls,
        *,
        min_speech_duration: float = 0.05,
        min_silence_duration: float = 0.25,
        padding_duration: float = 0.1,
        max_buffered_speech: float = 180.0,
        activation_threshold: float = 0.5,
        sample_rate: int = 16000,
        vad_event_queue: queue.Queue,
    ) -> "OrionVADV3":
        opts = _VADOptions(
            min_speech_duration=min_speech_duration,
            min_silence_duration=min_silence_duration,
            padding_duration=padding_duration,
            max_buffered_speech=max_buffered_speech,
            activation_threshold=activation_threshold,
            sample_rate=sample_rate,
        )
        return cls(opts=opts, vad_event_queue=vad_event_queue)

    def __init__(self, *, opts: _VADOptions, vad_event_queue: queue.Queue) -> None:
        super().__init__(capabilities=agents.vad.VADCapabilities(update_interval=0.032))
        self._opts = opts
        self._vad_event_queue = vad_event_queue

    def stream(self) -> "VADStreamV3":
        return VADStreamV3(self._opts, self._vad_event_queue)


class VADStreamV3(agents.vad.VADStream):
    """
    适配Orion流式ASR的VADStream
    """

    def __init__(self, opts: _VADOptions, vad_event_queue: queue.Queue) -> None:
        super().__init__()
        self._opts = opts
        self._asr_event_ch = utils.aio.Chan[ASRStreamEvent]()

        # Speech detection state
        self._speaking = False
        self._speech_duration = 0.0
        self._silence_duration = 0.0
        self._current_sample = 0
        self._speech_frames = 0
        self._silence_frames = 0

        # Use list for dynamic buffer
        self._speech_buffer_frames: list = []
        self._og_sample_rate = 0
        # Use list for temp buffer, save the last N frames
        self._temp_buffer_frames: list = []
        self._temp_buffer_size = 200
        self._network_delay_frame_count = 40

        self._vad_event_queue = vad_event_queue
        self._sid = ""

    def _copy_frame_to_speech_buffer(self, frame: rtc.AudioFrame) -> None:
        """Copy frame to speech buffer frames"""
        self._speech_buffer_frames.append(frame)

    def _copy_frame_to_temp_buffer(self, frame: rtc.AudioFrame) -> None:
        """Copy frame to temp buffer frames"""
        # Calculate max frames based on temp buffer size
        max_frames = self._temp_buffer_size

        # If temp buffer is full, remove oldest frame
        if len(self._temp_buffer_frames) >= max_frames:
            self._temp_buffer_frames = self._temp_buffer_frames[-max_frames + 1 :]
        self._temp_buffer_frames.append(frame)

    def _copy_temp_buffer_to_speech_buffer(self, copy_frame_count: int) -> None:
        """Copy frames from temp buffer to speech buffer"""
        if copy_frame_count > len(self._temp_buffer_frames):
            copy_frame_count = len(self._temp_buffer_frames)

        self._speech_buffer_frames = []
        self._speech_buffer_frames.extend(self._temp_buffer_frames[-copy_frame_count:])
        self._reset_temp_buffer()

    def _reset_speech_buffer(self) -> None:
        """Reset the speech buffer frames"""
        self._speech_buffer_frames = []

    def _reset_temp_buffer(self) -> None:
        """Reset the temp buffer frames"""
        self._temp_buffer_frames = []

    def _reset_all(self) -> None:
        """Reset all buffers and variables"""
        self._reset_speech_buffer()
        self._reset_temp_buffer()
        self._sid = ""
        self._speech_duration = 0.0
        self._silence_duration = 0.0

    def handle_speaking_vad_event(
        self, vad_event: dict, frame: rtc.AudioFrame
    ) -> Literal["vad_end", "vad_start", None]:
        logger.info(f"[Vad] {self._sid} Vad event: {vad_event}")
        if vad_event.get("type") == "vad_end":  # vad end event
            if vad_event.get("sid") == self._sid:  # correct event
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.END_OF_SPEECH,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        frames=[],
                        speaking=False,
                        audio_id=self._sid,
                    )
                )
                self._speaking = False
                logger.info(f"[Vad] {self._sid} Vad end event, finish current speech")
                return "vad_end"
            else:  # wrong event, ignore
                logger.error(f"[Vad] Wrong event, ignore {vad_event}")
                self._copy_frame_to_speech_buffer(frame)
                return
        elif vad_event.get("type") == "vad_start":  # wrong event
            if vad_event.get("sid") == self._sid:  # duplicate event, ignore
                self._copy_frame_to_speech_buffer(frame)
                logger.error(f"[Vad] Duplicate event, ignore {vad_event}")
                return
            else:  # send current speech and start new one
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.END_OF_SPEECH,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        speaking=False,
                        audio_id=self._sid,
                    )
                )
                self._asr_event_ch.send_nowait(
                    ASRStreamEvent(
                        type=ASRStreamEventType.END_OF_SPEECH,
                        frame=frame,
                        audio_id=self._sid,
                        samples_index=len(self._speech_buffer_frames),
                    )
                )
                self._reset_all()
                # start new speech
                self._sid = vad_event.get("sid")
                self._speaking = True
                self._copy_frame_to_speech_buffer(frame)  # skip copy temp buffer
                logger.warning(
                    f"[Vad] {self._sid} Encountered re-start event, start new speech"
                )
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.START_OF_SPEECH,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        speaking=True,
                        audio_id=self._sid,
                    )
                )
                return "vad_start"

    def handle_non_speaking_vad_event(
        self, vad_event: dict, frame: rtc.AudioFrame
    ) -> Literal["vad_start", None]:
        logger.info(f"[Vad] {self._sid} Vad event: {vad_event}")
        if vad_event.get("type") == "vad_end":  # wrong event, ignore; wakeup == False时，会发送两次vad_end
            logger.error(f"[Vad Non-speaking] Get wrong event, ignore {vad_event}")
            return
        else:  # vad start event
            self._sid = vad_event.get("sid")
            required_frame_count = vad_event.get("frames", 140)
            copy_frame_count = required_frame_count + self._network_delay_frame_count

            if copy_frame_count > self._temp_buffer_size:
                logger.warning(
                    f"[Vad] Copy frame count exceeds temp buffer size, use temp buffer size instead: {copy_frame_count} > {self._temp_buffer_size}, required: {required_frame_count}, network delay: {self._network_delay_frame_count}"
                )
                copy_frame_count = self._temp_buffer_size

            self._reset_speech_buffer()
            self._copy_temp_buffer_to_speech_buffer(copy_frame_count)  # fixed 480
            self._speech_duration = 0.0
            self._silence_duration = 0.0
            self._speaking = True
            self._copy_frame_to_speech_buffer(frame)
            logger.info(f"[Vad] {self._sid} vad start event, start new speech")
            self._event_ch.send_nowait(
                VADEvent(
                    type=VADEventType.START_OF_SPEECH,
                    samples_index=self._current_sample,
                    silence_duration=self._silence_duration,
                    speech_duration=self._speech_duration,
                    speaking=True,
                    audio_id=self._sid,
                )
            )
            return "vad_start"

    @utils.log_exceptions(logger=logger)
    async def _main_task(self) -> AsyncGenerator:
        frame_duration = 0.01  # 10ms per frame
        count = 0
        async for frame in self._input_ch:
            if not isinstance(frame, rtc.AudioFrame):
                continue

            count += 1
            vad_event_result = None
            if self._speaking:  # currently speaking, check vad event queue
                if self._vad_event_queue.qsize() > 0:
                    vad_event = self._vad_event_queue.get()
                    vad_event_result = self.handle_speaking_vad_event(vad_event, frame)
                else:  # if there is no vad event, continue with normal processing
                    self._copy_frame_to_speech_buffer(frame)
                    self._current_sample += frame.samples_per_channel
                    self._speech_duration += frame_duration

                    # check if speech buffer is too long
                    if self._speech_duration >= self._opts.max_buffered_speech:
                        # terminate current speech and set speaking to false
                        self._copy_frame_to_speech_buffer(frame)
                        logger.info(
                            f"[Vad] {self._sid} Encountered overflow, terminate current speech"
                        )
                        self._event_ch.send_nowait(
                            VADEvent(
                                type=VADEventType.END_OF_SPEECH,
                                samples_index=self._current_sample,
                                silence_duration=self._silence_duration,
                                speech_duration=self._speech_duration,
                                speaking=True,
                                audio_id=self._sid,
                            )
                        )
                        self._asr_event_ch.send_nowait(
                            ASRStreamEvent(
                                type=ASRStreamEventType.END_OF_SPEECH,
                                frame=frame,
                                audio_id=self._sid,
                                samples_index=len(self._speech_buffer_frames),
                            )
                        )
                        self._reset_all()
                        self._speaking = False
            else:
                if self._vad_event_queue.qsize() > 0:
                    vad_event = self._vad_event_queue.get()
                    vad_event_result = self.handle_non_speaking_vad_event(
                        vad_event, frame
                    )
                else:  # if there is no vad event, continue with normal processing
                    self._copy_frame_to_temp_buffer(frame)
                    self._silence_duration += frame_duration

            if vad_event_result == "vad_end":
                self._asr_event_ch.send_nowait(
                    ASRStreamEvent(
                        type=ASRStreamEventType.END_OF_SPEECH,
                        frame=frame,
                        audio_id=self._sid,
                        samples_index=len(self._speech_buffer_frames),
                    )
                )
                self._reset_all()  # after send end of speech event, reset all
            elif vad_event_result == "vad_start":
                if self._speech_buffer_frames:
                    for i, audio_frame in enumerate(self._speech_buffer_frames):
                        if i == 0:
                            self._asr_event_ch.send_nowait(
                                ASRStreamEvent(
                                    type=ASRStreamEventType.START_OF_SPEECH,
                                    frame=audio_frame,
                                    audio_id=self._sid,
                                    samples_index=len(self._speech_buffer_frames),
                                )
                            )
                        else:
                            self._asr_event_ch.send_nowait(
                                ASRStreamEvent(
                                    type=ASRStreamEventType.SPEAKING,
                                    frame=audio_frame,
                                    audio_id=self._sid,
                                    samples_index=len(self._speech_buffer_frames),
                                )
                            )

                else:
                    self._asr_event_ch.send_nowait(
                        ASRStreamEvent(
                            type=ASRStreamEventType.START_OF_SPEECH,
                            frame=frame,
                            audio_id=self._sid,
                            samples_index=len(self._speech_buffer_frames),
                        )
                    )
            else:
                if self._speaking:
                    self._asr_event_ch.send_nowait(
                        ASRStreamEvent(
                            type=ASRStreamEventType.SPEAKING,
                            frame=frame,
                            samples_index=len(self._speech_buffer_frames),
                            audio_id=self._sid,
                        )
                    )

            # Send inference event for every 20 frames (200ms)
            if count != 0 and count % 20 == 0:
                # logger.info(f"[Vad] Audio id: {self._sid} Send inference event, speaking: {self._speaking}")
                self._event_ch.send_nowait(
                    VADEvent(
                        type=VADEventType.INFERENCE_DONE,
                        samples_index=self._current_sample,
                        silence_duration=self._silence_duration,
                        speech_duration=self._speech_duration,
                        probability=int(self._speaking),
                        inference_duration=0.0,  # Not tracking actual inference time
                        speaking=self._speaking,
                        audio_id=self._sid,
                    )
                )
