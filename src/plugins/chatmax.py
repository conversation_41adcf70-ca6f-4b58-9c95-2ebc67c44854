import json

import aiohttp
from livekit.agents import llm, utils
from loguru import logger

from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.session_manager.chat_context import ChatContext


class LLM(llm.LLM):
    def __init__(
        self,
        *,
        base_url: str | None = None,
        api_key: str | None = None,
        session: aiohttp.ClientSession | None = None,
        memory: Memory | None = None,
        robot: Robot | None = None,
    ) -> None:
        self._session = session or utils.http_context.http_session()
        self.request_url = base_url
        self.api_key = api_key
        self.memory = memory
        self.robot = robot

    async def _stream_generator(self, user_query: str, history: list):
        headers = {
            "orionstar-api-key": self.api_key,
        }
        if self.robot.device_id == "VIRTUAL_DEVICE":  # for mobile test
            logger.warning(f"Use default robot info for request chatmax")
            robot_info = Robot(
                enterprise_id="orion.ovs.entprise.2311329785",
                device_id="M03SCN1A14024530EC45",
                group_id="ovs.group.160335000141624",
                client_id="orion.ovs.client.1514259512471",
            )
        else:
            robot_info = self.robot

        robot_context = f"当前机器人信息：\n {json.dumps(robot_info.robot_status_info(), ensure_ascii=False, indent=4)}"
        request_body = {
            "query_text": user_query,
            "context": robot_context,
            "history": history,
            "device_id": robot_info.device_id,
            "group_id": robot_info.group_id,
            "client_id": robot_info.client_id,
            "enterprise_id": robot_info.enterprise_id,
            "session_id": "test_id",  # v20230829 新增。多轮会话 id，不能超过 64 字节，只允许 0-9a-zA-z_.-
            "lang": "zh_CN",  # 可选，语言，格式为 zh_CN
            "enable_followup": 0,  # 是否返回相关问题列表，0：否 | 1：是
            "stream": 1,  # 可选，是否以流式输出, 默认是非流式，0: 否 | 1: 是
            "debug": 0,  # 可选，是否输出 debug 信息，默认不输出，0：不输出 | 1：输出
        }
        logger.info(
            f"Request chatmax body: {request_body} \n robot_info: {robot_context}"
        )

        async with self._session.post(
            self.request_url, headers=headers, json=request_body
        ) as response:
            if response.status == 200:
                # 使用异步生成器逐块返回数据
                async for chunk in response.content:
                    yield chunk
            else:
                raise Exception(f"Failed to fetch data, status code: {response.status}")

    def chat(
        self,
        *,
        chat_ctx: ChatContext,
        fnc_ctx: llm.function_context.FunctionContext | None = None,
        temperature: float | None = None,
        n: int | None = None,
        parallel_tool_calls: bool | None = None,
    ):
        history = _build_oai_context(chat_ctx)
        user_query = chat_ctx.messages[-1].content
        return self._stream_generator(user_query, history)


def _build_oai_context(
    chat_ctx: ChatContext,
) -> list[dict]:
    history = []
    messages = chat_ctx.messages
    for message in messages[:-1]:
        if message.role == "user":
            history.append({"question": message.content, "answer": ""})
        elif message.role == "assistant":
            if history:
                history[-1]["answer"] = message.content

    logger.info(f"Generate history: {history} of {messages[-1].content}")

    return history[:8]
