# English (United States) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-14 19:23+0800\n"
"PO-Revision-Date: 2025-03-08 14:29+0800\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: en_US\n"
"Language-Team: en_US <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: src/action/server_function.py:318
msgid "What date is today?"
msgstr "What date is today?"

#: src/action/skill_center/calendars.py:292
msgid "Monday"
msgstr "Monday"

#: src/action/skill_center/calendars.py:293
msgid "Tuesday"
msgstr "Tuesday"

#: src/action/skill_center/calendars.py:294
msgid "Wednesday"
msgstr "Wednesday"

#: src/action/skill_center/calendars.py:295
msgid "Thursday"
msgstr "Thursday"

#: src/action/skill_center/calendars.py:296
msgid "Friday"
msgstr "Friday"

#: src/action/skill_center/calendars.py:297
msgid "Saturday"
msgstr "Saturday"

#: src/action/skill_center/calendars.py:298
msgid "Sunday"
msgstr "Sunday"

#: src/action/skill_center/calendars.py:314
msgid "Never forget why you started, and your mission can be accomplished."
msgstr "Never forget why you started, and your mission can be accomplished."

#: src/action/skill_center/calendars.py:315
msgid "Never give up."
msgstr "Never give up."

#: src/action/skill_center/calendars.py:316
msgid "The road not taken never leads you astray."
msgstr ""

#: src/action/skill_center/calendars.py:317
msgid "Everyone has a unique purpose in life."
msgstr "Everyone has a unique purpose in life."

#: src/action/skill_center/calendars.py:318
msgid "Diligence is the mother of success."
msgstr "Diligence is the mother of success."

#: src/action/skill_center/calendars.py:319
msgid "Life is a journey; we are all travelers."
msgstr "Life is a journey; we are all travelers."

#: src/action/skill_center/calendars.py:320
msgid "When you bloom, the breeze will come naturally."
msgstr "When you bloom, the breeze will come naturally."

#: src/action/skill_center/calendars.py:321
msgid "Hope is the companion of existence."
msgstr "Hope is the companion of existence."

#: src/action/skill_center/calendars.py:322
msgid "Seize the day and enjoy life to the fullest."
msgstr "Seize the day and enjoy life to the fullest."

#: src/action/skill_center/calendars.py:323
msgid "The unexamined life is not worth living."
msgstr "The unexamined life is not worth living."

#: src/action/skill_center/calendars.py:439
msgid "Sorry, an error occurred while processing the calendar data."
msgstr "Sorry, an error occurred while processing the calendar data."

#: src/agent_core/single_action.py:504
msgid "Sorry, I didn't catch that. Could you please repeat?"
msgstr "Sorry, I didn't catch that. Could you please repeat?"

#: src/assistant/livekit_agent.py:445
#, python-format
msgid "Your name is `%s`，you are a `%s`，at the following company: `%s`"
msgstr "Your name is `%s`，you are a `%s`，at the following company: `%s`"

#: src/assistant/planner/recommend.py:624
msgid "我近期的形象"
msgstr "My recent appearance"

#~ msgid "充电桩"
#~ msgstr "Charging Point"

#~ msgid "Your name is `%s`，%s"
#~ msgstr "Your name is `%s`，%s"

