"""
消息发送模块 - 统一处理各种消息的发送
"""

import asyncio
import time
import traceback
from typing import Dict, Any, Optional
from loguru import logger

from src.assistant.chat_manager import OrionChatManager
from src.messages.run import RunMessage, InterruptSpeechContent
from src.utils.feishu_alarm import send_feishu_alarm


class MessageSender:
    """消息发送器 - 封装各种消息的发送逻辑"""

    def __init__(self, chat_manager: OrionChatManager, device_id: str):
        self._chat_manager = chat_manager
        self._device_id = device_id

    @property
    def device_id(self):
        return self._device_id

    @device_id.setter
    def device_id(self, value):
        self._device_id = value

    async def send_raw_message(self, message: Dict[str, Any]) -> Optional[Any]:
        """发送原始消息"""
        start = time.time()
        msg_type = message.get("msg_type", "unknown")
        logger.info(f"[MessageSender] Sending raw message: {msg_type}")

        try:
            # Add timeout to prevent blocking
            result = await asyncio.wait_for(
                self._chat_manager.send_message(message),
                timeout=30.0,  # 30 second timeout
            )
            elapsed = time.time() - start
            logger.info(f"[MessageSender] Send message elapsed: {elapsed:.3f}s")
            return result

        except asyncio.TimeoutError:
            elapsed = time.time() - start
            error_msg = f"[MessageSender] Message send timeout after {elapsed:.3f}s, msg_type: {msg_type}, device_id: {self._device_id}"
            logger.error(error_msg)
            await send_feishu_alarm(error_msg)
            return None

        except asyncio.CancelledError:
            logger.warning(
                f"[MessageSender] Message send cancelled, msg_type: {msg_type}"
            )
            raise

        except Exception as e:
            elapsed = time.time() - start
            error_msg = f"[MessageSender] Failed to send message after {elapsed:.3f}s, msg_type: {msg_type}, device_id: {self._device_id}, error: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            await send_feishu_alarm(error_msg)
            return None

    async def send_interrupt_speech_message(
        self, message: str, request_msg_id: str, session_id: str = ""
    ) -> Optional[Any]:
        """发送打断语音消息"""
        try:
            logger.info(
                f"[MessageSender] Preparing interrupt speech message: {request_msg_id}, device_id: {self._device_id}"
            )

            content = InterruptSpeechContent(
                message=message, request_msg_id=request_msg_id
            )

            interrupt_message = RunMessage(
                device_id=self._device_id,
                session_id=session_id,
                content=content,
                msg_type="run.interrupt_speech",
            )

            logger.info(
                f"[MessageSender] Sending interrupt speech message: {request_msg_id}"
            )
            return await self.send_raw_message(interrupt_message.model_dump())

        except Exception as e:
            error_msg = f"[MessageSender] Failed to create interrupt speech message: {request_msg_id}, device_id: {self._device_id}, error: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            await send_feishu_alarm(error_msg)
            return None
