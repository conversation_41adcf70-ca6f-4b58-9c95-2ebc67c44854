import json
import time
import traceback
import uuid
from typing import Dict

from livekit import rtc
from loguru import logger

from src.action.actions import ActionL<PERSON>
from src.action_executor.executor import _ActionInfo
from src.assistant.request_msg_tracker import RequestMsgTracker
from src.common.agent_config import AGENT_CONFIG, AgentConfig
from src.common.constant import SynthesizeType
from src.common.scene import SceneManager
from src.controller.command_handler import CommandHandler
from src.messages import (
    MonitorHeartbeatContent,
    MonitorMessage,
    RunMessage,
    RunStatusContent,
)
from src.session_manager.chat_context import ChatEvent
from src.session_manager.plan_running_status import (
    RuntimeStatusManager,
    TerminalRunningStatus,
)
from src.utils.feishu_alarm import send_feishu_alarm_sync

SLEEP_TIME = 5


class MessageHandler:
    def __init__(self, agent_assistant):
        self.agent_assistant = agent_assistant
        self.memory = agent_assistant.memory
        self.robot = agent_assistant.robot
        self.runtime_manager: "RuntimeStatusManager" = agent_assistant._runtime_manager
        # self.blackboard = agent_assistant._blackboard
        self.command_handler = CommandHandler(agent_assistant)
        self.request_msg_tracker = RequestMsgTracker(self.robot.device_id)

    async def on_message_received(self, message: rtc.ChatMessage) -> None:
        data_info = json.loads(message.message)
        msg_type = data_info.get("msg_type")
        logger.debug(
            f"Received {msg_type} message: {data_info} timestamp: {message.timestamp} msg_id: {message.id}"
        )

        # 使用消息处理器映射表
        message_handlers = {
            "run.run_action": self._handle_run_action,
            "run.state": self._handle_run_state,
            "run.interrupt_speech": self._handle_interrupt_speech,
            "run.connectivity_test": self._handle_connectivity_test,
            "state.robot_state": self._handle_robot_state,
            "monitor.heartbeat": self._handle_monitor_heartbeat_message,
            "cmd": self._handle_cmd_message,
            "plan.plan": self._handle_plan_message,
            "user.event": self._handle_user_event,
        }

        handler = message_handlers.get(msg_type)
        if handler:
            try:
                await handler(data_info, message)
            except Exception as e:
                logger.error(
                    f"Error handling message type {msg_type}: {e} {traceback.format_exc()}"
                )
        else:
            logger.error(f"Received message type: {msg_type} not supported")

    async def _process_run_action(
        self, run_action_message: RunMessage, message_id: str
    ) -> None:
        aos_session = self.agent_assistant.memory.get_aos_session(  # noqa
            self.agent_assistant.robot
        )
        action_info = _ActionInfo(
            action_name=run_action_message.content.action_name,
            node_id=run_action_message.content.node_id,
            parameters=run_action_message.content.parameters,
            run_id=run_action_message.content.run_id,
            plan_id=run_action_message.content.plan_id,
            request_msg_id=str(message_id),
            result_type="client_requested",
            # query_id=self.memory.run_query_map.get(
            #     run_action_message.content.run_id, ""
            # ),
            query_id="",
            version=self.robot.action_version,
        )

        # 追踪say和realtime_say action的request_msg_id
        action_name = run_action_message.content.action_name
        if (
            action_name.lower() in ["say", "realtime_say"]
            or "say" in action_name.lower()
        ):
            await self.request_msg_tracker.track_request(
                request_msg_id=str(message_id),
                action_name=action_name,
                run_id=run_action_message.content.run_id,
                node_id=run_action_message.content.node_id,
                plan_id=run_action_message.content.plan_id,
            )

        self.agent_assistant._action_ch.send_nowait(action_info)

    async def _handle_monitor_heartbeat(self, content: MonitorHeartbeatContent) -> None:
        content.get_server_info(self.agent_assistant)
        if not content.server.get("published_tracks"):
            err_msg = f"[Heartbeat: {self.robot.device_id}]. No published tracks. {content.server}"
            logger.warning(err_msg)
            send_feishu_alarm_sync(err_msg)
        # resubscribe to the track if the track is not in the subscribed track list
        if not content.server.get("subscribed_tracks"):
            err_msg = f"[Heartbeat: {self.robot.device_id}] Resubscribe to the track. Before resubscribe. {content.server}"
            logger.warning(err_msg)
            send_feishu_alarm_sync(err_msg)
            # Pause to ensure the track has been successfully published. Utilize synchronous sleep since the connection isn't established yet.
            logger.info(f"[Heartbeat: {self.robot.device_id}] Relink human user.")
            participant = self.agent_assistant._room.remote_participants.get(
                "human_user"
            )
            if participant:
                self.agent_assistant._human_input._participant = participant
            time.sleep(SLEEP_TIME)

            self.agent_assistant._human_input._subscribe_to_microphone()
            content.get_server_info(self.agent_assistant)  # update the subscribed track
            logger.warning(
                f"[Heartbeat: {self.robot.device_id}] After resubscribe. {content.server}"
            )

        monitor_message = MonitorMessage(
            device_id=self.robot.device_id, content=content
        )
        await self.agent_assistant._chat_manager.send_message(
            monitor_message.model_dump()
        )

    async def _handle_run_action(self, data_info: Dict, message: rtc.ChatMessage):
        run_action_message = RunMessage(**data_info)
        await self._process_run_action(run_action_message, message.id)

    async def _handle_robot_state(self, data_info: Dict, message: rtc.ChatMessage):
        logger.info(f"Received robot status message {data_info}")
        original_app_id = self.robot.APP_ID
        try:
            self.agent_assistant.update_robot_status(data_info.get("content", {}))
        except Exception as e:
            logger.error(
                f"Error when update robot status: {e} {traceback.format_exc()}"
            )

        current_app_id = self.robot.APP_ID
        if original_app_id != current_app_id:
            logger.info(f"App id changed: {original_app_id} -> {current_app_id}")
            if scene_change_context := (
                SceneManager.get_scene_change_context(
                    original_app_id=original_app_id,
                    changed_app_id=current_app_id,
                )
            ):
                await self.memory.commit_chat_assistant_message(
                    event=ChatEvent(
                        desc=f"The scene has switched: {scene_change_context.original_app_description} → {scene_change_context.changed_app_description}",
                    ),
                    robot=self.robot,
                    sid="",  # 场景切换事件没有query_id
                )

    async def _handle_run_state(self, data_info: Dict, message: rtc.ChatMessage):
        logger.info(f"Received run state message {data_info}")
        content = data_info.get("content", {})
        # update running status
        try:
            running_status = RunStatusContent(**content)
        except Exception as e:
            logger.error(f"Error when parse run state message: {e}")
            return

        if not await self.runtime_manager.get_plan_running_status(
            running_status.run_id
        ):  # ignore single action status
            return

        plan_running_status = await self.runtime_manager.get_plan_running_status(
            run_id=running_status.run_id,
        )
        if not plan_running_status:
            logger.error(
                f"Failed to get plan running status for plan {running_status.plan_id} run {running_status.run_id}"
            )
            return

        plan_running_status.status = running_status.status
        try:
            ret = await self.runtime_manager.save_plan_running_status(
                running_status.run_id, plan_running_status
            )
        except Exception as e:
            logger.error(
                f"Failed to save plan running status: {e} {plan_running_status.plan_id} {plan_running_status.run_id}"
            )
            return

        if not ret:
            logger.error(
                f"Failed to save plan running status {plan_running_status.plan_id} {plan_running_status.run_id}"
            )
            return

        if running_status.status in TerminalRunningStatus:  # reset run_id
            logger.info(
                f"Reset runtime status after run {running_status.run_id} finished"
            )

        for node_info in running_status.nodes:
            if not node_info.get("node_id"):
                logger.warning(f"Node info {node_info} does not have node_id")
                continue
            await self.runtime_manager.update_node_status(
                running_status.run_id,
                node_info.get("node_id"),
                node_info,
            )

            logger.debug(f"Node info: {node_info}")
            if node_info.get("status") and node_info.get("status") in [
                "succeeded",
                "rejected",
                "interrupted",
            ]:
                node_running_status = (
                    await self.runtime_manager.get_one_node_running_status(
                        running_status.run_id, node_info.get("node_id")
                    )
                )
                action_name = node_running_status.info.get("name")
                action_def = ActionLib().get_one_action(
                    name=action_name,
                    full_name=action_name,
                    agent_id=self.robot.agent_id,
                )
                if not action_def:  # CTRL Node or Not Found
                    logger.info(f"Action {action_name} not found")
                    return

                # if node_info.get("result"):  # update blackboard
                #     self.blackboard.save_action_result(
                #         plan_id=running_status.plan_id,
                #         run_id=running_status.run_id,
                #         action_id=node_info.get("node_id"),
                #         result_parameters=node_info.get("result"),
                #     )

                agent_config: AgentConfig = AGENT_CONFIG.get(self.robot.agent_id)
                if not agent_config:
                    logger.error(f"Agent config not found for {self.robot.agent_id}")
                    return

                # commit success node to chat history
                await self.agent_assistant.commit_node_to_chat_history(
                    running_status.plan_id,
                    running_status.run_id,
                    node_info.get("node_id"),
                )

                # generate follow-up
                if not self.robot.interface_state.app_id:
                    logger.warning(
                        f"Robot interface state app_id is not set, {self.robot.interface_state}"
                    )
                    return

                if (
                    # and self.robot.face_id  TODO(<EMAIL>): 需要能够判断当前是否有人脸，才能开启不用SAY来停止自动规划
                    agent_config.auto_generate_action_follow_up
                    and node_info.get("status") == "succeeded"
                ):
                    action_def = ActionLib().get_one_action(full_name=action_name)
                    if action_def.level == "global":
                        continue
                    if action_name.lower() not in [
                        "orion.agent.action.say",
                        "orion.app.promote.sales_pitch",
                        "orion.agent.action.exit_product_promotion_app",
                        "orion.agent.action.exit_promote_app",
                        "orion.app.promote.general_sales_service",
                        # "orion.app.promote.product_detail",
                        # "orion.app.promote.answer_user_question",
                    ]:  # ignore SAY action temporarily
                        query_id = "ACTION" + "_" + str(uuid.uuid4())
                        self.agent_assistant._synthesize_answer_with_debug_info_v2(
                            user_transcript="",
                            force_play=False,
                            query_id=query_id,
                            elapse_info={},
                            synthesize_type=SynthesizeType.ACTION,
                        )

    async def _handle_monitor_heartbeat_message(
        self, data_info: Dict, message: rtc.ChatMessage
    ):
        received_content = data_info.get("content", {})
        request_msg_id = str(message.id)
        content = MonitorHeartbeatContent(
            request_msg_id=request_msg_id, client=received_content.get("client", {})
        )
        await self._handle_monitor_heartbeat(content)

    async def _handle_plan_message(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        query = data_info.get("content", {}).get("query", "")
        if query:
            query_id = "TEXT" + "_" + str(uuid.uuid4())
            self.agent_assistant._synthesize_answer_with_debug_info_v2(
                user_transcript=query,
                force_play=False,
                query_id=query_id,
                elapse_info={},
            )

    async def _handle_cmd_message(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        await self.command_handler.handle_command(data_info, message)

    async def async_send_message(self, message: Dict):
        await self.agent_assistant._chat_manager.send_message(message)

    async def _handle_user_event(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        """处理用户事件消息"""
        agent_config: AgentConfig = AGENT_CONFIG.get(self.robot.agent_id)
        if not agent_config:  # Default: Skip
            logger.error(f"Agent config not found for {self.robot.agent_id}")
            return

        if not agent_config.auto_handle_event:
            return

        content = data_info.get("content", {})
        event = content.get("event")

        logger.info(f"Received user event: {event}")

        # 触发观察屏幕的相关逻辑
        query_id = "EVENT" + "_" + str(uuid.uuid4())
        self.agent_assistant._synthesize_answer_with_debug_info_v2(
            user_transcript="",
            force_play=False,
            query_id=query_id,
            elapse_info={},
            synthesize_type=SynthesizeType.EVENT,
            event=event,
        )

    async def _handle_interrupt_speech(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        logger.info(f"Received interrupt speech message: {data_info}")
        await self.agent_assistant.interrupt_agent_answer()

    async def _handle_connectivity_test(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        """处理连通性测试消息"""
        logger.info(f"Received connectivity test message: {data_info}")

        content = data_info.get("content", {})
        client_timestamp = content.get("client_timestamp")
        request_msg_id = str(message.id)

        # 构建回复消息
        response_message = {
            "msg_type": "run.connectivity_test",
            "device_id": self.robot.device_id,
            "content": {
                "server_timestamp": int(time.time() * 1000),  # 当前服务端时间戳(ms)
                "client_timestamp": client_timestamp,  # 直接使用客户端时间戳
                "request_msg_id": request_msg_id,
            },
        }

        # 发送回复消息
        await self.agent_assistant._chat_manager.send_message(response_message)
