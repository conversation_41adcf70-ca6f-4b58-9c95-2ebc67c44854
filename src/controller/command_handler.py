import time
from functools import partial
from typing import Dict, List
from concurrent.futures import ThreadPoolExecutor
from livekit import rtc
from loguru import logger

from src.common.agent_config import AGENT_CONFIG, LAUNCHER_AGENT_ID
from src.common.constant import SynthesizeType
from src.settings import agent_setting, face_id_redis_client
from src.utils.feishu_alarm import send_feishu_alarm_sync
from src.session_manager.memory import ChatMessage, ChatAction
from src.utils.diagnostic import report_wakeup_result
from src.utils.async_utils import callback


class CommandHandler:
    def __init__(self, agent_assistant):
        self.agent_assistant = agent_assistant
        self.memory = agent_assistant.memory
        self.robot = agent_assistant.robot
        self.thread_pool = ThreadPoolExecutor(max_workers=5)

    async def handle_command(self, data_info: Dict, message: rtc.ChatMessage) -> None:
        command = data_info.get("content", {}).get("command")
        cmd_handlers = {
            "clear_history": self._handle_clear_history,
            "generate_follow-up": self._handle_generate_followup,
            "generate_welcome": self._handle_generate_welcome,
            "sync_face-info": self._handle_sync_face_info,
            "vad_start": self._handle_vad_start,
            "vad_end": self._handle_vad_end,
            "wakeup_result": self._handle_wakeup_result,
        }
        handler = cmd_handlers.get(command)
        if handler:
            await handler(data_info, message)
        else:
            logger.error(f"Command {command} not supported")

    async def _handle_clear_history(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        await self.memory.clear_chat_history()

    async def _handle_generate_followup(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        logger.info("Received generate follow-up message")

        if not AGENT_CONFIG.get(
            self.robot.agent_id, LAUNCHER_AGENT_ID
        ).auto_generate_recommend_follow_up:
            logger.warning(f"Not support generate follow-up of {self.robot.agent_id}")
            return

        # get final assistant action
        assistant_action: ChatAction = None
        try:
            chat_msgs: List[ChatMessage] = (
                await self.memory.get_chat_context(max_chat_history=5)
            ).messages
            for msg in chat_msgs[::-1]:
                if msg.role == "assistant" and msg.action:
                    assistant_action = msg.action
                    break
        except Exception as e:
            logger.error(f"Failed to get assistant message: {e}")
            return

        if assistant_action and "OPEN_WEB_URL_DEFINED" in assistant_action.name:
            logger.warning(
                "Final assistant action is OPEN_WEB_URL_DEFINED, not support generate follow-up"
            )
            return

        prompt = data_info.get("content", {}).get("prompt", "")
        # get follow-up questions
        query_id = "TEXT" + "_" + "GENERATE_FOLLOW_UP" + "_" + "G" * 9
        self.agent_assistant._synthesize_answer_with_debug_info_v2(
            user_transcript="生成推荐问题",
            force_play=False,
            query_id=query_id,
            synthesize_type=SynthesizeType.RECOMMEND,
            elapse_info={},
            followup_prompt=prompt,
        )

    async def _handle_generate_welcome(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        logger.info("Received generate welcome message")

        kwargs = data_info.get("content", {}).get("kwargs", {})
        image_id = kwargs.get("image_id", "")
        image_url = kwargs.get("image_url", "")

        if not image_id or not image_url:
            logger.error(
                f"Failed to generate welcome: Invalid image_id or image_url: {image_id} {image_url}"
            )
            send_feishu_alarm_sync(
                f"机器人：{self.robot.device_id} 生成新人欢迎语失败，图片id或图片url为空: {image_id} {image_url}"
            )
            return

        image_info = {"image_id": image_id, "image_url": image_url}
        logger.info(
            f"Generate welcome with image_id: {image_id}, image_url: {image_url}"
        )

        query_id = "TEXT" + "_" + "GENERATE_WELCOME" + "_" + "G" * 9
        self.agent_assistant._synthesize_answer_with_debug_info_v2(
            user_transcript="生成欢迎语",
            force_play=False,
            query_id=query_id,
            image_info=image_info,
            synthesize_type=SynthesizeType.RECOMMEND,
            elapse_info={},
        )

    async def _handle_sync_face_info(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        logger.info("Received sync face info message")

        kwargs = data_info.get("content", {}).get("kwargs", {})
        image_id = kwargs.get("image_id", "")
        face_id = kwargs.get("face_id", "")
        user_name = kwargs.get("user_name", "")
        personal_welcome_message = kwargs.get("personal_welcome_message", "")

        if not image_id:
            logger.error(f"Failed to sync face info: Invalid image_id: {image_id}")
            return

        start_time = time.time()
        try:
            redis_key = f"face_id_{agent_setting.env}_{image_id}"
            face_id_redis_client.hset(redis_key, "face_id", face_id)
            face_id_redis_client.hset(redis_key, "user_name", user_name)
            face_id_redis_client.hset(
                redis_key, "personal_welcome_message", personal_welcome_message
            )
            face_id_redis_client.expire(redis_key, 300)  # Set expiration to 5 minutes
            logger.info(
                f"Successfully stored face info - image_id: {image_id}, face_id: {face_id}, user_name: {user_name}"
            )
        except Exception as e:
            logger.error(f"Failed to store face info in Redis: {str(e)}")

        end_time = time.time()
        logger.info(
            f"Time taken to store face info in Redis: {end_time - start_time} seconds"
        )

    async def _handle_vad_start(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        if not self.robot.allow_interrupt:
            return

        try:
            frames = data_info.get("content", {}).get("kwargs", {}).get("frames", 140)
            frames = int(frames)
        except Exception as e:
            logger.error(f"Failed to get frames from vad_start message: {e}")
            frames = 140

        vad_info = {
            "type": "vad_start",
            "frames": frames,
            "sid": data_info.get("content", {}).get("kwargs", {}).get("sid", ""),
        }
        self.agent_assistant._vad_event_queue.put(vad_info)

        if vad_info["sid"] in self.agent_assistant._client_wakeup_result:
            logger.error(f"[Vad] AudioID: {vad_info['sid']} Duplicate event")

        self.agent_assistant._client_wakeup_result[vad_info["sid"]] = {
            "vad_start": time.time(),
            "vad_end": None,
            "result": None,
        }

    async def _handle_vad_end(self, data_info: Dict, message: rtc.ChatMessage) -> None:
        vad_info = {
            "type": "vad_end",
            "sid": data_info.get("content", {}).get("kwargs", {}).get("sid", ""),
        }
        self.agent_assistant._vad_event_queue.put(vad_info)
        if vad_info["sid"] not in self.agent_assistant._client_wakeup_result:
            logger.error(
                f"[Vad] AudioID: {vad_info['sid']} Not found in client_wakeup_result"
            )
            self.agent_assistant._client_wakeup_result[vad_info["sid"]] = {
                "vad_start": None,
                "vad_end": time.time(),
                "result": None,
            }

        self.agent_assistant._client_wakeup_result[vad_info["sid"]]["vad_end"] = (
            time.time()
        )

    async def _handle_wakeup_result(
        self, data_info: Dict, message: rtc.ChatMessage
    ) -> None:
        sid = data_info.get("content", {}).get("kwargs", {}).get("sid", "")
        result = data_info.get("content", {}).get("kwargs", {}).get("result", False)
        if sid not in self.agent_assistant._client_wakeup_result:
            logger.error(f"[Vad] AudioID: {sid} Not found in client_wakeup_result")
            self.agent_assistant._client_wakeup_result[sid] = {
                "vad_start": None,
                "vad_end": None,
                "result": result,
            }
        if (
            self.agent_assistant._client_wakeup_result[sid]["result"] is None
        ):  # record result when it's not set
            self.agent_assistant._client_wakeup_result[sid]["result"] = result

        if result is False:  # 免唤醒结果为False时，直接发送vad_end事件，提前结束asr
            self.agent_assistant._vad_event_queue.put(
                {
                    "type": "vad_end",
                    "sid": sid,
                }
            )

        # report_func = partial(report_wakeup_result, sid, result)
        # start_time = time.time()
        # try:
        #     future = self.thread_pool.submit(report_func)
        #     future.add_done_callback(callback)
        #     logger.debug(
        #         f"[Wakeup Result] Start to report wakeup result to asr server: {sid} {result} Elapsed: {time.time() - start_time}"
        #     )
        # except Exception as e:
        #     logger.warning(
        #         f"[Wakeup Result] Failed to report wakeup result to asr server: {e} Elapsed: {time.time() - start_time}"
        #     )
