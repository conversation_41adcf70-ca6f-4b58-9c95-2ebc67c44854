import asyncio
import os
import queue
import sys
from concurrent.futures import ThreadPoolExecutor

import librosa
import redis
from livekit.agents import JobContext, JobProcess, WorkerOptions, cli
from livekit.agents.voice_assistant.voice_assistant import AssistantTranscriptionOptions
from loguru import logger
from mcp_use.client import MC<PERSON>lient
import psutil

from src.assistant.livekit_agent import AgentAssistant
from src.assistant.llm import orion_will_synthesize_assistant_reply
from src.assistant.transcription import <PERSON><PERSON>entenceTokenizer, OrionWordTokenizer
from src.plugins import orion_asr

# from src.plugins.orion_assistant import OpenAssistant
from src.plugins.orion_llm import LLM
from src.plugins.orion_tts_pcm import OrionTTS as OrionTTS_PCM
from src.plugins.orion_tts import OrionTTS as OrionTTS_MP3
from src.plugins.orion_vad_v3 import OrionVADV3
from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.settings import agent_setting
from src.utils.async_utils import callback
from src.utils.cos_client import CosClient
from src.utils.diagnostic_client import DiagnosticClient
from src.utils.log import rotator
from src.common.constant import Area


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


def _prepare_audio_task(executor) -> None:
    # use thread pool to prepare audio
    current_dir = os.path.dirname(os.path.abspath(__file__))
    audio_path = os.path.join(current_dir, "audios", "welcome.wav")
    logger.info(f"Prepare audio: {audio_path}")
    future = executor.submit(librosa.load, audio_path)
    future.add_done_callback(callback)


async def entrypoint(ctx: JobContext):
    # init logger
    # logger config
    log_dir = "logs"
    log_dir = os.path.join(BASE_DIR, log_dir)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logger.remove()
    logger.add(
        f"{log_dir}/assistant_{{time:YYYY_MM_DD}}.log",
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "{process.name}:{process.id}:{thread.name}:{thread.id} |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        rotation=rotator.should_rotate,
        retention="3 days",
        encoding="utf-8",
        enqueue=True,
        level=agent_setting.log_level,
    )
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "{process.name}:{process.id}:{thread.name}:{thread.id} |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        enqueue=True,
        level=agent_setting.log_level,
    )
    logger.debug(f"Inited logger {ctx.room.name}")

    # Connect to the LiveKit room
    try:
        await ctx.connect()
    except Exception as e:
        logger.error(f"Failed to connect to LiveKit room: {e}")
        ctx.shutdown(reason=f"Failed to connect to LiveKit room: {e}")
        return

    logger.configure(
        extra={
            "room_name": ctx.room.name,
            "query_id": "",
            "query": "",
            "plan_id": "",
            "run_id": "",
            "request_msg_id": "",
            "face_id": "",
            "app_id": "",
            "agent_id": "",
        }
    )
    logger.info(f"Connected to LiveKit room, {ctx._info.url}")

    cos_client = CosClient(  # tencent cos client, used to upload files
        secret_id=agent_setting.cos_secret_id,
        secret_key=agent_setting.cos_secret_key,
        region=agent_setting.cos_region,
        bucket=agent_setting.cos_bucket,
        prefix=agent_setting.cos_prefix,
    )

    diagnostic_client = DiagnosticClient(
        diagnostic_host=agent_setting.diagnostic_host,
        diagnostic_path=agent_setting.diagnostic_path,
        diagnostic_secret_key=agent_setting.diagnostic_secret_key,
    )

    robot = Robot()
    logger.debug("Initialized Robot")

    transcription_opts = AssistantTranscriptionOptions(
        sentence_tokenizer=OrionSentenceTokenizer(robot=robot, min_sentence_len=1),
        word_tokenizer=OrionWordTokenizer(robot=robot, ignore_punctuation=False),
        agent_transcription_speed=1.3,
    )

    redis_client = redis.Redis(
        host=agent_setting.redis_host,
        port=agent_setting.redis_port,
        db=agent_setting.redis_db,
        charset="utf-8",
        decode_responses=True,
    )  # a connection pool is created on redis.Redis()

    # VoiceAssistant is a class that creates a full conversational AI agent.
    # See https://github.com/livekit/agents/blob/main/livekit-agents/livekit/agents/voice_assistant/assistant.py
    # for details on how it works.
    memory = Memory(redis_client=redis_client)
    logger.debug("Initialized memory")

    vad_event_queue = queue.Queue()
    # if agent_setting.region_version == Area.overseas:
    #     llm = OpenAssistant(
    #         memory=memory,
    #         robot=robot,
    #         base_url=agent_setting.generate_text_model_base_url,
    #         model_name=agent_setting.generate_text_model,
    #         api_key=agent_setting.generate_text_model_api_key,
    #     )
    # else:
    llm = LLM(
        memory=memory,
        robot=robot,
        base_url=agent_setting.generate_text_model_base_url,
        model_name=agent_setting.generate_text_model,
        api_key=agent_setting.generate_text_model_api_key,
    )

    mcp_client = MCPClient()
    logger.debug("[Init MCP]")

    if agent_setting.region_version == Area.overseas:
        tts = OrionTTS_PCM(
            base_url=agent_setting.tts_base_url,
            endpoint=agent_setting.tts_endpoint,
            type=agent_setting.tts_type,
            robot=robot,
            speed=robot.speech_rate,
        )
    else:
        tts = OrionTTS_MP3(
            base_url=agent_setting.tts_base_url,
            endpoint=agent_setting.tts_endpoint,
            type=agent_setting.tts_type,
            robot=robot,
            speed=robot.speech_rate,
        )
    assistant = AgentAssistant(
        will_synthesize_assistant_reply=orion_will_synthesize_assistant_reply,
        vad=OrionVADV3.load(
            min_silence_duration=0.05,
            activation_threshold=0.4,
            vad_event_queue=vad_event_queue,
        ),  # Adjusted parameters for client VAD
        stt=orion_asr.STT(
            language=robot.language,
            api_key=agent_setting.asr_api_key,
            base_url=agent_setting.asr_base_url,
            cos_client=cos_client,
            memory=memory,
            robot=robot,
        ),
        llm=llm,
        tts=tts,
        memory=memory,
        cos_client=cos_client,
        redis_client=redis_client,
        transcription=transcription_opts,
        interrupt_speech_duration=0.2,
        robot=robot,
        diagnostic_client=diagnostic_client,
        vad_event_queue=vad_event_queue,
        mcp_client=mcp_client,
    )
    logger.debug("Initialized assistant")
    # Start the voice assistant with the LiveKit room
    assistant.start(ctx.room)
    logger.info("Started assistant")

    await asyncio.sleep(0.5)


def prewarm(proc: JobProcess):
    try:
        executor = ThreadPoolExecutor(max_workers=4)
        _prepare_audio_task(executor)
        executor.shutdown(wait=True)
    except Exception:
        logger.error("Failed to prepare audio task")


def _get_container_cpu_usage() -> float:
    """获取容器CPU使用率"""
    try:
        # 检查CPU配额（容器环境）
        quota_file = "/sys/fs/cgroup/cpu/cpu.cfs_quota_us"
        period_file = "/sys/fs/cgroup/cpu/cpu.cfs_period_us"

        if os.path.exists(quota_file) and os.path.exists(period_file):
            with open(quota_file, "r") as f:
                quota = int(f.read().strip())
            with open(period_file, "r") as f:
                period = int(f.read().strip())

            system_cpu = psutil.cpu_percent(1.0)

            if quota > 0 and period > 0:
                # 计算CPU限制核数
                cpu_limit_cores = quota / period
                logger.debug(
                    f"Container CPU: {system_cpu:.1f}% (limit: {cpu_limit_cores:.1f} cores)"
                )
            else:
                logger.debug(f"Container CPU: {system_cpu:.1f}% (no limit)")

            return system_cpu / 100
        else:
            # 非容器环境
            percent = psutil.cpu_percent(1.0)
            logger.debug(f"System CPU: {percent:.1f}%")
            return percent / 100

    except Exception as e:
        logger.warning(f"Failed to get CPU usage: {e}")
        percent = psutil.cpu_percent(1.0)
        return percent / 100


def _get_container_memory_usage() -> float:
    """获取容器内存使用率"""
    try:
        # 尝试读取容器内存使用情况（K8s Pod环境）
        if os.path.exists("/sys/fs/cgroup/memory/memory.usage_in_bytes"):
            logger.debug("Using cgroup v1 for memory")
            # cgroup v1
            with open("/sys/fs/cgroup/memory/memory.usage_in_bytes", "r") as f:
                current = int(f.read().strip())
            with open("/sys/fs/cgroup/memory/memory.limit_in_bytes", "r") as f:
                limit = int(f.read().strip())
                # 如果limit过大，说明没有设置限制，使用系统内存
                if limit > psutil.virtual_memory().total:
                    limit = psutil.virtual_memory().total
            percent = (current / limit) * 100
        elif os.path.exists("/sys/fs/cgroup/memory.current"):
            logger.debug("Using cgroup v2 for memory")
            # cgroup v2
            with open("/sys/fs/cgroup/memory.current", "r") as f:
                current = int(f.read().strip())
            with open("/sys/fs/cgroup/memory.max", "r") as f:
                limit_str = f.read().strip()
                limit = (
                    int(limit_str)
                    if limit_str != "max"
                    else psutil.virtual_memory().total
                )
            percent = (current / limit) * 100
        else:
            # 回退到系统内存
            logger.debug("Fallback to system memory")
            percent = psutil.virtual_memory().percent
    except Exception as e:
        logger.warning(f"Failed to get container memory usage: {e}")
        # 出错时回退到系统内存
        percent = psutil.virtual_memory().percent

    return percent / 100


def load_fnc() -> float:
    """
    容器资源负载监控函数
    优先使用内存使用率，如果内存使用率较低则参考CPU使用率
    """
    try:
        memory_usage = _get_container_memory_usage()
        cpu_usage = _get_container_cpu_usage()

        # 使用内存和CPU使用率的最大值作为负载指标
        # 这样可以更好地反映容器的真实负载情况
        load = max(memory_usage, cpu_usage)

        logger.info(
            f"Container load: {load * 100:.1f}% (Memory: {memory_usage * 100:.1f}%, CPU: {cpu_usage * 100:.1f}%)"
        )
        return load

    except Exception as e:
        logger.error(f"Failed to get container load: {e}")
        # 出错时回退到系统内存
        percent = psutil.virtual_memory().percent
        return percent / 100


if __name__ == "__main__":
    # Initialize the worker with the entrypoint
    print(f"Starting agent with config {agent_setting.model_dump()}")
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
            port=8002,
            shutdown_process_timeout=60 * 60,
            ws_url=agent_setting.livekit_url,
            api_key=agent_setting.livekit_api_key,
            api_secret=agent_setting.livekit_api_secret,
            initialize_process_timeout=60 * 5,
            load_threshold=0.75,
            num_idle_processes=3,
            load_fnc=load_fnc,
        )
    )
