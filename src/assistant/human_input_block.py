import asyncio
from typing import Any, TypeVar
import traceback

from livekit import rtc
from livekit.agents import (
    stt as speech_to_text,
)
from livekit.agents import utils
from livekit.agents import (
    vad as voice_activity_detection,
)
from livekit.agents.voice_assistant.human_input import HumanInput
from livekit.plugins import openai
from loguru import logger
from livekit.agents.stt import (
    SpeechEventType,
)
from livekit.agents.utils import aio

from src.plugins import orion_asr
from src.utils.async_utils import gracefully_cancel
from src.plugins.orion_asr import SpeechEvent
from src.assistant.stt_forwarder import OrionSTTSegmentsForwarder

T = TypeVar("T")


class OrionBlockHumanInput(HumanInput):
    @utils.log_exceptions(logger=logger)
    async def _recognize_task(self, audio_stream: rtc.AudioStream) -> None:
        """
        Receive the frames from the user audio stream and detect voice activity.
        """

        if not (
            isinstance(self._stt, openai.STT) or isinstance(self._stt, orion_asr.STT)
        ):
            raise ValueError(f"Unsupported STT type: {type(self._stt)}")

        stt_event_ch = aio.Chan[SpeechEvent]()
        vad_stream = self._vad.stream()
        self._vad_stream = vad_stream

        stt_forwarder = None
        if self._transcription:
            stt_forwarder = OrionSTTSegmentsForwarder(
                room=self._room,
                participant=self._participant,
                track=self._subscribed_track,
            )

        async def _audio_stream_co() -> None:
            # forward the audio stream to the VAD and STT streams
            # buffer_ = []
            # i = 0
            async for ev in audio_stream:
                # convert sample rate to 16k
                # logger.info(
                #     f"[OrionHumanInput] Audio stream frame: {ev.frame.sample_rate} {ev.frame.num_channels} {ev.frame.samples_per_channel}"
                # )
                # count = len(bytes(ev.frame.data))
                # logger.info(f"[OrionHumanInput] Audio stream frame count: {count}")
                # stt_frame = ev.frame.remix_and_resample(16000, 1)
                # debug stt frame
                # stt_frame = ev.frame
                # stt_stream.push_frame(stt_frame)
                # buffer_.append(stt_frame)
                # i += 1
                # if i % 1000 == 0:
                #     buffer = agents.utils.merge_frames(buffer_)
                #     buffer_ = []
                #     io_buffer = io.BytesIO()
                #     with wave.open(io_buffer, "wb") as wav:
                #         wav.setnchannels(buffer.num_channels)
                #         wav.setsampwidth(2)
                #         wav.setframerate(buffer.sample_rate)
                #         wav.writeframes(buffer.data)
                #         print(
                #             f"buffer info {buffer.sample_rate} {buffer.num_channels} {buffer.samples_per_channel}"
                #         )
                #
                #     # !!! save wav file with a random uuid
                #     wav_file = f"!!!!!before_vad_111111{uuid.uuid4()}.wav"
                #     with open(wav_file, "wb") as f:
                #         f.write(io_buffer.getvalue())

                vad_stream.push_frame(ev.frame)

        async def _vad_stream_co() -> None:
            async for ev in vad_stream:
                if ev.type == voice_activity_detection.VADEventType.START_OF_SPEECH:
                    # logger.debug("[!!!] vad detect start of speech")
                    self._speaking = True
                    self.emit("start_of_speech", ev)
                    # forward to stt
                    stt_event_ch.send_nowait(
                        SpeechEvent(SpeechEventType.START_OF_SPEECH)
                    )

                elif ev.type == voice_activity_detection.VADEventType.INFERENCE_DONE:
                    self._speech_probability = ev.probability
                    await self.async_emit("vad_inference_done", ev)
                elif ev.type == voice_activity_detection.VADEventType.END_OF_SPEECH:
                    # logger.debug("[!!!] vad detect end of speech")
                    self._speaking = False
                    self.emit("end_of_speech", ev)
                    try:
                        stt_event_ch.send_nowait(
                            SpeechEvent(
                                type=SpeechEventType.END_OF_SPEECH,
                                audio_id=ev.audio_id,
                            )
                        )
                    except Exception as e:
                        logger.error(
                            f"[OrionHumanInput] AudioID: {ev.audio_id} VAD send end of speech error:{ev} {e} {traceback.format_exc()}"
                        )
                        continue

                    try:
                        merged_frames = utils.merge_frames(ev.frames)
                        # logger.debug(
                        #     f"wrapper: event frames {event.frames[0].sample_rate} {event.frames[0].num_channels} {event.frames[0].samples_per_channel}"
                        # )
                    except Exception as e:
                        logger.error(
                            f"[OrionHumanInput] AudioID: {ev.audio_id} VAD merge frames error:{ev} {e} {traceback.format_exc()}"
                        )
                        continue

                    # call asr
                    try:
                        t_event = await self._stt.recognize(
                            buffer=merged_frames,
                            language=self._stt._opts.language,
                            audio_id=ev.audio_id,
                        )
                    except Exception as e:
                        logger.error(
                            f"AudioID: {ev.audio_id} Orion ASR error: {e} {traceback.format_exc()}"
                        )
                        continue

                    try:
                        stt_event_ch.send_nowait(
                            SpeechEvent(
                                type=SpeechEventType.FINAL_TRANSCRIPT,
                                alternatives=[t_event.alternatives[0]],
                                audio_id=t_event.audio_id,
                                elapsed=t_event.elapsed,
                            )
                        )
                    except Exception as e:
                        logger.error(
                            f"AudioID: {ev.audio_id} STT send final transcript error:{t_event} {e} {traceback.format_exc()}"
                        )
                        continue

        async def _stt_stream_co() -> None:
            async for ev in stt_event_ch:
                if stt_forwarder is not None:
                    stt_forwarder.update(ev)

                if ev.type == speech_to_text.SpeechEventType.FINAL_TRANSCRIPT:
                    self.emit("final_transcript", ev)
                elif ev.type == speech_to_text.SpeechEventType.INTERIM_TRANSCRIPT:
                    self.emit("interim_transcript", ev)

        tasks = [
            asyncio.create_task(_audio_stream_co()),
            asyncio.create_task(_vad_stream_co()),
            asyncio.create_task(_stt_stream_co()),
        ]
        try:
            await asyncio.gather(*tasks)
        finally:
            await gracefully_cancel(*tasks)

            if stt_forwarder is not None:
                await stt_forwarder.aclose()

            stt_event_ch.close()
            await vad_stream.aclose()

    async def async_emit(self, event: T, *args: Any, **kwargs: Any) -> None:
        if event in self._events:
            callables = self._events[event].copy()
            for callback in callables:
                await callback(*args, **kwargs)
