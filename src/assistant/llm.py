from typing import Optional, AsyncIterable

from livekit.agents.voice_assistant.voice_assistant import LLMStream, VoiceAssistant
from loguru import logger

from src.utils.llm import LLMConfig


def orion_will_synthesize_assistant_reply(
    assistant: "VoiceAssistant", messages: list, llm_config: Optional[LLMConfig] = None
) -> LLMStream:
    return assistant.llm.chat(messages=messages, llm_config=llm_config)


async def _llm_stream_to_str_iterable(
    stream,
) -> AsyncIterable[str]:
    logger.debug("Continue streaming from LLM...")
    async for chunk in stream:
        chunk_str = chunk.decode("utf-8")
        logger.debug(f"Continue streaming from LLM: {chunk_str}")
        yield chunk_str
