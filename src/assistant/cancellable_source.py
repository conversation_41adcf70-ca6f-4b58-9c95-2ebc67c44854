from __future__ import annotations

import asyncio
from typing import Any, Callable, Dict, Generic, Optional, Set, TypeVar, AsyncIterable

from livekit import rtc
from livekit.agents.voice_assistant.cancellable_source import (
    CancellableAudioSource,
    PlayoutHandle,
)
from livekit.agents import transcription, utils
from loguru import logger

from src.messages.run import ActionResultMessage


T = TypeVar("T")


class OrionCancellationSource(CancellableAudioSource):
    def play(
        self,
        playout_source: AsyncIterable[rtc.AudioFrame],
        transcription_fwd: transcription.TTSSegmentsForwarder | None = None,
        action_result_message: Dict = None,
    ) -> PlayoutHandle:
        if self._closed:
            raise ValueError("cancellable source is closed")

        handle = PlayoutHandle(
            playout_source=playout_source, transcription_fwd=transcription_fwd
        )
        self._playout_atask = asyncio.create_task(
            self._playout_task(self._playout_atask, handle, action_result_message)
        )

        return handle

    @utils.log_exceptions(logger=logger)
    async def _playout_task(
        self,
        old_task: asyncio.Task[None] | None,
        handle: PlayoutHandle,
        action_result_message: Dict = None,
    ) -> None:
        def _should_break():
            eps = 0.1
            return handle.interrupted and self._vol_filter.filtered() <= eps

        first_frame = True
        cancelled = False

        try:
            if old_task is not None:
                await utils.aio.gracefully_cancel(old_task)

            logger.debug("CancellableAudioSource._playout_task: started")

            async for frame in handle._playout_source:
                if first_frame:
                    if handle._tr_fwd is not None:
                        handle._tr_fwd.segment_playout_started()

                    self.emit("playout_started")
                    first_frame = False

                if _should_break():
                    cancelled = True
                    break

                # divide the frame by chunks of 20ms
                ms20 = frame.sample_rate // 50
                i = 0
                while i < len(frame.data):
                    if _should_break():
                        cancelled = True
                        break

                    rem = min(ms20, len(frame.data) - i)
                    data = frame.data[i : i + rem]
                    i += rem

                    tv = self._target_volume if not handle.interrupted else 0.0
                    dt = 1 / len(data)
                    for si in range(0, len(data)):
                        vol = self._vol_filter.apply(dt, tv)
                        data[si] = int((data[si] / 32768) * vol * 32768)

                    chunk_frame = rtc.AudioFrame(
                        data=data.tobytes(),
                        sample_rate=frame.sample_rate,
                        num_channels=frame.num_channels,
                        samples_per_channel=rem,
                    )
                    await self._source.capture_frame(chunk_frame)
                    handle._time_played += rem / frame.sample_rate
        finally:
            if not first_frame:
                if handle._tr_fwd is not None and not cancelled:
                    handle._tr_fwd.segment_playout_finished()

                self.emit("playout_stopped", cancelled)

            handle._done_fut.set_result(None)
            if handle._tr_fwd is not None:
                if not cancelled:  # 2025-04-22 如果正常结束，需要等待transcription, 否则会导致transcription的最后一包来不及发
                    logger.debug("transcription is not finished, wait for 0.1s")
                    await asyncio.sleep(0.1)

                await handle._tr_fwd.aclose()
            logger.debug("CancellableAudioSource._playout_task: ended")

        logger.info(f"[PlayoutTask] action_result_message: {action_result_message}")

        if action_result_message:
            logger.debug(f"Audio action callback msg: {action_result_message}")
            await self.async_emit("playout_task_done", action_result_message)

    async def async_emit(self, event: T, *args: Any, **kwargs: Any) -> None:
        if event in self._events:
            callables = self._events[event].copy()
            for callback in callables:
                await callback(*args, **kwargs)
