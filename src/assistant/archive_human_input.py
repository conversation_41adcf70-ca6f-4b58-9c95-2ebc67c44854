import asyncio
from typing import Any, TypeVar

T = TypeVar("T")

from livekit import rtc
from livekit.agents import (
    stt as speech_to_text,
)
from livekit.agents import (
    transcription,
    utils,
)
from livekit.agents import (
    vad as voice_activity_detection,
)
from livekit.agents.voice_assistant.human_input import HumanInput
from livekit.plugins import openai
from loguru import logger

from src.assistant.stream_adapter import OrionStreamAdapter
from src.plugins import orion_asr
from src.utils.async_utils import gracefully_cancel


class OrionHumanInput(HumanInput):
    @utils.log_exceptions(logger=logger)
    async def _recognize_task(self, audio_stream: rtc.AudioStream) -> None:
        """
        Receive the frames from the user audio stream and detect voice activity.
        """
        # stats = await self._subscribed_track.get_stats()
        # logger.error(f"Microphone Stats {self._subscribed_track.volume}")
        if isinstance(self._stt, openai.STT):
            stt = OrionStreamAdapter(stt=self._stt, vad=self._vad)
        elif isinstance(self._stt, orion_asr.STT):
            stt = OrionStreamAdapter(stt=self._stt, vad=self._vad)
        else:
            stt = self._stt

        logger.info(f"stt type: {type(stt)}")
        stt_stream = stt.stream()
        self._stt_stream = stt_stream

        vad_stream = self._vad.stream()
        self._vad_stream = vad_stream

        stt_forwarder = None
        if self._transcription:
            stt_forwarder = transcription.STTSegmentsForwarder(
                room=self._room,
                participant=self._participant,
                track=self._subscribed_track,
            )

        async def _audio_stream_co() -> None:
            # forward the audio stream to the VAD and STT streams
            # buffer_ = []
            # i = 0
            async for ev in audio_stream:
                # convert sample rate to 16k
                # logger.info(
                #     f"[OrionHumanInput] Audio stream frame: {ev.frame.sample_rate} {ev.frame.num_channels} {ev.frame.samples_per_channel}"
                # )
                # count = len(bytes(ev.frame.data))
                # logger.info(f"[OrionHumanInput] Audio stream frame count: {count}")
                # stt_frame = ev.frame.remix_and_resample(16000, 1)
                # debug stt frame
                stt_frame = ev.frame
                stt_stream.push_frame(stt_frame)
                # buffer_.append(stt_frame)
                # i += 1
                # if i % 1000 == 0:
                #     buffer = agents.utils.merge_frames(buffer_)
                #     buffer_ = []
                #     io_buffer = io.BytesIO()
                #     with wave.open(io_buffer, "wb") as wav:
                #         wav.setnchannels(buffer.num_channels)
                #         wav.setsampwidth(2)
                #         wav.setframerate(buffer.sample_rate)
                #         wav.writeframes(buffer.data)
                #         print(
                #             f"buffer info {buffer.sample_rate} {buffer.num_channels} {buffer.samples_per_channel}"
                #         )
                #
                #     # !!! save wav file with a random uuid
                #     wav_file = f"!!!!!before_vad_111111{uuid.uuid4()}.wav"
                #     with open(wav_file, "wb") as f:
                #         f.write(io_buffer.getvalue())

                vad_stream.push_frame(ev.frame)

        async def _vad_stream_co() -> None:
            async for ev in vad_stream:
                if ev.type == voice_activity_detection.VADEventType.START_OF_SPEECH:
                    # logger.debug("[!!!] vad detect start of speech")
                    self._speaking = True
                    self.emit("start_of_speech", ev)
                elif ev.type == voice_activity_detection.VADEventType.INFERENCE_DONE:
                    self._speech_probability = ev.probability
                    await self.async_emit("vad_inference_done", ev)
                elif ev.type == voice_activity_detection.VADEventType.END_OF_SPEECH:
                    # logger.debug("[!!!] vad detect end of speech")
                    self._speaking = False
                    self.emit("end_of_speech", ev)

        async def _stt_stream_co() -> None:
            async for ev in stt_stream:
                if stt_forwarder is not None:
                    stt_forwarder.update(ev)

                if ev.type == speech_to_text.SpeechEventType.FINAL_TRANSCRIPT:
                    self.emit("final_transcript", ev)
                elif ev.type == speech_to_text.SpeechEventType.INTERIM_TRANSCRIPT:
                    self.emit("interim_transcript", ev)

        tasks = [
            asyncio.create_task(_audio_stream_co()),
            asyncio.create_task(_vad_stream_co()),
            asyncio.create_task(_stt_stream_co()),
        ]
        try:
            await asyncio.gather(*tasks)
        finally:
            await gracefully_cancel(*tasks)

            if stt_forwarder is not None:
                await stt_forwarder.aclose()

            await stt_stream.aclose()
            await vad_stream.aclose()

    async def async_emit(self, event: T, *args: Any, **kwargs: Any) -> None:
        if event in self._events:
            callables = self._events[event].copy()
            for callback in callables:
                await callback(*args, **kwargs)
