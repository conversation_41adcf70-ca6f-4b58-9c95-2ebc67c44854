import asyncio
import dataclasses
import json
import os
import time
import traceback
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor
from datetime import datetime
from functools import partial
from typing import Dict, List, Optional
import pytz

import aiohttp
import mem0
import redis
from loguru import logger
from pydantic import BaseModel
from redis import Redis
from rich import print

from src.assistant.planner.recommend_prompts import (
    BASE_REQUIREMENTS,
    FESTIVALS,
    HOLIDAY_PROMPTS,
    LOCATION_PROMPTS,
    MULTI_PERSON_EXAMPLES,
    MULTI_PERSON_TEMPLATE,
    SINGLE_PERSON_EXAMPLES,
    SINGLE_PERSON_NO_HISTORY_TEMPLATE,
    SINGLE_PERSON_WITH_HISTORY_TEMPLATE,
    TIME_PROMPTS,
    VISION_PROMPT,
    WEATHER_PROMPTS,
    WEEKEND_PROMPTS,
)
from src.common.constant import RECOMMEND_ACTIONS
from src.common.toolkit import <PERSON><PERSON><PERSON><PERSON>it
from src.session_manager.chat_context import Chat<PERSON>ontext
from src.session_manager.memory import USER_MEMORY_PROMPT
from src.settings import agent_setting
from src.utils.async_utils import callback
from src.utils.feishu_alarm import send_feishu_alarm
from src.utils.general_environment_sense import (
    CalendarInfo,
    WeatherInfo,
    calendar,
    get_weather,
)
from src.utils.llm import LLMManager
from src.utils.mem0_adapter import _mem0_embedding_adapter
from src.utils.i18n import _
from src.utils.handle_image import get_image_content


OPENAI_IMAGE_PREFIX = "data:image/jpeg;base64,"

# Define constant at module level
PUNCTUATION_MARKS = {"，", "。", ",", ".", "！", "？", "；", "：", "!", "?"}


class FaceInfoResult(BaseModel):
    face_id: str = ""  # face_id 为空时，表示客户端未能根据人脸识别结果提取到用户信息
    user_name: str = ""
    personal_welcome_message: str = ""


async def handle_static_follow_up_action(memory, robot, followup_prompt):
    logger.info("Handle static follow up action")

    from src.utils.async_utils import get_handle_static_follow_up_action_language
    from src.utils.language_utils import detect_language

    chat_context_messages = (await memory.get_chat_context()).messages
    chat_history_prompt = "\n".join(
        LLMToolKit.build_conversation_progress(chat_context_messages)
    )

    latest_user_sentence = LLMToolKit.get_latest_sentence(chat_context_messages)
    detect_lang = detect_language(
        latest_user_sentence, robot.language, robot.multilingual
    )
    logger.info(
        f"[handle_static_follow_up_action] latest_user_sentence:{latest_user_sentence}"
    )
    logger.info(f"[handle_static_follow_up_action] detect_lang:{detect_lang}")

    language = get_handle_static_follow_up_action_language(robot, detect_lang)
    logger.info(f"[handle_static_follow_up_action] language:{language}")

    if not followup_prompt:
        followup_prompt = f"""Goal: Establish continuous, engaging conversations with users.
Skills list:
{json.dumps(RECOMMEND_ACTIONS, ensure_ascii=False, indent=4)}
===============================================
Current robot status:
{robot.robot_status_info()}
===============================================
Chat history:
{chat_history_prompt}
===============================================
Communication guidelines:
1. Note that you are identified as "assistant" in the chat history. Never repeat anything that "assistant" has already said in the chat history. Ensure each response is new and meaningful.
2. Your task is to maintain continuous dialogue. When user intent is unclear, actively ask questions based on history, current page content, and user's recent interests to guide the conversation. Since user questions have been answered, focus only on asking the next question.
3. Chat with users naturally, prioritizing conversation flow and user experience, only recommending relevant skills at appropriate moments.
4. Based on conversation history, skills list, and current status, prioritize recommending features relevant to user's current context and interests, avoiding repeated recommendations of the same feature.
5. Never include any phrases like "do you need help" in the conversation. You can engage in casual chat, but ensure the conversation is meaningful. If there are no suitable recommendations, output "暂无互动".
6. Flexibly apply contextual recommendations, considering user's current status and environmental information. For example:
   - Recommend hot pot restaurants in cold weather
   - Recommend tourist attractions or itineraries when users mention travel plans
   - Recommend relaxation techniques or leisure activities when users mention work stress
7. Reply in *{language}*
Follow all rules in the communication guidelines carefully to provide your answer and keep the words CONCISE (no more than 20 words in total)."""

    result = await LLMManager.invoke_generate_text_model(
        messages=[
            {
                "role": "system",
                "content": LLMToolKit.build_system_prompt(
                    robot.agent_id,
                    robot.APP_ID,
                    robot.PERSONA,
                    robot.LANGUAGE_STYLE,
                    language=robot.language,
                    objective=robot.OBJECTIVE,
                ),
            },
            {
                "role": "user",
                "content": followup_prompt,
            },
        ],
    )
    logger.info(f"[Followup] {followup_prompt}\n{result}")
    if "no interaction" in result or "暂无互动" in result:
        logger.info("No interaction")
        return

    if not result:
        logger.error("Failed to get GPT4O result")
        return

    return result.content


class WelcomeGenerator:
    redis_client: Redis

    def __init__(
        self,
        redis_client: "redis.Redis",
        mem0_client: "mem0.Memory",
        image_info: Dict,
        location: str,
        geo_location: str,
        device_id: str,
        use_history: bool = True,
        prompt: Optional[
            str
        ] = None,  # 自定义prompt，如果用户有自定义的prompt，直接使用用户自定义的prompt
        weekend_prompts: Optional[Dict] = WEEKEND_PROMPTS,
        weather_prompts: Optional[Dict] = WEATHER_PROMPTS,
        time_prompts: Optional[Dict] = TIME_PROMPTS,
        location_prompts: Optional[Dict] = LOCATION_PROMPTS,
        holiday_prompts: Optional[Dict] = HOLIDAY_PROMPTS,
        single_person_examples: Optional[str] = SINGLE_PERSON_EXAMPLES,
        multi_person_examples: Optional[str] = MULTI_PERSON_EXAMPLES,
        base_requirements: Optional[str] = BASE_REQUIREMENTS,
        vision_prompt: Optional[str] = VISION_PROMPT,
        mock: bool = False,
        mock_datetime: Optional[datetime] = None,
        mock_weather: Optional[WeatherInfo] = None,
        mock_calendar: Optional[CalendarInfo] = None,
        mock_previous_descriptions: Optional[List[str]] = None,
        mock_face_info: Optional[FaceInfoResult] = None,
        thread_pool: Optional[ThreadPoolExecutor] = None,
        persona: Optional[str] = None,
        language: str = "",
        timezone: str = "",
    ):
        self.redis_client = redis_client
        self.mem0_client = mem0_client
        self.image_info = image_info
        self.location = location
        self.geo_location = geo_location
        self.use_history = use_history
        self.elapse_info = {}
        self.debug_info = {}
        self.start_time = time.time()
        self.device_id = device_id
        self.language = language
        self.timezone = timezone
        # Prompts can be customized
        self.prompt = prompt
        self.weather_prompts = weather_prompts
        self.time_prompts = time_prompts
        self.location_prompts = location_prompts
        self.holiday_prompts = holiday_prompts
        self.single_person_examples = single_person_examples
        self.multi_person_examples = multi_person_examples
        self.base_requirements = base_requirements
        self.weekend_prompts = weekend_prompts
        self.vision_prompt = vision_prompt

        # Mock data for testing
        self.mock = mock
        self.mock_datetime = mock_datetime
        self.mock_weather = mock_weather
        self.mock_calendar = mock_calendar
        self.mock_previous_descriptions = mock_previous_descriptions
        self.mock_face_info = mock_face_info
        self.thread_pool = thread_pool

        self.persona = persona

    async def _generate_environment_prompt(
        self,
        current_time: datetime,
        weather_info: WeatherInfo,
        calendar_info: CalendarInfo,
    ) -> str:
        """生成环境信息提示语

        Returns:
            str: 包含地点、时间、天气和日历信息的环境提示语
        """
        current_env_prompt = f"当前地点：{self.location}"

        # 添加时间信息
        current_env_prompt += (
            f"""\n当前时间：{current_time.strftime("%Y-%m-%d %H:%M:%S")}"""
        )

        # 添加天气信息
        if weather_info:
            current_env_prompt += f"""\n实时天气情况：天气：{weather_info.weather}，温度：{weather_info.temperature}°C，风力：{weather_info.wind_speed}级"""

        # 添加日历信息
        if calendar_info:
            if calendar_info.festival in FESTIVALS:
                current_env_prompt += f"""\n今天的节日：{calendar_info.festival}"""
            if calendar_info.lunar:
                current_env_prompt += f"""\n今天的农历日期：{calendar_info.lunar}"""
            if calendar_info.solarterm:
                current_env_prompt += f"""\n今天的节气：{calendar_info.solarterm}"""
            if calendar_info.week:
                current_env_prompt += f"""\n今天星期几：{calendar_info.week}"""
            if calendar_info.motto:
                current_env_prompt += f"""\n今天的励志格言：{calendar_info.motto}"""

        self.debug_info["current_env_prompt"] = current_env_prompt
        return current_env_prompt

    async def run(self) -> Optional[str]:
        """Main method to generate welcome text"""
        # create tasks for concurrent execution
        logger.info(f"Start to generate welcome text for image: {self.image_info}")
        if self.prompt:
            prompt = self.prompt.format(
                persona_core_objective=self.persona,
            )  # 补充人设信息
            logger.info(f"Use custom prompt: {prompt}")
            try:
                welcome_text = await self._get_llm_result(prompt)
            except Exception as e:
                logger.error(f"Error invoking LLM: {e}")
            logger.info(f"Welcome text: {welcome_text}")
            return welcome_text

        appearance_task = asyncio.create_task(self.get_appearance_description())
        weather_task = asyncio.create_task(
            get_weather(self.geo_location, self.language)
        )
        calendar_task = asyncio.create_task(
            calendar(timezone=self.timezone, language=self.language)
        )
        face_task = asyncio.create_task(self.get_face_info())
        # Wait for both tasks to complete
        try:
            model_result = await appearance_task
        except Exception as e:
            logger.error(f"Error getting appearance info: {e}")
            model_result = None

        if model_result is None:  # 如果获取外观描述失败，兜底
            face_task.cancel()
            model_result = {"person_count": -1, "description": "暂无描述"}

        is_single_person = model_result.get("person_count") == 1
        appearance_description = model_result.get("description")
        self.debug_info["appearance_model_result"] = model_result
        self.debug_info["appearance_model"] = agent_setting.vision_model

        face_info = FaceInfoResult()
        if not is_single_person:
            logger.info("Multiple person detected")
            face_task.cancel()
        else:
            face_info: FaceInfoResult = await face_task
            logger.info(
                f"Face id: {face_info.face_id}, user name: {face_info.user_name} personal welcome message: {face_info.personal_welcome_message}"
            )

        if self.mock and self.mock_face_info:
            face_info = self.mock_face_info
        self.debug_info["face_info"] = face_info.model_dump()

        start_time = time.time()
        weather_info: WeatherInfo = await weather_task
        self.elapse_info["get_weather_time"] = time.time() - start_time  # nearly 0s
        logger.info(f"Weather info: {weather_info}")
        if weather_info:
            self.debug_info["weather_info"] = dataclasses.asdict(weather_info)

        start_time = time.time()
        calendar_info: CalendarInfo = await calendar_task
        self.elapse_info["get_calendar_time"] = time.time() - start_time  # nearly 0s
        logger.info(f"Calendar info: {calendar_info}")
        if calendar_info:
            self.debug_info["calendar_info"] = dataclasses.asdict(calendar_info)

        if (
            is_single_person and face_info.face_id
        ):  # only get previous descriptions for single person, skip for multi-person
            previous_descriptions = await self.get_previous_descriptions(
                face_info.face_id
            )
        else:
            previous_descriptions = []
        logger.info(f"Previous descriptions: {previous_descriptions}")
        self.debug_info["previous_descriptions"] = previous_descriptions

        current_time = datetime.now()
        try:
            tz = pytz.timezone(self.timezone)
            # 正确方法：先获取UTC时间，再转换到目标时区
            current_time = datetime.now(pytz.UTC).astimezone(tz)
            logger.debug(f"成功设置时区: {self.timezone}, 当前时间: {current_time}")
        except Exception as e:
            logger.error(f"设置时区失败: {e}，使用系统默认时区")

        if self.mock:
            if self.mock_weather:
                weather_info = self.mock_weather
            if self.mock_calendar:
                calendar_info = self.mock_calendar
            if self.mock_datetime:
                current_time = self.mock_datetime

        context_interactions = await self.get_context_interactions(
            current_time, weather_info, calendar_info
        )
        self.debug_info["context_interactions"] = context_interactions

        name_requirements = self.get_name_requirements(
            user_name=face_info.user_name,
            personal_welcome_message=face_info.personal_welcome_message,
            is_multi_person=not is_single_person,
        )
        self.debug_info["name_requirements"] = name_requirements

        base_requirements = self.base_requirements.format(
            name_requirements=name_requirements
        )
        self.debug_info["base_requirements"] = base_requirements

        if self.mock and self.mock_previous_descriptions:
            previous_descriptions = self.mock_previous_descriptions
            self.debug_info["mock_previous_descriptions"] = previous_descriptions

        # 使用新的函数生成环境提示语
        current_env_prompt = await self._generate_environment_prompt(
            current_time, weather_info, calendar_info
        )

        if face_info.personal_welcome_message:
            # 使用常量来检查结尾是否是标点符号
            if (
                face_info.personal_welcome_message
                and face_info.personal_welcome_message[-1] in PUNCTUATION_MARKS
            ):
                personal_welcome_message_prompt = (
                    f"{face_info.personal_welcome_message}... "
                )
            else:
                personal_welcome_message_prompt = (
                    f"{face_info.personal_welcome_message}，... "
                )
        else:
            personal_welcome_message_prompt = ""

        if is_single_person:
            if previous_descriptions:
                prompt = SINGLE_PERSON_WITH_HISTORY_TEMPLATE.format(
                    model_result=appearance_description,
                    previous_appearance_descriptions="\n".join(previous_descriptions),
                    BASE_REQUIREMENTS=base_requirements,
                    context_interactions=context_interactions,
                    SINGLE_PERSON_EXAMPLES=self.single_person_examples,
                    PERSONAL_WELCOME_MESSAGE=personal_welcome_message_prompt,
                    current_env=current_env_prompt,
                    persona_core_objective=self.persona,
                    language=self.language,
                )
            else:
                prompt = SINGLE_PERSON_NO_HISTORY_TEMPLATE.format(
                    model_result=appearance_description,
                    BASE_REQUIREMENTS=base_requirements,
                    context_interactions=context_interactions,
                    SINGLE_PERSON_EXAMPLES=self.single_person_examples,
                    current_env=current_env_prompt,
                    PERSONAL_WELCOME_MESSAGE=personal_welcome_message_prompt,
                    persona_core_objective=self.persona,
                    language=self.language,
                )
        else:
            prompt = MULTI_PERSON_TEMPLATE.format(
                model_result=appearance_description,
                BASE_REQUIREMENTS=base_requirements,
                context_interactions=context_interactions,
                MULTI_PERSON_EXAMPLES=self.multi_person_examples,
                current_env=current_env_prompt,
                persona_core_objective=self.persona,
                language=self.language,
            )

        self.debug_info["prompt"] = prompt
        print(f"Prompt: {prompt}")
        start_time = time.time()

        try:
            welcome_text = await self._get_llm_result(prompt)
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"Error invoking LLM: {e}")
            return

        if personal_welcome_message_prompt:
            if welcome_text.startswith(f"{face_info.personal_welcome_message}"):
                welcome_text = welcome_text[
                    len(f"{face_info.personal_welcome_message}") :
                ]
                # Use the constant instead of inline set
                if welcome_text and welcome_text[0] in PUNCTUATION_MARKS:
                    welcome_text = welcome_text[1:]

        self.elapse_info["generate_welcome_time"] = time.time() - start_time

        if is_single_person and face_info.face_id and self.thread_pool:
            start_time = time.time()
            self.save_appearance_to_memory(appearance_description, face_info.face_id)
            self.elapse_info["save_appearance_time"] = time.time() - start_time

        self.elapse_info["total_time"] = time.time() - self.start_time
        logger.info(f"Welcome text: {welcome_text} Elapse info: {self.elapse_info}")

        return welcome_text

    async def _get_llm_result(self, prompt: str) -> str:
        """Get LLM result"""
        try:
            welcome_result = await LLMManager.invoke_generate_text_model(
                messages=[{"role": "user", "content": prompt}]
            )
            logger.debug(f"GPT4 result: {welcome_result}")
            welcome_text = welcome_result.content
        except Exception as e:
            logger.error(f"Error invoking GPT4: {e}")
            return
        return welcome_text

    def get_name_requirements(
        self,
        user_name: str = "",
        personal_welcome_message: str = "",
        is_multi_person: bool = False,
    ) -> str:
        """Get name requirements based on user name and person count"""
        if is_multi_person:
            return "直接用'你们'来称呼用户"
        else:
            if user_name:
                if personal_welcome_message and user_name in personal_welcome_message:
                    return "直接用'你'来称呼用户"
                else:
                    return f"直接用'{user_name}'来称呼用户"
            else:
                return "直接用'你'来称呼用户"

    async def get_face_info(self, timeout: float = 1.5) -> FaceInfoResult:
        """Get face_id and user_name from redis with retry"""
        key = f"face_id_{agent_setting.env}_{self.image_info['image_id']}"
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                face_id = self.redis_client.hget(key, "face_id")
                if face_id is None:
                    raise ValueError(
                        f"Wait Sync face info for image_id: {self.image_info['image_id']}"
                    )
                user_name = self.redis_client.hget(key, "user_name") or ""
                personal_welcome_message = (
                    self.redis_client.hget(key, "personal_welcome_message") or ""
                )
                logger.info(
                    f"Get face_id, user_name, personal_welcome_message from redis: {face_id} {user_name} {personal_welcome_message}"
                )
                try:
                    self.redis_client.expire(key, 1)
                except Exception as e:
                    logger.error(f"Error setting expire for key {key}: {str(e)}")

                self.elapse_info["get_face_id_time"] = time.time() - start_time
                return FaceInfoResult(
                    face_id=face_id,
                    user_name=user_name,
                    personal_welcome_message=personal_welcome_message,
                )
            except Exception as e:
                logger.info(f"Wait for face_id from redis: {str(e)}")
                await asyncio.sleep(0.2)
                continue

        logger.warning(
            f"Timeout waiting for face_id for image_id: {self.image_info['image_id']}"
        )
        return FaceInfoResult()

    async def get_appearance_description(self) -> Optional[Dict]:
        """Get appearance description from multimodal model"""
        prompt = self.vision_prompt
        image_content = await get_image_content(self.image_info["image_url"])

        payload = {
            "model": agent_setting.vision_model,
            "temperature": 0.0,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": image_content},
                        },
                        {"text": prompt, "type": "text"},
                    ],
                }
            ],
        }

        start_time = time.time()
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {agent_setting.vision_api_key}",
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{agent_setting.vision_base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=5),
                ) as response:
                    result = await response.json()
                    logger.info(f"[get_appearance_description] raw result: {result}")
                    result = (
                        result.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                    )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            err_msg = f"Failed to get multimodal result: error: {e.__class__.__name__} payload:{payload}"
            logger.error(err_msg)
            await send_feishu_alarm(err_msg)
            raise e

        if not result:
            logger.error("Failed to get multimodal result")
            raise Exception(
                f"Failed to get multimodal result. image_url: {self.image_info['image_url']} request_url: {f'{agent_setting.vision_base_url}/chat/completions'}"
            )

        # parse json
        if result:
            # 定义需要移除的前缀列表
            prefixes_to_remove = [
                "```json",
                "```",
                "单人示例：",
                "多人示例：",
                "单人示例:",
                "多人示例:",
            ]

            # 移除结尾的 ```
            if result.endswith("```"):
                result = result[:-3]

            # 移除开头的前缀
            for prefix in prefixes_to_remove:
                if result.startswith(prefix):
                    result = result[len(prefix) :]
                    break

            result = result.strip()

            try:
                result = json.loads(result)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON: {e}")
                raise e

        logger.info(
            f"get_appearance_description result: {result} elapsed_time: {time.time() - start_time}"
        )
        self.elapse_info["multimodal_time"] = time.time() - start_time
        return result

    async def get_previous_descriptions(self, user_id: str) -> List[str]:
        """Get previous appearance descriptions from mem0"""
        if not self.use_history:
            return []

        try:
            start_time = time.time()
            raw_result = self.mem0_client.search(
                _("我近期的形象"),
                user_id=user_id,
                filters={"category": "appearance"},
            )
            self.elapse_info["mem0_time"] = time.time() - start_time

            # 按天汇总记忆
            descriptions_by_date = {}
            for mem in raw_result.get("results", []):
                timestamp = mem.get("updated_at") or mem.get("created_at")
                description = mem.get("memory")
                if timestamp and description:
                    try:
                        # 转换时间戳到datetime
                        dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                        # 获取日期（不含时间）
                        date = dt.date()

                        # 将同一天的描述合并到一个列表中
                        if date in descriptions_by_date:
                            if (
                                description not in descriptions_by_date[date]
                            ):  # 避免重复
                                descriptions_by_date[date].append(description)
                        else:
                            descriptions_by_date[date] = [description]

                    except (ValueError, TypeError) as e:
                        logger.error(f"Failed to parse timestamp {timestamp}: {e}")
                        continue

            # 按日期排序并获取最近两天的记录
            sorted_dates = sorted(descriptions_by_date.keys(), reverse=True)[:2]

            # 组合最终结果
            sorted_descriptions = []
            for date in sorted_dates:
                # 将同一天的描述用分号连接
                combined_description = "; ".join(descriptions_by_date[date])
                # 使用当天23:59:59的时间戳，保证同一天的记录排序在一起
                day_timestamp = int(
                    datetime.combine(date, datetime.max.time()).timestamp()
                )
                sorted_descriptions.append((day_timestamp, combined_description))

            return [
                f"记录时间：{datetime.fromtimestamp(ts).strftime('%Y-%m-%d')} 外貌特点：{desc}"
                for ts, desc in sorted_descriptions
            ]

        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"Failed to get previous descriptions: {e}")
            return []

    def get_weather_based_prompt(
        self, weather_info: Optional[WeatherInfo]
    ) -> Optional[str]:
        """Get weather-based interaction prompt"""
        if not weather_info:
            return None

        # Special weather types (highest priority)
        if weather_info.weather in [
            "大雨",
            "暴雨",
            "大暴雨",
            "特大暴雨",
            "中雨",
            "中雨-大雨",
            "大雨-暴雨",
            "暴雨-大暴雨",
            "大暴雨-特大暴雨",
            "极端降雨",
            "强雷阵雨",
            "雷阵雨",
            # EN
            "Moderate or heavy rain at times",
            "Heavy rain at times",
            "Heavy rain",
            "Moderate or heavy freezing rain",
            "Moderate or heavy rain shower",
            "Torrential rain shower",
            "Moderate or heavy rain with thunder",
        ]:
            weather_type = self.weather_prompts["rainy"]
            return self._concat_prompts(
                f"天气特殊事件：{weather_info.weather}\n你可以根据天气进行互动，要严格按照用户的衣着来给出回应，不能随意编造。互动示例：",
                weather_type["prompts"],
            )

        if weather_info.weather in [
            "小雪",
            "中雪",
            "大雪",
            "暴雪",
            "小雪-中雪",
            "中雪-大雪",
            "大雪-暴雪",
            "极端降雪",
            "强降雪",
            "暴雪-特大暴雪",
            # EN
            "Blowing snow",
            "Patchy moderate snow",
            "Moderate snow",
            "Patchy heavy snow",
            "Heavy snow",
            "Blizzard",
        ]:
            weather_type = self.weather_prompts["snowy"]
            return self._concat_prompts(
                f"天气特殊事件：{weather_info.weather}\n你可以根据天气互动，互动示例：",
                weather_type["prompts"],
            )

        if weather_info.weather in [
            "大风",
            "烈风",
            "狂爆风",
            "风暴",
            "强风/劲风",
            "龙卷风",
            "狂爆风",
            "飓风",
        ]:
            weather_type = self.weather_prompts["windy"]
            return self._concat_prompts(
                f"天气特殊事件：{weather_info.weather}\n你可以根据天气互动，互动示例：",
                weather_type["prompts"],
            )

        if weather_info.wind_speed > 5:  # 海外版本靠风力来判断
            weather_type = self.weather_prompts["windy"]
            return self._concat_prompts(
                f"天气特殊事件：风力：{weather_info.wind_speed}\n你可以根据天气互动，互动示例：",
                weather_type["prompts"],
            )

        if weather_info.weather in ["霾", "中度霾", "重度霾", "严重霾"]:
            weather_type = self.weather_prompts["haze"]
            return self._concat_prompts(
                f"天气特殊事件：{weather_info.weather}\n你可以根据天气互动，互动示例：",
                weather_type["prompts"],
            )

        # Temperature-based prompts
        if weather_info.temperature >= 35:
            weather_type = self.weather_prompts["hot"]
        elif weather_info.temperature <= 0:
            weather_type = self.weather_prompts["cold"]
        else:
            return None

        return self._concat_prompts(
            "你可以根据天气互动，互动示例：", weather_type["prompts"]
        )

    def get_time_based_prompt(self, current_time: datetime) -> str:
        """Get time-based interaction prompt"""
        # Mon~Fri
        if current_time.weekday() > 5:  # Sat, Sun use weekend-based prompt
            return ""

        hour = current_time.hour
        minute = current_time.minute

        if hour < 10:
            time_type = self.time_prompts.get("morning")
        elif 10 <= hour < 11:
            if minute < 30:
                time_type = self.time_prompts.get("forenoon")
            else:
                time_type = self.time_prompts.get("noon1")
        elif 11 <= hour < 12:
            time_type = self.time_prompts.get("noon1")
        elif 12 <= hour < 14:
            time_type = self.time_prompts.get("noon")
        elif 14 <= hour < 16:
            time_type = self.time_prompts.get("afternoon")
        elif 16 <= hour < 20:
            time_type = self.time_prompts.get("evening")
        elif hour >= 20:
            time_type = self.time_prompts.get("night")
        else:
            return ""

        if not time_type:
            return ""

        return self._concat_prompts(
            f"特殊时间事件：{time_type['name']}\n你可以根据时间互动，互动示例：",
            time_type["prompts"],
        )

    def get_weekend_based_prompt(self, current_date: datetime) -> Optional[str]:
        """Get weekend-based interaction prompt"""
        # TODO: 根据时区获取做 weekday 判断
        weekday = current_date.weekday()

        if weekday == 5:  # Saturday
            weekend_type = self.weekend_prompts["saturday"]
        elif weekday == 6:  # Sunday
            weekend_type = self.weekend_prompts["sunday"]
        else:
            return None

        return self._concat_prompts(
            f"特殊时间事件：{weekend_type['name']}\n你可以根据周末互动，互动示例：",
            weekend_type["prompts"],
        )

    def get_location_based_prompt(self) -> Optional[str]:
        """Get location-based interaction prompt"""
        if self.location not in self.location_prompts:
            return None

        location_type = self.location_prompts[self.location]
        return self._concat_prompts(
            f"特殊地点事件：{location_type['name']}\n你可以根据地点互动，互动示例：",
            location_type["prompts"],
        )

    async def get_context_interactions(
        self,
        current_time: datetime,
        weather_info: Optional[WeatherInfo],
        calendar_info: Optional[CalendarInfo],
    ) -> str:
        """Get random context interactions"""
        available_prompts = []

        # Get all available prompts
        location_prompt = self.get_location_based_prompt()
        if location_prompt:
            available_prompts.append(location_prompt)

        if weather_info:
            weather_prompt = self.get_weather_based_prompt(weather_info)
            if weather_prompt:
                available_prompts.append(weather_prompt)

        time_prompt = self.get_time_based_prompt(current_time)
        if time_prompt:
            available_prompts.append(time_prompt)

        weekend_prompt = self.get_weekend_based_prompt(current_time)
        if weekend_prompt:
            available_prompts.append(weekend_prompt)

        holiday_prompt = self.get_holiday_based_prompt(calendar_info.festival)
        if holiday_prompt:
            available_prompts.append(holiday_prompt)

        if not available_prompts:
            return ""

        # Randomly select 1-2 prompts
        # num_prompts = random.randint(1, min(2, len(available_prompts)))
        # selected_prompts = random.sample(available_prompts, num_prompts)

        return "\n\n".join(available_prompts)

    def save_appearance_to_memory(self, model_result: str, user_id: str):
        """Save appearance description to memory using thread pool"""
        logger.bind(face_id=user_id).info(
            f"Saving appearance to memory: {model_result}"
        )

        # Create partial function with necessary parameters
        save_func = partial(
            self.mem0_client.add,
            model_result,
            user_id=user_id,
            metadata={"category": "appearance"},
            filters={"category": "appearance"},
        )

        try:
            # Execute storage operation in thread pool and add callback
            future = self.thread_pool.submit(save_func)
            future.add_done_callback(callback)
            return future  # 立即返回future对象，不等待完成
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.bind(face_id=user_id).error(
                f"Error submitting to thread pool: {e} {traceback.format_exc()}"
            )
            return None

    def get_holiday_based_prompt(self, festival: str) -> Optional[str]:
        """节日互动提示"""
        # Find the corresponding holiday from HOLIDAY_PROMPTS
        if festival and festival in FESTIVALS:
            return f"特殊节日事件: {festival}, 你可以根据节日互动，互动示例：\n{self.holiday_prompts}"

        return ""

    @staticmethod
    def _concat_prompts(prefix: str, prompts: List[str]) -> str:
        for prompt in prompts:
            prefix += f"\n- {prompt}"
        return prefix


async def handle_new_face_follow_up(
    image_info: Dict,
    location: str,
    geo_location: str,
    mem0_client: "mem0.Memory",
    redis_client: "redis.Redis" = None,
    use_history: bool = True,
    device_id: str = "",
    thread_pool: Optional[ThreadPoolExecutor] = None,
    persona: str = "",
    single_person_prompt: str = "",
    multiple_person_prompt: str = "",
    prompt: str = "",
    language: str = "",
    timezone: str = "",
) -> Optional[str]:
    if not single_person_prompt:
        single_person_prompt = SINGLE_PERSON_EXAMPLES
    if not multiple_person_prompt:
        multiple_person_prompt = MULTI_PERSON_EXAMPLES

    generator = WelcomeGenerator(
        redis_client=redis_client,
        mem0_client=mem0_client,
        image_info=image_info,
        location=location,
        geo_location=geo_location,
        use_history=use_history,
        device_id=device_id,
        thread_pool=thread_pool,
        persona=persona,
        single_person_examples=single_person_prompt,
        multi_person_examples=multiple_person_prompt,
        prompt=prompt,
        language=language,
        timezone=timezone,
    )
    return await generator.run()


async def delete_memory():
    config = {
        # "graph_store": {
        #     "provider": "neo4j",
        #     "config": {
        #         "url": "neo4j+s://2df96e51.databases.neo4j.io",
        #         "username": "neo4j",
        #         "password": "eVBj2QAboT885RcAENI57IIg_hM59ucYTJlIO95hG_M",
        #     },
        # },
        "version": "v1.1",
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": f"{agent_setting.env}_mem0_embeddings_bge_1024",
                "host": agent_setting.qdrant_host,
                "port": 6333,
                "embedding_model_dims": 1024,
            },
        },
        "custom_prompt": USER_MEMORY_PROMPT,
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o",
                "temperature": 0.2,
                "max_tokens": 1500,
            },
        },
    }

    mem0_client = mem0.Memory.from_config(config_dict=config)
    ret = mem0_client.delete_all(user_id="test_user")
    print(ret)


async def test_recommend():
    from src.common.agent_config import (
        LAUNCHER_AGENT_ID,
    )

    config = {
        # "graph_store": {
        #     "provider": "neo4j",
        #     "config": {
        #         "url": "neo4j+s://2df96e51.databases.neo4j.io",
        #         "username": "neo4j",
        #         "password": "eVBj2QAboT885RcAENI57IIg_hM59ucYTJlIO95hG_M",
        #     },
        # },
        "version": "v1.1",
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": f"{agent_setting.env}_mem0_embeddings_bge_1024",
                "host": agent_setting.qdrant_host,
                "port": 6333,
                "embedding_model_dims": 1024,
            },
        },
        "custom_prompt": USER_MEMORY_PROMPT,
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o",
                "temperature": 0.2,
                "max_tokens": 1500,
                "openai_base_url": agent_setting.plan_model_base_url,
            },
        },
    }

    mem0_client = mem0.Memory.from_config(config_dict=config)
    # monkey patch the embedding model
    mem0_client.embedding_model = _mem0_embedding_adapter()
    test_images = [
        # "https://beta-jiedai.ainirobot.com/orics/down/aios001_20241126_5d8a5197ed932d6d34f59ac3fbb3c244.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1213/f/b/2/9/fb299c449fc4f7882259157139a82da4.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1111/c/4/0/9/c409fca43e7e63591f24e8efa80c30e8.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1107/3/7/3/0/37301c448456c70d1a787cc3e2d25a85.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1107/5/f/f/e/5ffe628dde8229812490c0ced0fe5e83.jpeg",
        "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1113/7/8/1/c/781cdf5cb16ef560f4a19078c24d1463.jpeg",
        # "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1113/6/2/c/6/62c6735c9954f4bf2238bf624607dc84.jpeg",
    ]
    extra_test_images = [
        # "https://beta-jiedai.ainirobot.com/orics/down/aios001_20241011_43dfc781ee299cfc49e7a0f51a277def.jpeg",
        # "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1114/c/7/5/7/c7572297722d6c70e07b10b859567a0b.jpeg",
        # "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1114/d/a/3/c/da3c833ac780ad45dddb4096b5c67399.jpeg"
    ]
    # thread_pool = ThreadPoolExecutor(max_workers=5)
    for pic in extra_test_images + test_images:
        image_info = {
            "image_id": str(uuid.uuid4()),
            "image_url": pic,
        }
        generator = WelcomeGenerator(
            # prompt=AGENT_CONFIG[PROMOTE_AGENT_ID].recommend_prompt.get(
            #     "prompt", ""
            # ),  # 测试promotion opk prompt
            redis_client=redis.Redis(
                host=agent_setting.redis_host,
                port=agent_setting.redis_port,
                db=agent_setting.face_id_cache_db,
            ),
            mem0_client=mem0_client,
            image_info=image_info,
            location="前台",
            geo_location="北京",
            use_history=False,
            device_id="G" * 9,
            mock=True,
            mock_previous_descriptions=[
                "记录时间：2024-11-25 外貌特点：短发; 戴眼镜; 穿黑色外套; 穿白色内搭; 表情平静"
            ],
            mock_face_info=FaceInfoResult(
                face_id="cici",
                user_name="樊扬",
                personal_welcome_message="你好",
            ),
            # TODO 缺少robot
            persona=LLMToolKit.init_persona_core_objective(
                LAUNCHER_AGENT_ID, "app"
            ).persona,
            # mock_calendar=CalendarInfo(
            #     festival="元旦",
            #     date="2024-01-01",
            #     week="",
            #     lunar="",
            #     solarterm="",
            #     motto="",
            # ),
            thread_pool=None,
            # mock_datetime=datetime(2024, 1, 1, 10, 10, 29),
        )
        await generator.run()


async def test_static_handle_follow_up():
    from src.session_manager.chat_context import ChatMessage

    class MockMemory:
        def get_chat_context(self):
            return ChatContext(
                messages=[
                    ChatMessage(role="user", content="你好"),
                    ChatMessage(role="assistant", content="你好!很高兴见到你"),
                    ChatMessage(role="user", content="打开猎豹官网"),
                ]
            )

    class MockRobot:
        def robot_status_info(self):
            return """当前状态：正常运行中\n当前位置：前台\n当前时间：2024-01-01 10:00:00\n应用页面HTML:<b><d><m><d><d><h><a>猎豹移动官网</a></h><u><L><a>人工智能业务</a></l><l><a>云管理业务</a></l><l><a>国际广毕业务</a></l><L><a>
APP应用业务</a></l><l><a>新闻资讯</a></l><l><a>公司介绍</a></l><l><a>联系我们</a></l></u></d><d><d><d><d><d><a>Visit Link</a></d><d><a>Visit Link</a></d><d><a>Visit Liink</a></d><d><a>Vis
it Link</a></d></d></d><d><d><d><d><h>猎豹移动四大业务板块</h></d><d><a><d>人工智能业务</d><d>全球领先的AI大模型及软硬件机器人解决方案提供商</d></a><a><d>云管理业务</d><d>AI大模型
代云管理服务先行者</d></a><a><d>国际广告业务</0d><d>Facebook-级代理精准覆盖全球流量</d></a><a><d>APP应用业务</d><d>全球领先的工具应用开发商</d></a></d></d><d><d><h>人工智能业务
·猎户星空</h><s>全球领先的AI大模型及软硬件机器人解决方案提供商,\n产品和服务涵盖大模型、套件、AI应用、大模型机器...</s></d><d><d><d>模型</d><d>猎户星空大模型</d><d><d><d><d>套件</d><d>大模型
套件全家桶</d></d><d><d>应用</d><d>AI老板智囊团和数字员工</d></d><d><d>应用</d><d>大模型机器人<</d></d></d><d><d><d><h>云管理业务 ·聚云科技</h></d><d><h>聚云科技</h><p>多云管理服务提
供商(MSP),云管理服务先行者,\n致力于成为客户业务创新、数字化转型可信赖的合作伙伴</p><s><s><s>云原生赋能</s><s>多云管理平台</s><s>Data-0高性能系统数据框架</s></s></s></
告业务</h></d><d><h>国际广告业务</h><p>深耕中国电商、游李戏、应用、品牌等领域的出海营销服务,\n值得信赖的中国企业出海合作伙伴</p><s><s>品牌出海</s><s>电商出海</s><s>游戏出海</s><s>APP
出海</s></s></d></d><d><d><d><h>APP应用业务</h><s>聚焦产品为王、用户至上,打造新形式下ToC产品更极致的用户体验,\n不断满足用户工具软件服务需求</s></d></d><d><d><h>工具应用</h><h><s>全球领
先的工具应用开发商</s></h></d><d><d><d><d><h><a>猎豹清理大师</a></h><d><s>全球安卓优化明星软件,全球累积下载近30亿次,拥有碎片清理、卡慢优化、云相册、全自动清理等多项功能。</s></d><d><d><a>
全球下载量近30亿</a><a>国内应用市场评分同类领先</a></d></d></d><d><d><h><a>猎豹浏览器</a></h><d><s>知名双核安全浏览器,高速浏览、酷炫设计,BIPS云安全体系,专业级防木马、钓鱼能力,20多
年积累,保...</s></d></d></d></d><d><h>游戏</h><h><s>"轻游戏"概念的引领者、中国游戏出海CP合作伙伴</s></h></d><d><d><a>Play</a></d><d><h>Cheetah Games</h><p>猎豹移动为旗下游戏发行部门,
于2014年正式成立。经过多年发展,猎豹游戏已经构建了全球游戏发行网络,并...</p><a>了解更多</a></d></d></d></d></d><<d><d><d><u><l><a>隐私政策</a><l><l><a>营业执照</a></l><l><s>猎豹移动+2024
京公网安备11010502030117号,<a>京ICP备12038800号-7</a>【京B2-20221410】</s></l></u></d><d><d><d><u:><l>举报邮箱</l><l><a><EMAIL></a></l><l><l><l><s>举报入口</s></l><l>举报电话</l><l><a>010
62927779</a></โ></u></d></d></d></d></m></d><s><d><s><<t>circle-left3</t></s><s><t>circle-left4</t></s><s><t>anew-tab</t></s><s><t>github</t></s></d></s></b>"""

        from src.common.agent_config import LAUNCHER_AGENT_ID, Launcher_App_Id

        agent_id = LAUNCHER_AGENT_ID
        APP_ID = Launcher_App_Id

    memory = MockMemory()
    robot = MockRobot()

    result = await handle_static_follow_up_action(memory, robot)
    print(f"Follow up result: {result}")


if __name__ == "__main__":
    os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key

    # asyncio.run(delete_memory())
    asyncio.run(test_recommend())
    # asyncio.run(test_static_handle_follow_up())
