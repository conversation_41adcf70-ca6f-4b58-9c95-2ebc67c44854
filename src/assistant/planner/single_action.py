import asyncio
import copy
import time
import traceback
import uuid
from datetime import datetime
from typing import Dict, List, Optional
import json

from loguru import logger
from pydantic import BaseModel

from src.action.actions import ActionLib
from src.agent_core.intervention_agent import InterventionAgent
from src.agent_core.models.model import AgentParameter, InterventionAgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.assistant.model import _PlanInfo
from src.common.agent_config import (
    AGENT_CONFIG,
    AgentConfig,
    Opk_Cruise_App_Id,
    Opk_Guide_App_Id,
    Opk_Lead_App_Id,
    OverSea_Opk_Cruise_App_Id,
    OverSea_Opk_Guide_App_Id,
    OverSea_Opk_Lead_App_Id,
)
from src.common.constant import (
    KNOWLEDGE_QA_ACTION_NAME,
    Area,
    SynthesizeType,
    language_dict_zh,
    LANGUAGE_CODE_TO_ENGLISH_NAME,
)
from src.common.model import Debug
from src.intervene.intervene_action import ActionIntervenor, ActionResponse
from src.messages import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>Message,
    PlanContent,
)
from src.session_manager.robot import Robot
from src.settings import agent_setting
from src.common.toolkit import LLMToolKit
from src.utils.llm import LLMConfig

from .base import BasePlanner
from .transition_prompts import TransitionPrompts
from ...utils.language_utils import detect_language
from ...utils.llm import LLMManager, ModelResult, parse_output_to_dict


class ActionInterventionResult(BaseModel):
    """Result of action intervention process"""

    best_action: Optional[Dict] = None
    return_directly: bool = False
    elapsed_time: float = 0.0
    action_response: Optional["ActionResponse"] = None

    class Config:
        arbitrary_types_allowed = True


class InterventionResult(BaseModel):
    """Result of intervention process"""

    response: Optional["_PlanInfo"] = None
    debug_info: Dict = {}
    elapse_info: Dict = {}

    class Config:
        arbitrary_types_allowed = True


class SingleActionPlanner(BasePlanner):
    """
    A planner that generates a single action plan.
    """

    def _enable_intervention(self) -> bool:
        """
        Check if we should enable intervention.
        """
        if not self.robot.is_builtin_app:
            return False

        if agent_setting.region_version == Area.overseas:  # 海外版本暂时不支持干预
            return False

        if (
            self.robot.action_version not in ["draft", "oversea_draft"]
            and self.robot.action_version < "v1.0.3"
        ):  # 旧版本暂不支持干预
            return False

        if self.robot.APP_ID in [
            Opk_Lead_App_Id,
            Opk_Cruise_App_Id,
            OverSea_Opk_Lead_App_Id,
            OverSea_Opk_Cruise_App_Id,
        ]:  # 封闭式OPK不支持干预
            return False

        if self.robot.APP_ID in [
            Opk_Guide_App_Id,
            OverSea_Opk_Guide_App_Id,
        ]:  # 导览OPK有条件支持干预
            if not self.robot.enable_qa:
                return False

        return True

    def _get_language_prompt(self, query_text: str):
        language_code = detect_language(
            query_text,
            self.robot.language,
            self.robot.multilingual,
        )
        if agent_setting.region_version == Area.domestic:
            return language_dict_zh.get(language_code, language_code)

        return LANGUAGE_CODE_TO_ENGLISH_NAME.get(language_code, language_code)

    async def _async_plan_text(self, query_text: str, query_id: str):
        language_prompt = self._get_language_prompt(query_text)
        chat_context = await self.memory.get_chat_context(max_chat_history=2)
        conversation_progress = "\n".join(
            LLMToolKit.build_conversation_progress(chat_context.messages)
        )
        pco = LLMToolKit.init_persona_core_objective(
            self.robot.agent_id,
            self.robot.interface_state.app_id,
            self.robot.PERSONA,
            self.robot.LANGUAGE_STYLE,
            self.robot.OBJECTIVE,
        )
        start_at = time.time()
        robot_info = self.robot.robot_status_info()
        if self.robot.interface_state.interface_info:
            try:
                screen_info = (
                    self.robot.interface_state.interface_info[400:] + "..."
                    if len(self.robot.interface_state.interface_info) > 400
                    else self.robot.interface_state.interface_info
                )
                robot_info["屏幕信息"] = screen_info
            except Exception as e:
                logger.error(f"Error when get screen info: {e}")

        # 获取过渡话术prompt
        prompt = TransitionPrompts.get_prompt(
            query_text, language_prompt, conversation_progress
        )

        messages = [  # 提供信息内容越多，模型更倾向于直接作答
            {
                "role": "system",
                "content": f"You are a helpful assistant. Your language style is {pco.language_style}",
            },
            {"role": "user", "content": prompt},
        ]
        # llm_config = {"temperature": 0.7}
        # action_info = _ActionInfo(
        #     action_name="orion.agent.action.REALTIME_SAY",
        #     node_id=str(uuid.uuid4()),
        #     parameters={"messages": messages, "llm_config": llm_config},
        #     run_id=str(uuid.uuid4()),
        #     plan_id=str(uuid.uuid4()),
        #     add_to_chat_ctx=False,
        #     query_id=query_id,
        #     result_type="server_sent",
        #     version=self.robot.action_version,
        #     request_msg_id="",
        # )
        # self.action_ch.send_nowait(action_info)

        llm_config = LLMConfig(
            llm_model_name=agent_setting.generate_text_model,
            api_key=agent_setting.generate_text_model_api_key,
            base_url=agent_setting.generate_text_model_base_url,
            temperature=0.3,
        )

        try:
            result: ModelResult = await LLMManager.invoke_generate_text_model(
                messages=messages,
                llm_config=llm_config,
            )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"Error when invoke LLM: {e}")
            return

        # 解析JSON格式的返回结果
        if result.content:
            try:
                response_data, err_msg = parse_output_to_dict(result.content)
                if err_msg:
                    raise json.JSONDecodeError(err_msg, "", 0)
                is_chitchat = response_data.get("is_chitchat", True)
                transition_phrase = response_data.get("transition_phrase", "")

                # 如果不是闲聊且有过场话术，则播放
                if not is_chitchat and transition_phrase:
                    self._say(query_id, transition_phrase)
            except (json.JSONDecodeError, KeyError) as e:
                logger.error(
                    f"Failed to parse transition response JSON: {e}, content: {result.content}"
                )

        logger.info(
            f"{query_id} 过渡话术 Prompt: {messages}\n\nResponse: {result} Elapsed: {time.time() - start_at}"
        )

    async def plan(  # noqa
        self,
        query_text: str,
        query_id: str,
        elapse_info: Dict,
        synthesize_type: SynthesizeType,
        image_info: Dict | None = None,
        followup_prompt: str | None = None,
        block_action_fullnames: List[str] | None = None,
    ) -> Optional["_PlanInfo"]:
        """
        Generate a single action plan.
        :param query_text:
        :param query_id:
        :param elapse_info:
        :return:
        """
        async_play_plan_task = None
        if synthesize_type in [SynthesizeType.USER_QUERY]:
            if (
                agent_setting.turn_on_transition
                and self.robot.APP_ID
                not in [  # Guide OPK不支持过渡话术
                    Opk_Guide_App_Id,
                    OverSea_Opk_Guide_App_Id,
                ]
            ):
                async_play_plan_task = asyncio.create_task(
                    self._async_plan_text(
                        query_text,
                        query_id,
                        # agent_parameter.candidate_actions,
                    )
                )

        face_id = self.robot.face_id
        agent_logger = logger.bind(
            query_id=query_id,
            query=query_text,
            face_id=face_id,
            app_id=self.robot.APP_ID,
            agent_id=self.robot.agent_id,
        )
        agent_logger.info(
            f"Start single action planning {self.robot.APP_ID} {synthesize_type}"
        )
        # record page_id and app_id when start planning
        start_page_id = self.robot.interface_state.page_id
        start_app_id = self.robot.APP_ID

        start_at = time.time()
        elapse_info["plan_start_timestamp"] = time.time()

        # single action
        if not isinstance(self.robot, Robot):
            raise TypeError(
                f"Expected self.robot to be an instance of Robot, but got {type(self.robot)}"
            )

        if synthesize_type in [SynthesizeType.ACTION, SynthesizeType.EVENT]:  # 特殊场景
            candidate_actions = []

            for action in self.candidate_actions:
                agent_config: Optional[AgentConfig] = AGENT_CONFIG.get(
                    self.robot.agent_id
                )
                if not agent_config:  # fallback to SAY action
                    agent_logger.error(
                        f"Agent config not found for {self.robot.agent_id}"
                    )
                    candidate_actions = [
                        ActionLib().get_one_action(full_name="orion.agent.action.SAY")
                    ]
                    break

                agent_follow_up_trigger_actions = [
                    action.lower() for action in agent_config.follow_up_trigger_actions
                ]
                if action.full_name in agent_follow_up_trigger_actions:
                    candidate_actions.append(action)
            agent_logger.info(
                f"Candidate actions: {candidate_actions} for synthesize_type: {synthesize_type}"
            )
        else:
            logger.debug("Select candidate actions for user query")
            if self.robot.is_agent_sdk_compatible:
                logger.debug("Current use actions from robot")
                candidate_actions = copy.deepcopy(self.robot.actions)
                logger.debug(f"Candidate actions: {candidate_actions}")
            else:
                logger.debug("Current use built-in actions")
                candidate_actions = copy.deepcopy(self.candidate_actions)
                logger.debug(f"Candidate actions: {candidate_actions}")

            candidate_action_full_names = [
                action.full_name for action in candidate_actions
            ]

            if self.robot.app_config.enable_mcp:
                for action in ActionLib()._actions:  # add MCP actions
                    if action.full_name not in candidate_action_full_names:
                        if action.source == "mcp":
                            candidate_actions.append(action)

        if block_action_fullnames:
            candidate_actions = [
                action
                for action in candidate_actions
                if action.full_name.lower() not in block_action_fullnames
            ]

        if not candidate_actions:
            logger.warning(f"No candidate actions for query: {query_text}")
            return None

        # Temporary fix: 导览OPK支持禁用knowledge_qa
        if self.robot.APP_ID in [
            Opk_Guide_App_Id,
            OverSea_Opk_Guide_App_Id,
        ]:
            if self.robot.enable_qa:  # 提问过程不能停止
                candidate_actions = [
                    action
                    for action in candidate_actions
                    if action.full_name.lower()
                    not in [
                        "orion.agent.action.common_pause",
                        "orion.agent.action.pause",
                    ]
                ]
            else:  # 运动讲解过程不能问答
                candidate_actions = [
                    action
                    for action in candidate_actions
                    if action.full_name.lower()
                    not in [
                        f"orion.agent.action.{KNOWLEDGE_QA_ACTION_NAME.lower()}",
                        "orion.agent.action.knowledge_qa",
                        "orion.agent.action.say",
                    ]
                ]

        agent_parameter = AgentParameter(
            query=query_text,
            query_id=query_id,
            memory=self.memory,
            robot=self.robot,
            candidate_actions=candidate_actions,
            use_candidate_actions_directly=self.robot.is_agent_sdk_compatible,
            face_id=face_id,
            synthesize_type=synthesize_type,
            run_step_queue=self.run_step_queue,
            rag_manager=self.rag_manager,
        )
        if synthesize_type == SynthesizeType.RECOMMEND:
            if image_info:
                agent_logger.info("Start new face follow up")
                return await self._handle_new_face_follow_up(
                    query_id, agent_parameter, image_info
                )
            else:
                agent_logger.info("Start static follow up")
                return await self._handle_static_follow_up_action(
                    query_id, agent_parameter, followup_prompt
                )

        # if self.robot.interface_state.package_name not in SUPPORTED_PACKAGE_NAMES:
        #     self._sync_plan_status(
        #         query_id,
        #         status="rejected",
        #         message="抱歉，当前的界面无法规划，请您先进入到小豹APP",
        #         user_transcript=query_text,
        #     )
        #     return

        is_admin_mode = False
        if (
            query_text
            and agent_setting.region_version == Area.domestic
            and agent_setting.admin_forspoken in query_text
        ):
            is_admin_mode = True

        # load support actions
        load_support_action_start_time = time.time()
        agent_parameter.candidate_actions = await ActionLib().load_support_action(
            agent_parameter,
            is_admin_mode=is_admin_mode,
            use_candidate_actions_directly=self.robot.is_agent_sdk_compatible,
        )
        if (
            not agent_parameter.candidate_actions
            or len(agent_parameter.candidate_actions)
            == 1  # 如果只有1个action，只会是clarify action
        ):
            agent_logger.error(
                f"No support actions found for {self.robot.APP_ID} {self.robot.agent_id}"
            )
            return
        load_support_action_elapsed_time = time.time() - load_support_action_start_time
        elapse_info["load_support_action_elapsed_time"] = (
            load_support_action_elapsed_time
        )

        # Run qa intervention and action intervention in parallel
        # Create separate elapse_info for each task to avoid interference
        qa_elapse_info = {}
        action_elapse_info = {}

        qa_intervention_task = asyncio.create_task(
            self._handle_intervention(
                query_text,
                query_id,
                face_id,
                start_at,
                qa_elapse_info,
            )
        )
        action_intervention_task = asyncio.create_task(
            self._handle_action_intervention(
                query_id,
                query_text,
                agent_parameter.candidate_actions,
                action_elapse_info,
            )
        )
        intervention_start_at = time.time()
        qa_intervention_result, action_intervention_result = await asyncio.gather(
            qa_intervention_task, action_intervention_task
        )
        intervention_elapsed = time.time() - intervention_start_at

        # Create separate intervention timing info for diagnostic_info
        intervention_timing_info = {
            "intervention_total_process_time": intervention_elapsed,
            "intervention_qa_process_time": qa_intervention_result.elapse_info.get(
                "intervention_elapsed_time", 0
            ),
            "intervention_action_process_time": action_intervention_result.elapsed_time,
            "qa_intervention_details": qa_elapse_info,
            "action_intervention_details": action_elapse_info,
        }

        # Add intervention timing info to elapse_info
        elapse_info["intervention_total_process_time"] = intervention_elapsed
        elapse_info["intervention_qa_process_time"] = (
            qa_intervention_result.elapse_info.get("intervention_elapsed_time", 0)
        )
        elapse_info["intervention_action_process_time"] = (
            action_intervention_result.elapsed_time
        )

        # Check if qa intervention found a response
        if qa_intervention_result.response:
            if async_play_plan_task:
                async_play_plan_task.cancel()
            return qa_intervention_result.response

        # Process action intervention result
        if (
            action_intervention_result.best_action
            and not action_intervention_result.return_directly
        ):
            agent_parameter.candidate_actions = [action_intervention_result.best_action]
            agent_parameter.extract_slots_only = True

        # remove 'only_intervention' from actions
        agent_parameter.candidate_actions = [
            a for a in agent_parameter.candidate_actions if not a["only_intervention"]
        ]

        # If action intervention found a response, use that response
        a_invoke = SingleActionAgent.a_invoke
        if (
            action_intervention_result.best_action
            and action_intervention_result.return_directly
        ):
            if async_play_plan_task:
                async_play_plan_task.cancel()

            try:
                a_invoke = InterventionAgent.a_invoke
                best_action_name = action_intervention_result.best_action.get("name")
                logger.info(f"Intervention candidate action: {best_action_name}")
                best_action = ActionLib().get_one_action(best_action_name)
                agent_parameter = InterventionAgentParameter(
                    query=query_text,
                    query_id=query_id,
                    memory=self.memory,
                    robot=self.robot,
                    candidate_actions=[best_action],
                    use_candidate_actions_directly=True,
                    face_id=face_id,
                    synthesize_type=synthesize_type,
                    run_step_queue=self.run_step_queue,
                    resource_id=action_intervention_result.action_response.resource_id,
                    resource_type=action_intervention_result.action_response.resource_type,
                )
            except asyncio.CancelledError:
                raise
            except Exception as e:
                agent_logger.error(
                    f"Failed to get best action for {self.robot.APP_ID} {self.robot.agent_id}: {e}"
                )

        status = "succeeded"
        plan_id = "failed"
        plan_xml = "failed"
        run_id = "failed"
        return_type = "failed"

        await self._sync_plan_status()
        start_timestamp = time.time()
        try:
            agent_result = await a_invoke(agent_parameter, agent_logger)

            agent_core_elapse_time = self.build_agent_core_elapse_time_metrics(
                agent_result.debug, start_timestamp
            )
            for k, v in agent_core_elapse_time.items():
                elapse_info[f"agent_core_{k}"] = v

            debug = agent_result.debug.model_dump()
            if agent_result.return_type == "failed":
                status = "failed"

            return_type = agent_result.return_type
            confirmed_action = agent_result.confirmed_action
        except asyncio.CancelledError:
            raise
        except Exception as e:
            agent_logger.error(f"Error when invoke agent: {e} {traceback.format_exc()}")
            status = "failed"
            agent_result = None
            debug = {
                "error": f"Failed to invoke planner agent {e} {traceback.format_exc()}"
            }
            confirmed_action = None

        if status == "succeeded":
            if agent_result.debug.action_flag == "strategy":  # 快指令
                if async_play_plan_task:
                    async_play_plan_task.cancel()

            plan_id = agent_result.plan.id
            plan_xml = agent_result.plan.content
            run_id = str(uuid.uuid4())
            agent_logger = agent_logger.bind(plan_id=plan_id, run_id=run_id)

            elapse_info["knowledge_qa_query_rewrite_time"] = (
                agent_result.debug.knowledge_debug_info.get(
                    "query_rewrite_cost_time", 0
                )
                / 1000
            )
            elapse_info["knowledge_qa_knowledge_search_time"] = (
                agent_result.debug.knowledge_debug_info.get(
                    "knowledge_search_time_ms", 0
                )
                / 1000
            )
            elapse_info["knowledge_qa_total_time"] = (
                agent_result.debug.knowledge_debug_info.get("total_time_ms", 0) / 1000
            )
        end_at = datetime.now().isoformat()
        elapse_info["plan_end_timestamp"] = time.time()
        elapse_info["plan_total_cost_time"] = time.time() - start_at

        if return_type in ["failed", "ask"]:  # behavior_tree, failed, ask
            execute_immediately = False
        else:
            execute_immediately = True

        iso_elapse_info = self.elapse_info_timestamp_to_isoformat(elapse_info)
        agent_logger.info(
            f"QueryID: {query_id}, Plan ID: {plan_id}, query_text:{query_text}, IN SINGLE Elapse info: {iso_elapse_info}"
        )

        plan_content = PlanContent(
            status=status,
            message="??",
            plan_id=plan_id,
            plan_type="single_action",
            plan_xml=plan_xml,
            execute_immediately=execute_immediately,
            run_id=run_id,
            app_id=start_app_id,
            page_id=start_page_id,
        )

        diagnostic_info = {
            "status": self.memory.stage,
            "user_query": query_text,
            "execute_immediately": execute_immediately,
            "start_at": datetime.fromtimestamp(start_at).isoformat(),
            "end_at": end_at,
            "agent_debug": self._filter_debug_for_bo(debug),
            "intervention_debug_info": qa_intervention_result.debug_info,
            "intervention_timing_info": intervention_timing_info,
            "studio_block_actions": block_action_fullnames,
            "return_type": return_type,
            "action_version": self.robot.action_version,
            "plan_id": plan_id,
            "run_id": run_id,
            "plan_xml": plan_xml,
        }

        # Create and send the message
        agent_answer_speech = self._create_and_send_plan_message(
            query_id=query_id,
            face_id=face_id,
            query_text=query_text,
            plan_content=plan_content,
            diagnostic_info=diagnostic_info,
            elapse_info=iso_elapse_info,
            confirmed_action=confirmed_action,
        )

        # Save diagnostic info
        record = await self._prepare_diagnostic_record(
            diagnostic_info=diagnostic_info,
            query_text=query_text,
        )

        asyncio.create_task(
            self._send_diagnostic_info(query_id, face_id, record, iso_elapse_info)
        )
        agent_logger.info(
            f"Plan ID: {plan_id} {query_text} Total Debug info: {diagnostic_info}"
        )

        if status == "failed":  # return directly if failed
            # self.robot.allow_interrupt = False  # TODO: remove this, demo
            return

        if execute_immediately is True:
            if agent_result and agent_result.action:
                await self.blackboard.init_plan_parameters(plan_id, agent_result.action)
                await self.blackboard.init_run_parameters(
                    run_id=run_id, plan_id=plan_id
                )
                # init running status
                await self.runtime_manager.init_plan(run_id, plan_xml, plan_id)

        # send server actions
        # pre_execute_actions = agent_result.pre_execute_actions
        # if pre_execute_actions:
        #     assert len(pre_execute_actions) == 1, (
        #         "Single action should have only one server action"
        #     )
        #     preprocess_action = pre_execute_actions[0]
        #     # send server action
        #     action_info = _ActionInfo(
        #         action_name=preprocess_action.action_name,
        #         node_id=preprocess_action.action_id,
        #         parameters=preprocess_action.parameters,
        #         run_id=run_id,
        #         plan_id=agent_result.plan.id,
        #         request_msg_id="",
        #         result_type="server_preprocessed",
        #         query_id=query_id,
        #         version=self.robot.action_version,
        #     )
        #     self.action_ch.send_nowait(action_info)

        # self.robot.allow_interrupt = True  # TODO: remove this, demo
        return agent_answer_speech

    def _filter_debug_for_bo(self, debug) -> dict:
        """过滤debug信息，移除agent_messages和tools字段，用于上报到BO"""
        # 如果debug是Debug对象，先转换为字典
        if hasattr(debug, "model_dump"):
            debug_dict = debug.model_dump()
        else:
            # 如果debug已经是字典，直接复制
            debug_dict = debug.copy() if isinstance(debug, dict) else {}

        # 移除敏感信息
        debug_dict.pop("agent_messages", None)
        debug_dict.pop("tools", None)
        return debug_dict

    def build_agent_core_elapse_time_metrics(
        self, agent_debug: Debug, start_timestamp: float
    ) -> dict:
        return {
            "load_context_time": agent_debug.load_context_cost_time,
            "embedded_time": agent_debug.embedded_cost_time,
            "call_summary_llm_time": agent_debug.summary_cost_time,
            "select_few_shot_time": agent_debug.select_few_shot_cost_time,
            "call_select_action_llm_time": agent_debug.call_agent_core_cost_time,
            "load_user_profile_time": agent_debug.user_profile_cost_time,
            "total_time": agent_debug.total_elapsed_time,
            "end_timestamp": agent_debug.end_timestamp,
            "start_timestamp": start_timestamp,
            "llm_retry_count": agent_debug.retry,
            "strategy_action_time": agent_debug.strategy_action_cost_time,
        }

    def elapse_info_timestamp_to_isoformat(self, elapse_info: Dict) -> Dict:
        elapse_info_result = {}
        for k, v in elapse_info.items():
            elapse_info_result[k] = (
                datetime.fromtimestamp(v).isoformat() if k.endswith("_timestamp") else v
            )
        return elapse_info_result

    async def _handle_intervention(
        self,
        query_text: str,
        query_id: str,
        face_id: str,
        start_at: float,
        elapse_info: Dict,
    ) -> InterventionResult:
        """
        Handle the intervention process including both making the intervention call
        and processing the result if successful.
        Returns the intervention result.
        """
        agent_logger = logger.bind(
            query_id=query_id,
            query=query_text,
            face_id=face_id,
            app_id=self.robot.APP_ID,
            agent_id=self.robot.agent_id,
        )

        # Check if intervention is enabled
        intervention_debug_info = {}
        if not self._enable_intervention():
            return InterventionResult(
                response=None,
                debug_info=intervention_debug_info,
                elapse_info=elapse_info,
            )

        # Process intervention
        intervention_result = None
        try:
            intervention_start_time = time.time()
            agent_logger.info(f"Starting intervention process for query: {query_text}")
            intervention_result = await self._intervention(query_text)
            intervention_elapsed = time.time() - intervention_start_time
            elapse_info["intervention_elapsed_time"] = intervention_elapsed

            if intervention_result:
                elapse_info = {**elapse_info, **intervention_result.elapse_info}
                intervention_debug_info = intervention_result.debug_info

            agent_logger.info(f"Intervention completed in {intervention_elapsed:.4f}s")
        except asyncio.CancelledError:
            raise
        except Exception as e:
            agent_logger.error(f"Error when intervention: {e} {traceback.format_exc()}")
            intervention_result = None

        # Return early if no intervention result or no answer
        if not (intervention_result and intervention_result.answer):
            return InterventionResult(
                response=None,
                debug_info=intervention_debug_info,
                elapse_info=elapse_info,
            )

        agent_logger.info(f"Match QA: {intervention_result}")
        plan_id = str(uuid.uuid4())
        run_id = str(uuid.uuid4())

        diagnostic_info = {
            "status": self.memory.stage,
            "user_query": query_text,
            "execute_immediately": True,
            "start_at": datetime.fromtimestamp(start_at).isoformat(),
            "end_at": datetime.now().isoformat(),
            "action_version": self.robot.action_version,
            "plan_id": plan_id,
            "run_id": run_id,
            "plan_xml": "",
            "debug_info": intervention_debug_info,
        }

        iso_elapse_info = self.elapse_info_timestamp_to_isoformat(elapse_info)

        # build chat picture message
        chat_answer_message = ChatAnswerMessage(
            content=ChatAnswerContent(
                question=intervention_result.question,
                user_query=query_text,
                image_info=intervention_result.answer.image_info,
                video_info=intervention_result.answer.video_info,
                radio_info=intervention_result.answer.radio_info,
                answer=intervention_result.answer.answer[-1]
                if intervention_result.answer.answer
                else None,  # -1: 润色后的答案
                uuid=intervention_result.answer.uuid,
                pair_id=intervention_result.answer.pair_id,
            ),
            device_id=self.robot.device_id,
            session_id=run_id,
            diagnostic_info=diagnostic_info,
        )

        plan_info = _PlanInfo(
            message=chat_answer_message.model_dump(),
            source=None,
            info_type="data",
            user_question=query_text,
            allow_interruptions=self.allow_interruptions,
            add_to_chat_ctx=False,
            synthesis_handle=None,
            query_id=query_id,
            face_id=face_id,
        )

        self.deferred_validation.on_new_synthesis(query_text)
        self.plan_ch.send_nowait(plan_info)

        # save diagnostic info
        record = await self._prepare_diagnostic_record(
            diagnostic_info=diagnostic_info,
            query_text=query_text,
        )
        asyncio.create_task(
            self._send_diagnostic_info(query_id, face_id, record, iso_elapse_info)
        )

        return InterventionResult(
            response=plan_info,
            debug_info=intervention_debug_info,
            elapse_info=elapse_info,
        )

    async def _handle_action_intervention(
        self,
        query_id: str,
        query_text: str,
        candidate_actions: List,
        elapse_info: Dict,
    ) -> ActionInterventionResult:
        """
        Handle action intervention process to get the best action.
        Returns the action intervention result.
        """
        start_fetch_topk_actions_time = time.time()
        best_action = {}
        return_directly = False
        action_response = None
        _dbg_info = {}
        _language_code = self.robot.language
        try:
            _language_code = detect_language(
                query_text,
                self.robot.language,
                self.robot.multilingual,
            )
        except Exception as e:
            logger.error(
                f"Error when detect language: {e}, use default language: {self.robot.language}, multilingual: {self.robot.multilingual}, query_text: {query_text}"
            )

        if not self.robot.app_config.enable_action_intervention:
            return ActionInterventionResult(
                best_action=best_action,
                return_directly=return_directly,
                elapsed_time=time.time() - start_fetch_topk_actions_time,
                action_response=action_response,
            )

        try:
            (
                best_action_responses,
                _dbg_info,
            ) = await ActionIntervenor().fetch_top_k_actions(
                query_id=query_id,
                query_text=query_text,
                candidate_actions_with_fullname=[
                    a["fullname"] for a in candidate_actions
                ],
                # candidate_actions_with_client_alias=[
                #     a.get("client_alias")
                #     for a in candidate_actions
                #     if a.get("client_alias")
                # ],  # 企业级技能干预传入这个参数
                language_code=_language_code,
                enterprise_id=self.robot.enterprise_id,
                app_id=self.robot.APP_ID,
                top_k=1,
            )
            if best_action_responses:
                action_response = best_action_responses[0]
                best_action_fullname = action_response.action_fullname
                return_directly = action_response.have_slots
                for act_ in candidate_actions:
                    if act_["fullname"] == best_action_fullname:
                        best_action: "Dict" = act_
                        break

                if action_response.have_slots or not best_action.get("parameters"):
                    return_directly = True
        except asyncio.CancelledError:
            raise
        except Exception as e:
            best_action = {}
            return_directly = False
            action_response = None

            logger.error(f"Error when fetch topk actions: {e}")

        # update elapse_info
        fetch_topk_actions_elapsed_time = time.time() - start_fetch_topk_actions_time
        elapse_info["fetch_topk_actions_elapsed_time"] = fetch_topk_actions_elapsed_time
        if _dbg_info:
            elapse_info.update(_dbg_info)

        logger.info(
            f"[SingleActionAgent] user_query:{query_text} best_action: {best_action} elapsed_time: {fetch_topk_actions_elapsed_time}"
        )

        return ActionInterventionResult(
            best_action=best_action,
            return_directly=return_directly,
            elapsed_time=fetch_topk_actions_elapsed_time,
            action_response=action_response,
        )
