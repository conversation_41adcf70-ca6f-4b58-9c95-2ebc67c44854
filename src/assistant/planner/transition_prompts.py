"""
过渡话术prompt管理模块
"""

from src.common.constant import Area
from src.settings import agent_setting


class TransitionPrompts:
    """管理过渡话术的prompt"""

    @staticmethod
    def get_domestic_prompt(
        query_text: str, language_prompt: str, chat_history: str = ""
    ) -> str:
        """获取国内版本的过渡话术prompt"""
        return f"""你是一个机器人，你需要完成两个步骤的任务：

## 步骤1：判断用户输入类型
根据以下标准，结合用户的语音输入和对话历史，判断用户的语音输入是"闲聊内容"还是"任务导向请求"。

### 闲聊内容的特征：
- 社交性问候：问好、告别、礼貌性寒暄，比如：早上好、你好、再见等
- 情感表达：感叹、赞美、抱怨、情绪分享
- 询问个人信息：比如姓名、年龄等
- 不完整或模糊的表达：单词片段、语义不清、上下文不足

### 任务导向请求的特征：
- 信息获取需求：用户想要了解、查询、获得具体信息，天气查询、周边推荐等
- 明确的功能调用：用户清楚知道想要什么服务或功能，如导航请求（带我去xx）
- 娱乐功能请求：用户要求唱歌、跳舞、表演、播放音乐、讲故事、讲笑话等功能

## 步骤2：生成transition_phrase（仅针对任务导向请求）
如果判断为任务导向请求，则生成transition_phrase，要求：
- 富有创意个性，口语化、生动、自然的一个陈述句，避免官方套话，接近15个字
- 不要重复用户的语音输入
- 不进行用户问题的澄清，不提问，因为后续环节会进行澄清
- 保持过场话术为陈述句，不要使用疑问句，此环节不需要用户回答
- **使用语言：{language_prompt}回复，只使用纯文本，句号和逗号。不能使用任何超链接和表情。**

### 过场话术示例（你需要自己创造，不能照搬）
- "马上为你探测xx城市的天气变化"
- "好的，我们现在就去xx"
- "路线规划师已准备就绪"
- "让我做你的专属向导"
- "正在启动xx搜索雷达"
- "美妙的音乐即将送达"

## 对话历史
{chat_history}

## 用户语音输入：{query_text}

## 输出格式
请严格按照以下JSON格式输出：
{{"is_chitchat": true/false, "transition_phrase": "如果is_chitchat为true则为空字符串，否则生成语言为{language_prompt}的过渡话术"}}
直接输出JSON"""

    @staticmethod
    def get_overseas_prompt(
        query_text: str, language_prompt: str, chat_history: str = ""
    ) -> str:
        """获取海外版本的过渡话术prompt"""
        return f"""You are a robot, and you need to complete a two-step task:

## Step 1: Determine User Input Type
Determine whether the user's voice input is "chitchat content" or "task-oriented request" according to the following criteria, combined with the user's voice input and conversation history.

### Chitchat Content Characteristics:
- Social greetings: hello, goodbye, polite exchanges, such as: good morning, good evening, hello, goodbye, etc.
- Emotional expressions: exclamations, praise, complaints, emotion sharing
- Asking for personal information: such as name, age, etc.
- Incomplete or vague expressions: word fragments, unclear meaning, insufficient context

### Task-Oriented Request Characteristics:
- Information seeking needs: user wants to learn, query, obtain specific information, weather queries, nearby recommendations, etc.
- Clear function calls: user clearly knows what service or function they want, such as navigation requests (take me to xx)
- Entertainment function requests: user requests singing, dancing, performing, playing music, storytelling and other functions

## Step 2: Generate transition_phrase (Only for Task-Oriented Requests)
If determined to be a task-oriented request, generate transition_phrase with requirements:
- Creative and personalized, conversational, vivid, natural declarative sentence, avoid corporate templates, approximately 15 words
- Don't repeat what the user said
- Don't clarify user questions or ask questions, as subsequent steps will handle clarification
- **Use {language_prompt} for reply, only use plain text, periods and commas. Cannot use any hyperlinks and emojis.**

### Creative Examples (you need to create your own, not copy)
- "I'll immediately detect the weather changes in xx city for you"
- "Alright, let's go to xx now"
- "The route planner is ready"
- "Let me be your exclusive guide"
- "Now activating xx search radar"
- "Wonderful music coming your way"

## Chat History
{chat_history}

## User voice input: {query_text}

## Output Format
Please strictly follow the following JSON format output:
{{"is_chitchat": true/false, "transition_phrase": "empty string if is_chitchat is true, otherwise generate language as {language_prompt} transition phrase"}}

Do not provide any explanations, strictly output JSON format only."""

    @staticmethod
    def get_prompt(
        query_text: str, language: str = "中文", chat_history: str = ""
    ) -> str:
        """根据当前区域设置获取对应的prompt"""
        if agent_setting.region_version == Area.domestic:
            return TransitionPrompts.get_domestic_prompt(
                query_text, language, chat_history
            )
        else:
            return TransitionPrompts.get_overseas_prompt(
                query_text, language, chat_history
            )
