import asyncio
import copy
import time
import traceback
from abc import abstractmethod
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from datetime import datetime
from typing import Dict, List, Literal, Optional

from loguru import logger
from redis.asyncio import Redis
import inspect
from livekit.agents.utils import http_context
from pydantic import BaseModel

from src.action_executor.action_runner import ActionRunner
from src.action_executor.executor import _ActionInfo
from src.agent_core.models.model import AgentParameter, Debug
from src.assistant.model import _PlanInfo
from src.common.agent_config import AGENT_CONFIG, AgentConfig
from src.common.constant import INTERVENTION_THRESHOLD, Area, InterventionThreshold
from src.common.toolkit import LLMToolKit
from src.controller.message_handler import MessageHandler
from src.intervene.intervene import Answer, QAManager, QAResponse
from src.messages.plan import (
    PlanContent,
    PlanMessage,
)
from src.session_manager.blackboard import Blackboard
from src.session_manager.memory import Memory
from src.session_manager.plan_running_status import Runtime<PERSON><PERSON>us<PERSON><PERSON>ger
from src.session_manager.robot import Robot
from src.settings import agent_setting, face_id_redis_client
from src.utils.async_utils import get_language
from src.utils.cos_client import CosClient
from src.utils.diagnostic_client import DiagnosticClient
from src.common.model import RunStep
from src.agent_core.models.agent_core import RunAction

from .recommend import handle_new_face_follow_up, handle_static_follow_up_action
from ...rag import RAGManager
from ...rag.rag_manager import RAGProviderType

BUILTIN_ID = "G" * 9


class InterventionResult(BaseModel):
    question: str = ""
    answer: Answer | None = None
    debug_info: Dict
    elapse_info: Dict


class BasePlanner:
    """
    Abstract class for the planner
    """

    def __init__(
        self,
        memory: Memory,
        robot: Robot,
        action_ch,
        general_ch,
        plan_ch,
        deferred_validation,
        cos_client: "CosClient",
        candidate_actions: list,
        runtime_manager: "RuntimeStatusManager",
        blackboard: "Blackboard",
        action_runner: "ActionRunner",
        message_handler: "MessageHandler",
        allow_interruptions: bool = True,
        diagnostic_client: "DiagnosticClient" = None,
        run_step_queue: asyncio.Queue | None = None,
    ) -> None:
        self.memory = memory
        self.robot = robot
        self.action_ch = action_ch
        self.general_ch = general_ch
        self.plan_ch = plan_ch
        self.deferred_validation = deferred_validation
        self.cos_client = cos_client
        self.candidate_actions = candidate_actions
        self.allow_interruptions = allow_interruptions
        self.runtime_manager = runtime_manager
        self.blackboard = blackboard
        self.action_runner = action_runner
        self.message_handler = message_handler
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.diagnostic_client: "DiagnosticClient" = diagnostic_client
        self.async_redis_client: "Redis" = Redis(
            host=agent_setting.redis_host,
            port=agent_setting.redis_port,
            db=agent_setting.redis_db,
            encoding="utf-8",
            decode_responses=True,
        )
        self._session = http_context.http_session()
        self.qa_manager = QAManager()
        self.run_step_queue = run_step_queue
        if not isinstance(self.robot, Robot):
            raise TypeError(
                f"Expected self.robot to be an instance of Robot, but got {type(self.robot)}"
            )

        if agent_setting.region_version == Area.domestic:
            self.rag_manager = RAGManager(provider_type=RAGProviderType.CHATMAX)
        else:
            self.rag_manager = RAGManager(provider_type=RAGProviderType.OPENAI_VECTOR)

    def _say(
        self,
        query_id: str,
        text: str,
        result_type: Literal[
            "server_sent", "client_requested", "server_preprocessed"
        ] = "server_sent",
        add_to_chat_ctx: bool = True,
        run_id: str = BUILTIN_ID,
        plan_id: str = BUILTIN_ID,
        node_id: str = BUILTIN_ID,
        request_msg_id: str = "",
        version: str = "draft",
    ):
        action_info = _ActionInfo(
            action_name="orion.agent.action.SAY",
            node_id=node_id,
            parameters={"text": text},
            run_id=run_id,
            plan_id=plan_id,
            request_msg_id=request_msg_id,
            result_type=result_type,
            add_to_chat_ctx=add_to_chat_ctx,
            query_id=query_id,
            version=version,
        )
        self.action_ch.send_nowait(action_info)

    @abstractmethod
    async def plan(
        self,
        query_text: str,
        query_id: str,
        elapse_info: Dict,
    ) -> None:
        """
        :param query_text:
        :param query_id:
        :param elapse_info:
        :return:
        """
        pass

    async def _sync_plan_status(
        self,
    ):
        await self.run_step_queue.put(
            RunStep(
                step_name="thinking",
            )
        )

    async def _handle_static_follow_up_action(
        self,
        query_id: str,
        agent_parameter: "AgentParameter",
        followup_prompt: str | None = None,
    ):
        result = await handle_static_follow_up_action(
            self.memory, self.robot, followup_prompt
        )
        if not result:
            return

        self._say(
            query_id,
            result,
            run_id=BUILTIN_ID,
            plan_id=BUILTIN_ID,
            version=self.robot.action_version,
        )

    async def _handle_new_face_follow_up(
        self, query_id: str, agent_parameter: "AgentParameter", image_info: Dict
    ):
        if self.robot.indoor_location_history:
            location = self.robot.indoor_location_history[-1]
        else:
            location = "未知"

        persona = LLMToolKit.init_persona_core_objective(
            self.robot.agent_id,
            self.robot.APP_ID,
            self.robot.PERSONA,
            self.robot.LANGUAGE_STYLE,
            self.robot.OBJECTIVE,
        ).persona
        try:
            agent_config: AgentConfig = AGENT_CONFIG.get(self.robot.agent_id)
            customized_prompt = agent_config.recommend_prompt.get("prompt")
            if callable(customized_prompt):
                if inspect.iscoroutinefunction(customized_prompt):
                    customized_prompt = await customized_prompt(agent_parameter)
                else:
                    customized_prompt = customized_prompt(agent_parameter)
        except Exception:
            customized_prompt = ""
        logger.bind(query_id=query_id).info(f"Customized prompt: {customized_prompt}")

        # def _play_slogan():
        #     if (
        #         self.robot.APP_ID not in OPK_INFO
        #         or "slogans" not in OPK_INFO[self.robot.APP_ID]
        #     ):
        #         return

        #     slogans = OPK_INFO[self.robot.APP_ID]["slogans"]
        #     if not slogans:
        #         return
        #     # choose one slogan randomly
        #     slogan = random.choice(slogans)
        #     self._say(
        #         query_id,
        #         slogan,
        #         run_id=BUILTIN_ID,
        #         plan_id=BUILTIN_ID,
        #         version=self.robot.action_version,
        #     )

        # _play_slogan()

        geo_location = (
            self.robot.geo_location.city
            if agent_setting.region_version == Area.domestic
            else f"{self.robot.geo_location.latitude},{self.robot.geo_location.longitude}"
        )
        try:
            result = await handle_new_face_follow_up(
                image_info,
                location,
                geo_location,
                self.memory.mem0_client,
                face_id_redis_client,
                device_id=self.robot.device_id,
                thread_pool=self.thread_pool,
                persona=persona,
                prompt=customized_prompt,
                language=get_language(self.robot, lang_flag="zh"),
                timezone=self.robot.timezone,
            )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.bind(query_id=query_id).error(
                f"Failed to handle new face follow up: {e} {traceback.format_exc()}"
            )
            return

        if not result:
            return

        self._say(
            query_id,
            result,
            run_id=BUILTIN_ID,
            plan_id=BUILTIN_ID,
            version=self.robot.action_version,
        )

    async def _send_diagnostic_info(
        self,
        query_id: str,
        face_id: str,
        debug_info: Dict,
        elapse_info: Dict,
    ):
        # get audio_path_list
        cos_paths = []
        # 1. split query_id by "_"
        audio_ids = [audio_id for audio_id in query_id.split("_") if audio_id]
        for audio_id in audio_ids:
            audio_key = f"asr_file_path_{agent_setting.env}_{audio_id}"
            logger.bind(query_id=query_id).info(
                f"[BO] Get audio path from redis: {audio_key}"
            )
            audio_path = await self.async_redis_client.get(audio_key)
            if audio_path:
                total_cos_path = f"https://{agent_setting.cos_bucket}.cos.{agent_setting.cos_region}.myqcloud.com/{agent_setting.cos_prefix}/{audio_path}"
                logger.bind(query_id=query_id).info(
                    f"[BO] Get audio path from cos: {total_cos_path}"
                )
                cos_paths.append(total_cos_path)

        logger.bind(query_id=query_id, plan_id=debug_info.get("plan_id", "")).info(
            f"[BO]Final cos_paths: {cos_paths}"
        )

        plan_info = debug_info.pop("plan_xml", "")
        debug_info["face_id"] = face_id
        debug_info["elapse_info"] = elapse_info

        await self.diagnostic_client.send_diagnostic(
            sid=query_id,
            client_id=self.robot.client_id,
            enterprise_id=self.robot.enterprise_id,
            device_id=self.robot.device_id,
            group_id=self.robot.group_id,
            query=debug_info.get("user_query"),
            answer=debug_info.get("answer", ""),
            model=self.robot.product_model,
            lang_str=self.robot.language,
            speaker_id=1 if face_id else 0,
            extra_info={},
            debug_info=debug_info,
            plan_info=plan_info,
            audio_path_list=cos_paths,
        )

    async def _intervention(self, query_text: str) -> InterventionResult | None:
        """
        intervention process
        """
        elapse_info = {}
        debug_info = {}
        start_time = time.time()
        elapse_info["intervention_start_timestamp"] = start_time

        logger.bind(query_text=query_text).info(
            f"Starting intervention process for query: {query_text}"
        )

        # Get threshold configuration
        threshold_start = time.time()
        intervention_threshold_level = (
            InterventionThreshold.high
        )  # TODO(<EMAIL>): 获取平台配置阈值
        try:
            intervention_threshold = INTERVENTION_THRESHOLD[
                intervention_threshold_level
            ][agent_setting.embedding_model]
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"Error when get intervention threshold: {e}")
            intervention_threshold = 0.9

        elapse_info["threshold_fetch_time"] = time.time() - threshold_start
        debug_info["threshold_level"] = intervention_threshold_level
        debug_info["threshold_value"] = intervention_threshold

        # Fetch QA matches
        qa_fetch_start = time.time()
        try:
            intervention_result: List[
                QAResponse
            ] = await self.qa_manager.fetch_top_k_qa(
                self.robot.enterprise_id,
                self.robot.device_id,
                query_text,
                threshold=intervention_threshold,
                top_k=1,
                language=self.robot.language,
            )
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"Error when fetch QA matches: {e}")
            debug_info["fetch_qa_exception"] = str(e)
            intervention_result = []

        qa_fetch_time = time.time() - qa_fetch_start
        elapse_info["qa_fetch_time"] = qa_fetch_time

        logger.bind(
            query_text=query_text,
        ).info(
            f"Intervention result: {intervention_result}, threshold: {intervention_threshold}, fetch_time: {qa_fetch_time:.4f}s"
        )

        debug_info["intervention_result"] = []
        matched_question = ""
        if intervention_result:
            # get answer via api
            answer_fetch_start = time.time()
            qa_id = intervention_result[0].qa_id
            qa_lang = intervention_result[0].lang
            qa_score = intervention_result[0].score
            matched_question = intervention_result[0].question or ""
            debug_info["intervention_result"] = [
                item.model_dump() for item in intervention_result
            ]

            try:
                intervention_data = await self.qa_manager.async_get_answer_by_id(
                    qa_id, qa_lang, need_polish_answer=False
                )
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"Error when get intervention data: {e}")
                intervention_data = None
            elapse_info["answer_fetch_time"] = time.time() - answer_fetch_start
            debug_info["intervention_answer"] = intervention_data.model_dump()

            # Calculate total time
            total_time = time.time() - start_time
            elapse_info["intervention_total_time"] = total_time
            elapse_info["intervention_end_timestamp"] = time.time()

            logger.bind(query_text=query_text).info(
                f"Intervention completed in {total_time:.4f}s with QA ID: {qa_id}, Score: {qa_score}"
            )

            return InterventionResult(
                question=matched_question,
                answer=intervention_data,
                debug_info=debug_info,
                elapse_info=elapse_info,
            )

        # No match found
        elapse_info["intervention_total_time"] = time.time() - start_time
        elapse_info["intervention_end_timestamp"] = time.time()

        logger.bind(query_text=query_text).info(
            f"No intervention match found. Process took {elapse_info['intervention_total_time']:.4f}s"
        )
        return InterventionResult(
            question="",
            answer=None,
            debug_info=debug_info,
            elapse_info=elapse_info,
        )

    def _create_and_send_plan_message(
        self,
        query_id: str,
        face_id: str,
        query_text: str,
        plan_content: PlanContent,
        diagnostic_info: Dict,
        elapse_info: Dict,
        confirmed_action: Optional[RunAction] = None,
    ) -> _PlanInfo:
        """Helper method to create and send plan message to avoid code duplication"""
        plan_message = PlanMessage(
            action_version=self.robot.action_version,
            content=plan_content,
            device_id=self.robot.device_id,
            diagnostic_info=diagnostic_info,
            elapse_info=elapse_info,
            query_id=query_id,
            user_query=query_text,
        )

        logger.bind(
            query_id=query_id,
            face_id=face_id,
            query_text=query_text,
        ).critical(f"General info confirmed action {confirmed_action}")

        plan_info = _PlanInfo(
            message=plan_message.model_dump(),
            source=None,
            info_type="data",
            user_question=query_text,
            allow_interruptions=self.allow_interruptions,
            add_to_chat_ctx=False,
            synthesis_handle=None,
            query_id=query_id,
            face_id=face_id,
            confirmed_action=confirmed_action,
        )
        # self.deferred_validation.on_new_synthesis(query_text)
        self.plan_ch.send_nowait(plan_info)

        return plan_info

    async def _prepare_diagnostic_record(
        self,
        diagnostic_info: Dict,
        query_text: str,
    ) -> Dict:
        """Helper method to prepare diagnostic record to avoid code duplication"""
        record = copy.deepcopy(diagnostic_info)
        record["robot_status"] = self.robot.robot_debug_info()

        if not query_text:
            try:
                chat_context = await self.memory.get_chat_context(max_chat_history=2)
                query_text = "".join(
                    LLMToolKit.build_conversation_progress(chat_context.messages)
                )
            except Exception as e:
                logger.error(f"Error when get chat context: {e}")
                query_text = "无Query"
            record["user_query"] = query_text

        return record

    def build_agent_core_elapse_time_metrics(
        self, agent_debug: Debug, start_timestamp: float
    ) -> dict:
        return {
            "load_context_time": agent_debug.load_context_cost_time,
            "load_action_time": agent_debug.load_action_cost_time,
            "embedded_time": agent_debug.embedded_cost_time,
            "call_summary_llm_time": agent_debug.summary_cost_time,
            "select_few_shot_time": agent_debug.select_few_shot_cost_time,
            "call_select_action_llm_time": agent_debug.agent_call_cost_time,
            "load_user_profile_time": agent_debug.user_profile_cost_time,
            "total_time": agent_debug.total_elapsed_time,
            "end_timestamp": agent_debug.end_timestamp,
            "start_timestamp": start_timestamp,
            "llm_retry_count": agent_debug.retry,
        }

    def elapse_info_timestamp_to_isoformat(self, elapse_info: Dict) -> Dict:
        elapse_info_result = {}
        for k, v in elapse_info.items():
            elapse_info_result[k] = (
                datetime.fromtimestamp(v).isoformat() if k.endswith("_timestamp") else v
            )
        return elapse_info_result
