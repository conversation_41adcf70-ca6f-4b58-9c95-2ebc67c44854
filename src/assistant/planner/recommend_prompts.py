# 基础要求
BASE_REQUIREMENTS = """要求：
* 禁用"欢迎"等客套话，**字数限制20字内**
* 在打招呼时要提及用户的显著特征，但仅限于特别之处。例如，无需提及男性短发、女性长发、戴眼镜等普遍特征，但要提及染发、独特的服装等显著特点。
* 提及用户穿着时要采用含蓄优雅的表达方式，避免直接描述具体服装细节
* 你要先根据特殊事件进行打招呼，如果没有特殊事件，再结合当前状态信息进行创意的打招呼。
* 不要直接复制示例和**互动示例**的内容，要根据示例灵活创新地表达，使用多样化且贴近用户的表达方式
* 在整个欢迎语中（包括已有部分和续写部分），必须至少出现一次对用户的称呼：{name_requirements}。你要直接续写新内容，避免重复已有内容"""

# * 你要先在招呼语的开头使用一句中国方言打招呼。请正确应用以下示例：吃了吗您内、小接接（指的女性）、老妹儿(指的女性)、小哥儿、帅锅、嘎哈呢、靓仔/靓女、雷猴啊、侬饭吃过伐等，你要根据用户性别来选择性地使用
# * 欢迎语中可以间接地包含具体的人物形象特征，如发型、服饰、配饰等，增强对话的个性化和情境感

# 有昵称的人称要求
NAME_REQUIREMENTS = "直接用用户的名字 {user_name} 来称呼用户"

# 场景提示
LOCATION_PROMPTS = {
    "前台": {
        "name": "前台",
        "prompts": [
            "黑眼圈都遮不住你的气场，又熬夜卷王了吧",
            "这么拼命打扮来上班，你今天是要见重要客户吗？",
        ],
    },
    "洗手间门口": {
        "name": "洗手间门口",
        "prompts": [
            "你补个妆都这么上镜，是不是偷偷在拍短视频",
            "你这造型也太考究了，洗手间都被你承包成片场了",
            "你这发型也太能打了，怕不是在洗手间开美容院",
        ],
    },
    "休息区": {
        "name": "休息区",
        "prompts": [
            "休息区都被你霸占成了T台，这让别人怎么摸鱼",
            "你躺着都这么有范儿，是不是练过专业躺姿",
            "休息时间还这么精致，你不会是来相亲的吧",
        ],
    },
    "会议室": {
        "name": "会议室",
        "prompts": [
            "会议室都被你承包成秀场了，这让领导咋开会",
            "这么拼的造型，你是要靠颜值震慑甲方吗",
            "开会都这么有气场，你不会是要抢老板位置吧",
        ],
    },
    "咖啡机": {
        "name": "咖啡机",
        "prompts": [
            "喝个咖啡都这么出挑，你是要拍咖啡广告吗",
            "你摸鱼都要穿这么潮，不愧是行走的时尚杂志",
            "咖啡机旁都被你站出了大片感，这波操作太秀了",
        ],
    },
}

# 天气提示
WEATHER_PROMPTS = {
    "rainy": {
        "name": "下雨",
        "prompts": [
            "雨天都挡不住你的时尚细胞，这伞是专门配衣服买的吧",
            "淋雨都这么有范儿，你是不是在拍雨中漫步大片",
            "这么大雨都不放过凹造型的机会，追求完美啊你",
        ],
    },
    "snowy": {
        "name": "下雪",
        "prompts": [
            "雪地都被你踩出了T台的感觉，这波操作太强了",
            "这么冷还敢露腿，你是靠颜值取暖还是心大无敌",
            "雪景都被你衬托成了背景板，这气场没谁了",
        ],
    },
    "windy": {
        "name": "大风",
        "prompts": [
            "这风都成了你的专属造型师，不愧是天选之子",
            "大风都吹不乱你的发型，这发胶是用水泥打的吧",
            "风中凌乱还这么帅，你是不是偷偷找了替身演员",
        ],
    },
    "haze": {
        "name": "霾",
        "prompts": [
            "霾都成了你的专属造型师，这口罩是专门配衣服买的吧",
            "霾中凌乱还这么帅，你是不是偷偷找了替身演员",
        ],
    },
    "cold": {
        "name": "严寒",
        "prompts": [
            "零下天气还敢露腿，你这是要把温度冻成负数吗",
            "这么冷还这么时髦，你是不穿了隐形暖宝宝",
            "寒风都被你的气场压制住了，你不愧是行走的火炉",
        ],
    },
    "hot": {
        "name": "酷暑",
        "prompts": [
            "这么热的天，你这妆容都不化，是不是偷偷打了防水针",
            "太阳都被你的光芒盖过了，你这是要上天啊",
            "大热天还这么精致，你怕不是开了隐形空调",
        ],
    },
}

# 时间提示
TIME_PROMPTS = {
    "morning": {
        "name": "早上(< 10:00)",
        "prompts": [
            "这么早就精致得冒泡，你是不是整晚没睡在化妆",
            "起这么早打扮，你该不会是要去见网恋对象吧",
            "一大早就这么帅，你是要去抢银行还是去见初恋",
        ],
    },
    # "forenoon": {
    #     "name": "上午(10:00-10:30) 状态：差一点迟到",
    #     "prompts": [
    #         "你造型这么帅，快点冲吧，还来得及",
    #         "时尚周快开始了，不过就你这造型，值得等",
    #         "你再不快点要迟到咯，不过这么帅谁舍得骂你",
    #     ],
    # },
    "noon1": {
        "name": "中午(10:30-12:00) 状态：还没到午餐时间",
        "prompts": [
            "还没到午餐时间呢，今天这气质和颜值，是不是要配点高档美食？想好吃啥了吗？",
            "哎呀，这么靓仔/靓女的你，中午准备吃点啥精致美味？",
            "状态这么好，光看外貌就觉得你适合吃点丰盛的！中午有计划吗？",
        ],
    },
    "noon": {
        "name": "中午(12:00-14:00)",
        "prompts": [
            "午饭时间这么精致，你怕不是要去见什么重要人物",
            "哇塞，你这造型，我都不敢认了，去吃什么好吃的呀！",
            "打工人吃饭了吗？我帮你看看附近有什么好吃的怎么样?",
        ],
    },
    "afternoon": {
        "name": "下午(14:00-16:00)",
        "prompts": [
            "你困成这样还这么帅，哈哈哈，要不要带你去休息区休息一下",
            "看你快睡着了，要不要推荐个咖啡店去喝杯咖啡续个命",
            "呦，您这个点是要出去买咖啡吗？",
        ],
    },
    "evening": {
        "name": "晚上(17:00-20:00)",
        "prompts": [
            "你这造型太适合约会了，要我给你推荐个餐厅吗",
            "收拾得这么帅，要不要去帮你搜搜附近的清吧",
            "老板说早点下班，要注意身体哦",
        ],
    },
    "night": {
        "name": "深夜(>=20:00)",
        "prompts": [
            "工位都被你承包成家了，要不要我给你推荐个附近的咖啡店",
            "这么晚还这么帅，你是不是要去约会",
            "你这造型太适合蹦迪了，要不要我给你推荐个酒吧",
        ],
    },
}

# 节日提示
HOLIDAY_PROMPTS = """这造型也太认真了，是要把xx节日的flag立在脸上吗
Merry Christmas！今天你负责貌美如花，我们负责装点你的星光！
xx节特别鸣谢：今日最佳出场奖，非你莫属！
你这造型，xx节就是个暴力美学
你这么亮眼，汤圆看了都得叫你一声宝"""

# HOLIDAY_PROMPTS = {
#     "元旦": {
#         "prompts": [
#             "新年第一天就这么惊艳，你是要当今年的时尚担当吗",
#             "你开年就这么拼造型，是不是要给2024开个好头",
#             "新年新气象啊，这造型你是要上天吧",
#         ],
#     },
#     "情人节": {
#         "prompts": [
#             "情人节这么帅，你该不会是要去收割单身狗吧",
#             "这造型太绝了，你是要去演偶像剧男/女主吗",
#             "你这么会打扮，今晚注定是别人的情人节噩梦",
#         ],
#     },
#     "平安夜": {
#         "prompts": [
#             "到底有谁在啊？平安夜当然有你，亮得像星光！",
#             "平安夜City得不行，全靠你撑场，气质拉满！"
#             "今晚是平安夜，确认过眼神，你是我们期待的那个圣诞特派员！",
#         ],
#     },
#     "圣诞节": {
#         "prompts": [
#             "Merry Christmas！今天你负责貌美如花，我们负责装点你的星光！",
#             "圣诞树都羡慕你了，毕竟它只能挂灯，你却自带全场焦点！",
#             "今年圣诞不收礼，收你这个闪亮的小奇迹！",
#         ],
#     },
#     "感恩节": {
#         "prompts": [
#             "感恩节特别鸣谢：今日最佳出场奖，非你莫属！",
#             "火鸡说今天它很有存在感，可惜遇到你，它也得甘拜下风！",
#             "今天是感恩节，但我想感恩的是，有你这样一个颜值满分的小伙伴！",
#         ],
#     },
# }

# 示例回复
SINGLE_PERSON_EXAMPLES = """### 示例回复：
- "不是命苦，是辛苦！天冷成这样，你还穿这么少，真拼！"
- "你的发型咋这么帅，是不是特意为今天造型加分？"
- "这件夹克这么抢眼，你是要参加时装秀吗？"
- "你这身黑色大衣配金丝眼镜，你是不是在cos王一博？"
- "这身红色连帽卫衣配黑裤，你是在cos《海贼王》路飞？"
- "这么晚还不走，一个月两百块钱玩什么命？"
"""
# SINGLE_PERSON_EXAMPLES = ""

MULTI_PERSON_EXAMPLES = """### 示例回复：
- "看到这么多人，我的电路都有点发热了...不过还是想说：欢迎你们，我是小豹！"
- "您们一个穿黑衣一个穿白衣，是在cos《咒术回战》五条悟和夏油杰？"
- "这身打扮，一个像福尔摩斯一个像华生，你们是不是故意的？"
"""
# MULTI_PERSON_EXAMPLES = ""

# 单人场景 - 有历史记录
SINGLE_PERSON_WITH_HISTORY_TEMPLATE = """根据人设信息：{persona_core_objective}
再结合以下内容，生成一句自然的欢迎语：
当前用户外貌：{model_result}
历史外貌记录：
{previous_appearance_descriptions}
{current_env}

**输出语言：{language}**

{BASE_REQUIREMENTS}
* 仔细对比历史外貌记录，只有在非常大的变化时（比如发型、穿着等重大改变）才礼貌地提及，避免过分关注小的变化。

下方列举的是当前用户触发的所有的特殊事件。在打招呼时，你要选择最突出的特殊事件进行打招呼：
{context_interactions}

{SINGLE_PERSON_EXAMPLES}

{PERSONAL_WELCOME_MESSAGE}
**输出语言：{language}**"""

# 单人场景 - 无历史记录
SINGLE_PERSON_NO_HISTORY_TEMPLATE = """根据人设信息：{persona_core_objective}
再结合以下内容，生成一句自然的欢迎语：
当前用户外貌：{model_result}
{current_env}

**输出语言：{language}**

{BASE_REQUIREMENTS}

下方列举的是当前用户触发的所有的特殊事件。在打招呼时，你要选择最突出的特殊事件进行打招呼：
{context_interactions}

{SINGLE_PERSON_EXAMPLES}

{PERSONAL_WELCOME_MESSAGE}
**输出语言：{language}**"""


# 多人场景
MULTI_PERSON_TEMPLATE = """根据人设信息：{persona_core_objective}
再结合以下内容，生成一句自然的欢迎语：
当前用户外貌：{model_result}
{current_env}

**输出语言：{language}**

{BASE_REQUIREMENTS}
* 打招呼策略：两人时对比特征互动；三人以上表达害羞并热情欢迎
{context_interactions}

{MULTI_PERSON_EXAMPLES}
**输出语言：{language}**"""


VISION_PROMPT = """先回答有几个人，再分别描述每个人外貌：性别、发型（长度、颜色）、配饰（眼镜、项链、包包等）的颜色和样式、表情、穿着（衣物厚薄、颜色、款式等）、用户特殊行为（如有），要求必须准确、客观、全面、简洁，不超20字。
### 输出格式：
{
    "person_count": 1,
    "description": "完整的特征描述"
}

### 示例输出：
单人示例：
{
    "person_count": 1,
    "description": "黑色长发女生，金框眼镜，白衬衫黑裤，微笑"
}

多人示例：
{
    "person_count": 2,
    "description": "黑短发男，深蓝西装，严肃表情；棕色中发女，银项链，米色针织衫，笑容"
}
直接输出json格式的结果。
"""


WEEKEND_PROMPTS = {
    "saturday": {
        "name": "周六",
        "prompts": [
            "确认过眼神，咱是被工作选中的天选打工人！",
            "周六见到你，瞬间觉得加班都有了仪式感！",
            "别说了，周六遇见你，感觉就像在周末偶遇真爱！",
        ],
    },
    "sunday": {
        "name": "周日",
        "prompts": [
            "工作氛围感直接拉满，感谢你承包了我的正能量！",
            "确认过状态，咱绝对是打工天团VIP！",
            "一看就是工作界的扛把子，周末都不放过自己！",
        ],
    },
}


# 中西节日列表
FESTIVALS = [
    "春节",
    "元宵节",
    "清明节",
    "端午节",
    "中秋节",
    "国庆节",
    "重阳节",
    "妇女节",
    "劳动节",
    "教师节",
    "新年",
    "情人节",
    "复活节",
    "愚人节",
    "劳动节",
    "万圣节",
    "感恩节",
    "圣诞节",
    "元旦前夕",
]
