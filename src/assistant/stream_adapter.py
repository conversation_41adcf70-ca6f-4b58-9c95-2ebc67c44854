import asyncio

from livekit.agents import utils
from livekit.agents.stt import (
    SpeechEventType,
    SpeechStream,
    StreamAdapter,
    StreamAdapterWrapper,
)
from livekit.agents.vad import VADEventType
from loguru import logger

from src.plugins.orion_asr import SpeechEvent


class OrionStreamAdapter(StreamAdapter):
    def stream(self, *, language: str | None = None) -> SpeechStream:
        return OrionStreamAdapterWrapper(self._vad, self._stt, language=language)


class OrionStreamAdapterWrapper(StreamAdapterWrapper):
    @utils.log_exceptions(logger=logger)
    async def _main_task(self) -> None:
        async def _forward_input():
            """forward input to vad"""
            async for input in self._input_ch:
                if isinstance(input, self._FlushSentinel):
                    self._vad_stream.flush()
                    continue
                # 16k 1 160
                # logger.debug(
                #     f"[Orion StreamAdapterWrapper] frame sample rate {input.sample_rate} {input.num_channels} {input.samples_per_channel}"
                # )
                # count = len(bytes(input.data))
                # logger.info(f"[Orion StreamAdapterWrapper] frame count {count} {input}")
                self._vad_stream.push_frame(input)

            # logger.debug("[!!!] end input vad stream")
            self._vad_stream.end_input()

        async def _recognize():
            """recognize speech from vad"""
            async for event in self._vad_stream:
                if event.type == VADEventType.START_OF_SPEECH:
                    logger.info("recognize speech vad VADEventType.START_OF_SPEECH")
                    self._event_ch.send_nowait(
                        SpeechEvent(SpeechEventType.START_OF_SPEECH)
                    )
                elif event.type == VADEventType.END_OF_SPEECH:
                    # logger.debug(f"recognize speech vad VADEventType.END_OF_SPEECH")
                    self._event_ch.send_nowait(
                        SpeechEvent(
                            type=SpeechEventType.END_OF_SPEECH,
                        )
                    )

                    merged_frames = utils.merge_frames(event.frames)
                    # logger.debug(
                    #     f"wrapper: event frames {event.frames[0].sample_rate} {event.frames[0].num_channels} {event.frames[0].samples_per_channel}"
                    # )
                    # call asr
                    try:
                        t_event = await self._stt.recognize(
                            buffer=merged_frames, *self._args, **self._kwargs
                        )
                    except ValueError as e:
                        logger.error(f"Orion ASR error: {e}")
                        continue

                    self._event_ch.send_nowait(
                        SpeechEvent(
                            type=SpeechEventType.FINAL_TRANSCRIPT,
                            alternatives=[t_event.alternatives[0]],
                            audio_id=t_event.audio_id,
                            elapsed=t_event.elapsed,
                        )
                    )

        await asyncio.gather(_forward_input(), _recognize())
