import re
import functools

from livekit.agents.tokenize import basic, tokenizer, token_stream
from livekit.agents.tokenize.basic import _TokenizerOptions
from livekit.agents.tokenize._basic_sent import split_sentences as basic_split_sentences
from loguru import logger

from src.session_manager.robot import Robot
from src.common.constant import LanguageEnum
from src.utils.language_utils import detect_language

__all__ = ["OrionWordTokenizer", "OrionSentenceTokenizer"]


def _convert_language(language: str) -> str:
    """
    目前只支持两种切割方式
    """
    if language in [
        LanguageEnum.zh,
        LanguageEnum.zh_tw,
        LanguageEnum.zh_gd,
        LanguageEnum.ja_jp,
        LanguageEnum.ko_kr,
    ]:  # 按字分割
        return LanguageEnum.zh
    return LanguageEnum.en


class OrionWordTokenizer(basic.WordTokenizer):
    def __init__(self, *, ignore_punctuation: bool = True, robot: Robot = None) -> None:
        self.robot = robot
        super().__init__(ignore_punctuation=ignore_punctuation)

    def tokenize(self, text: str, *, language: str | None = None) -> list[str]:
        language = detect_language(text, self.robot.language, self.robot.multilingual)
        # logger.debug(f"Word tokenize language: {language}")
        language = _convert_language(language)
        # logger.debug(f"Word tokenize language after convert: {language}")
        if language == LanguageEnum.zh:
            return list(text)  # 中文直接返回字符列表
        return super().tokenize(text, language=language)

    def format_words(self, words: list[str]) -> str:
        language = detect_language(
            " ".join(words), self.robot.language, self.robot.multilingual
        )
        # logger.debug(f"Format words language: {language}")
        language = _convert_language(language)
        # logger.debug(f"Format words language after convert: {language}")
        if language == LanguageEnum.zh:
            return "".join(words)
        return " ".join(words)


# rule based segmentation from https://stackoverflow.com/a/31505798, works surprisingly well
def split_sentences(
    text: str,
    min_sentence_len: int = 20,
    system_language: str = "en_US",
    is_multilingual: bool = False,
) -> list[str]:
    """the text can't contains substrings "<prd>" or "<stop>"""
    # logger.debug(f"Before chinese split {text}")

    # 根据系统语言和是否多语言，选择不同的分割方式
    language = detect_language(text, system_language, is_multilingual)
    logger.debug(f"Sentence tokenize language: {language}")
    language = _convert_language(language)
    logger.debug(f"Sentence tokenize language after convert: {language}")
    if language == LanguageEnum.en:
        return basic_split_sentences(text, min_sentence_len)

    alphabets = r"([A-Za-z])"
    prefixes = r"(Mr|St|Mrs|Ms|Dr)[.]"
    suffixes = r"(Inc|Ltd|Jr|Sr|Co)"
    starters = r"(Mr|Mrs|Ms|Dr|Prof|Capt|Cpt|Lt|He\s|She\s|It\s|They\s|Their\s|Our\s|We\s|But\s|However\s|That\s|This\s|Wherever)"
    acronyms = r"([A-Z][.][A-Z][.](?:[A-Z][.])?)"
    websites = r"[.](com|net|org|io|gov|edu|me)"
    digits = r"([0-9])"
    multiple_dots = r"\.{2,}"

    # fmt: off
    text = " " + text + "  "
    # text = text.replace("\n"," ")
    text = re.sub(prefixes,"\\1<prd>",text)
    text = re.sub(websites,"<prd>\\1",text)
    text = re.sub(digits + "[.]","\\1<prd>",text)
    text = re.sub(multiple_dots, lambda match: "<prd>" * len(match.group(0)), text)
    if "Ph.D" in text:
        text = text.replace("Ph.D.","Ph<prd>D<prd>")
    text = re.sub(r"\s" + alphabets + "[.] "," \\1<prd> ",text)
    text = re.sub(acronyms+" "+starters,"\\1<stop> \\2",text)
    text = re.sub(alphabets + "[.]" + alphabets + "[.]" + alphabets + "[.]","\\1<prd>\\2<prd>\\3<prd>",text)
    text = re.sub(alphabets + "[.]" + alphabets + "[.]","\\1<prd>\\2<prd>",text)
    text = re.sub(r" "+suffixes+"[.] "+starters," \\1<stop> \\2",text)
    text = re.sub(r" "+suffixes+"[.]"," \\1<prd>",text)
    text = re.sub(r" " + alphabets + "[.]"," \\1<prd>",text)

    if "”" in text:
        text = text.replace(".”","”.")
    if "\"" in text:
        text = text.replace(".\"","\".")
    if "!" in text:
        text = text.replace("!\"","\"!")
    if "?" in text:
        text = text.replace("?\"","\"?")

    if "。" in text:
        text = text.replace("。”","”。")
    if "？" in text:
        text = text.replace("？”","”？")
    if "！" in text:
        text = text.replace("！”","”！")
    if "；" in text:
        text = text.replace("；”","”；")
    if "：" in text:
        text = text.replace("：”","”：")
    if "，" in text:
        text = text.replace("，”","”，")

    text = text.replace(".",".<stop>")
    text = text.replace("?","?<stop>")
    text = text.replace("!","!<stop>")
    text = text.replace("。","。<stop>")
    text = text.replace("？","？<stop>")
    text = text.replace("！","！<stop>")
    text = text.replace("；","；<stop>")
    text = text.replace("：","：<stop>")
    text = text.replace("，","，<stop>")
    text = text.replace("<prd>",".")
    sentences = text.split("<stop>")
    sentences = [s.strip() for s in sentences]
    if sentences and not sentences[-1]:
        sentences = sentences[:-1]
    # fmt: on

    new_sentences = []
    buff = ""
    for sentence in sentences:
        buff += " " + sentence
        logger.debug(f"buff: {buff}")
        if len(buff) > min_sentence_len:
            new_sentences.append(buff[1:])
            buff = ""

    if buff:
        new_sentences.append(buff[1:])

    logger.debug(f"Before: {text}\nAfter basic_split_sentences: {new_sentences}")
    return new_sentences


class OrionSentenceTokenizer(basic.SentenceTokenizer):
    def __init__(
        self,
        *,
        language: str = "en_US",
        robot: Robot = None,
        min_sentence_len: int = 20,
        stream_context_len: int = 10,
    ) -> None:
        self._config = _TokenizerOptions(
            language=language,
            min_sentence_len=min_sentence_len,
            stream_context_len=stream_context_len,
        )
        self.robot = robot

    def tokenize(self, text: str, *, language: str | None = None) -> list[str]:
        return split_sentences(
            text,
            min_sentence_len=self._config.min_sentence_len,
            system_language=self.robot.language,
            is_multilingual=self.robot.multilingual,
        )

    def stream(self, *, language: str | None = None) -> tokenizer.SentenceStream:
        return token_stream.BufferedSentenceStream(
            tokenizer=functools.partial(
                split_sentences,
                min_sentence_len=self._config.min_sentence_len,
                system_language=self.robot.language,
                is_multilingual=self.robot.multilingual,
            ),
            min_token_len=self._config.min_sentence_len,
            min_ctx_len=self._config.stream_context_len,
        )
