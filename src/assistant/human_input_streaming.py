import asyncio
import json
import time
import traceback
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from typing import TYPE_CHECKING, Any, List, TypeVar

import aiohttp
import livekit
import livekit.agents
import redis
from livekit import rtc
from livekit.agents import (
    stt as speech_to_text,
)
from livekit.agents import utils
from livekit.agents import (
    vad as voice_activity_detection,
)
from livekit.agents.stt import (
    SpeechData,
    SpeechEventType,
)
from livekit.agents.utils import aio, http_context
from livekit.agents.voice_assistant.human_input import HumanInput
from livekit.plugins import openai
from loguru import logger

from src.assistant.stt_forwarder import OrionSTTSegmentsForwarder
from src.plugins import orion_asr
from src.plugins.orion_asr import STT, SpeechEvent
from src.plugins.orion_vad_v3 import (
    ASRStreamEventType,
    VADStreamV3,
)
from src.settings import agent_setting
from src.utils.async_utils import callback, gracefully_cancel
from src.utils.cos_client import CosClient
from src.utils.feishu_alarm import send_feishu_alarm

if TYPE_CHECKING:
    from src.session_manager.robot import Robot


T = TypeVar("T")


class UserSemanticsInfo:
    def __init__(self, conn_id, robot: "Robot"):
        self.client_id = robot.client_id or "orion.ovs.client.1514259512471"
        self.enterprise_id = robot.enterprise_id
        self.group_id = robot.group_id or "ovs.group.158623137963780"
        self.version = "1.0"
        self.model = ""
        self.sn = conn_id
        self.devid = robot.device_id or "M03SCN1A14024530EC45"
        self.open_multi_lang = True if robot.multilingual else False

    def to_dict(self):
        return {
            "client_id": self.client_id,
            "enterprise_id": self.enterprise_id,
            "group_id": self.group_id,
            "version": self.version,
            "model": self.model,
            "sn": self.sn,
            "devid": self.devid,
            "is_agent": True,  # 是否是agentos
            "open_multi_lang": self.open_multi_lang,  # 是否使用多语言
        }


class WebSocketManager:
    def __init__(self, robot: "Robot", session: aiohttp.ClientSession | None = None):
        self._robot = robot
        self._session = session
        self._connections = {}  # Map of audio_id to connection info
        self._cleanup_tasks = {}  # Map of audio_id to cleanup tasks

    def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure a session exists and return it."""
        if not self._session:
            self._session = http_context.http_session()
        return self._session

    async def set_stt_channel(self, stt_event_ch):
        self._stt_event_ch = stt_event_ch

    async def _handle_ws_message(self, ws, audio_id: str):
        logger.info(
            f"[OrionStreamingHumanInput] Started _handle_ws_message for {audio_id}"
        )
        async for msg in ws:
            try:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    logger.info(
                        f"[OrionStreamingHumanInput] WS message data for {audio_id}: {data}"
                    )

                    # 检查基本结构
                    asr_param = data.get("asr_param", {})
                    asr_content = data.get("asr_content", {})

                    # 处理识别结果包, 包类型"": 识别结果包"semantics": 语义结果包"resource"：资源结果包
                    pkg_type = asr_param.get("pkg_type")
                    if pkg_type not in ["", "semantics", "decoder", "resource"]:
                        continue

                    is_final = (asr_param.get("idx") or 0) == -9999 or (
                        asr_param.get("status") or 0
                    ) == 3

                    message_audio_id = asr_param.get("sid")

                    # 获取识别结果
                    text = ""
                    nbest = asr_content.get("nbest", [])
                    if nbest:
                        text = nbest[0]  # 取第一个结果

                    speech_data = SpeechData(
                        text=text,
                        start_time=0,
                        end_time=0,
                        confidence=0,
                        language=self._robot.language,
                    )

                    # 获取连接信息
                    conn_info = self._connections.get(audio_id, {})
                    first_frame_time = conn_info.get("first_frame_time")
                    last_frame_time = conn_info.get("last_frame_time")

                    # 计算elapsed时间
                    if is_final:  # 如果是最终结果，则计算从尾包到最后一帧的时间
                        elapsed = (
                            time.time() - last_frame_time if last_frame_time else 0
                        )
                        logger.info(
                            f"[OrionStreamingHumanInput] ASR final transcript: {text} "
                            f"音频ID: {message_audio_id} "
                            f"环境：{agent_setting.env} "
                            f"设备ID: {self._robot.device_id}"
                        )
                    else:  # 如果是中间结果，则计算发送第一包之前到收到当前帧的时间
                        elapsed = (
                            time.time() - first_frame_time if first_frame_time else 0
                        )

                    event = SpeechEvent(
                        type=SpeechEventType.FINAL_TRANSCRIPT
                        if is_final
                        else SpeechEventType.INTERIM_TRANSCRIPT,
                        alternatives=[speech_data],
                        audio_id=message_audio_id,
                        elapsed=elapsed,
                    )

                    if hasattr(self, "_stt_event_ch") and self._stt_event_ch:
                        self._stt_event_ch.send_nowait(event)

                    if asr_param.get("err_no", 0) != 0:
                        logger.error(f"ASR error for {audio_id}: {data}")

                    if is_final:
                        await self.cleanup_connection(audio_id)
            except Exception as e:
                logger.error(
                    f"Error processing WebSocket message for {audio_id}: {e} {traceback.format_exc()}"
                )
                await send_feishu_alarm(
                    f"Error processing WebSocket message for {audio_id}: {e} {traceback.format_exc()}"
                )
                continue

    def _create_params(self, index: int, audio_id: str):
        semantics_info = UserSemanticsInfo(audio_id, self._robot)

        return {
            "sid": audio_id,
            "idx": str(index),
            "ws_version": 1,
            "sdk_version": 1,
            "audio_type": 0,  # K16_PCM_HEAD
            "lang": self._robot.language_code,
            "protocol": "0",
            "devid": self._robot.device_id,
            "ver": "1.0.0",
            "pfm": "linux",
            "pid": self._robot.product_id,
            "user_semantics": json.dumps(semantics_info.to_dict()),
        }

    async def connect(self, audio_id: str) -> None:
        """Establish WebSocket connection and set up message handling for a specific audio_id."""
        try:
            # First check if a connection already exists for this audio_id
            if audio_id in self._connections:
                logger.info(f"Connection for {audio_id} already exists, reusing")
                return self._connections[audio_id]["ws"]

            # Log current connection count
            current_connection_count = len(self._connections)
            logger.info(
                f"Current WebSocket connection count: {current_connection_count}"
            )

            # Check if count exceeds threshold and send alarm
            if current_connection_count >= 20:
                current_audio_ids = list(self._connections.keys())

                error_msg = f"WebSocket connection count ({current_connection_count}) exceeds threshold (20), Audio IDs: {current_audio_ids}"
                logger.warning(error_msg)
                await send_feishu_alarm(error_msg)

            session = self._ensure_session()
            ws = await session.ws_connect(agent_setting.asr_ws_url)

            # Start message handler
            message_task = asyncio.create_task(self._handle_ws_message(ws, audio_id))

            # Send initial parameters
            params = self._create_params(1, audio_id)
            logger.info(
                f"Initializing WebSocket connection for {audio_id}: {params} ws_url: {agent_setting.asr_ws_url} ws_status: {ws.closed}"
            )
            await ws.send_json(params)

            first_frame_time = time.time()

            # Store connection info
            self._connections[audio_id] = {
                "ws": ws,
                "message_task": message_task,
                "first_frame_time": first_frame_time,
                "last_frame_time": first_frame_time,
            }

            # Log updated connection count
            new_connection_count = len(self._connections)
            logger.info(
                f"WebSocket connection established. New connection count: {new_connection_count}"
            )

            return ws

        except Exception as e:
            logger.error(f"WebSocket connection error for {audio_id}: {e}")
            await send_feishu_alarm(
                f"asr调用异常，地址：{agent_setting.asr_ws_url}, audio_id: {audio_id}, error: {str(e)}"
            )
            await self.cleanup_connection(audio_id)
            raise

    async def reconnect(
        self,
        audio_id: str,
        is_first_batch: bool = False,
        packet: bytes | None = None,
    ) -> None:
        """Handle reconnection logic with optional resend of last packet."""
        logger.warning(f"Reconnecting WebSocket connection for {audio_id}")
        await self.cleanup_connection(audio_id)

        # Create new connection
        ws = await self.connect(audio_id)

        if packet:
            if is_first_batch:
                await ws.send_bytes(packet)
            else:
                # For non-first batches, we need to add PCM header
                combined_data = (
                    bytearray([0x05, 0x00, 0x00, 0x00])
                    + packet[
                        packet.find(b"\r\n--DD**ASR**LIB\r\n")
                        + len(b"\r\n--DD**ASR**LIB\r\n") :
                    ]
                )
                await ws.send_bytes(b"\r\n--DD**ASR**LIB\r\n" + combined_data)

    async def send_frames(
        self, frames: bytes, audio_id: str, is_first_batch: bool = False
    ) -> None:
        """Send audio frames with reconnection handling."""
        if not frames:
            return

        # Get connection for this audio_id
        conn_info = self._connections.get(audio_id)
        if not conn_info:
            logger.warning(f"No connection found for {audio_id}, creating new one")
            await self.connect(audio_id)
            conn_info = self._connections.get(audio_id)

        if not conn_info or not conn_info.get("ws") or conn_info["ws"].closed:
            logger.warning(f"No valid connection for {audio_id}, reconnecting")
            await self.connect(audio_id)
            conn_info = self._connections.get(audio_id)

        ws = conn_info["ws"]
        BOUNDARY_START = "\r\n--DD**ASR**LIB\r\n"

        # Prepare the packet
        if is_first_batch:
            combined_data = bytearray([0x05, 0x00, 0x00, 0x00]) + frames
        else:
            combined_data = frames

        packet = BOUNDARY_START.encode() + combined_data

        try:
            await ws.send_bytes(packet)
            # Update last frame time
            self._connections[audio_id]["last_frame_time"] = time.time()
        except aiohttp.ClientConnectionResetError as e:
            logger.error(
                f"ClientConnectionResetError Error sending bytes for {audio_id}: {e} Websocket {ws.closed} 设备ID: {self._robot.device_id} 环境：{agent_setting.env}"
            )
            await send_feishu_alarm(
                f"ClientConnectionResetError Error sending bytes for {audio_id}: {e} Websocket {ws.closed} 设备ID: {self._robot.device_id} 环境：{agent_setting.env}"
            )
            # Attempt reconnection with packet resend
            await self.reconnect(audio_id, is_first_batch, packet)
        except Exception as e:
            logger.error(
                f"Error sending bytes for {audio_id}: {e} Websocket {ws.closed} 设备ID: {self._robot.device_id}"
            )
            await send_feishu_alarm(
                f"Error sending bytes for {audio_id}: {e} Websocket {ws.closed} 设备ID: {self._robot.device_id}"
            )
            raise

    async def send_end_message(self, sequence: int, audio_id: str) -> None:
        """Send end message for the WebSocket connection."""
        conn_info = self._connections.get(audio_id)
        if not conn_info or not conn_info.get("ws") or conn_info["ws"].closed:
            logger.warning(
                f"Cannot send end message - no valid connection for {audio_id}"
            )
            return

        ws = conn_info["ws"]

        try:
            self._connections[audio_id]["last_frame_time"] = time.time()
            end_params = self._create_params(-(sequence + 1), audio_id)
            await ws.send_json(end_params)

            # 创建定时器，在5秒后检查连接是否已关闭，否则强制关闭
            # 释放连接条件2: 发送尾包n秒后如果还没结束，直接结束
            cleanup_task = asyncio.create_task(self._delayed_cleanup(audio_id, 60))
            self._cleanup_tasks[audio_id] = cleanup_task

        except Exception as e:
            logger.error(f"Error sending end message for {audio_id}: {e}")
            await self.cleanup_connection(audio_id)

    async def _delayed_cleanup(self, audio_id: str, delay: float) -> None:
        """Wait for specified delay then force cleanup if connection still exists."""
        await asyncio.sleep(delay)
        logger.info(
            f"Checking if connection for {audio_id} needs forced cleanup after {delay}s"
        )
        if audio_id in self._connections:
            logger.warning(
                f"[OrionStreamingHumanInput] Force cleaning up connection for {audio_id} after {delay}s timeout"
            )
            await self.cleanup_connection(audio_id)

    async def cleanup_connection(self, audio_id: str) -> None:
        """Clean up WebSocket connection and related tasks for a specific audio_id."""
        # First check if the connection exists before trying to remove it
        # This prevents race conditions where cleanup might be called multiple times
        if audio_id not in self._connections:
            return

        # Log current connection count before removal
        current_connection_count = len(self._connections)
        logger.info(
            f"[OrionStreamingHumanInput] Cleaning up connection for {audio_id}. Current connection count: {current_connection_count}"
        )

        conn_info = self._connections.pop(audio_id, None)
        if not conn_info:
            logger.warning(
                f"[OrionStreamingHumanInput] No connection info found for {audio_id}"
            )
            return

        # Close websocket and log file descriptor info
        ws = conn_info.get("ws")
        if ws and not ws.closed:
            logger.info(
                f"[OrionStreamingHumanInput] Closing WebSocket connection for {audio_id}"
            )
            await ws.close()

        # Cancel cleanup task if exists
        cleanup_task = self._cleanup_tasks.pop(audio_id, None)
        if cleanup_task and not cleanup_task.done():
            logger.info(
                f"[OrionStreamingHumanInput] Cancelling cleanup task for {audio_id}"
            )
            try:
                await gracefully_cancel(cleanup_task)
            except Exception as e:
                logger.error(
                    f"[OrionStreamingHumanInput] Error cancelling cleanup task for {audio_id}: {e}"
                )

        # Cancel message task
        message_task = conn_info.get("message_task")
        if message_task and not message_task.done():
            logger.info(
                f"[OrionStreamingHumanInput] Cancelling message task for {audio_id}"
            )
            try:
                await gracefully_cancel(message_task)
            except Exception as e:
                logger.error(
                    f"[OrionStreamingHumanInput] Error cancelling message task for {audio_id}: {e}"
                )

        # Log updated connection count after removal
        new_connection_count = len(self._connections)
        logger.info(
            f"[OrionStreamingHumanInput] Connection for {audio_id} cleaned up. New connection count: {new_connection_count}"
        )

    async def cleanup_all(self) -> None:
        """Clean up all WebSocket connections and related tasks."""
        logger.info(
            f"[OrionStreamingHumanInput] Cleaning up all WebSocketManager connections and session. Total connections: {len(self._connections)}"
        )

        # First cleanup all connections
        audio_ids = list(self._connections.keys())
        for audio_id in audio_ids:
            await self.cleanup_connection(audio_id)

    def is_connected(self, audio_id: str) -> bool:
        """Check if a specific audio_id has an active connection."""
        conn_info = self._connections.get(audio_id)
        if not conn_info or not conn_info.get("ws"):
            return False
        return not conn_info["ws"].closed


class OrionStreamingHumanInput(HumanInput):
    def __init__(
        self,
        *,
        room: rtc.Room,
        vad: voice_activity_detection.VAD,
        stt: speech_to_text.STT,
        participant: rtc.RemoteParticipant,
        transcription: bool,
        robot: "Robot",
        redis_client: redis.Redis,
        cos_client: CosClient,
        http_session: aiohttp.ClientSession | None = None,
    ) -> None:
        super().__init__(
            room=room,
            vad=vad,
            stt=stt,
            participant=participant,
            transcription=transcription,
        )
        self._robot = robot
        self.redis_client = redis_client
        self.cos_client = cos_client
        self._executor = ThreadPoolExecutor(max_workers=4)
        self._session = http_session

    def _ensure_session(self) -> aiohttp.ClientSession:
        if not self._session:
            self._session = http_context.http_session()

        return self._session

    @utils.log_exceptions(logger=logger)
    async def _recognize_task(self, audio_stream: rtc.AudioStream) -> None:
        """
        Receive the frames from the user audio stream and detect voice activity.
        """

        if not (
            isinstance(self._stt, openai.STT) or isinstance(self._stt, orion_asr.STT)
        ):
            raise ValueError(f"Unsupported STT type: {type(self._stt)}")

        stt_event_ch = aio.Chan[SpeechEvent]()
        vad_stream: "VADStreamV3" = self._vad.stream()
        self._vad_stream = vad_stream
        asr_event_ch = self._vad_stream._asr_event_ch

        stt_forwarder = None
        if self._transcription:
            stt_forwarder = OrionSTTSegmentsForwarder(
                room=self._room,
                participant=self._participant,
                track=self._subscribed_track,
            )

        ws_manager = WebSocketManager(self._robot, self._ensure_session())
        await ws_manager.set_stt_channel(stt_event_ch)

        try:

            async def _audio_stream_co() -> None:
                # forward the audio stream to the VAD and STT streams
                async for ev in audio_stream:
                    vad_stream.push_frame(ev.frame)

            async def _vad_stream_co() -> None:
                async for ev in vad_stream:
                    if ev.type == voice_activity_detection.VADEventType.START_OF_SPEECH:
                        # logger.debug("[!!!] vad detect start of speech")
                        self._speaking = True
                        self.emit("start_of_speech", ev)
                        # forward to stt
                        stt_event_ch.send_nowait(
                            SpeechEvent(SpeechEventType.START_OF_SPEECH)
                        )

                    elif (
                        ev.type == voice_activity_detection.VADEventType.INFERENCE_DONE
                    ):
                        self._speech_probability = ev.probability
                        await self.async_emit("vad_inference_done", ev)
                    elif ev.type == voice_activity_detection.VADEventType.END_OF_SPEECH:
                        # logger.debug("[!!!] vad detect end of speech")
                        self._speaking = False
                        self.emit("end_of_speech", ev)
                        try:
                            stt_event_ch.send_nowait(
                                SpeechEvent(
                                    type=SpeechEventType.END_OF_SPEECH,
                                    audio_id=ev.audio_id,
                                )
                            )
                        except Exception as e:
                            logger.error(
                                f"[OrionHumanInput] AudioID: {ev.audio_id} VAD send end of speech error:{ev} {e} {traceback.format_exc()}"
                            )
                            continue

            async def _asr_stream_co() -> None:
                FRAMES_BATCH_SIZE = 10  # 每10帧发送一次
                audio_sequences = {}  # 每个audio_id独立维护sequence
                audio_batches = {}  # 每个audio_id独立维护batch
                audio_events = {}  # 每个audio_id独立维护events

                try:
                    async for ev in asr_event_ch:
                        try:
                            audio_id = ev.audio_id

                            if ev.type == ASRStreamEventType.SPEAKING:
                                # 初始化当前音频ID的数据结构
                                if audio_id not in audio_batches:
                                    audio_batches[audio_id] = []
                                    audio_events[audio_id] = []
                                    audio_sequences[audio_id] = 0

                                # 添加到当前批次和总事件
                                audio_batches[audio_id].append(ev)
                                audio_events[audio_id].append(ev)

                                # 如果达到批次大小，处理并发送
                                if len(audio_batches[audio_id]) >= FRAMES_BATCH_SIZE:
                                    audio_sequences[audio_id] += 1
                                    sequence = audio_sequences[audio_id]

                                    # 处理并发送批次
                                    audio_frames = [
                                        event.frame for event in audio_batches[audio_id]
                                    ]
                                    audio_buffer = livekit.agents.utils.merge_frames(
                                        audio_frames
                                    )
                                    processed_data = await STT._process_audio(
                                        audio_buffer,
                                        audio_id,
                                        remove_header=True,
                                    )

                                    logger.debug(
                                        f"[OrionStreamingHumanInput] AudioID: {audio_id} Send No.{sequence} frames: {len(processed_data)}"
                                    )

                                    await ws_manager.send_frames(
                                        processed_data, audio_id
                                    )
                                    audio_batches[audio_id].clear()

                            elif ev.type == ASRStreamEventType.START_OF_SPEECH:
                                logger.debug(
                                    f"[OrionStreamingHumanInput] AudioID: {audio_id} ASRStreamEventType.START_OF_SPEECH"
                                )

                                # 重置当前音频ID的状态
                                audio_batches[audio_id] = []
                                audio_events[audio_id] = []
                                audio_sequences[audio_id] = 0

                                # 确保为该音频ID创建连接
                                await ws_manager.connect(audio_id)

                            elif ev.type == ASRStreamEventType.END_OF_SPEECH:
                                logger.debug(
                                    f"[OrionStreamingHumanInput] AudioID: {audio_id} ASRStreamEventType.END_OF_SPEECH"
                                )

                                # 添加到事件列表
                                if audio_id in audio_events:
                                    audio_events[audio_id].append(ev)

                                # 保存音频文件
                                if audio_id in audio_events and audio_events[audio_id]:
                                    asyncio.create_task(
                                        self._save_audio_file(
                                            audio_id, audio_events[audio_id]
                                        )
                                    )

                                # 发送剩余帧
                                if (
                                    audio_id in audio_batches
                                    and audio_batches[audio_id]
                                ):
                                    sequence = audio_sequences.get(audio_id, 0) + 1
                                    audio_sequences[audio_id] = sequence

                                    audio_frames = [
                                        event.frame for event in audio_batches[audio_id]
                                    ]
                                    audio_buffer = livekit.agents.utils.merge_frames(
                                        audio_frames
                                    )
                                    processed_data = await STT._process_audio(
                                        audio_buffer,
                                        audio_id,
                                        remove_header=True,
                                    )

                                    await ws_manager.send_frames(
                                        processed_data, audio_id
                                    )

                                # 发送尾包
                                sequence = audio_sequences.get(audio_id, 0)
                                await ws_manager.send_end_message(sequence, audio_id)

                                # 清理这个音频ID的批次数据
                                if audio_id in audio_batches:
                                    audio_batches[audio_id].clear()

                        except Exception as e:
                            logger.error(
                                f"Error during ASRStreamEventType handling for {ev.audio_id}: {e}"
                            )
                            await send_feishu_alarm(
                                f"Error during ASRStreamEventType handling for {ev.audio_id}: {e}"
                            )
                finally:
                    # Ensure we clean up all connections when the stream ends
                    logger.info(
                        "ASR stream ended, cleaning up all WebSocket connections"
                    )
                    await ws_manager.cleanup_all()

            async def _stt_stream_co() -> None:
                async for ev in stt_event_ch:
                    if stt_forwarder is not None:
                        stt_forwarder.update(ev)

                    if ev.type == speech_to_text.SpeechEventType.FINAL_TRANSCRIPT:
                        logger.info(
                            f"[OrionStreamingHumanInput] Final transcript: {ev.alternatives[0]}"
                        )
                        self.emit("final_transcript", ev)
                    elif ev.type == speech_to_text.SpeechEventType.INTERIM_TRANSCRIPT:
                        logger.debug(
                            f"[OrionStreamingHumanInput] Interim transcript: {ev.alternatives[0]}"
                        )
                        self.emit("interim_transcript", ev)

            tasks = [
                asyncio.create_task(_audio_stream_co()),
                asyncio.create_task(_vad_stream_co()),
                asyncio.create_task(_asr_stream_co()),
                asyncio.create_task(_stt_stream_co()),
            ]

            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in _recognize_task: {e}")
            await send_feishu_alarm(f"Error in _recognize_task: {e}")
        finally:
            # Ensure everything gets cleaned up
            await gracefully_cancel(*tasks)

            if stt_forwarder is not None:
                await stt_forwarder.aclose()

            stt_event_ch.close()
            await vad_stream.aclose()

            # Make sure all WebSocket connections are properly closed including the session
            await ws_manager.cleanup_all()

    async def async_emit(self, event: T, *args: Any, **kwargs: Any) -> None:
        if event in self._events:
            callables = self._events[event].copy()
            for callback in callables:
                await callback(*args, **kwargs)

    def _cache_file_path(self, audio_id: str, file_path: str) -> None:
        """Cache file path in Redis"""
        logger.info(f"AudioID: {audio_id} Caching file path {file_path}")
        try:
            self.redis_client.setex(
                f"asr_file_path_{agent_setting.env}_{audio_id}",
                agent_setting.asr_file_path_cache_ttl,
                file_path,
            )
        except Exception as e:
            logger.error(f"AudioID: {audio_id} Error caching file path: {e}")

    async def _save_audio_file(self, audio_id: str, events: List) -> None:
        """Save audio file to COS"""
        start = time.time()
        frames = [event.frame for event in events]
        audio_buffer = livekit.agents.utils.merge_frames(frames)
        wav_data = await STT._process_audio(audio_buffer, audio_id)
        logger.debug(f"{audio_id} Orion STT Merge frames elapsed {time.time() - start}")
        # save audio file to COS
        env = agent_setting.env
        today = time.strftime("%Y-%m-%d", time.localtime())
        file_path = f"{env}/{self._robot.device_id}/{today}/{audio_id}.wav"
        future = self._executor.submit(self.cos_client.upload_file, wav_data, file_path)
        future.add_done_callback(callback)

        # cache file path with audio_id
        future = self._executor.submit(self._cache_file_path, audio_id, file_path)
        future.add_done_callback(callback)
