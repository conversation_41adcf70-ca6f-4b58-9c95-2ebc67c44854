import asyncio
import copy
import json
from typing import Any, Dict, Literal, Optional, TypeVar
import traceback
import time
import uuid

from livekit.rtc.chat import <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatMessage
from livekit.rtc.room import DataPacket, Room
from loguru import logger


from src.session_manager.robot import Robot
from src.utils.feishu_alarm import send_feishu_alarm, send_feishu_alarm_sync


_CHAT_TOPIC = "lk-chat-topic"
_CHAT_UPDATE_TOPIC = "lk-chat-update-topic"

EventTypes = Literal["message_received",]
T = TypeVar("T")

PACKET_CONTENT_SIZE = 3000


class OrionChatManager(ChatManager):
    def __init__(self, room: Room, robot: Robot):
        super().__init__(room)

        self.max_cache_size = 50
        self.message_cache = {}
        self.robot = robot

    async def async_emit(self, event: T, *args: Any, **kwargs: Any) -> None:
        if event in self._events:
            callables = self._events[event].copy()
            for callback in callables:
                await callback(*args, **kwargs)

    async def send_message(self, message: Dict) -> Optional["ChatMessage"]:
        """Send a chat message to the end user using LiveKit Chat Protocol.

        Args:
            message (str): the message to send

        Returns:
            ChatMessage: the message that was sent
        """
        if not self._room:  # wait for room to be set
            return

        # record diagnostic info
        logger.debug(f"Sending message with diagnostic info: {message}")
        msg_type = message.get("msg_type")
        logger.info(f"Sending msg_type: {msg_type}")
        _ = message.pop("diagnostic_info", None)

        message_id = str(uuid.uuid4())
        message_str = json.dumps(message, ensure_ascii=False)

        # check the bytes of the message, if greater than 14KB, split the message
        message_bytes = len(message_str.encode("utf-8"))

        if message_bytes > 14 * 1024:
            if (
                self.robot.action_version not in ["draft", "oversea_draft"]
                and self.robot.action_version < "v1.0.5"
            ):  # 旧版本不支持分包
                await send_feishu_alarm(
                    f"Message is too large: {message_bytes} {message_str[:100]}"
                )
                return

            logger.debug(
                f"Message is too large, splitting into chunks: {message_bytes}"
            )
            # 简单切分content字符串
            content_str = json.dumps(message["content"], ensure_ascii=False)
            chunks = [
                content_str[i : i + PACKET_CONTENT_SIZE]
                for i in range(0, len(content_str), PACKET_CONTENT_SIZE)
            ]
            total = len(chunks)
            for i, chunk in enumerate(chunks):
                new_msg = message.copy()
                new_msg["content"] = chunk
                new_msg["total"] = total
                new_msg["idx"] = i + 1
                logger.debug(f"[New Message] {new_msg}")
                new_msg_str = json.dumps(new_msg, ensure_ascii=False)

                msg = ChatMessage(
                    message=new_msg_str,
                    is_local=True,
                    participant=self._lp,
                    id=message_id,
                )
                payload = json.dumps(msg.asjsondict())
                logger.info(
                    f"[Large Message {message_id}: {i + 1}] Sending message: {payload}"
                )
                await self._lp.publish_data(
                    payload=payload,
                    topic=_CHAT_TOPIC,
                )

            result = ChatMessage(
                id=message_id,
                message=message_str,
                is_local=True,
                participant=self._lp,
            )
        else:
            message["total"] = 1
            message["idx"] = 1
            message_str = json.dumps(message, ensure_ascii=False)

            result = await super().send_message(message_str)
            logger.debug(
                f"Sending message: {result.message} timestamp: {result.timestamp} msg_id: {result.id}"
            )
            logger.info(f"[Sending Normal Message: {message_id}]: {message_str}")
        return result

    def _on_data_received(self, dp: DataPacket):
        # handle both new and updates the same way, as long as the ID is in there
        # the user can decide how to replace the previous message
        logger.debug(f"Received data packet: {dp.data}")
        if dp.topic == _CHAT_TOPIC or dp.topic == _CHAT_UPDATE_TOPIC:
            try:
                msg = None
                parsed = json.loads(dp.data)
                message_str = parsed.get("message")
                message_parsed = json.loads(message_str)

                if message_parsed.get("total", 1) != 1:
                    # 合并消息
                    if parsed["id"] not in self.message_cache:
                        self.message_cache[parsed["id"]] = []

                    # 在分块里直接加 created_at 字段
                    message_parsed["received_at"] = time.time()
                    self.message_cache[parsed["id"]].append(message_parsed)

                    if len(self.message_cache) > self.max_cache_size:
                        # 移除最早的消息
                        self.message_cache.pop(next(iter(self.message_cache)))

                    cached_messages = self.message_cache[parsed["id"]]
                    if len(cached_messages) == message_parsed.get("total"):
                        # 按照idx排序
                        cached_messages.sort(key=lambda x: x.get("idx"))
                        # 合并消息
                        merged_msg_content = "".join(
                            [msg.get("content") for msg in cached_messages]
                        )

                        merged_msg = copy.deepcopy(message_parsed)
                        merged_msg["content"] = merged_msg_content
                        try:
                            merged_msg["content"] = json.loads(merged_msg["content"])
                        except Exception as e:
                            logger.warning(
                                "failed to receive merged message: %s", e, exc_info=e
                            )
                            return

                        logger.info(f"Build merged message: {merged_msg}")
                        parsed["message"] = json.dumps(merged_msg, ensure_ascii=False)
                        msg = ChatMessage.from_jsondict(parsed)

                        self.message_cache.pop(parsed["id"])
                else:
                    # 正常消息
                    logger.info(f"Received normal message: {parsed}")
                    msg = ChatMessage.from_jsondict(parsed)

                if msg:
                    if dp.participant:
                        msg.participant = dp.participant
                    # Use asyncio.create_task to run async_emit without blocking the sync callback
                    asyncio.create_task(self.async_emit("message_received", msg))

            except Exception as e:
                logger.error(
                    f"Failed to parse chat message: {e} {traceback.format_exc()}"
                )
                send_feishu_alarm_sync(
                    f"Failed to parse chat message: {e} {traceback.format_exc()}"
                )
        # 清理过期缓存
        self.clean_expired_cache()

    def clean_expired_cache(self):
        """清理5分钟前的缓存分块消息"""
        now = time.time()
        expired_keys = []
        for k, v in self.message_cache.items():
            # v 是 List[message_parsed]，取最早的 created_at
            if v and now - min([m.get("received_at", 0) for m in v]) > 300:
                expired_keys.append(k)
        for k in expired_keys:
            logger.info(f"Remove expired message cache: {k}")
            self.message_cache.pop(k)
