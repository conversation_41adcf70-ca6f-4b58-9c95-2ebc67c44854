import asyncio
import json
import queue
import time
import traceback
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from datetime import datetime
from random import choice
from typing import AsyncIterable, Dict, List, Optional, Tuple

import redis
from livekit import rtc
from livekit.agents import tts, utils, vad
from livekit.agents.llm import LLMStream
from livekit.agents.voice_assistant import VoiceAssistant
from livekit.agents.voice_assistant.cancellable_source import PlayoutHandle
from loguru import logger
from mcp_use.client import MCPClient
from pydantic import ValidationError

from src.action.report_action_processor import ReportActionManager
from src.action.action_version.version_manager import ActionVersionManager
from src.action.actions import (
    ActionLib,
)
from src.action.model import RequestLLMConfig
from src.action.server_function import AudioOutput, FunctionResult
from src.action_executor.action_runner import ActionRunner
from src.action_executor.executor import _ActionInfo
from src.assistant.agent_output import AgentOutput
from src.assistant.cancellable_source import OrionCancellationSource
from src.assistant.chat_manager import OrionChatManager
from src.assistant.human_input_streaming import OrionStreamingHumanInput
from src.assistant.llm import _llm_stream_to_str_iterable
from src.assistant.model import _GeneralInfo, _PlanInfo
from src.assistant.planner import SingleActionPlanner
from src.assistant.request_msg_tracker import RequestMsgTracker
from src.assistant.transcription import OrionSentenceTokenizer
from src.common.agent_config import Launcher_App_Id, OverSea_Opk_Launcher_App_Id
from src.common.constant import Area, SynthesizeType
from src.common.model import RunStep, get_step_display_name
from src.controller.message_handler import MessageHandler
from src.controller.message_sender import MessageSender
from src.mcp.mcp_adapter import load_mcp_actions
from src.mcp.mcp_manager import MCPManager
from src.messages import (
    ActionResultContent,
    ActionResultMessage,
    ChatAnswerContent,
    ChatAnswerMessage,
    RunMessage,
    RunParameterContent,
)
from src.messages.run import RunStepContent
from src.plugins import orion_asr
from src.session_manager.blackboard import Blackboard
from src.session_manager.chat_context import (
    ChatAction,
    ChatEvent,
    ChatImage,
    ChatMessage,
    ChatParameter,
    ChatResult,
)
from src.session_manager.memory import Memory
from src.session_manager.plan_running_status import RuntimeStatusManager
from src.session_manager.robot import Robot, MODULE_APP_PACKAGE
from src.settings import AgentMode, SummaryMode, agent_setting
from src.utils.async_utils import callback, gracefully_cancel, run_async
from src.utils.cos_client import CosClient
from src.utils.diagnostic import save_diagnostic_info
from src.utils.diagnostic_client import DiagnosticClient
from src.utils.feishu_alarm import send_feishu_alarm
from src.utils.general_environment_sense import get_weather
from src.utils.i18n import _, set_language
from src.utils.limit_dict import LimitedSizeDict


class AgentAssistant(VoiceAssistant):
    def __init__(
        self,
        memory: Memory,
        cos_client: "CosClient",
        redis_client: "redis.Redis",
        tts: tts.TTS,
        robot: "Robot",
        diagnostic_client: "DiagnosticClient",
        vad_event_queue: "queue.Queue",
        mcp_client: Optional["MCPClient"] = None,
        *args,
        **kwargs,
    ) -> None:
        kwargs["chat_ctx"] = None  # we won't use chat_ctx
        kwargs["tts"] = tts
        super().__init__(*args, **kwargs)

        self._general_ch = utils.aio.Chan[_GeneralInfo]()
        self._action_ch = utils.aio.Chan[_ActionInfo]()
        self._audio_action_ch = utils.aio.Chan[_ActionInfo]()
        self._plan_ch = utils.aio.Chan[_PlanInfo]()
        self._chat_manager = None
        self._memory = memory
        self._robot = robot
        self._query_id = ""
        self._cos_client = cos_client
        self._runtime_manager = RuntimeStatusManager(redis_client=redis_client)
        self._total_actions = None
        self._blackboard = Blackboard(
            redis_client=redis_client,
        )
        self._queue_run_step = asyncio.Queue(maxsize=100)
        self._report_action_manager = ReportActionManager()

        self._action_runner = ActionRunner(
            memory=self.memory,
            cos_client=self._cos_client,
            robot=self.robot,
            runtime_manager=self._runtime_manager,
            redis_client=redis_client,
            blackboard=self._blackboard,
            diagnostic_client=diagnostic_client,
            run_step_queue=self._queue_run_step,
        )
        self.message_handler = MessageHandler(
            agent_assistant=self,
        )
        self.request_msg_tracker = RequestMsgTracker(self.robot.device_id)

        if not tts.capabilities.streaming:
            from livekit.agents import tts as text_to_speech

            tts = text_to_speech.StreamAdapter(
                tts=tts,
                sentence_tokenizer=OrionSentenceTokenizer(
                    robot=self.robot, min_sentence_len=5
                ),
            )
        self._tts = tts

        self._mcp_client = mcp_client
        self._mcp_manager = MCPManager(mcp_client)

        self._thread_pool_executor = ThreadPoolExecutor(max_workers=4)

        # init single action planner
        self._single_action_planner = SingleActionPlanner(
            memory=self.memory,
            robot=self.robot,
            action_ch=self._action_ch,
            general_ch=self._general_ch,
            plan_ch=self._plan_ch,
            deferred_validation=self._deferred_validation,
            cos_client=self._cos_client,
            candidate_actions=self._total_actions,
            allow_interruptions=self._opts.allow_interruptions,
            runtime_manager=self._runtime_manager,
            blackboard=self._blackboard,
            action_runner=self._action_runner,
            message_handler=self.message_handler,
            diagnostic_client=diagnostic_client,
            run_step_queue=self._queue_run_step,
        )

        self._diagnostic_client = diagnostic_client

        # queue of vad events
        self._vad_event_queue = vad_event_queue
        self._client_wakeup_result = LimitedSizeDict(
            size_limit=50
        )  # 客户端检测结果, audio_id: {"vad_start": datetime, "vad_end": datetime, "result": bool}

        # audio event timestamp
        self.init_audio_event_timestamp()
        self._message_sender = None

    @property
    def memory(self) -> Memory:
        return self._memory

    @property
    def robot(self) -> Robot:
        return self._robot

    @property
    def blackboard(self) -> Blackboard:
        return self._blackboard

    def init_audio_event_timestamp(self):
        self._vad_end_timestamp = 0.0
        self._vad_start_timestamp = 0.0
        self._asr_end_timestamp = 0.0
        self._asr_cost_time = 0.0
        self._wakeup_cost_time = 0.0

    async def _loop_send_run_step_message(self):
        step_id = 0
        while True:
            step_content: RunStep = await self._queue_run_step.get()
            await asyncio.sleep(0.2)
            logger.info(f"Ignored run step {step_content}")
            continue

            try:
                run_msg = RunMessage(
                    msg_type="run.step",
                    content=RunStepContent(
                        step_id=step_id,
                        step_name=step_content.step_name,
                        step_display_name=get_step_display_name(
                            self.robot.language, step_name=step_content.step_name
                        ),
                        step_result=step_content.step_result,
                        status=step_content.status,
                        error_msg=step_content.error_msg,
                        timestamp=step_content.timestamp,
                    ),
                    device_id=self.robot.device_id,
                    diagnostic_info={},
                ).model_dump()
                logger.info(f"Run step --> {run_msg}")
                await self._chat_manager.send_message(run_msg)
            except Exception as e:
                logger.exception(
                    f"Error when send run step message: {e} {traceback.format_exc()}"
                )
            step_id += 1

    def _switch_agent_mode(self, agent_mode: Optional[str]):
        """
        切换agent模式, TODO: 需要优化，目前是直接更新agent_setting
        """
        if not agent_mode:
            return

        # 极速和满血模式标识:turbo极速，full_power满血; 默认config中配置了turbo
        if agent_mode == AgentMode.full_power:
            agent_setting.plan_model_api_key = (
                agent_setting.full_power_plan_model_api_key
            )
            agent_setting.plan_model_base_url = (
                agent_setting.full_power_plan_model_base_url
            )
            agent_setting.plan_model = agent_setting.full_power_plan_model

            agent_setting.summary_model = agent_setting.full_power_generate_text_model

            agent_setting.summary_model_api_key = (
                agent_setting.full_power_plan_model_api_key
            )
            agent_setting.summary_model_base_url = (
                agent_setting.full_power_plan_model_base_url
            )
            agent_setting.summary_mode = SummaryMode.ADVANCED

        elif agent_mode == AgentMode.turbo:
            agent_setting.plan_model_api_key = agent_setting.turbo_plan_model_api_key
            agent_setting.plan_model_base_url = agent_setting.turbo_plan_model_base_url
            agent_setting.plan_model = agent_setting.turbo_plan_model

            agent_setting.summary_model_base_url = (
                agent_setting.turbo_plan_model_base_url
            )
            agent_setting.summary_model_api_key = agent_setting.turbo_plan_model_api_key
            agent_setting.summary_model = agent_setting.turbo_generate_text_model
            agent_setting.summary_mode = SummaryMode.ADVANCED

    def update_robot_status(self, content: dict):
        """
        更新机器人状态：采用部分更新逻辑，只更新content中存在的字段
        """

        logger.info(f"Update robot status: {content}")

        if "language" in content:
            # 暂时去掉语言限制，看效果
            # if agent_setting.region_version == Area.domestic:
            #     content["language"] = LanguageEnum.zh
            # else:
            #     if content["language"] not in [LanguageEnum.zh, LanguageEnum.en]:
            #         content["language"] = LanguageEnum.en

            if self.robot.language != content["language"]:
                set_language(content["language"])

        if "multilingual" in content:
            try:
                content["multilingual"] = int(content["multilingual"])
                if content["multilingual"] != 1:  # fallback multilingual to turn off
                    content["multilingual"] = 0
            except Exception as e:
                logger.error(
                    f"Error when update multilingual: {e} {content['multilingual']}, fallback to 0"
                )
                content["multilingual"] = 0

        if "updated_at" not in content:
            content["updated_at"] = datetime.now().timestamp()

        if agent_mode := content.get("agent_mode", None):  # 切换agent模式
            self._switch_agent_mode(agent_mode)

        updated_interface_state = content.pop("interface_state", {})
        if package_name := updated_interface_state.get("package_name", None):
            if package_name == MODULE_APP_PACKAGE:
                if not updated_interface_state.get("app_id", None):
                    if agent_setting.region_version == Area.domestic:
                        updated_interface_state["app_id"] = Launcher_App_Id
                    else:
                        updated_interface_state["app_id"] = OverSea_Opk_Launcher_App_Id

        indoor_location = content.pop("indoor_location", "")
        if indoor_location:
            self.robot.update_indoor_location(indoor_location)

        if self.robot.face_id:
            if "face_id" in content:
                new_face_id = content["face_id"]
                if new_face_id != self.robot.face_id:
                    logger.info(
                        f"Face id changed: {new_face_id} original face_id: {self.robot.face_id}"
                    )

        updated_geo_location = {}
        if geo_location := content.pop("geo_location", None):
            try:  # update geo location
                updated_geo_location = json.loads(geo_location)
            except Exception as e:
                logger.error(f"Error when parse geo_location: {e} {geo_location}")

        # update interface state
        if "clickable_elements" in updated_interface_state:
            clickable_elements = (
                updated_interface_state.pop("clickable_elements", []) or []
            )
            if clickable_elements:
                if isinstance(clickable_elements, str):
                    try:
                        clickable_elements = json.loads(clickable_elements)
                    except Exception as e:
                        logger.error(f"Error when parse clickable_elements: {e}")
                        clickable_elements = []
            updated_interface_state["clickable_elements"] = clickable_elements

        if updated_interface_state:
            content["interface_state"] = updated_interface_state

        if updated_geo_location:
            content["geo_location"] = updated_geo_location

        # 增加昵称
        if "character_state" in content:
            if self.robot.is_builtin_app:
                content["character_state"]["persona"] = self._rebuild_persona(
                    content["character_state"]
                )
            else:
                content["character_state"]["persona"] = content["character_state"].get(
                    "persona", ""
                )

            if not content["character_state"]["persona"]:
                content["character_state"]["persona"] = (
                    self.robot.character_state.persona
                )

        if "product_id" in content:  # TODO: better solution, compatible with pydantic
            try:
                content["product_id"] = int(content["product_id"])
            except Exception as e:
                msg = f"Error when parse product_id: {e} {content['product_id']}"
                logger.error(msg)

        if "app_actions" in content:
            content["app_actions"] = self._report_action_manager.handle_action_update(
                content["app_actions"], action_type="app"
            )

        if "page_actions" in content:
            content["page_actions"] = self._report_action_manager.handle_action_update(
                content["page_actions"], action_type="page"
            )

        if "block_actions" in content:
            content["block_actions"] = self._report_action_manager.handle_action_update(
                content["block_actions"], action_type="block"
            )

        # update action version
        if "action_version" in content:
            self._update_action_version(content["action_version"])

        logger.info(f"Before update: {self._robot}")
        # update self._robot
        self._robot.update(content)
        logger.info(f"After update: {self._robot}")

    def _rebuild_persona(self, character_state: dict) -> str:
        nickname = character_state.get("nickname", "")
        role = character_state.get("role", "")
        company_profile = character_state.get("company_profile", "")

        if nickname and role and company_profile:
            raw_text = _(
                "Your name is `%s`，you are a `%s`，at the following company: `%s`"
            )
            return raw_text % (nickname, role, company_profile)

    def _update_action_version(self, version: str):
        logger.info(f"Start to update action version: {version}")
        try:
            self._total_actions = ActionVersionManager().fetch_actions_by_version(
                version
            )
            ActionLib().update_builtin_actions(self._total_actions)
        except Exception as e:
            logger.error(
                f"Error when fetch actions by version: {e} {traceback.format_exc()}"
            )
            return

        self._single_action_planner.candidate_actions = self._total_actions
        logger.info(f"Update action version: {version} success")

    def _link_participant(self, identity: str) -> None:
        logger.debug(f"participant identity: {identity}")
        participant = self._room.remote_participants.get(identity)
        if participant is None:
            logger.error("_link_participant must be called with a valid identity")
            return

        self._human_input = OrionStreamingHumanInput(
            room=self._room,
            vad=self._vad,
            stt=self._stt,
            participant=participant,
            transcription=self._opts.transcription.user_transcription,
            robot=self.robot,
            redis_client=self.memory.redis_client,
            cos_client=self._cos_client,
        )

        self._chat_manager = OrionChatManager(room=self._room, robot=self.robot)
        self._message_sender = MessageSender(self._chat_manager, self.robot.device_id)
        logger.info(f"Robot info: {self.robot} LLM {self.llm}")

        def _update_robot_status(
            participant: rtc.Participant, old_metadata: str, new_metadata: str
        ):
            try:
                content = json.loads(new_metadata)
                if "action_version" not in content:  # 确保action_version存在
                    content["action_version"] = "draft"
                self.update_robot_status(content)
            except ValidationError as e:
                error_msg = f"Failed to parse client's robot status: {e.errors()}"
                logger.error(error_msg)
                send_feishu_alarm(error_msg)
            except Exception as e:
                logger.error(f"Error when update robot status: {e} {new_metadata}")

            logger.info(
                f"Robot status metadata changed from {old_metadata} to {new_metadata}"
            )

        # init robot status, fixed ids
        _update_robot_status(participant, "", participant.metadata)
        set_language(self.robot.language)  # init language
        self.memory.device_id = self.robot.device_id
        self._action_runner.action_result_cache.device_id = self.robot.device_id
        self.blackboard.device_id = self.robot.device_id
        self._runtime_manager.device_id = self.robot.device_id

        self._room.on("participant_metadata_changed", _update_robot_status)
        self._action_runner.total_actions = self._total_actions
        self._single_action_planner.candidate_actions = self._total_actions

        def _on_start_of_speech(ev: vad.VADEvent) -> None:
            self._plotter.plot_event("user_started_speaking")
            self.emit("user_started_speaking")
            # self._deferred_validation.on_human_start_of_speech(ev)

        async def _on_vad_updated(ev: vad.VADEvent) -> None:
            if not self._track_published_fut.done():
                return

            assert self._agent_output is not None

            # tv = 1.0
            # if self._opts.allow_interruptions:
            #     tv = max(0.0, 1.0 - ev.probability)
            #     self._agent_output.audio_source.target_volume = tv

            # smoothed_tv = self._agent_output.audio_source.smoothed_volume

            # self._plotter.plot_value("raw_vol", tv)
            # self._plotter.plot_value("smoothed_vol", smoothed_tv)
            # self._plotter.plot_value("vad_probability", ev.probability)

            if (
                ev.speech_duration >= self._opts.int_speech_duration
                and ev.probability > 0.5
                and ev.audio_id
            ):
                logger.debug(
                    f"[!!!] User speech duration: {ev.speech_duration} seconds interrupted"
                )
                try:
                    await self._interrupt_if_needed(ev.audio_id)
                except Exception as e:
                    logger.error(f"Error when interrupt: {e} {traceback.format_exc()}")

        def _on_end_of_speech(ev: vad.VADEvent) -> None:
            self._plotter.plot_event("user_stopped_speaking")
            self.emit("user_stopped_speaking")
            logger.debug("[!!!] User end speech")
            # self._deferred_validation.on_human_end_of_speech(ev)

        def _on_interim_transcript(ev: orion_asr.SpeechEvent) -> None:
            self._transcribed_interim_text = ev.alternatives[0].text
            logger.debug(f"[!!!] interim transcript: {self._transcribed_interim_text}")

        def _on_final_transcript(ev: orion_asr.SpeechEvent) -> None:
            logger.debug(f"[!!!] User final transcript: {ev.alternatives[0].text}")

            # Check wakeup result
            start_at = time.time()
            wakeup_successful, wakeup_info = run_async(
                self._check_wakeup_result(ev.audio_id, pop_result=True)
            )
            wakeup_cost_time = time.time() - start_at

            if (
                not wakeup_successful
            ):  # 如果wakeup result是False，则认为用户不是在和机器人说话，当噪声处理
                ev.alternatives[0].text = ""

            if (
                "ASR ERROR" in ev.alternatives[0].text
            ):  # Remove error text, TODO: Better way
                ev.alternatives[0].text = ""

            if not ev.alternatives[0].text:  # return empty text when encounter noise
                if not self._transcribed_text:
                    return

                # 如果被噪声打断时还没有进行plan或speech，需要启动；否则不打断已有的合成
                if self._agent_playing_speech or (  # 正在播放或正在生成plan
                    self._agent_answer_atask and not self._agent_answer_atask.done()
                ):
                    return

                logger.warning(
                    f"Noise final: {self._query_id} {self._transcribed_text} noise_id: {ev.audio_id}"
                )
                # 2025.05.13（<EMAIL>）理论上讲，不会再存在没有进行plan或speech的情况，因为打断经过了免唤醒过滤
                # 2025.07.22（<EMAIL>）更换了ASR后，又出现了这种情况，继续兜底
                logger.info(f"Synthesize after noise: {self._transcribed_text}")
                self._synthesize_answer_with_debug_info_v2(
                    user_transcript=self._transcribed_text,
                    force_play=False,
                    query_id=self._query_id,
                    elapse_info={
                        "wakeup_cost_time": self._wakeup_cost_time,
                        "vad_start_timestamp": self._vad_start_timestamp,
                        "vad_end_timestamp": self._vad_end_timestamp,
                        "asr_end_timestamp": self._asr_end_timestamp,
                        "asr_cost_time": self._asr_cost_time,
                    },
                )
                return

            # record timestamp
            self._vad_end_timestamp = wakeup_info.get("vad_end")
            self._vad_start_timestamp = wakeup_info.get("vad_start")
            self._asr_end_timestamp = time.time()
            # !!! TODO: 如何计算asr_cost_time
            self._asr_cost_time = ev.elapsed
            self._wakeup_cost_time = wakeup_cost_time

            if self._transcribed_text:
                logger.info(
                    f"transcribed_text: {self._transcribed_text} new asr text: {ev.alternatives[0].text}"
                )

                self._transcribed_text = (
                    f"{self._transcribed_text}...{ev.alternatives[0].text}"
                )
                self._query_id += "_" + ev.audio_id

                # 如果self._transcribed_text超过5个句子，则只保留最后5个句子
                sentences = self._transcribed_text.split("...")
                if len(sentences) > 5:
                    self._transcribed_text = "...".join(sentences[-5:])
                    self._query_id = "_".join(self._query_id.split("_")[-5:])

            else:
                self._transcribed_text = ev.alternatives[0].text
                self._query_id = ev.audio_id

            logger.info(
                f"User's final transcript: {self._transcribed_text} query id: {self._query_id} and synthesize"
            )

            self._synthesize_answer_with_debug_info_v2(
                user_transcript=self._transcribed_text,
                force_play=False,
                query_id=self._query_id,
                elapse_info={
                    "vad_start_timestamp": self._vad_start_timestamp,
                    "vad_end_timestamp": self._vad_end_timestamp,
                    "asr_end_timestamp": self._asr_end_timestamp,
                    "asr_cost_time": self._asr_cost_time,
                    "wakeup_cost_time": self._wakeup_cost_time,
                },
            )

        def _on_llm_images(images: List[Dict[str, str]], accumulated_text: str) -> None:
            logger.debug(
                f"Received llm image {images} accumulated_text: {accumulated_text}"
            )
            # update side_2_side_context with image
            chat_images = []
            for image in images:
                chat_images.append(
                    ChatImage(
                        image=image["file_url"],
                        inference_width=int(image["width"]),
                        inference_height=int(image["height"]),
                    )
                )

            # self.memory.commit_chat_assistant_message(images=chat_images)  #TODO: 历史消息暂不支持image
            # send images to user
            chat_answer_content = ChatAnswerContent(
                image_info=json.dumps(images), answer=accumulated_text
            )
            chat_answer_message = ChatAnswerMessage(
                device_id=self.robot.device_id,
                content=chat_answer_content,
                diagnostic_info={},
            )
            image_data = _GeneralInfo(
                info_type="data",
                message=chat_answer_message.model_dump(),
                source="",
                user_question="",
                allow_interruptions=True,
                add_to_chat_ctx=False,  # already added to chat ctx manually
                synthesis_handle=None,
            )
            logger.debug(f"Send image data: {image_data}")
            self._general_ch.send_nowait(image_data)

        async def _on_update_blackboard(plan_id, run_id):
            """
            Sync parameters directly to robot
            """
            parameters = await self.blackboard.get_running_parameters(plan_id, run_id)
            logger.debug(f"start to send parameter msg to robot {plan_id} {run_id}")
            content = RunParameterContent(
                parameters=parameters,
                plan_id=plan_id,
                run_id=run_id,
            )
            message = RunMessage(
                msg_type="run.parameters",
                content=content,
                device_id=self.robot.device_id,
            )
            logger.info(f"Blackboard update message {message.model_dump()}")
            await self._chat_manager.send_message(message.model_dump())

        self._human_input.on("start_of_speech", _on_start_of_speech)
        self._human_input.on("vad_inference_done", _on_vad_updated)
        self._human_input.on("end_of_speech", _on_end_of_speech)
        self._human_input.on("interim_transcript", _on_interim_transcript)
        self._human_input.on("final_transcript", _on_final_transcript)
        # !!!
        self._chat_manager.on(
            "message_received", self.message_handler.on_message_received
        )
        self._blackboard.on("update_blackboard", _on_update_blackboard)

        # received llm image
        self.on("llm_images", _on_llm_images)

    async def _check_wakeup_result(
        self, audio_id: str, pop_result: bool = False
    ) -> Tuple[bool, Dict]:
        """
        Check wakeup result for given audio_id.
        Returns True if wakeup is successful, False otherwise.
        """
        # logger.debug(f"Check wakeup result for audio_id: {audio_id}")
        # logger.debug(f"Wakeup result: {self._client_wakeup_result}")
        wakeup_info = self._client_wakeup_result.get(audio_id)
        if wakeup_info is None:  # key不存在，直接当噪声
            return False, {}

        wakeup_result = wakeup_info.get("result")
        if wakeup_result is None:  # 需要等待结果
            vad_start = wakeup_info.get("vad_start")
            if not vad_start:
                logger.warning(f"No vad_start found for audio_id: {audio_id}")
                return False, {}

            # VAD_START 后等待最多2秒
            while time.time() - vad_start < 2:
                wakeup_info = self._client_wakeup_result.get(audio_id, {})
                wakeup_result = wakeup_info.get("result")
                if wakeup_result is not None:
                    # clear wakeup result
                    if pop_result:
                        self._client_wakeup_result.pop(audio_id)
                    break
                await asyncio.sleep(0.1)

            if wakeup_result is None:  # 超时
                logger.warning(f"Wakeup result timeout for audio_id: {audio_id}")
                # update wakeup result to False
                wakeup_info["result"] = False
                return False, wakeup_info

        return bool(wakeup_result), wakeup_info

    async def _main_playout_task(self) -> None:
        async for general_info in self._general_ch:
            try:
                self._agent_playing_speech = general_info
                logger.info(f"Play speech: {general_info}")
                await self._play_speech(general_info)
            except Exception as e:
                logger.error(f"Error when play speech: {e} {general_info}")
            finally:
                self._agent_playing_speech = None

    async def _main_plan_task(self) -> None:
        async for plan_info in self._plan_ch:
            try:
                await self._handle_plan_info(plan_info)
            except Exception as e:
                logger.error(f"Error when handle_plan: {e} {plan_info}")

    async def _handle_plan_info(self, plan_info: _PlanInfo):
        plan_info.message["query_id"] = plan_info.query_id
        try:
            vad_end_datetime = plan_info.message.get("elapse_info", {}).get(
                "vad_end_timestamp"
            )
        except Exception as e:
            logger.error(f"Error when get vad_end_datetime: {e}")
            vad_end_datetime = None
        # convert iso to timestamp
        if vad_end_datetime:
            vad_end_timestamp = datetime.fromisoformat(vad_end_datetime).timestamp()
            plan_info.message["elapse_info"]["total_cost_time"] = (
                time.time() - vad_end_timestamp
            )
        # reset audio event timestamp
        self.init_audio_event_timestamp()

        start = time.time()
        # plan.plan 切换为其他消息时，不再有卡顿情况出现
        # plan_info.message["msg_type"] = "test"
        await self._chat_manager.send_message(plan_info.message)
        logger.info(f"Send Plan message elapsed: {time.time() - start}")
        if plan_info.message["msg_type"] in ["plan.plan", "chat.qa_answer"]:
            await self._commit_user_message_if_needed(
                plan_info.user_question,
                False,
                plan_info,
                None,
            )  # TODO: improve add user message to chat context
            # TODO(<EMAIL>): add assistant data to chat context

    async def _handle_stream_action(
        self, action_info, action_result: FunctionResult
    ) -> None:
        logger.info(
            f"[query_id: {action_info.query_id}] Stream action {action_info.node_id} {action_info}"
        )

        try:
            llm_config = RequestLLMConfig(**action_result.content.content.llm_config)
            llm_config.mode = "full" if self.robot.is_builtin_app else "turbo"
        except Exception as e:
            logger.error(
                f"Error when parse llm config: {e} {action_result.content.content.llm_config}"
            )
            llm_config = None

        llm_stream = self._opts.will_synthesize_assistant_reply(
            self,
            action_result.content.content.messages,
            llm_config=llm_config,
        )
        if asyncio.iscoroutine(llm_stream):
            llm_stream = await llm_stream

        logger.info("Generated general info to start speech")
        speech = _GeneralInfo(
            info_type="llm_stream_action_speech",
            message={
                "device_id": self.robot.device_id,
                "msg_type": "run.action_result",
                "content": {
                    "run_id": action_info.run_id,
                    "plan_id": action_info.plan_id,
                    "node_id": action_info.node_id,
                    "action_name": action_info.action_name,
                    "status": True,  # 是否执行成功
                    "message": "",
                    "request_msg_id": action_info.request_msg_id,
                    "result_type": "client_requested",  # server_sent / client_requested / server_preprocessed
                    "result_id": str(uuid.uuid4()),  # 结果id
                    "push_audio": False,  # 是否要推音频流; 如果要推，下一条是音频流
                    "result": None,
                },
                "diagnostic_info": {},
            },
            source=llm_stream,
            user_question="",
            allow_interruptions=self._opts.allow_interruptions,
            add_to_chat_ctx=True,
            synthesis_handle=None,
            query_id=action_info.query_id,
        )
        # self._deferred_validation.on_new_synthesis(user_transcript)
        self._agent_answer_speech = speech

        logger.debug(
            f"[query_id: {action_info.query_id}] Previous audio finished, sending new audio to channel"
        )
        self._general_ch.send_nowait(speech)

    def _send_image_to_client(self, images):
        if not images:
            return

        image_data = [
            {
                "file_url": image.file_url,
                "width": image.width,
                "height": image.height,
            }
            for image in images
        ]

        chat_answer_content = ChatAnswerContent(
            image_info=json.dumps(image_data),
            answer=choice(
                [
                    "您的图片已生成，快来看看，超赞的！",
                    "图片准备好了，速来围观吧！",
                    "这张图片为您量身定制，希望惊艳到你！",
                    "图片生成成功，期待您的点评！",
                    "您的图片闪亮登场，准备好惊喜了吗？",
                    "这是为您专属生成的图片，绝对哇塞！",
                    "图片完成啦，赶紧来瞧一瞧吧！",
                    "您的图片现已上线，速来欣赏！",
                    "图片新鲜出炉，希望让您眼前一亮！",
                    "精美图片已备好，赶紧来一睹为快！",
                ]
            ),
        )
        chat_answer_message = ChatAnswerMessage(
            device_id=self.robot.device_id,
            content=chat_answer_content,
            diagnostic_info={},
        )
        logger.info(f"Send image message: {chat_answer_message.model_dump()}")
        image_data = _GeneralInfo(
            info_type="data",
            message=chat_answer_message.model_dump(),
            source="",
            user_question="",
            allow_interruptions=True,
            add_to_chat_ctx=False,
            synthesis_handle=None,
        )
        logger.debug(f"Send image data: {image_data}")
        self._general_ch.send_nowait(image_data)

    @utils.log_exceptions(logger=logger)
    async def _run_weather_task(self):
        """
        Background task to update weather cache every 2 hours.
        """
        while True:
            try:
                start_time = time.time()
                geo_location = (
                    self.robot.geo_location.city
                    if agent_setting.region_version == Area.domestic
                    else f"{self.robot.geo_location.latitude},{self.robot.geo_location.longitude}"
                )
                weather = await get_weather(geo_location, language=self.robot.language)
                if weather:
                    logger.info(
                        f"Weather updated: Temperature: {weather.temperature}°C, "
                        f"Weather: {weather.weather}"
                    )
                else:
                    logger.warning("Failed to fetch weather data")

                # Sleep until next 2-hour interval
                elapsed = time.time() - start_time
                sleep_time = max(0, 7200 - elapsed)  # 7200 seconds = 2 hours
                await asyncio.sleep(sleep_time)

            except asyncio.CancelledError:
                logger.info("Weather task cancelled")
                raise
            except Exception as e:
                logger.error(f"Error in weather task: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying on error

    @utils.log_exceptions(logger=logger)
    async def _run_mcp_config_sync_task(self):
        """
        Background task to sync mcp config every 1 hour.
        """
        while True:
            try:
                start_time = time.time()
                await self._mcp_manager.update_mcp_servers(self.robot.enterprise_id)
                # Sleep until next 1-hour interval
                elapsed = time.time() - start_time
                sleep_time = max(0, 3600 - elapsed)  # 3600 seconds = 1 hour
                await asyncio.sleep(sleep_time)

            except asyncio.CancelledError:
                logger.info("MCP config sync task cancelled")
                raise
            except Exception as e:
                logger.error(f"Error in mcp config sync task: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying on error

    async def _run_audio_action_task(self) -> None:
        async for action_info in self._audio_action_ch:
            try:
                action_result = await self._action_runner.run(action_info)
                if action_result.type == "function":
                    logger.error(f"Function action not supported: {action_info}")
                    continue

                audio_output: AudioOutput = action_result.content
                # handle streaming content
                if (
                    audio_output.content.stream
                    and action_info.result_type != "server_preprocessed"
                ):  # ignore server_preprocessed action's stream
                    await self._handle_stream_action(action_info, action_result)
                    continue

                # handle non-streaming content
                if action_info.result_type != "server_preprocessed":
                    await self.say(
                        audio_output.content.text,
                        msg_type="run.action_result",
                        run_id=action_info.run_id,
                        plan_id=action_info.plan_id,
                        node_id=action_info.node_id,
                        request_msg_id=action_info.request_msg_id,
                        result_type=action_info.result_type,
                        action_name=action_info.action_name,
                        add_to_chat_ctx=action_info.add_to_chat_ctx,
                    )
            except Exception as e:
                logger.error(
                    f"Error when run action: {e} {action_info} {traceback.format_exc()}"
                )
                continue

    async def _run_one_action(self, action_info) -> None:
        query_id = action_info.query_id
        action_def = ActionLib().get_one_action(action_info.action_name)
        if not action_def:
            logger.error(f"Action {action_info.action_name} not found")
            await send_feishu_alarm(f"Action {action_info.action_name} not found")
            return

        if action_def.audio_output:
            self._audio_action_ch.send_nowait(action_info)
            return

        try:
            action_result = await self._action_runner.run(action_info)
        except Exception as e:
            logger.exception(e)
            logger.error(f"Error when run action: {e} {action_info}")
            return

        # remove debug info
        action_result.debug = {}

        # send images separately
        if action_result.content.images:
            self._send_image_to_client(action_result.content.images)

        if action_result.type == "function":
            result = action_result.content.result
            if action_result.content.audio_request:
                result["audio_result"] = (
                    action_result.content.audio_request.content.model_dump()
                )
        else:
            logger.error(
                f"Action result function type not supported: {action_result.type}"
            )
            result = {}

        action_result_content = ActionResultContent(
            plan_id=action_info.plan_id,
            run_id=action_info.run_id,
            node_id=action_info.node_id,
            request_msg_id=action_info.request_msg_id,
            result_type=action_info.result_type,
            action_name=action_info.action_name,
            status=True if action_result.status == "succeeded" else False,
            result=result,
            message="",
            result_id=str(uuid.uuid4()),
            push_audio=True if action_result.type == "audio" else False,
        )
        action_result_message = ActionResultMessage(
            device_id=self.robot.device_id,
            content=action_result_content,
            diagnostic_info={},
        )

        message = action_result_message.model_dump()
        message["query_id"] = query_id
        start = time.time()
        logger.info("Start send action result message")
        await self._chat_manager.send_message(message)
        logger.info(f"Send action result message elapsed: {time.time() - start}")

    async def _run_action_task(self) -> None:
        """
        Run action task
        :return:
        """
        async for action_info in self._action_ch:
            try:
                await self._run_one_action(action_info)
            except Exception as e:
                print(traceback.format_exc())
                logger.error(f"Error when run action: {e} {action_info}")
                continue

    @utils.log_exceptions(logger=logger)
    async def _main_task(self) -> None:
        logger.info("[!!!] Starting VoiceAssistant main task")
        if self._opts.plotting:
            self._plotter.start()

        audio_source = rtc.AudioSource(self._tts.sample_rate, self._tts.num_channels)
        track = rtc.LocalAudioTrack.create_audio_track("assistant_voice", audio_source)
        self._agent_publication = await self._room.local_participant.publish_track(
            track, rtc.TrackPublishOptions(source=rtc.TrackSource.SOURCE_MICROPHONE)
        )

        cancellable_audio_source = OrionCancellationSource(source=audio_source)
        self._agent_output = AgentOutput(
            room=self._room,
            source=cancellable_audio_source,
            llm=self._llm,
            tts=self._tts,
            robot=self._robot,
        )

        def _on_playout_started() -> None:
            self._plotter.plot_event("agent_started_speaking")
            self.emit("agent_started_speaking")

        def _on_playout_stopped(cancelled: bool) -> None:
            self._plotter.plot_event("agent_stopped_speaking")
            self.emit("agent_stopped_speaking")

        async def _on_action_playout_task_done(message: Dict) -> None:
            """
            Only for action result message
            :param message:
            :return:
            """
            logger.info(f"Audio action finished: {message}")
            await self._chat_manager.send_message(message)
            # 在发送回调后移除request_msg_id追踪
            request_msg_id = message.get("content", {}).get("request_msg_id")
            if request_msg_id:
                await self.request_msg_tracker.untrack_request(request_msg_id)

        cancellable_audio_source.on("playout_started", _on_playout_started)
        cancellable_audio_source.on("playout_stopped", _on_playout_stopped)
        cancellable_audio_source.on("playout_task_done", _on_action_playout_task_done)

        self._track_published_fut.set_result(None)

        # run async tasks in parallel
        await asyncio.gather(
            self._main_playout_task(),
            self._main_plan_task(),
            self._run_action_task(),
            self._run_audio_action_task(),
            self._run_weather_task(),
            self._run_mcp_config_sync_task(),
            self._loop_send_run_step_message(),
        )

    def empty_queue(self, q: asyncio.Queue):
        while not q.empty():
            q.get_nowait()
            q.task_done()

    async def interrupt_agent_answer(
        self, task: asyncio.Task | None = None, callback_request: bool = False
    ) -> None:
        """
        Interrupt the agent answer
        """
        logger.debug(f"[{self._query_id}] Clear all channel")

        logger.info(f"[{self._query_id}] Interrupt agent answer")
        try:
            request_msg_id = self._agent_playing_speech.message.get("content", {}).get(
                "request_msg_id", ""
            )
        except Exception as e:
            logger.warning(f"Error when get request_msg_id: {e}")
            request_msg_id = ""

        original_interrupted = None
        if self._agent_playing_speech and self._agent_playing_speech.synthesis_handle:
            logger.info(f"[{self._query_id}] {request_msg_id} Interrupt agent answer")
            original_interrupted = (
                self._agent_playing_speech.synthesis_handle.interrupted
            )
            logger.info(
                f"[{self._query_id}] {request_msg_id} Interrupt agent answer original_interrupted: {original_interrupted}"
            )
            self._agent_playing_speech.synthesis_handle.interrupt()

        # TODO(<EMAIL>): better
        # 当前逻辑：一单打断， clear all channel, 假定队列中的数据都是为了响应当前的用户的需求，一旦打断就需要清空所有 channel 的数据；包括了客户端请求的数据
        # 后续可以拆分数据，存在可打断和不可打断的 channel 中
        # 下一时刻，如果仍有client端请求的action进来无法取消，需要客户端同步取消所有请求的action
        # stop synthesis
        if task:
            if not task.done():
                logger.info(f"[{self._query_id}] Cancel agent plan task")
                try:
                    task.cancel()
                except Exception as e:
                    logger.error(f"Error when cancel agent plan task: {e}")

        logger.info(f"[{self._query_id}] Clear all channel")
        self._general_ch._queue.clear()
        self._action_ch._queue.clear()
        self._audio_action_ch._queue.clear()
        self._plan_ch._queue.clear()
        self.empty_queue(self._queue_run_step)

        # send a message to the client to stop the current action
        if original_interrupted is not None:
            if request_msg_id and not original_interrupted:
                logger.info(
                    f"[{self._query_id}] {request_msg_id} Send interrupt message to client"
                )
                if not self._message_sender:
                    logger.error(
                        f"[{self._query_id}] {request_msg_id} Message sender not initialized"
                    )
                    return
                try:
                    await self._message_sender.send_interrupt_speech_message(
                        message="用户说话打断了当前的操作",
                        request_msg_id=request_msg_id,
                    )
                except Exception as e:
                    logger.error(
                        f"[{self._query_id}] {request_msg_id} Error when send interrupt message: {e}"
                    )

                # untrack request
                await self.request_msg_tracker.untrack_request(request_msg_id)

        if callback_request:
            # get all tracking request msg ids
            request_msg_ids = await self.request_msg_tracker.get_all_tracking_keys()
            logger.info(f"[{self._query_id}] {request_msg_ids} callback request")
            for request_msg_id in request_msg_ids:
                await self._message_sender.send_interrupt_speech_message(
                    message="用户说话打断了当前的操作", request_msg_id=request_msg_id
                )
                await self.request_msg_tracker.untrack_request(request_msg_id)

        logger.info("Interrupt final")
        self._agent_answer_speech = None

    async def _interrupt_if_needed(self, audio_id: str) -> None:
        """
        Check whether the current assistant speech should be interrupted
        """
        # should interrupt
        check_wakeup_result, _ = await self._check_wakeup_result(audio_id)
        # logger.debug(f"Check wakeup result: {check_wakeup_result} {audio_id}")
        if not check_wakeup_result:
            return

        if self._opts.int_min_words != 0:
            # check the final/interim transcribed text for the minimum word count
            # to interrupt the agent speech
            interim_words = self._opts.transcription.word_tokenizer.tokenize(
                text=self._transcribed_interim_text
            )
            if len(interim_words) < self._opts.int_min_words:
                logger.info(f"Interim words: {len(interim_words)} failed to interrupt")
                return

        await self.interrupt_agent_answer(task=self._agent_answer_atask)

    # def _validate_answer_if_needed(self) -> None:
    #     """
    #     Check if the user speech should be validated/played
    #     """
    #     logger.info(f"[query_id: {self._query_id}] Validate answer if needed")
    #     if not self._agent_answer_speech:
    #         return
    #
    #     if self._agent_answer_speech.synthesis_handle is None:
    #         return
    #
    #     if (
    #         self._agent_answer_speech is not None
    #         and not self._agent_answer_speech.synthesis_handle.interrupted
    #     ):
    #         self._general_ch.send_nowait(self._agent_answer_speech)
    #         self._agent_answer_speech = None
    #     elif not self._opts.preemptive_synthesis and self._transcribed_text:
    #         logger.bind(query_id=self._query_id).info("Validate synthesis")
    #         self._synthesize_answer_with_debug_info_v2(
    #             user_transcript=self._transcribed_text,
    #             force_play=True,
    #             query_id=self._query_id,
    #             elapse_info={
    #                 "wakeup_cost_time": self._wakeup_cost_time,
    #                 "vad_start_timestamp": self._vad_start_timestamp,
    #                 "vad_end_timestamp": self._vad_end_timestamp,
    #                 "asr_end_timestamp": self._asr_end_timestamp,
    #                 "asr_cost_time": self._asr_cost_time,
    #             },
    #         )

    async def _update_mcp_servers(self):
        """
        Update MCP servers and actions
        """
        start_at = time.time()
        try:
            async with asyncio.timeout(3):
                await self._mcp_manager.update_mcp_servers(self.robot.enterprise_id)
        except asyncio.TimeoutError:
            logger.error("Update MCP servers timeout")

        try:
            # update mcp tools to action lib
            mcp_actions = load_mcp_actions(self._mcp_client)
            ActionLib().update_mcp_actions(mcp_actions)
            logger.info(
                f"Loaded {len(mcp_actions)} MCP actions: {[action.name for action in mcp_actions]}"
            )
        except Exception as e:
            logger.error(f"Error when update mcp servers: {e}")
        return time.time() - start_at

    async def _fetch_block_actions(self):
        """
        Fetch block actions from studio
        """
        start_fetch_block_actions_time = time.time()
        block_action_fullnames = []
        try:
            block_actions = await ActionLib().fetch_block_actions_from_studio(
                self.robot.enterprise_id
            )
            block_action_fullnames = [
                f"{action['action_name_space']}.{action['action_name']}".lower()
                for action in block_actions
            ]
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(
                f"Error when fetching block actions: {e} {traceback.format_exc()}"
            )

        fetch_block_actions_elapsed = time.time() - start_fetch_block_actions_time
        return block_action_fullnames, fetch_block_actions_elapsed

    def _synthesize_answer_with_debug_info_v2(
        self,
        *,
        user_transcript: str,
        force_play: bool,
        query_id: str,
        elapse_info: Dict | None = None,
        synthesize_type: SynthesizeType = SynthesizeType.USER_QUERY,
        image_info: Dict | None = None,
        event: str | None = None,
        followup_prompt: str | None = None,
    ) -> None:
        """
        Synthesize the answer to the user question and make sure
        only one answer is synthesized at a time

        Args:
            synthesize_type: Type of synthesis to perform ('user_query', 'recommend', 'action', 'event')

        v2: chat as an action
        """
        logger.bind(query_id=query_id, query=user_transcript).info(
            "Synthesize answer with debug info v2"
        )

        @utils.log_exceptions(logger=logger)
        async def _synthesize_answer_task(old_task: asyncio.Task[None]) -> None:
            if synthesize_type == SynthesizeType.USER_QUERY:  # 只有用户query需要打断
                logger.info(
                    f"Interrupt current synthesis task because of user query: {query_id}"
                )
                await self.interrupt_agent_answer(task=old_task, callback_request=True)

            # Use an async task to synthesize the agent answer to
            # allow users to execute async code inside the will_create_llm_stream callback
            assert self._agent_output is not None, (
                "agent output should be initialized when ready"
            )

            if old_task is not None:
                logger.bind(query_id=query_id).info(f"Cancel old_task: {old_task}")
                await gracefully_cancel(old_task)
                # self._robot.allow_interrupt = True  # TODO: remove this, demo

            elapse_info["answer_start_timestamp"] = time.time()

            # Run MCP server update and block action fetching in parallel
            update_mcp_task = asyncio.create_task(self._update_mcp_servers())
            fetch_block_actions_task = asyncio.create_task(self._fetch_block_actions())

            # Wait for both tasks to complete
            load_mcp_servers_cost_time, block_actions_result = await asyncio.gather(
                update_mcp_task, fetch_block_actions_task
            )

            # Get results
            elapse_info["load_mcp_servers_cost_time"] = load_mcp_servers_cost_time
            block_action_fullnames, fetch_block_actions_elapsed = block_actions_result
            elapse_info["fetch_block_actions_elapsed"] = fetch_block_actions_elapsed

            if synthesize_type == SynthesizeType.EVENT:
                if event:
                    await self.memory.commit_chat_user_message(
                        event=ChatEvent(desc=event), robot=self.robot, sid=query_id
                    )

            # 选择单个action
            agent_answer_speech = await self._single_action_planner.plan(
                query_id=query_id,
                query_text=user_transcript,
                elapse_info=elapse_info,
                synthesize_type=synthesize_type,
                image_info=image_info,
                followup_prompt=followup_prompt,
                block_action_fullnames=block_action_fullnames,
            )
            if agent_answer_speech:
                self._agent_answer_speech = agent_answer_speech
            else:
                # clear user transcript
                self._transcribed_text = ""
                self._query_id = ""

        user_transcript = user_transcript.strip()
        if synthesize_type == SynthesizeType.USER_QUERY and not user_transcript:
            return

        if synthesize_type in [SynthesizeType.ACTION, SynthesizeType.EVENT]:
            if self._agent_answer_atask and not self._agent_answer_atask.done():
                logger.bind(query_id=query_id).info(
                    f"{synthesize_type} is not allowed when agent answer task is running"
                )
                return

        if self._robot.disable_plan:  # SKIP plan
            # commit user message
            if synthesize_type == SynthesizeType.USER_QUERY:
                # BUGFIX：run_async 会创建一个完全独立的事件循环在新线程中运行协程。
                asyncio.create_task(
                    self._memory.commit_chat_user_message(
                        text=user_transcript, robot=self.robot, sid=query_id
                    )
                )
            elif synthesize_type == SynthesizeType.EVENT:
                asyncio.create_task(
                    self._memory.commit_chat_user_message(
                        event=ChatEvent(desc=event), robot=self.robot, sid=query_id
                    )
                )

            return

        old_task = self._agent_answer_atask

        self._agent_answer_atask = asyncio.create_task(
            _synthesize_answer_task(old_task)
        )

    async def _commit_user_message_if_needed(
        self,
        user_question: str,
        user_speech_commited: bool,
        general_info: _GeneralInfo | _PlanInfo | None = None,
        play_handle: PlayoutHandle | None = None,
        join_fut: asyncio.Future | None = None,
        min_time_played_for_commit: float = 1.5,
    ) -> None:
        _logger = logger.bind(query_id=general_info.query_id)

        if not user_question or user_speech_commited:
            return

        if (
            general_info
            and general_info.synthesis_handle
            and general_info.synthesis_handle.interrupted
        ):
            return

        # make sure at least some speech was played before committing the user message
        # since we try to validate as fast as possible it is possible the agent gets interrupted
        # really quickly (barely audible), we don't want to mark this question as "answered".
        if (
            play_handle
            and play_handle.time_played < min_time_played_for_commit
            and not join_fut.done()
        ):
            return

        await self._memory.commit_chat_user_message(
            text=user_question, robot=self.robot, sid=general_info.query_id
        )
        logger.debug(f"[!!!] commit user message: {user_question}")
        self.emit("user_speech_committed", user_question)

        self._transcribed_text = self._transcribed_text[len(user_question) :]
        # update query id
        text_count = len(user_question.split("..."))
        audio_ids = self._query_id.split("_")
        self._query_id = "_".join(audio_ids[text_count:])
        # reset final transcript time
        self._final_transcript_time = None

        user_speech_commited = True

        # Submit to thread pool without blocking
        # TODO(<EMAIL>): 需要使用VAD START时的face_id
        if general_info.face_id:
            future = self._thread_pool_executor.submit(
                self._memory.add_user_memory,
                user_question,
                general_info.face_id,
                self._robot.language,
            )
            future.add_done_callback(callback)

        current_session = self._memory.get_aos_session(self._robot)

        # set confirmed action
        if general_info.confirmed_action:
            if current_session:
                current_session.confirmed_action = general_info.confirmed_action
                _logger.info(f"Set confirmed action {general_info.confirmed_action}")
        else:  # Reset confirmed action when commit user message
            if current_session.confirmed_action:
                current_session.confirmed_action = None
                _logger.info("Reset confirmed_action when commit user message")

    async def _play_speech(self, general_info: _GeneralInfo) -> None:
        logger.info(f"[!!!] VoiceAssistant._play_speech started {general_info}")

        user_speech_commited = False
        user_question = general_info.user_question

        if general_info.info_type == "llm_stream_action_speech":
            # LLM stream 调用 _agent_synthesize 时， 同时调用了LLM，tts，所以需要在队列中排队等待真正的合成时机
            logger.info(f"Start to play llm stream action speech {general_info}")
            try:
                general_info.synthesis_handle = self._agent_synthesize(
                    transcript=_llm_stream_to_str_iterable(general_info.source)
                )
            except Exception as e:
                logger.error(f"Error when play llm stream action speech: {e}")
                general_info.synthesis_handle = None
                await send_feishu_alarm(
                    f"Error when play llm stream action speech: {e} {traceback.format_exc()}"
                )

        elif general_info.info_type == "speech_action":
            try:
                general_info.synthesis_handle = self._agent_synthesize(
                    transcript=general_info.source
                )
            except Exception as e:
                logger.error(f"Error when play speech action: {e}")
                general_info.synthesis_handle = None
                await send_feishu_alarm(
                    f"Error when play speech action: {e} {traceback.format_exc()}"
                )

            logger.info(f"Start to SAY {general_info.source}")

        if general_info.synthesis_handle:  # 语音
            try:
                action_audio_callback_message = general_info.message or None
                play_handle = general_info.synthesis_handle.play(
                    action_audio_callback_message
                )
            except Exception as e:
                logger.error(f"Error when play speech: {e}")
                general_info.synthesis_handle = None
                await send_feishu_alarm(
                    f"Error when play speech: {e} {traceback.format_exc()}"
                )
        else:
            play_handle = None

        # if general_info.info_type == "data":
        #     logger.info(f"send result message elapsed: {time.time()}")
        #     general_info.message["query_id"] = general_info.query_id
        #     try:
        #         vad_end_datetime = general_info.message.get("elapse_info", {}).get(
        #             "vad_end_timestamp"
        #         )
        #     except Exception as e:
        #         logger.error(f"Error when get vad_end_datetime: {e}")
        #         vad_end_datetime = None
        #     # convert iso to timestamp
        #     if vad_end_datetime:
        #         vad_end_timestamp = datetime.fromisoformat(vad_end_datetime).timestamp()
        #         general_info.message["elapse_info"]["total_cost_time"] = (
        #             time.time() - vad_end_timestamp
        #         )
        #     # reset audio event timestamp
        #     self.init_audio_event_timestamp()
        #
        #     start = time.time()
        #     logger.info("Start send message")
        #     await self._chat_manager.send_message(general_info.message)
        #     logger.info(f"Send message elapsed: {time.time() - start}")
        #     if general_info.message["msg_type"] in ["plan.plan", "chat.qa_answer"]:
        #         await self._commit_user_message_if_needed(
        #             user_question,
        #             user_speech_commited,
        #             general_info,
        #             play_handle,
        #         )  # TODO: improve add user message to chat context
        #     # TODO(<EMAIL>): add assistant data to chat context
        #     return

        # assert self._agent_playing_speech is not None
        speech_info = general_info

        assert self._agent_output is not None, (
            "agent output should be initialized when ready"
        )

        synthesis_handle = speech_info.synthesis_handle
        if synthesis_handle.interrupted:
            return

        join_fut = play_handle.join()

        # logger.debug(
        #     f"[!!!] play_handle time played: {play_handle.time_played} elapsed: {time.time() - self._vad_end_timestamp}"
        # )
        # wait for the play_handle to finish and check every 1s if the user question should be committed
        while not join_fut.done():
            await asyncio.wait(
                [join_fut], return_when=asyncio.FIRST_COMPLETED, timeout=1.0
            )

            await self._commit_user_message_if_needed(
                user_question,
                user_speech_commited,
                speech_info,
                play_handle,
                join_fut,
            )

        await self._commit_user_message_if_needed(
            user_question,
            user_speech_commited,
            speech_info,
            play_handle,
            join_fut,
        )

        if (
            speech_info.info_type in ["speech_action", "llm_stream_action_speech"]
            and play_handle.time_played > 0.0
        ):  # notify_finish
            start_at = datetime.now().isoformat()
            message = speech_info.message
            message.update({"start_at": start_at, "end_at": start_at})
            try:
                await self._chat_manager.send_message(message)

                # 在发送回调后移除request_msg_id追踪
                request_msg_id = message.get("content", {}).get("request_msg_id")
                if request_msg_id:
                    await self.request_msg_tracker.untrack_request(request_msg_id)

            except Exception as e:
                logger.error(f"Error when send message: {e}")
            # reset message
            speech_info.message = {}

        # collected_text = speech_info.synthesis_handle.collected_text  # llm collected text
        synthesized_text = (
            speech_info.synthesis_handle.synthesized_text
        )  # tts synthesized text
        interrupted = speech_info.synthesis_handle.interrupted

        if (
            speech_info.add_to_chat_ctx
            and (not user_question or user_speech_commited)
            and play_handle.time_played > 0.0  # filter out empty speech
        ):
            msg = ChatMessage(
                content=synthesized_text,
                role="assistant",
                app_id=self.robot.APP_ID,
                page_id=self.robot.PAGE_ID,
                agent_id=self.robot.agent_id,
                face_id=self.robot.face_id,
            )
            # save answer collected text
            if speech_info.query_id:
                await asyncio.create_task(
                    save_diagnostic_info(
                        self._cos_client,
                        query_id=speech_info.query_id,
                        device_id=self.robot.device_id,
                        diagnostic_info={"ROBOT SAY": synthesized_text},
                        type_="SAY",
                    )
                )

            # commit assistant msg
            logger.debug(f"Commit assistant message: {synthesized_text}")
            await self._memory.commit_chat_assistant_message(
                synthesized_text, robot=self.robot, sid=speech_info.query_id
            )

            if interrupted:
                logger.info(f"agent speech interrupted: {synthesized_text}")
                self.emit("agent_speech_interrupted", msg)
            else:
                self.emit("agent_speech_committed", msg)

    async def commit_node_to_chat_history(
        self, plan_id: str, run_id: str, node_id: str
    ):
        try:
            node_running_status = (
                await self._runtime_manager.get_one_node_running_status(run_id, node_id)
            )
        except Exception as e:
            logger.error(
                f"Error when get node running status: {e} {plan_id} {run_id} {node_id}"
            )
            return

        logger.info(f"node_running_status: {node_running_status}")
        if not node_running_status:
            logger.error(f"Node running status not found: {plan_id} {run_id} {node_id}")
            return

        try:
            action_parameters = await self.blackboard.get_running_node_parameters(
                plan_id, run_id, node_id
            )
        except Exception as e:
            logger.error(
                f"Error when get action parameters: {e} {plan_id} {run_id} {node_id}"
            )
            return

        logger.info(f"action_parameters: {action_parameters}")

        if not action_parameters:
            logger.warning(f"Action parameters not found: {plan_id} {run_id} {node_id}")
            return

        action_name = action_parameters.action_name
        action_def = ActionLib().get_one_action(
            name=action_name,
            agent_id=self.robot.agent_id,
        )
        action_display_name = node_running_status.info.get("display_name")

        chat_parameters = []
        for param in action_def.parameters:
            if param.is_hidden:  # ignore hidden parameters
                continue

            chat_parameters.append(
                ChatParameter(
                    name=param.name,
                    value=action_parameters.input_parameters.get(param.name),
                    desc=param.desc,
                )
            )

        logger.bind(run_id=run_id, plan_id=plan_id).info(
            f"action_name: {action_name} action_display_name: {action_display_name} chat_parameters: {chat_parameters}"
        )

        # result
        chat_result = []
        for result in action_def.result_schema:
            chat_result.append(
                ChatResult(
                    name=result.name,
                    desc=result.desc,
                    value=action_parameters.result_parameters.get(result.name),
                    type=result.type,
                )
            )
        logger.bind(run_id=run_id, plan_id=plan_id).info(
            f"chat action result parameters: {chat_result}"
        )

        chat_action = ChatAction(
            name=action_name,
            display_name=action_display_name,
            parameters=chat_parameters,
            result=chat_result,
        )
        # query_id = self.memory.run_query_map.get(run_id, "")
        await self.memory.commit_chat_assistant_message(
            action=chat_action, robot=self.robot
        )
        logger.bind(run_id=run_id, plan_id=plan_id).info(
            f"commit chat action: {chat_action}"
        )

    async def say(
        self,
        source: str | LLMStream | AsyncIterable[str],
        *,
        query_id: str = "",
        msg_type: str = "run.action_result",
        run_id: str = "",
        plan_id: str = "",
        node_id: str = "",
        request_msg_id: str = "",
        result_type: str = "server_sent",
        action_name: str = "",
        allow_interruptions: bool = True,
        add_to_chat_ctx: bool = True,
    ) -> None:
        """
        Make the assistant say something.
        The source can be a string, an LLMStream or an AsyncIterable[str]

        Args:
            source: the source of the speech
            allow_interruptions: whether the speech can be interrupted
            add_to_chat_ctx: whether to add the speech to the chat context
        """
        await self._track_published_fut
        assert self._agent_output is not None, (
            "agent output should be initialized when ready"
        )

        logger.info(f"Agent Say: {source}")
        speech = _GeneralInfo(
            info_type="speech_action",
            message={
                "device_id": self.robot.device_id,
                "session_id": "111",
                "msg_type": msg_type,
                "content": {
                    "run_id": run_id,
                    "plan_id": plan_id,
                    "node_id": node_id,
                    "action_name": action_name,
                    "status": True,  # 是否执行成功
                    "message": "",
                    "request_msg_id": request_msg_id,
                    "result_type": result_type,  # server_sent / client_requested / server_preprocessed
                    "result_id": str(uuid.uuid4()),  # 结果id
                    "push_audio": False,  # 是否要推音频流; 如果要推，下一条是音频流
                    "result": None,
                },
                "diagnostic_info": {},
            },
            source=source,
            user_question="",
            allow_interruptions=allow_interruptions,
            add_to_chat_ctx=add_to_chat_ctx,
            synthesis_handle=None,
            query_id=query_id,
        )
        self._agent_answer_speech = speech
        self._general_ch.send_nowait(speech)
