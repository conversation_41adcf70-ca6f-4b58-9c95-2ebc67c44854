"""
Request Message ID Tracker

用于追踪request_msg_id的状态，存储在Redis中。
主要用于追踪say和realtime_say action的执行状态。
"""

import json
import time
from typing import Dict, List, Optional
from loguru import logger

from src.settings import agent_setting, global_async_redis_client
from src.utils.feishu_alarm import send_feishu_alarm


class RequestMsgTracker:
    """Request Message ID状态追踪器"""

    def __init__(self, device_id: str):
        self.device_id = device_id

        # Redis key前缀设计
        self.key_prefix = f"{agent_setting.env}_request_msg_tracker_{device_id}"
        self.expire_time = 60 * 30  # 30分钟过期时间
        self.max_keys_limit = 20  # 最多取20个key

    async def track_request(
        self,
        request_msg_id: str,
        action_name: str,
        run_id: str,
        node_id: str,
        plan_id: str = "",
    ) -> bool:
        """
        开始追踪一个request_msg_id

        Args:
            request_msg_id: 请求消息ID
            action_name: action名称 (say 或 realtime_say)
            run_id: 运行ID
            node_id: 节点ID
            plan_id: 计划ID

        Returns:
            bool: 是否成功记录
        """
        if action_name.lower() not in [
            "say",
            "realtime_say",
            "orion.agent.action.say",
            "orion.agent.action.realtime_say",
        ]:
            logger.debug(f"Action {action_name} is not tracked")
            return False

        key = f"{self.key_prefix}:{request_msg_id}"

        tracking_data = {
            "request_msg_id": request_msg_id,
            "action_name": action_name,
            "run_id": run_id,
            "node_id": node_id,
            "plan_id": plan_id,
            "start_time": time.time(),
            "status": "tracking",
        }

        try:
            await global_async_redis_client.setex(
                key, self.expire_time, json.dumps(tracking_data, ensure_ascii=False)
            )
            logger.info(
                f"Started tracking request_msg_id: {request_msg_id} for action: {action_name}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to track request_msg_id {request_msg_id}: {e}")
            return False

    async def untrack_request(self, request_msg_id: str) -> bool:
        """
        停止追踪一个request_msg_id (在回调发送后调用)

        Args:
            request_msg_id: 请求消息ID

        Returns:
            bool: 是否成功移除
        """
        key = f"{self.key_prefix}:{request_msg_id}"

        try:
            result = await global_async_redis_client.delete(key)
            if result:
                logger.info(f"Stopped tracking request_msg_id: {request_msg_id}")
                return True
            else:
                logger.warning(f"Request_msg_id {request_msg_id} was not being tracked")
                return False
        except Exception as e:
            logger.error(f"Failed to untrack request_msg_id {request_msg_id}: {e}")
            return False

    async def get_tracking_status(self, request_msg_id: str) -> Optional[Dict]:
        """
        获取特定request_msg_id的追踪状态

        Args:
            request_msg_id: 请求消息ID

        Returns:
            Optional[Dict]: 追踪状态数据，如果不存在返回None
        """
        key = f"{self.key_prefix}:{request_msg_id}"

        try:
            data = await global_async_redis_client.get(key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"Failed to get tracking status for {request_msg_id}: {e}")
            return None

    async def get_all_tracking_keys(self) -> List[str]:
        """
        获取所有正在追踪的request_msg_id，最多返回20个

        Returns:
            List[str]: request_msg_id列表
        """
        pattern = f"{self.key_prefix}:*"

        try:
            # 使用SCAN来获取keys，避免KEYS命令的性能问题
            keys = []
            async for key in global_async_redis_client.scan_iter(
                match=pattern, count=100
            ):
                keys.append(key)
                if len(keys) >= self.max_keys_limit:
                    err_msg = (
                        f"request_msg_tracker超过20个key限制，当前数量: {len(keys)}"
                    )
                    await send_feishu_alarm(
                        f"request_msg_tracker超过20个key限制，当前数量: {len(keys)}"
                    )
                    logger.error(err_msg)
                    break

            # 提取request_msg_id
            request_msg_ids = []
            for key in keys:
                request_msg_id = key.split(":")[-1]
                request_msg_ids.append(request_msg_id)

            logger.info(
                f"Found {len(request_msg_ids)} tracking keys (limit: {self.max_keys_limit})"
            )
            return request_msg_ids

        except Exception as e:
            logger.error(f"Failed to get tracking keys: {e}")
            return []

    async def close(self):
        """关闭Redis连接"""
        try:
            await global_async_redis_client.aclose()
        except Exception as e:
            logger.error(f"Failed to close redis connection: {e}")
