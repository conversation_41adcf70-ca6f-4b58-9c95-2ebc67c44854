from livekit.agents.transcription.stt_forwarder import STTSegmentsForwarder
from livekit.agents.stt import SpeechEventType
from src.plugins.orion_asr import SpeechEvent as OrionSpeechEvent
from livekit import rtc


class OrionSTTSegmentsForwarder(STTSegmentsForwarder):
    def update(self, ev: OrionSpeechEvent):
        if ev.type == SpeechEventType.INTERIM_TRANSCRIPT:
            # TODO(theomonnom): We always take the first alternative, we should mb expose opt to the
            # user?
            text = ev.alternatives[0].text
            self._queue.put_nowait(
                rtc.TranscriptionSegment(
                    id=ev.audio_id,
                    text=text,
                    start_time=0,
                    end_time=0,
                    final=False,
                    language="",  # TODO
                )
            )
        elif ev.type == SpeechEventType.FINAL_TRANSCRIPT:
            text = ev.alternatives[0].text
            self._queue.put_nowait(
                rtc.TranscriptionSegment(
                    id=ev.audio_id,
                    text=text,
                    start_time=0,
                    end_time=0,
                    final=True,
                    language="",  # TODO
                )
            )
