from typing import Dict, Any, AsyncIterable, Optional
from dataclasses import dataclass

from livekit.agents.llm import LLMStream

from src.assistant.agent_output import SynthesisHandle
from src.agent_core.models.agent_core import RunAction


@dataclass
class _GeneralInfo:
    info_type: str
    message: Dict[str, Any]
    source: str | LLMStream | AsyncIterable[str]
    user_question: str  # empty when the speech isn't a response to a user query
    allow_interruptions: bool
    add_to_chat_ctx: bool
    synthesis_handle: SynthesisHandle
    query_id: str = ""
    face_id: str = ""
    confirmed_action: Optional[RunAction] = None


@dataclass
class _PlanInfo:
    info_type: str
    message: Dict[str, Any]
    source: str | LLMStream | AsyncIterable[str]
    user_question: str  # empty when the speech isn't a response to a user query
    allow_interruptions: bool
    add_to_chat_ctx: bool
    synthesis_handle: SynthesisHandle
    query_id: str = ""
    face_id: str = ""
    confirmed_action: Optional[RunAction] = None
