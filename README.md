# 目录结构
- src
    - agent_core: agent核心模块，plan、replan、feedback
    - controller: 调度入口
    - bt_lib: 资源库
    - utils: 工具
    - server.py 启动文件
- tests: 单元测试

# 启动
```shell
docker run -p 8001:8002 -e LIVEKIT_URL=wss://product-5cwv3qz8.livekit.cloud -e LIVEKIT_API_KEY=APITDwFiKxtne9p -e LIVEKIT_API_SECRET=qFIaeoBWSllhr0QwguHkZofumagIcsNLP0sLk4CH67E -e ELEVEN_API_KEY=sk_e2230f3f05f6128b3b7a09849c30c9fc16410b4133e066de -e DEEPGRAM_API_KEY=**************************************** -e OPENAI_API_KEY=*************************************************** --network=host -td reg.ainirobot.com/juyan/livekit:0.1.0
```

```shell
# workdir: cd easyNLP
export PYTHONPATH=.
python src/server.py dev
```

# FewShot 初始化、更新操作
> python scripts/reload_few_shot.py

# 国际化
1. 提取所有待翻译文本
```shell
pybabel extract -F babel.cfg -o messages.pot .
```

2. 初始化某个语言的翻译
```shell
pybabel init -i messages.pot -d src/locale -l zh_CN
```

执行后：
```
/src/locale
  /en_US/LC_MESSAGES/messages.po
  /zh_CN/LC_MESSAGES/messages.po
```

3. 更新 .po 文件（如果代码里新增了 _("text")）
```shell
pybabel extract -F babel.cfg -o messages.pot .
pybabel update -i messages.pot -d src/locale
```

4. 编译 .po 文件
```shell
pybabel compile -d src/locale
```

# 系统性能指标说明 (elapse_info)

## 📋 总体流程概览

EasyNLP 系统从接收语音到生成执行计划的完整流程耗时分析，总计 **4.493秒**

```
用户语音 → VAD检测 → ASR识别 → 规划准备 → 并行干预处理 → Agent推理 → 知识问答 → 规划完成
```

**总耗时计算**: VAD(0.637s) + ASR(0.321s) + 唤醒(0.0006s) + 规划(3.535s) = **4.493秒**

---

## 🔍 详细阶段分解

### 1. **语音前端处理阶段** (总计 ~0.958秒)

| 指标 | 耗时 | 说明 |
|------|------|------|
| `vad_start_timestamp` → `vad_end_timestamp` | 0.637s | 语音活动检测，判断用户是否在说话 |
| `asr_cost_time` | 0.321s | 语音识别，将语音转换为文字 |
| `wakeup_cost_time` | 0.0006s | 唤醒词检测处理时间 |

**说明**: 语音处理是串行执行，VAD完成后才能进行ASR识别

### 2. **系统准备阶段** (总计 ~0.050秒)

| 指标 | 耗时 | 说明 |
|------|------|------|
| `answer_start_timestamp` | - | 系统开始准备回答的时间点 |
| `load_mcp_servers_cost_time` | 0.049s | 加载MCP服务器配置 |
| `fetch_block_actions_elapsed` | 0.00002s | 获取被阻止的动作列表 |
| `plan_start_timestamp` | - | 正式开始规划的时间点 |

### 3. **动作加载阶段** (总计 ~0.001秒)

| 指标 | 耗时 | 说明 |
|------|------|------|
| `load_support_action_elapsed_time` | 0.0008s | 加载支持的动作列表，系统优化很好 |

### 4. **🔥 并行干预处理阶段** (总计 ~0.149秒)

**这里是系统的一个重要优化点！**

| 指标 | 耗时 | 执行方式 | 说明 |
|------|------|----------|------|
| `intervention_total_process_time` | 0.149s | **并行总时间** | 两个干预任务并行执行的实际耗时 |
| `intervention_qa_process_time` | 0.148s | 并行任务1 | QA知识库干预处理 |
| `intervention_action_process_time` | 0.105s | 并行任务2 | 动作干预处理 |

**优化效果**: 如果串行执行需要 0.148 + 0.105 = 0.253s，并行执行只需 0.149s，节省了 **0.104秒 (约41%)**

### 5. **🚀 Agent Core 推理阶段** (总计 ~3.377秒) - **系统主要瓶颈**

| 指标 | 耗时 | 占比 | 说明 |
|------|------|------|------|
| `agent_core_load_context_time` | 0.0008s | 0.02% | 加载对话上下文 |
| `agent_core_embedded_time` | 0s | 0% | 向量化处理时间 |
| `agent_core_call_summary_llm_time` | 0s | 0% | 对话摘要生成 |
| `agent_core_select_few_shot_time` | 0s | 0% | Few-shot示例选择 |
| **`agent_core_call_select_action_llm_time`** | **3.372s** | **99.9%** | **🔥 LLM推理选择动作 - 最大瓶颈** |
| `agent_core_load_user_profile_time` | 0s | 0% | 用户画像加载 |
| `agent_core_strategy_action_time` | 0s | 0% | 策略动作处理 |
| `agent_core_total_time` | 3.377s | 100% | Agent Core总处理时间 |
| `agent_core_llm_retry_count` | 0 | - | LLM调用重试次数 |

**时间线**:
- `agent_core_start_timestamp`: 2025-06-25T11:18:17.545102
- `agent_core_end_timestamp`: 2025-06-25T11:18:20.921611

### 6. **知识问答处理阶段** (与Agent Core并行)

| 指标 | 耗时 | 说明 |
|------|------|------|
| `knowledge_qa_query_rewrite_time` | 0s | 查询重写时间 |
| `knowledge_qa_knowledge_search_time` | 0.745s | 知识库搜索时间 |
| `knowledge_qa_total_time` | 2.275s | 知识问答总处理时间 |

**注意**: 这个阶段与Agent Core推理并行执行，不会额外增加总耗时

### 7. **规划完成阶段**

| 指标 | 耗时 | 说明 |
|------|------|------|
| `plan_end_timestamp` | - | 规划完成时间点 |
| `plan_total_cost_time` | 3.535s | 从规划开始到完成的总时间 |

---

## ⚡ 并行处理优化分析

### 并行优化1: 干预处理阶段
```python
# 代码中的并行处理
qa_intervention_task = asyncio.create_task(...)
action_intervention_task = asyncio.create_task(...)
results = await asyncio.gather(qa_intervention_task, action_intervention_task)
```
**效果**: 节省 0.104秒 (约41%的干预处理时间)

### 并行优化2: 知识搜索
Agent Core推理和知识问答并行执行，最大化利用等待时间

## 📊 完整数据示例解读

基于实际运行数据的完整 `elapse_info` 示例：
```json
{
    "vad_start_timestamp": "2025-06-25T11:18:16.371911",           // VAD开始检测时间
    "vad_end_timestamp": "2025-06-25T11:18:17.008990",             // VAD结束时间 (0.637s)
    "asr_end_timestamp": "2025-06-25T11:18:17.335156",             // ASR识别完成时间
    "asr_cost_time": 0.32110118865966797,                          // ASR耗时 0.321s
    "wakeup_cost_time": 0.0006146430969238281,                     // 唤醒检测 0.0006s
    "answer_start_timestamp": "2025-06-25T11:18:17.338954",        // 开始准备回答
    "load_mcp_servers_cost_time": 0.04900336265563965,             // MCP服务器加载 0.049s
    "fetch_block_actions_elapsed": 0.00002384185791015625,         // 获取阻止动作 0.00002s
    "plan_start_timestamp": "2025-06-25T11:18:17.388544",          // 规划开始时间
    "load_support_action_elapsed_time": 0.0007817745208740234,     // 加载支持动作 0.0008s
    "intervention_total_process_time": 0.14851999282836914,        // 🔥 并行干预总时间 0.149s
    "intervention_qa_process_time": 0.14798283576965332,           // ├─ QA干预 0.148s
    "intervention_action_process_time": 0.10470128059387207,       // └─ 动作干预 0.105s (并行)
    "agent_core_load_context_time": 0.0007925033569335938,         // Agent上下文加载 0.0008s
    "agent_core_embedded_time": 0,                                 // 向量化时间 0s
    "agent_core_call_summary_llm_time": 0,                         // 摘要LLM 0s
    "agent_core_select_few_shot_time": 0,                          // Few-shot选择 0s
    "agent_core_call_select_action_llm_time": 3.3721413612365723,  // 🔥 LLM推理 3.372s (瓶颈)
    "agent_core_load_user_profile_time": 0,                        // 用户画像 0s
    "agent_core_total_time": 3.376505136489868,                    // Agent Core总时间 3.377s
    "agent_core_end_timestamp": "2025-06-25T11:18:20.921611",      // Agent Core结束时间
    "agent_core_start_timestamp": "2025-06-25T11:18:17.545102",    // Agent Core开始时间
    "agent_core_llm_retry_count": 0,                               // LLM重试次数 0
    "agent_core_strategy_action_time": 0,                          // 策略动作时间 0s
    "knowledge_qa_query_rewrite_time": 0,                          // 查询重写 0ms
    "knowledge_qa_knowledge_search_time": 0.744627,                // 知识搜索 0.74462s
    "knowledge_qa_total_time_ms": 2.274643,                        // 知识问答总时间 2.274643s
    "plan_end_timestamp": "2025-06-25T11:18:20.923141",            // 规划结束时间
    "plan_total_cost_time": 3.5345969200134277                     // 🎯 总耗时 3.535s
}
```

### 📈 关键数据洞察

1. **总耗时**: 4.493秒 (VAD+ASR+唤醒+规划)，其中规划阶段占78.7%
2. **最大瓶颈**: `agent_core_call_select_action_llm_time` 3.372s，占总时间75.1%
3. **语音处理**: VAD(0.637s) + ASR(0.321s) = 0.958s，占总时间21.3%
4. **优化亮点**: 并行干预处理节省了0.104秒
5. **系统状态**: 无重试、无错误，运行稳定

### 🕐 完整时间线
```
16:371911 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 20:923141
         VAD检测(0.637s)     ASR识别(0.321s)    规划阶段(3.535s)
         ├──────────────────┤ ┌──────────────┐   ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
         17:008990           17:335156           17:388544                                                                                                                                                                                                     20:923141
```

这个数据显示系统整体运行良好，LLM推理是最大瓶颈，语音处理也占有一定比重。