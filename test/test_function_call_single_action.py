import json
import sys
import unittest

from redis import Redis

sys.path.append("/root/projects/easyNLP")
from src.action.action_version.version_manager import ActionVersionManager
from src.action.actions import Launcher_App_Id
from src.action.retrieve import MaterialRetriever
from src.agent_core.function_call.convert_actions_to_schema import (
    batch_convert_action_to_schema,
)
from src.agent_core.models.model import AgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.session_manager.memory import Memory
from src.session_manager.robot import InterfaceState, Robot
from src.settings import agent_setting

candidate_actions = ActionVersionManager().fetch_actions_by_version("draft")

testcase_redis_client = Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.test_case_redis_db,
)


class TestSingleAgent(unittest.IsolatedAsyncioTestCase):
    def tearDown(self):
        MaterialRetriever.reload_qdrant_client()

    async def test_qa_and_face_recognition(self):
        q = AgentParameter(
            query="大点声",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_qa_and_face_recognition",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )

        # result = await SingleActionAgent.a_invoke(q)
        # self.assertTrue("FACE_RECOGNITION" in result.plan.content)

        q.query = "你知道孙名言吗"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("KNOWLEDGE_QA" in result.plan.content)
        q.memory.clear_chat_history()

    async def test_speak(self):
        # 大笑10声吧让我听听。
        q = AgentParameter(
            run_step_queue=asyncio.Queue(),
            query="大笑10声吧让我听听。",
            candidate_actions=candidate_actions,
            memory=Memory(redis_client=testcase_redis_client, device_id="test_speak"),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SAY" in result.plan.content)

        q.memory.commit_chat_user_message(text="大笑10声吧让我听听。")
        await q.memory.commit_chat_assistant_message(text="哈哈哈哈哈哈哈哈哈哈哈哈哈")
        q.memory.commit_chat_user_message(text="我想包饺子")
        await q.memory.commit_chat_assistant_message(text="好啊，需要帮忙吗")
        q.memory.commit_chat_user_message(text="帮我带瓶醋")
        await q.memory.commit_chat_assistant_message(text="好的，我将帮你带瓶醋")
        q.memory.commit_chat_user_message(text="我的宠物 喂了吗?")
        await q.memory.commit_chat_assistant_message(text="您的宠物已经喂了")
        q.memory.commit_chat_user_message(text="帮我买点面粉")
        await q.memory.commit_chat_assistant_message(text="好的，我将帮你买点面粉")
        q.memory.commit_chat_user_message(text="帮我买点韭菜还有鸡蛋")
        await q.memory.commit_chat_assistant_message(
            text="好的，我将帮你买点韭菜还有鸡蛋"
        )
        q.query = "我想干啥来着？"
        await asyncio.sleep(10)

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SAY" in result.plan.content)
        self.assertTrue("饺子" in result.plan.content or "韭菜" in result.plan.content)

    async def test_json_schema(self):
        q = AgentParameter(
            query="大笑10声吧让我听听。",
            candidate_actions=candidate_actions,
            memory=Memory(redis_client=testcase_redis_client, device_id="test_speak"),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        result = await batch_convert_action_to_schema(candidate_actions, q, "zh")
        # 将结果保存到文件
        output_file = "function_schema.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"成功将 {len(result)} 个操作转换为JSON schema并保存到 {output_file}")


if __name__ == "__main__":
    import asyncio
    import sys
    import unittest

    # 获取要运行的测试方法名
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
    else:
        test_name = "test_speak"  # 默认运行的测试方法

    # 异步运行单个测试方法
    async def run_single_test():
        test = TestSingleAgent()
        try:
            # 手动调用setUp（如果有）
            if hasattr(test, "asyncSetUp"):
                await test.asyncSetUp()
            # 运行测试方法
            test_method = getattr(test, test_name)
            await test_method()
            print(f"✅ 测试 {test_name} 成功")
        except Exception as e:
            print(f"❌ 测试 {test_name} 失败: {e}")
            import traceback

            traceback.print_exc()
        finally:
            # 手动调用tearDown（如果有）
            if hasattr(test, "asyncTearDown"):
                await test.asyncTearDown()

    # 运行测试
    asyncio.run(run_single_test())
