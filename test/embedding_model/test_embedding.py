import time
import asyncio
from src.action.retrieve import _MaterialRetriever
from src.settings import agent_setting

async def test_embedding_models():
    """测试不同嵌入模型的性能"""
    test_texts_cn = [
        "这是一个测试句子，用来测试不同模型的性能",
        "人工智能正在改变我们的生活",
        "今天天气真不错，适合出去散步",
        "机器人可以帮助人类完成很多工作"
    ]
    
    test_texts_en = [
        "Yesterday is history, tomorrow is a mystery, but today is a gift. That's why it's called the present.",
        "Life is like a box of chocolates. You never know what you're gonna get.",
        "It's funny how you remember some things, but some things you can't.",
        "My mom always said, 'You've got to put the past behind you before you can move on.'"
    ]
    
    models = [
        "text-embedding-ada-002",
        "text-embedding-3-small", 
        "text-embedding-3-large",
        "bge"
    ]
    
    print("开始性能测试...\n")
    
    results = []
    for model in models:
        print(f"\n测试模型: {model}")
        retriever = _MaterialRetriever(
            version="test",
            env="test"
        )
        
        # 批量处理测试
        print("批量处理测试:")
        times = []
        dimensions = None
        error_count = 0
        total_attempts = 3
        
        for i in range(total_attempts):
            start_time = time.time()
            try:
                embeddings = await retriever._embedding(test_texts_cn + test_texts_en)
                end_time = time.time()
                elapsed = end_time - start_time
                times.append(elapsed)
                dimensions = len(embeddings[0].embedding)
                print(f"  第{i+1}次批量测试耗时: {elapsed:.3f} 秒 (平均每句 {elapsed/len(test_texts_cn + test_texts_en):.3f} 秒)")
            except Exception as e:
                error_count += 1
                print(f"  第{i+1}次批量测试失败: {str(e)}")
                continue
        
        # 单句处理测试 - 中文
        print("\n中文单句处理测试:")
        cn_single_times = []
        cn_error_count = 0
        total_cn_attempts = len(test_texts_cn) * 10
        
        for i, text in enumerate(test_texts_cn):
            text_times = []
            for j in range(10):
                start_time = time.time()
                try:
                    embedding = await retriever._embedding([text])
                    end_time = time.time()
                    elapsed = end_time - start_time
                    text_times.append(elapsed)
                except Exception as e:
                    cn_error_count += 1
                    print(f"  句子{i+1}第{j+1}次测试失败: {str(e)}")
                    continue
            if text_times:
                avg_time = sum(text_times) / len(text_times)
                cn_single_times.append(avg_time)
                print(f"  句子{i+1}平均耗时: {avg_time:.3f} 秒 (成功率: {len(text_times)}/10)")
                
        # 单句处理测试 - 英文
        print("\n英文单句处理测试:")
        en_single_times = []
        en_error_count = 0
        total_en_attempts = len(test_texts_en) * 10
        
        for i, text in enumerate(test_texts_en):
            text_times = []
            for j in range(10):
                start_time = time.time()
                try:
                    embedding = await retriever._embedding([text])
                    end_time = time.time()
                    elapsed = end_time - start_time
                    text_times.append(elapsed)
                except Exception as e:
                    en_error_count += 1
                    print(f"  句子{i+1}第{j+1}次测试失败: {str(e)}")
                    continue
            if text_times:
                avg_time = sum(text_times) / len(text_times)
                en_single_times.append(avg_time)
                print(f"  句子{i+1}平均耗时: {avg_time:.3f} 秒 (成功率: {len(text_times)}/10)")
        
        if times and cn_single_times and en_single_times:
            batch_avg = sum(times) / len(times)
            cn_avg = sum(cn_single_times) / len(cn_single_times)
            en_avg = sum(en_single_times) / len(en_single_times)
            results.append({
                "model": model,
                "batch_avg_time": batch_avg,
                "batch_per_text_time": batch_avg / len(test_texts_cn + test_texts_en),
                "cn_single_avg_time": cn_avg,
                "en_single_avg_time": en_avg,
                "dimensions": dimensions,
                "batch_success_rate": f"{len(times)}/{total_attempts}",
                "cn_success_rate": f"{len(cn_single_times) * 10 - cn_error_count}/{total_cn_attempts}",
                "en_success_rate": f"{len(en_single_times) * 10 - en_error_count}/{total_en_attempts}"
            })
    
    # 打印汇总结果
    print("\n测试结果汇总:")
    print("------------------------")
    for result in sorted(results, key=lambda x: x["batch_avg_time"]):
        print(f"模型: {result['model']}")
        print(f"批量处理平均耗时: {result['batch_avg_time']:.3f} 秒")
        print(f"批量处理每句平均耗时: {result['batch_per_text_time']:.3f} 秒")
        print(f"中文单句平均耗时: {result['cn_single_avg_time']:.3f} 秒")
        print(f"英文单句平均耗时: {result['en_single_avg_time']:.3f} 秒")
        print(f"向量维度: {result['dimensions']}")
        print(f"批量处理成功率: {result['batch_success_rate']}")
        print(f"中文处理成功率: {result['cn_success_rate']}")
        print(f"英文处理成功率: {result['en_success_rate']}")
        print("------------------------")

if __name__ == "__main__":
    asyncio.run(test_embedding_models())
