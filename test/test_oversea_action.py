import os

from src.agent_core.single_action import SingleActionAgent
from src.common.constant import Area
from src.session_manager.robot import Robot
from src.settings import agent_setting

os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key

import unittest

from redis import Redis

from src.action.action_version.version_manager import ActionVersionManager
from src.action.retrieve import MaterialRetriever
from src.agent_core.models.model import AgentParameter
from src.session_manager.memory import Memory

candidate_actions = ActionVersionManager().fetch_actions_by_version("oversea_draft")

testcase_redis_client = Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.test_case_redis_db,
)


class TestOverSeaSingleAgent(unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        agent_setting.region_version = Area.overseas

    def tearDown(self):
        MaterialRetriever.reload_qdrant_client()

    async def test_knowledge_qa(self):
        q = AgentParameter(
            query="Who does your company test?",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="TestOverSeaSingleAgent_test_knowledge_qa",
            ),
            robot=Robot(
                language="en_US",
            ),
        )
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "What products do you have?"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)


if __name__ == "__main__":
    unittest.main()
