import unittest

from redis import Redis

from src.action.server_function import query_weather
from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.settings import agent_setting
from src.utils.llm import LLMManager

testcase_redis_client = Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.test_case_redis_db,
)


class TestServerFunction(unittest.IsolatedAsyncioTestCase):
    async def test_query_weather(self):
        robot = Robot(language="zh")
        memory = Memory(
            redis_client=testcase_redis_client,
            device_id="TestServerFunction_test_query_weather",
        )
        memory.commit_chat_user_message(text="大笑10声吧让我听听。")
        await memory.commit_chat_assistant_message(
            text="哈哈哈哈哈哈哈哈哈哈哈哈哈", need_summary=False
        )
        memory.commit_chat_user_message(text="你真无聊")

        # 1.当前天气
        result = await query_weather(
            "杭州",
            "city",
            __robot=robot,
            __memory=memory,
            _USER_QUERY="杭州的天气怎么样？",
        )

        result = await LLMManager.invoke_generate_text_model(
            messages=result.content.audio_request.content.messages,
        )

        self.assertIn("杭州", result.content)
        print(result.content)

        # 2.未来天气
        result = await query_weather(
            "杭州",
            "city",
            __robot=robot,
            __memory=memory,
            _USER_QUERY="杭州的后天天气怎么样？",
        )

        result = await LLMManager.invoke_generate_text_model(
            messages=result.content.audio_request.content.messages,
        )

        self.assertIn("杭州", result.content)
        print(result.content)

        # 3.测试外语
        robot.language = "en_US"
        result = await query_weather(
            "杭州",
            "city",
            __robot=robot,
            __memory=memory,
            _USER_QUERY="weather in hangzhou",
        )

        result = await LLMManager.invoke_generate_text_model(
            messages=result.content.audio_request.content.messages,
        )

        self.assertIn("hangzhou", result.content.lower())
        print(result.content)


if __name__ == "__main__":
    unittest.main()
