import asyncio
import os
import unittest

from redis import Redis

from src.action.action_version.version_manager import ActionVersionManager
from src.action.actions import ActionLib
from src.agent_core.models.model import AgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.common.agent_config import LAUNCHER_AGENT_ID
from src.session_manager.memory import Memory
from src.session_manager.robot import Robot
from src.settings import agent_setting

os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key
testcase_redis_client = Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.test_case_redis_db,
)


class TestSingleAgent(unittest.IsolatedAsyncioTestCase):
    async def build_agent_parameter(self, q: str = "") -> AgentParameter:
        p = AgentParameter(
            query=q,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_weather",
            ),
            robot=Robot(
                agent_id=LAUNCHER_AGENT_ID,
            ),
            run_step_queue=asyncio.Queue(),
        )

        p.robot.geo_location.city = "北京"
        candidate_actions = ActionVersionManager().fetch_actions_by_version("draft")
        ActionLib().update_builtin_actions(candidate_actions)
        p.candidate_actions = candidate_actions
        p.candidate_actions = await ActionLib().load_support_action(
            p, is_admin_mode=False
        )

        return p

    async def test_weather(self):
        p = await self.build_agent_parameter(q="奥森公园天气怎么样？")

        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("weather", result.plan.content)
        self.assertIn('city="北京"', result.plan.content)

    async def test_register_action(self):
        p = await self.build_agent_parameter(q="我是新来的员工，要注册下信息")
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("register", result.plan.content)
        self.assertIn('nick_name=""', result.plan.content)

        p.query = "这是我们的新客户，帮他把个人信息录入系统中"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("register", result.plan.content)
        self.assertIn('nick_name=""', result.plan.content)
        self.assertIn('welcome_message=""', result.plan.content)

        p.query = "你好，我叫彭于晏"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("register", result.plan.content)
        self.assertIn('nick_name="彭于晏"', result.plan.content)
        self.assertIn('welcome_message=""', result.plan.content)

        p.query = "这是新来的客户，他叫大壮"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("register", result.plan.content)
        self.assertIn('nick_name="大壮"', result.plan.content)
        self.assertIn('welcome_message=""', result.plan.content)

    async def test_generate_message(self):
        p = await self.build_agent_parameter(
            q="我们测试组今天来了一位新同事，你代表公司欢迎欢迎他"
        )
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("generate_message", result.plan.content)

        p.query = "张总是我们业务的合作伙伴今天来我们公司参观，现在参观结束要返回了，你代表公司欢送一下他"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("generate_message", result.plan.content)

        p.query = "李总今天来我们公司参观，你代表公司欢迎一下他"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("generate_message", result.plan.content)

        p.query = "傅老板是猎户星空的boss，快过年了，老板从美国出差回来，欢迎他归来"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("generate_message", result.plan.content)

        p.query = "马上就到元宵节了，有一个重要的客户来公司访问，代表老板欢迎下"
        result = await SingleActionAgent.a_invoke(p)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("generate_message", result.plan.content)


if __name__ == "__main__":
    unittest.main()
