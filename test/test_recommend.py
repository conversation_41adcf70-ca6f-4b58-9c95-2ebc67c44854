import unittest
from unittest.mock import MagicMock, patch

from src.assistant.planner.recommend import handle_static_follow_up_action


class TestHandleStaticFollowUpAction(unittest.IsolatedAsyncioTestCase):
    async def test_handle_static_follow_up_action_greeting(self):
        # Mock memory
        mock_memory = MagicMock()
        mock_memory.get_chat_context.return_value.messages = [
            MagicMock(role="user", content="小王小王。/[小豹小豹]"),
            MagicMock(role="assistant", content="我在呢！有什么需要帮忙的吗？"),
        ]

        # Set up robot state
        mock_robot = MagicMock()
        mock_robot.robot_status_info.return_value = {
            "人物背景": "我是小豹，来自猎户星空，一家专注于AI技术研发机器人的科技企业。我是20岁的男机器人。",
            "当前音量": 30,
            "当前时间": "2024年11月06日 11点10分34秒",
            "当前地理位置": "北京市北京市朝阳区三间房(地区)乡在万东科技文化创意产业园附近",
            "当前室内点位": "未知",
            "上一次室内点位": "未知",
            "当前速度": 1.0,
            "目标最大平稳速度": 1.0,
            "电池电量": 99,
            "系统语言": "中文",
            "屏幕信息": "\n",
        }

        # Call the actual function
        result = await handle_static_follow_up_action(mock_memory, mock_robot)

        # Assert that the result is as expected
        # Note: You need to know the expected result from the actual function
        self.assertIsNotNone(result)

    async def test_handle_static_follow_up_action_weather_inquiry(self):
        # Mock memory
        mock_memory = MagicMock()
        mock_memory.get_chat_context.return_value.messages = [
            MagicMock(role="user", content="今天天气怎么样？"),
            MagicMock(role="assistant", content="今天天气晴朗，气温0度到10度。"),
        ]

        # Set up robot state
        mock_robot = MagicMock()
        mock_robot.robot_status_info.return_value = {
            "系统语言": "中文",
            # ... other state info ...
        }

        # Call the actual function
        result = await handle_static_follow_up_action(mock_memory, mock_robot)

        # Assert that the result is as expected
        self.assertIsNotNone(result)

    async def test_handle_static_follow_up_action_time_inquiry(self):
        # Mock memory
        mock_memory = MagicMock()
        mock_memory.get_chat_context.return_value.messages = [
            MagicMock(role="user", content="现在几点了？"),
            MagicMock(role="assistant", content="现在是11点10分。"),
        ]

        # Set up robot state
        mock_robot = MagicMock()
        mock_robot.robot_status_info.return_value = {
            "当前时间": "2024年11月06日 11点10分34秒",
            # ... other state info ...
        }

        # Call the actual function
        result = await handle_static_follow_up_action(mock_memory, mock_robot)

        # Assert that the result is as expected
        self.assertIsNotNone(result)

    async def test_handle_static_follow_up_action_battery_status(self):
        # Mock memory
        mock_memory = MagicMock()
        mock_memory.get_chat_context.return_value.messages = [
            MagicMock(role="user", content="电池电量还剩多少？"),
            MagicMock(role="assistant", content="电池电量还剩99%。"),
        ]

        # Set up robot state
        mock_robot = MagicMock()
        mock_robot.robot_status_info.return_value = {
            "电池电量": 99,
            # ... other state info ...
        }

        # Call the actual function
        result = await handle_static_follow_up_action(mock_memory, mock_robot)

        # Assert that the result is as expected
        self.assertIsNotNone(result)

    async def test_handle_static_follow_up_action_no_interaction(self):
        # Mock memory
        mock_memory = MagicMock()
        mock_memory.get_chat_context.return_value.messages = [
            MagicMock(role="assistant", content="你好，有什么我可以帮助你的么"),
        ]

        # Set up robot state
        mock_robot = MagicMock()
        mock_robot.robot_status_info.return_value = {
            "系统语言": "中文",
            # ... other state info ...
        }

        # Mock the _get_gpt4o_result to return "暂无互动"
        with patch(
            "src.assistant.planner.recommend._get_gpt4o_result", return_value="暂无互动"
        ):
            result = await handle_static_follow_up_action(mock_memory, mock_robot)

        # Assert that the result is None, as the function should return early
        self.assertIsNone(result)

    async def test_handle_static_follow_up_action_recognition(self):
        # Mock memory
        mock_memory = MagicMock()
        mock_memory.get_chat_context.return_value.messages = [
            MagicMock(role="user", content="你认识我吗？"),
            MagicMock(role="assistant", content="我认识你，你是小a。"),
        ]

        # Set up robot state
        mock_robot = MagicMock()
        mock_robot.robot_status_info.return_value = {
            "系统语言": "中文",
            # ... other state info ...
        }

        # Call the actual function
        result = await handle_static_follow_up_action(mock_memory, mock_robot)

        # Assert that the result is as expected
        self.assertIsNone(result)


if __name__ == "__main__":
    unittest.main()
