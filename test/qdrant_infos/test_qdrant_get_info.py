from qdrant_client import QdrantClient
from qdrant_client.http import models
import json
from datetime import datetime
import os

qdrant_host = "*************"
qdrant_port = 6333

class TestQdrantGetInfo:
    def client(self):
        # 创建 Qdrant 客户端连接
        return QdrantClient(host=qdrant_host, port=qdrant_port)
    
    def test_get_collection_info(self, client):
        # 测试获取集合信息
        collection_name = "test_collection"
        
        # 首先创建一个测试集合
        client.recreate_collection(
            collection_name=collection_name,
            vectors_config=models.VectorParams(size=128, distance=models.Distance.COSINE)
        )
        
        # 获取集合信息
        collection_info = client.get_collection(collection_name=collection_name)
        
        # 验证集合信息
        assert collection_info is not None
        assert collection_info.status == "green"
        assert collection_info.vectors_count == 0
        
    def test_list_collections(self, client):
        # 测试列出所有集合
        collections = client.get_collections()
        
        # 验证返回的是列表
        assert isinstance(collections.collections, list)
        
    def test_get_collection_stats(self, client):
        # 测试获取集合统计信息
        collection_name = "test_stats_collection"
        
        # 创建测试集合
        client.recreate_collection(
            collection_name=collection_name,
            vectors_config=models.VectorParams(size=128, distance=models.Distance.COSINE)
        )
        
        # 获取集合统计信息
        stats = client.get_collection(collection_name=collection_name)
        
        # 验证统计信息
        assert stats.vectors_count == 0
        assert stats.config.params.vectors.size == 128
        
    def test_get_collection_detailed_info(self, client):
        # 测试获取集合的详细配置信息
        collection_name = "test_detailed_collection"
        
        # 创建一个带有特定配置的测试集合
        client.recreate_collection(
            collection_name=collection_name,
            vectors_config=models.VectorParams(
                size=256,  # 向量维度
                distance=models.Distance.EUCLID  # 使用欧氏距离
            ),
            optimizers_config=models.OptimizersConfigDiff(
                indexing_threshold=20000  # 设置索引阈值
            ),
            on_disk_payload=True  # 启用磁盘存储
        )
        
        # 获取集合详细信息
        collection_info = client.get_collection(collection_name=collection_name)
        
        # 验证集合配置
        assert collection_info.config.params.vectors.size == 256
        assert collection_info.config.params.vectors.distance == models.Distance.EUCLID
        assert collection_info.config.optimizers_config.indexing_threshold == 20000
        assert collection_info.config.on_disk_payload == True
        
        # 验证集合状态
        assert collection_info.status == "green"
        assert collection_info.vectors_count == 0

    def teardown_method(self, method):
        # 测试后清理
        client = QdrantClient(host="localhost", port=6333)
        collections = client.get_collections()
        for collection in collections.collections:
            client.delete_collection(collection_name=collection.name)

def save_to_file(data, filename):
    """保存数据到文件"""
    os.makedirs('test/qdrant_infos', exist_ok=True)
    with open(f'test/qdrant_infos/{filename}', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def main():
    try:
        # 创建测试类实例
        test_instance = TestQdrantGetInfo()
        
        # 创建客户端
        client = test_instance.client()
        
        print("开始查询 Qdrant 集合信息...")
        
        # 准备保存的数据
        all_collections_data = {}
        
        # 获取所有集合并按名称排序
        collections = client.get_collections()
        collections.collections.sort(key=lambda x: x.name)
        
        # 准备汇总信息
        summary_info = []
        
        for collection in collections.collections:
            collection_name = collection.name
            print(f"\n正在处理集合: {collection_name}")
            
            try:
                # 获取集合详细信息
                info = client.get_collection(collection_name=collection_name)
                
                # 获取点数信息
                points_count = info.points_count if hasattr(info, 'points_count') else info.vectors_count
                
                # 收集汇总信息，处理可能的 None 值
                vector_size = info.config.params.vectors.size if info and hasattr(info, 'config') else 'N/A'
                distance = str(info.config.params.vectors.distance) if info and hasattr(info, 'config') else 'N/A'
                
                summary_info.append({
                    "集合名称": collection_name,
                    "点数量": points_count,
                    "向量维度": vector_size,
                    "距离计算方式": distance
                })
                
                # 准备集合数据
                collection_data = {
                    "基本信息": {
                        "状态": info.status,
                        "向量数量": info.vectors_count,
                        "向量维度": info.config.params.vectors.size,
                        "距离计算方式": str(info.config.params.vectors.distance),
                        "索引配置": {
                            "索引阈值": info.optimizer_config.indexing_threshold if hasattr(info, 'optimizer_config') and hasattr(info.optimizer_config, 'indexing_threshold') else None,
                            "是否启用磁盘存储": info.config.on_disk_payload if hasattr(info.config, 'on_disk_payload') else None
                        }
                    },
                    "点数据统计": {
                        "总点数": info.vectors_count,
                        "已索引点数": info.indexed_vectors_count if hasattr(info, 'indexed_vectors_count') else None,
                        "点数据大小": info.points_count if hasattr(info, 'points_count') else None,
                        "分段数量": info.segments_count if hasattr(info, 'segments_count') else None
                    },
                    "示例数据": [],
                    "字段信息": {},
                    "存储信息": {
                        "向量存储大小": info.storage_size if hasattr(info, 'storage_size') else None,
                        "Payload存储大小": info.payload_storage_size if hasattr(info, 'payload_storage_size') else None,
                    }
                }
                
                # 获取示例数据
                points = client.scroll(
                    collection_name=collection_name,
                    limit=5
                )[0]
                
                for point in points:
                    point_data = {
                        "点ID": str(point.id),
                        "Payload数据": point.payload if point.payload else {},
                        "向量维度": len(point.vector) if point.vector is not None else None
                    }
                    collection_data["示例数据"].append(point_data)
                
                # 获取字段信息
                if hasattr(info, 'payload_schema'):
                    collection_data["字段信息"] = {
                        field_name: {
                            "类型": str(field_schema),
                            "索引类型": str(field_schema.params) if hasattr(field_schema, 'params') else None
                        }
                        for field_name, field_schema in info.payload_schema.items()
                    }
                
                all_collections_data[collection_name] = collection_data
            
            except Exception as e:
                print(f"处理集合 {collection_name} 时出错: {str(e)}")
                summary_info.append({
                    "集合名称": collection_name,
                    "点数量": 'Error',
                    "向量维度": 'Error',
                    "距离计算方式": 'Error'
                })
        
        # 分离空集合和非空集合
        non_empty_collections = [info for info in summary_info 
                               if isinstance(info['点数量'], (int, float)) and info['点数量'] > 0]
        empty_collections = [info for info in summary_info 
                           if isinstance(info['点数量'], (int, float)) and info['点数量'] == 0]
        error_collections = [info for info in summary_info 
                           if not isinstance(info['点数量'], (int, float))]
        
        # 按集合名称排序
        non_empty_collections.sort(key=lambda x: x['集合名称'])
        empty_collections.sort(key=lambda x: x['集合名称'])
        error_collections.sort(key=lambda x: x['集合名称'])
        
        print("\n=============== 集合汇总信息 ===============")
        print(f"总集合数量: {len(summary_info)}")
        print(f"非空集合数量: {len(non_empty_collections)}")
        print(f"空集合数量: {len(empty_collections)}")
        if error_collections:
            print(f"错误集合数量: {len(error_collections)}")
        
        # 打印非空集合信息
        print("\n=============== 非空集合信息 ===============")
        if non_empty_collections:
            print(f"\n包含数据的集合:")
            print("-" * 100)
            print(f"{'集合名称':<50} {'点数量':<15} {'向量维度':<15} {'距离计算方式':<15}")
            print("-" * 100)
            for info in non_empty_collections:
                print(f"{str(info['集合名称']):<50} {str(info['点数量']):<15} {str(info['向量维度']):<15} {str(info['距离计算方式']):<15}")
            print("-" * 100)
            print(f"非空集合总点数: {sum(info['点数量'] for info in non_empty_collections):,}")
        else:
            print("没有找到包含数据的集合")
        
        # 打印空集合信息
        print("\n=============== 空集合信息 ===============")
        if empty_collections:
            print(f"\n空集合列表:")
            print("-" * 100)
            print(f"{'集合名称':<50} {'点数量':<15} {'向量维度':<15} {'距离计算方式':<15}")
            print("-" * 100)
            for info in empty_collections:
                print(f"{str(info['集合名称']):<50} {str(info['点数量']):<15} {str(info['向量维度']):<15} {str(info['距离计算方式']):<15}")
            print("-" * 100)
        else:
            print("没有空集合")
        
        # 如果有错误集合，打印错误集合信息
        if error_collections:
            print("\n=============== 错误集合信息 ===============")
            print(f"\n获取信息出错的集合:")
            print("-" * 100)
            print(f"{'集合名称':<50} {'点数量':<15} {'向量维度':<15} {'距离计算方式':<15}")
            print("-" * 100)
            for info in error_collections:
                print(f"{str(info['集合名称']):<50} {str(info['点数量']):<15} {str(info['向量维度']):<15} {str(info['距离计算方式']):<15}")
            print("-" * 100)
        
        print("\n=========================================")
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存总体信息
        save_to_file(all_collections_data, f'collections_info_{timestamp}.json')
        
        print(f"\n详细信息已保存到 qdrant_info/collections_info_{timestamp}.json")
        print(f"文件保存路径: {os.path.abspath(f'qdrant_info/collections_info_{timestamp}.json')}")
        
    except Exception as e:
        print(f"\n查询过程中发生错误: {str(e)}")

def export_specific_collection():
    """导出指定集合的数据"""
    try:
        # 创建客户端连接
        client = QdrantClient(host=qdrant_host, port=qdrant_port)
        collection_name = "few_shot_embeddings_draft_bge_1024_dev_promote_dev"
        print(f"\n开始导出集合: {collection_name}")
        
        # 获取集合信息
        info = client.get_collection(collection_name=collection_name)
        
        # 准备集合数据
        collection_data = {
            "基本信息": {
                "状态": info.status,
                "向量数量": info.vectors_count,
                "向量维度": info.config.params.vectors.size,
                "距离计算方式": str(info.config.params.vectors.distance)
            },
            "示例数据": []
        }
        
        # 获取所有数据点
        points = client.scroll(
            collection_name=collection_name,
            limit=info.vectors_count  # 获取所有数据点
        )[0]
        
        for point in points:
            point_data = {
                "点ID": str(point.id),
                "Payload数据": point.payload if point.payload else {},
                "向量维度": len(point.vector) if point.vector is not None else None
            }
            collection_data["示例数据"].append(point_data)
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存数据
        save_to_file(collection_data, f'{collection_name}_{timestamp}.json')
        
        print(f"\n数据已保存到 qdrant_info/{collection_name}_{timestamp}.json")
        print(f"总共导出 {len(collection_data['示例数据'])} 条数据")
        
    except Exception as e:
        print(f"导出过程中发生错误: {str(e)}")

if __name__ == "__main__":
    # main()  
    export_specific_collection()  # 使用新函数
