import logging
import unittest
from unittest.mock import MagicMock, patch

import redis

from src.session_manager.running_status import ActionR<PERSON><PERSON><PERSON><PERSON>, ActionStatus
from src.settings import agent_setting

# Configure basic logging to avoid errors during testing
logging.basicConfig(level=logging.CRITICAL)


class TestActionResultCache(unittest.TestCase):

    def setUp(self):
        self.redis_client_mock = MagicMock(spec=redis.Redis)
        self.action_result_cache = ActionResultCache(self.redis_client_mock)
        self.action_running_id = "test_action_id"
        self.action_status_data = {"status": "succeeded", "message": "Action completed"}
        self.action_status = ActionStatus(**self.action_status_data)

    def test_init(self):
        self.assertEqual(self.action_result_cache.redis_client, self.redis_client_mock)
        self.assertEqual(
            self.action_result_cache.key_prefix, f"{agent_setting.env}_action_result"
        )
        self.assertEqual(self.action_result_cache.expire_duration, 600)

    def test_device_id_setter_getter(self):
        device_id = "test_device"
        self.action_result_cache.device_id = device_id
        self.assertEqual(self.action_result_cache.device_id, device_id)

    @patch("src.session_manager.running_status.logger.error")
    def test_get_action_status_fail_decode(self, mock_logger_error):
        self.redis_client_mock.get.return_value = b"invalid_json"
        result = self.action_result_cache.get_action_status(self.action_running_id)
        self.assertIsNone(result)
        mock_logger_error.assert_called_once()

    # @patch("src.session_manager.running_status.logger.info")
    # def test_get_action_status_success(self, mock_logger_info):
    #     # Ensure the mock is configured just before the method that triggers the get
    #     expected_json = json.dumps(self.action_status_data).encode()
    #     self.redis_client_mock.get.return_value = expected_json
    #
    #     # Call set_action_status first to ensure data is set in the mock
    #     self.action_result_cache.set_action_status(
    #         self.action_running_id, self.action_status
    #     )
    #
    #     # Now call get_action_status and assert the results
    #     result = self.action_result_cache.get_action_status(self.action_running_id)
    #     self.assertIsNotNone(result)  # Ensure result is not None
    #     self.assertIsInstance(result, ActionStatus)
    #     self.assertEqual(result, self.action_status)
    #     mock_logger_info.assert_called_once_with(
    #         f"[action_running_id: {self.action_running_id}] Get action status: {self.action_status}"
    #     )

    @patch("src.session_manager.running_status.logger.error")
    def test_set_action_status_fail(self, mock_logger_error):
        self.redis_client_mock.setex.return_value = False
        self.action_result_cache.set_action_status(
            self.action_running_id, self.action_status
        )
        mock_logger_error.assert_called_once()


if __name__ == "__main__":
    unittest.main()
