import asyncio
import os
import sys

# 添加项目根目录到Python模块搜索路径
current_file_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_file_path))
sys.path.insert(0, project_root)


from src.common.agent_config import (
    Launcher_App_Id,
    Opk_Guide_App_Id,
    Opk_Receive_App_Id,
)
from src.settings import agent_setting

os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key

import unittest

from redis import Redis

from src.action.action_version.version_manager import ActionVersionManager
from src.action.retrieve import MaterialRetriever
from src.action.server_function import generate_message
from src.agent_core.models.model import AgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.session_manager.chat_context import (
    ChatAction,
    ChatContext,
    ChatEvent,
    ChatMessage,
    ChatParameter,
)
from src.session_manager.memory import Memory
from src.session_manager.robot import InterfaceState, Robot

candidate_actions = ActionVersionManager().fetch_actions_by_version("draft")

testcase_redis_client = Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.test_case_redis_db,
)


class TestSingleAgent(unittest.IsolatedAsyncioTestCase):
    def tearDown(self):
        MaterialRetriever.reload_qdrant_client()

    async def test_qa_and_face_recognition(self):
        q = AgentParameter(
            query="大点声",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_qa_and_face_recognition",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
            run_step_queue=asyncio.Queue(),
        )

        result = await SingleActionAgent.a_invoke(q)
        # self.assertTrue("FACE_RECOGNITION" in result.plan.content)

        q.query = "你知道孙名言吗"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("KNOWLEDGE_QA" in result.plan.content)

        q.query = "张小风是谁？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("KNOWLEDGE_QA" in result.plan.content)

        q.query = "看看我是谁？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("FACE_RECOGNITION" in result.plan.content)

    async def test_recommend(self):
        q = AgentParameter(
            query="查一下附近的英语辅导机构",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_recommend"
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        result = await SingleActionAgent.a_invoke(q)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("OPEN_WEB_URL", result.plan.content)
        q.memory.clear_chat_history()

        q.query = "附近有什么推荐的美食"
        result = await SingleActionAgent.a_invoke(q)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("OPEN_WEB_URL", result.plan.content)
        self.assertIn("美食", result.plan.content)
        q.memory.clear_chat_history()

        q.query = "附近有什么推荐的景点"
        result = await SingleActionAgent.a_invoke(q)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("OPEN_WEB_URL", result.plan.content)
        self.assertIn("景点", result.plan.content)
        q.memory.clear_chat_history()

        q.query = "附近有什么推荐的约会地点"
        result = await SingleActionAgent.a_invoke(q)
        self.assertEqual(result.return_type, "plan")
        self.assertIn("OPEN_WEB_URL", result.plan.content)

        q.query = "来到新地方总想尝尝地道的美食有什么推荐"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("RECOMMEND", result.plan.content)

        q.query = "我想去吃火锅，四惠附近有什么推荐吗"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("RECOMMEND", result.plan.content)
        self.assertIn("四惠", result.plan.content)

        q.query = "西湖附近有推荐的约会的地方吗"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("RECOMMEND", result.plan.content)
        self.assertIn("西湖", result.plan.content)

        q.query = """<Robot> said '您好，很开心为您服务'
<Robot> said '四五六,你好，你又来了'
<Robot> said '你这身打扮很适合夜晚，是不是还有重要活动？'
<User> said '夜晚都适合什么打的'
<Robot> said '夜晚适合穿一些闪亮或者深色的衣服哦，比如小黑裙、亮片装饰的上衣，再搭配一双漂亮的高跟鞋，既优雅又时尚。'
<Robot> said '您好，很开心为您服务'
<Robot> said '你好，欢迎你哦'
<User> JUST SAID '你帮我看一下这附近有没有什么好吃的'"""

        q.robot.geo_location.describe = "猎豹移动大厦"
        q.robot.indoor_location_history = ["测试部"]
        result = await SingleActionAgent.a_invoke(q)

        q.memory.clear_chat_history()

    async def test_speak(self):
        # 大笑10声吧让我听听。
        q = AgentParameter(
            query="大笑10声吧让我听听。",
            candidate_actions=candidate_actions,
            memory=Memory(redis_client=testcase_redis_client, device_id="test_speak"),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SAY" in result.plan.content)

        q.memory.commit_chat_user_message(text="大笑10声吧让我听听。")
        await q.memory.commit_chat_assistant_message(text="哈哈哈哈哈哈哈哈哈哈哈哈哈")
        q.memory.commit_chat_user_message(text="我想包饺子")
        await q.memory.commit_chat_assistant_message(text="好啊，需要帮忙吗")
        q.memory.commit_chat_user_message(text="帮我带瓶醋")
        await q.memory.commit_chat_assistant_message(text="好的，我将帮你带瓶醋")
        q.memory.commit_chat_user_message(text="我的宠物 喂了吗?")
        await q.memory.commit_chat_assistant_message(text="您的宠物已经喂了")
        q.memory.commit_chat_user_message(text="帮我买点面粉")
        await q.memory.commit_chat_assistant_message(text="好的，我将帮你买点面粉")
        q.memory.commit_chat_user_message(text="帮我买点韭菜还有鸡蛋")
        await q.memory.commit_chat_assistant_message(
            text="好的，我将帮你买点韭菜还有鸡蛋"
        )
        q.query = "我想干啥来着？"
        await asyncio.sleep(10)

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SAY" in result.plan.content)
        self.assertTrue("饺子" in result.plan.content or "韭菜" in result.plan.content)

    async def test_weather(self):
        # 1
        q = AgentParameter(
            query="看天气",
            candidate_actions=candidate_actions,
            memory=Memory(redis_client=testcase_redis_client, device_id="test_weather"),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)

        self.assertEqual(result.return_type, "plan")
        self.assertIn("WEATHER_GET_REALTIME", result.plan.content)
        self.assertTrue("北京" in result.plan.content or "朝阳" in result.plan.content)

        q.query = "我明天去天安门，需要带伞吗？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("WEATHER_GET" in result.plan.content)
        self.assertTrue('city="天安门"' not in result.plan.content)

        q.query = "天安门的天气怎么样"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("WEATHER_GET" in result.plan.content)
        self.assertTrue('city="天安门"' not in result.plan.content)

        q.query = "接下来一周，哪天会下雨吗？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertEqual("plan", result.return_type)
        self.assertTrue("WEATHER_GET" in result.plan.content)
        self.assertTrue("朝阳" in result.plan.content or "北京" in result.plan.content)

        # 2
        q.query = "看一下胡志明市的天气"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("SAY", result.plan.content)

        q.query = "查一下纽约的天气"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("SAY", result.plan.content)

        q.query = "我明天去莫斯科，需要带伞吗？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SAY" in result.plan.content)

        # 3
        q.memory.commit_chat_user_message(text="北京的天气")
        await q.memory.commit_chat_assistant_message(
            text="好的，我将查询北京今天的天气"
        )
        await q.memory.commit_chat_assistant_message(
            text="今天北京中雨转多云，气温20-24度，空气质量优。"
        )
        q.query = "啥时候会下雪"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "SAY" in result.plan.content or "WEATHER_GET" in result.plan.content
        )

        q.memory.clear_chat_history()
        q.memory.commit_chat_user_message(text="看一下北京今天的天气情况")
        await q.memory.commit_chat_assistant_message(
            text="好的，我将查询北京今天的天气"
        )
        await q.memory.commit_chat_assistant_message(
            text="今天北京中雨转多云，气温20-24度，空气质量优。"
        )
        q.query = "菏泽呢"
        result = await SingleActionAgent.a_invoke(q)

        self.assertTrue("WEATHER_GET" in result.plan.content)

    async def test_input_number(self):
        # 1
        q = AgentParameter(
            candidate_actions=candidate_actions,
            query="5 A 8 B",
            memory=Memory(redis_client=testcase_redis_client, device_id="test"),
            robot=Robot(
                interface_state=InterfaceState(
                    app_id=Opk_Receive_App_Id,
                    interface_info="请输入手机号后4位",
                )
            ),
        )
        result = await SingleActionAgent.a_invoke(q)

        self.assertTrue("SAY" in result.plan.content, result.plan.content)

        # 2
        q.query = "5180"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("LAST_4_DIGITS_INPUT" in result.plan.content)

        # 3
        q.query = "52"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "LAST_4_DIGITS_INPUT" in result.plan.content or "SAY" in result.plan.content
        )

        # 4
        q.query = "天气怎么样"
        result = await SingleActionAgent.a_invoke(q)
        if result.return_type == "plan":
            self.assertTrue(
                "SAY" in result.plan.content or "WEATHER_GET" in result.plan.content
            )
        q.memory.commit_chat_user_message(text="你好 我是小明 我来面试")
        await q.memory.commit_chat_assistant_message(text="你好，小明 欢迎你")
        # q.memory.commit_chat_user_message(text="我是来面试的")
        q.query = "3131"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("LAST_4_DIGITS_INPUT" in result.plan.content)

        q.memory.clear_chat_history()

    async def test_calendar(self):
        # 1
        q = AgentParameter(
            query="到中秋还有多久",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_calendar"
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("CALENDAR" in result.plan.content)

        # 2
        q.query = "还有多久过年"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("CALENDAR" in result.plan.content)
        q.memory.clear_chat_history()

    async def test_navigate(self):
        # 1
        p = AgentParameter(
            query="去卫生间",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_navigate"
            ),
            robot=Robot(
                device_id="M03SCN1A14024430N038",
                enterprise_id="orion.ovs.entprise.1429922673",
                interface_state=InterfaceState(app_id=Launcher_App_Id),
            ),
        )
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "NAVIGATE_START" in result.plan.content or "SAY" in result.plan.content
        )

        # 2
        p.query = "参观一下公司的荣誉墙"
        p.robot = Robot(
            device_id="MC1BCN2L1102473143DF",
            enterprise_id="orion.ovs.entprise.1429922673",
            interface_state=InterfaceState(app_id=Launcher_App_Id),
        )
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "GUIDE_ROUTE_SELECTION_FROM_MAP" in result.plan.content
            or "SAY" in result.plan.content
            or "GUIDE_INTRODUCTION" in result.plan.content
        )

        # 我要去开会，你帮我找个会议室
        p.query = "我要去开会，你帮我找个会议室"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "NAVIGATE_START" in result.plan.content or "SAY" in result.plan.content
        )

        p.query = "带刘书记去咱们的党史展览馆参观一下"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "GUIDE_ROUTE_SELECTION_FROM_MAP" in result.plan.content
            or "GUIDE_INTRODUCTION" in result.plan.content
            or "SAY" in result.plan.content
        )

        p.query = "带我去一下附近的饮水机"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SAY" in result.plan.content or "休息区" in result.plan.content)

        p.query = "帮我看看附近有没有空的停车位"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "SAY" in result.plan.content or "OPEN_WEB_URL" in result.plan.content
        )

        p.query = "带刘书记参观一下智慧餐厅和咱们摆放专利证书的地方，再去我们的合作伙伴展览看看，最后再去休息区"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "SAY" in result.plan.content
            or "GUIDE_ROUTE_SELECTION_FROM_MAP" in result.plan.content
        )
        p.memory.clear_chat_history()

    async def test_welcome_config(self):
        # 1
        q = AgentParameter(
            query="下次见到我叫我小俊俊，告诉我“今天记得吃晚饭",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_welcome_config"
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("CONFIGURE_WELCOME_MESSAGE" in result.plan.content)

        # 2
        q.query = "下次见到我告诉我今天记得吃晚饭"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("CONFIGURE_WELCOME_MESSAGE" in result.plan.content)
        self.assertTrue(
            "nick_name" not in result.plan.content
            or 'nick_name=""' in result.plan.content
        )

        q.memory.commit_chat_user_message(text="下次见到我叫我小俊俊")
        await q.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="CONFIGURE_WELCOME_MESSAGE",
                parameters=[
                    ChatParameter(name="welcome_message", value="下次见到我叫我小俊俊"),
                    ChatParameter(name="nick_name", value="小俊俊"),
                ],
            )
        )
        q.robot.interface_state.interface_info = "正在弹出待确定框"

        await q.memory.commit_chat_assistant_message(
            event=ChatEvent(desc="从通用场景 -> 人员注册场景")
        )

        q.query = "确定"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("CONFIRM" in result.plan.content)

    async def test_generate_message(self):
        p = AgentParameter(
            query="这是马老板，给我欢迎一下他",
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_generate_message"
            ),
            candidate_actions=candidate_actions,
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "GENERATE_MESSAGE" in result.plan.content
            or "CONFIGURE_WELCOME_MESSAGE" in result.plan.content
        )

        p.query = "这是张老板，是我们重要的客户，你写个七言绝句欢迎一下他"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("GENERATE_MESSAGE" in result.plan.content)
        p.memory.clear_chat_history()

        instruction = "欢迎重要客户张老板，写一首七言绝句。"
        result = await generate_message(instruction, __robot=p.robot)

        p.query = "韩总是我们的总代理商，这次来公司给我们带来很多业务上的支持，帮我热烈欢迎一下韩总"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("GENERATE_MESSAGE" in result.plan.content)
        p.memory.clear_chat_history()

        print(result)

    async def test_robot_move_and_turn(self):
        p = AgentParameter(
            query="转100圈",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_robot_move_and_turn"
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        p.memory.clear_chat_history()

        # 转100圈、
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SAY" in result.plan.content)

        p.query = "转70度"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "TURN_DIRECTION" in result.plan.content and "70" in result.plan.content
        )

        # 转19圈
        p.query = "转39圈"
        result = await SingleActionAgent.a_invoke(p)
        # todo fix
        self.assertTrue(
            "SAY" in result.plan.content or "TURN_DIRECTION" in result.plan.content
        )

        # 转19圈
        p.query = "转10圈半"
        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("TURN_DIRECTION", result.plan.content)
        self.assertTrue("3780" in result.plan.content)

        # 转十圈
        p.query = "转10圈/转十圈"
        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("TURN_DIRECTION", result.plan.content)

        p.query = "转5圈"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("TURN_DIRECTION" in result.plan.content)

        # 向前走3米
        p.query = "向前走10米"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "SAY" in result.plan.content or "MOVE_DIRECTION" in result.plan.content
        )

        p.query = "向前走1米"
        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("MOVE_DIRECTION", result.plan.content)

        p.query = "向后走100米"
        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("SAY", result.plan.content)

        p.query = "向前走30米"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SAY", result.plan.content)

        # 走一个六亲不认的步伐
        p.query = "走一个六亲不认的步伐"
        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("START_DANCE", result.plan.content)
        # 你退后一点
        p.query = "你退后一点"
        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("MOVE_DIRECTION", result.plan.content)

        p.query = "鞠个躬"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("HEAD_NOD" in result.plan.content)

    async def test_settings(self):
        # 你声音太小了，我听不到
        p = AgentParameter(
            query="你声音太小了，我听不到",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_settings"
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SET_VOLUME" in result.plan.content)
        p.query = "大点声"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SET_VOLUME" in result.plan.content)

        p.query = "安静一点"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SET_VOLUME" in result.plan.content)

        p.query = "走慢点走慢点"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("ADJUST_SPEED" in result.plan.content)

        p.query = "走快点，再快点"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("ADJUST_SPEED" in result.plan.content)

        p.query = "你能听到我说话吗？"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SAY" in result.plan.content)
        self.assertTrue("不" not in result.plan.content)
        p.memory.clear_chat_history()

    async def test_chat_history(self):
        chat_history = ChatContext(
            messages=[
                ChatMessage(
                    role="assistant",
                    content="OK",
                ),
                ChatMessage(role="user", content="看一下北京今天的天气情况"),
                ChatMessage(role="assistant", content="好的，我将查询北京今天的天气"),
                ChatMessage(
                    role="assistant",
                    content="嗯嗯",
                ),
                ChatMessage(
                    role="assistant",
                    content="今天北京中雨转多云，气温20-24度，空气质量优。",
                ),
                ChatMessage(  # 模拟噪音干扰
                    role="user",
                    content="1234",
                ),
                ChatMessage(  # 模拟噪音干扰
                    role="user",
                    content="899",
                ),
            ]
        )

        q = AgentParameter(
            query="菏泽呢",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_chat_history",
                max_chat_history=10,
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.update_context(chat_history, q.memory.chat_context_prefix)
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("WEATHER_GET_REALTIME" in result.plan.content)
        q.memory.clear_chat_history()

    async def test_send_message(self):
        # 1
        q = AgentParameter(
            query="给他发个消息",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_send_message",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("SAY", result.plan.content)
        q.query = "我在公司门口，你给朱大哥发个消息，我在这等他"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SEND_MESSAGE" in result.plan.content)

        q.query = "给张刘说一声，我在这等他"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("SEND_MESSAGE" in result.plan.content)

        q.memory.clear_chat_history()

    async def test_outdoor_navigation(self):
        # 1
        q = AgentParameter(
            query="从四惠汽车站开车到中关村",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_outdoor_navigation",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)

        q.query = "问澳门的关闸到威尼斯人的路线"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)

        # 附近的地铁/公交站怎么走
        q.query = "附近的地铁站/公交站怎么走"
        q.robot.geo_location.describe = "猎豹移动"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "SAY" in result.plan.content or "OPEN_WEB_URL" in result.plan.content
        )
        self.assertIn("猎豹移动", result.plan.content)

        # 2
        q.query = "从团结湖公园骑车去颐和园"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)

        # 走着去传媒大学
        q.query = "我走着去传媒大学，你帮我看看怎么走"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)
        self.assertIn("猎豹移动", result.plan.content)

        q.query = "看一下去澳门的航班"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)
        self.assertIn("https://flights.ctrip.com/online", result.plan.content)

        q.query = "明天去成都的机票帮我看看"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)
        self.assertIn("https://flights.ctrip.com/online", result.plan.content)

        q.query = "九寨沟附近的酒店推荐一下"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)

        q.query = "后天去厦门玩，帮我查一下哪里的酒店"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)

        q.query = "看看猎豹移动的官网"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)
        self.assertIn("www.cmcm.com", result.plan.content)

        q.memory.commit_chat_user_message(text="还有多久个？/[还有多久]")
        await q.memory.commit_chat_assistant_message(
            text="春节是1月29日，今天距离2025年春节还有63天"
        )
        q.query = "你帮我查一下那一天从北京到山东的航班。/[你帮我查一下那一天从北京到山东的航班]"

        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)
        self.assertIn("2025-01-29", result.plan.content)

    # async def test_summary(self):
    # result = await LLMToolKit.summary(
    #     chat_history=["你还", "帮我查一下去上海的机票", "明天的"],
    #     follow_up_input="明天的机票",
    # )

    # self.assertTrue(result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "user:你好",
    #         "assistant:你好！很高兴见到你，有什么我可以帮忙的吗？",
    #         "assistant:你好！很高兴见到你，有什么我可以帮忙的吗？",
    #     ],
    #     "真实的他们",
    # )

    # self.assertTrue(result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "user:持续性对以都是有很多。公司很多",
    #         "assistant:请告诉我您具体想了解公司哪方面的信息，比如公司的产品、业务、领导等，我可以帮您更好地筛选。",
    #     ],
    #     "他没有...没有直接定，这不是我期望的。...看一下这个，有个是中文的。...你心表达的是...这样吗？现在好多都这样，就前面会有一句，有的那个。",
    # )

    # self.assertTrue(result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "user:真实的他们",
    #         "assistant:关于“真实的他们”的信息可能涉及到很多方面，您能具体说明一下您想了解的内容吗？比如是某个特定的领域、人物还是事件？",
    #         "assistant:王晓溪，欢迎您的到来",
    #     ],
    #     "你叫什么名字？",
    # )

    # self.assertTrue("名字" in result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "user:你能够告诉我中国面积最大的省是哪里吗？...然后我想去那里转一转，然后。...那你再告诉我这个省里面面积最大的市是哪里？...我也想去那里转一转。...然你再告诉我那个。...最大的线是哪里？然后在我也想去转转。",
    #         "assistant:这个问题我懂！中国面积最大的省是新疆，最大的地级市是咸阳市，最大的县是若羌县，都在新疆。希望这些地方能让你有个美好的旅行体验！",
    #     ],
    #     "明月几时有，把酒问青天。不知天上宫阙，今夕是何年。我欲乘风归去，又恐琼楼玉宇，高处不胜寒。起舞弄清影，何似在人间。转朱阁，低绮户，照无眠。不应有恨，何事长向别时圆？人有悲欢离合，月有阴晴圆缺，此事古难全。但愿人长久，千里共婵娟。",
    # )

    # self.assertNotIn("最大", result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "user: 北京的天气...怎么样？",
    #         "assistant: 北京今天的天气是多云，气温在20-24度之间，空气质量优。",
    #         "user:帮我查一下附近的便利 ",
    #         "assistant:好的，我将查询附近的便利店",
    #     ],
    #     "明天会下雨吗？",
    # )

    # self.assertIn("明天", result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "user:你能够告诉我中国面积最大的省是哪里吗？...然后我想去那里转一转，然后。...那你再告诉我这个省里面面积最大的市是哪里？...我也想去那里转一转。...然你再告诉我那个。...最大的线是哪里？然后在我也想去转转。",
    #         "assistant:这个问题我懂！中国面积最大的省是新疆，最大的地级市是咸阳市，最大的县是若羌县，都在新疆。希望这些地方能让你有个美好的旅行体验！",
    #         "user:唧唧唧唧唧，木兰当户织。不闻机杼声，惟闻女叹息。女欲何所思，你欲何所忆何。昨日见军帖，贺汗大点兵。...卷，军书十二卷，卷卷有爷名，阿爷无大儿，木兰无爪兄不问。",
    #         "assistant:听起来你在背诵《木兰辞》呢！这首诗歌讲述了花木兰替父从军的故事，非常动人。",
    #     ],
    #     "user:阿爷无大儿，木兰无长兄。军书十二卷，卷卷有爷名。/[那也不大木兰无长兄均属实案卷卷券有缘民]",
    # )

    # self.assertNotIn("最大", result.content)

    # result = await LLMToolKit.summary(
    #     [
    #         "<User> SAY '还有多久过年？'",
    #         "<Robot> EXECUTED ACTION CALENDAR(query='还有多久过年？')",
    #         "<Robot> SAY '春节是1月29日'",
    #     ],
    #     "<User> SAY '你帮我查一下那一天从北京到山东的航班'",
    # )
    # self.assertIn("1月29", result.content)

    async def test_schedule_memory(self):
        q = AgentParameter(
            query="关于我你都了解什么",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_schedule_memory",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )

        # Mock the memory retrieval
        async def mock_get_memories(*args, **kwargs):
            return [
                "Use timestamps in memory records to differentiate past memories from current plans. Analyzing these records reveals user patterns and enables better suggestions.",
                "Current Time: 2024-12-11 10:00:00",
                "Record Time: 2023-12-01 Memory: 明天要去欢乐谷玩",
                "Record Time: 2023-12-05 Memory: 下周二要去医院做体检",
            ]

        # Replace the real get_memories with our mock
        original_get_memories = q.memory.get_memories
        q.memory.get_memories = mock_get_memories
        q.use_user_mem = True
        q.robot.face_id = "test_schedule_memory"

        try:
            # Test asking about tomorrow's schedule
            result = await SingleActionAgent.a_invoke(q)
            print(result.plan.content)
        finally:
            # Restore the original method
            q.memory.get_memories = original_get_memories
            q.memory.clear_chat_history()

    async def test_can_send_message(self):
        q = AgentParameter(
            query="给马鑫发一个微信消息",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_can_send_message",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)
        # 拒绝发送微信消息
        self.assertIn("SAY", result.plan.content)

        q.memory.commit_chat_user_message(text="发信息给刘宇翔")
        await q.memory.commit_chat_assistant_message(
            text="请问您想发送什么内容给刘宇翔呢？"
        )
        q.memory.commit_chat_user_message(text="下午两点开会")

        result = await SingleActionAgent.a_invoke(q)
        # 拒绝发送微信消息
        self.assertIn("SEND_MESSAGE", result.plan.content)

    async def test_memory(self):
        q = AgentParameter(
            query="上一句话我说了什么",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_memory",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        q.memory.commit_chat_user_message(text="继续完成刚才的命令")
        await q.memory.commit_chat_assistant_message(text="好的，现在就去执行")

        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("SAY", result.plan.content)

        self.assertTrue(
            "继续完成" in result.plan.content or "刚才的命令" in result.plan.content
        )

        q.memory.clear_chat_history()

    async def test_multiple_action(self):
        q = AgentParameter(
            query="你能向右转3圈，再向左转2圈吗",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_multiple_action",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )

        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)

        # 拒绝 or 只向右转三圈
        self.assertTrue(
            "SAY" in result.plan.content
            or (
                "orion.agent.action.TURN_DIRECTION" in result.plan.content
                and "1080" in result.plan.content
            )
        )

    async def test_enum_value(self):
        p = AgentParameter(
            query="带刘书记去咱们的党史展览馆参观一下",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=Redis(
                    host=agent_setting.redis_host,
                    port=agent_setting.redis_port,
                    db=3,
                ),
                device_id="benchmark",
            ),
            robot=Robot(
                device_id="M03SCN1A14024430N038",
                enterprise_id="orion.ovs.entprise.1429922673",
            ),
        )

        result = await SingleActionAgent.a_invoke(p)
        self.assertIn("GUIDE_ROUTE_SELECTION_FROM_MAP", result.plan.content)

    async def test_knowledge_qa(self):
        q = AgentParameter(
            query="中国的本土宗教是什么？他们的代表人物有哪些",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_knowledge_qa",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )
        q.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(q)

        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "刘关张说的是谁你知道吗？他们出自那本书"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "三个兄弟去买帽子。老大说：“我戴的帽子是红色。”老二说：“老大说的是真的。”老三说：“老二说的是假的。”请问老大戴的帽子是什么颜色？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "KNOWLEDGE_QA" in result.plan.content or "SAY" in result.plan.content
        )

        q.query = "宋朝那么繁荣为什么还是被打"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "普渡机器人和mini都是你们公司的吗"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "一头牛往左转2圈，然后往右转2圈，现在他的尾巴朝哪？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("SAY", result.plan.content)

        q.query = "一头牛往左转2圈，然后往右转2圈，现在他的尾巴朝哪？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("SAY", result.plan.content)

        q.query = "那你们公司都有哪些机器人，有没有能够乘坐电梯的"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

    async def test_vision(self):
        q = AgentParameter(
            query="你猜猜我是男的还是女的",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_vision",
            ),
            robot=Robot(interface_state=InterfaceState(app_id=Launcher_App_Id)),
        )

        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("ANSWER_QUESTION_FROM_VISION", result.plan.content)

        q.query = "我是谁"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("FACE_RECOGNITION", result.plan.content)

        q.query = "猜猜我是谁呀"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("FACE_RECOGNITION", result.plan.content)

        q.query = "今天的天气适合散步吗？或者可以去爬山"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("WEATHER_GET_REALTIME", result.plan.content)

        q.query = "字太小了，又离这么远，能不能过来让我看清点"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "MOVE_DIRECTION" in result.plan.content
            or "SET_VOLUME" in result.plan.content
        )

        q.query = "你看我今天心情怎么样？"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "SAY" in result.plan.content
            or "ANSWER_QUESTION_FROM_VISION" in result.plan.content
        )

        q.query = "那你看看今天的天气怎么样"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("WEATHER_GET_REALTIME", result.plan.content)

        q.query = "那你看一下刘总来没来吧"
        result = await SingleActionAgent.a_invoke(q)
        self.assertTrue(
            "SAY" in result.plan.content or "SEND_MESSAGE" in result.plan.content
        )

        q.query = "我穿的衣服好看吗"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("ANSWER_QUESTION_FROM_VISION", result.plan.content)

    async def test_clean_text(self):
        q = AgentParameter(
            query="""<User> said '怎么从昆明去'
<User> said '我明天要去上海出差帮我看一下机票'
<Robot> Triggered Event [desc='App Switch: 通用场景 → 浏览器场景']
<Robot> Executed Action. FLIGHT_TICKET_QUERY(departure_city_code=KMG,arrival_city_code=SHA,departure_date=202½-03-20) Result:{}
<Robot> said '查到明天昆明到上海的航班信息了，您想了解具体的航班时间和价格吗？'
<Robot> Triggered Event [desc='App Switch: 浏览器场景 → 通用场景']
<User> said '我从北京去上海出差帮我看一下机票'
<Robot> Triggered Event [desc='App Switch: 通用场景 → 浏览器场景']
<Robot> Executed Action. FLIGHT_TICKET_QUERY(departure_city_code=PEK,arrival_city_code=SHA,departure_date=2025-03-20) Result:{}
<Robot> said '查到了明天北京到上海的航班信息，您想了解具体的时间和价格吗？'
<User> said '我还是坐火车去吧'
<Robot> Executed Action. TRAIN_TICKET_QUERY(departure_city=北京,arrival_city=上海,departure_date=2025-03-20) Result:{}
<Robot> said '查到明天北京到上海的火车票信息了，您想了解具体车次和时间吗？'
'算了我还是直接开车去吧'""",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_clean_text"
            ),
            robot=Robot(
                interface_state=InterfaceState(
                    app_id=Launcher_App_Id,
                    interface_info="""<b><d><d><d><d><d><a>携程旅行网</a></d></d><d><u><l><d><b><d><s>登录</s></d></b></d></l><l><b>注册</b></l><l><d><b>我的订单</b></d></l><l><d><b>联系客服</b></d></l></u></d></d></d><d><d><d><d><d><f><l>出发城市</l></f><f><l>到达城市</l></f></d><d><d><d><l>出发日期</l><s>2025-03-20</s><s>星期四</s></d><d>添加返程</d></d></d><b>搜索</b></d></d></d><d><h>北京-上海<s>单程2025-03-20(共53车次)</s><d><a>火车票</a><s>></s><s>北京到上海火车票</s></d></h></d><d><d><d><u><l><d>03-19</d><d>今天</d></l><l><d>03-20</d><d>明天</d></l><l><d>03-21</d><d>周五</d></l><l><d>03-22</d><d>周六</d></l><l><d>03-23</d><d>周日</d></l><l><d>03-24</d><d>周一</d></l></u><u><l><d>出发<s>早 - 晚</s></d></l><l><d>运行时长</d></l><l><d>价格排序</d></l></u></d><d><d><d>首都国际机场</d><d>8小时55分</d><d>虹桥国际机场</d><d><d>500</d><d>超实惠</d></d><b>查看</b></d></d><s><d><d><d>06:20</d><d>北京南</d></d><d><d>5时38分</d><d>G103</d></d><d><d>11:58</d><d>上海虹桥</d></d><d><d>553</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>553</d><b>预订</b></l><l><s>一等座</s><d>930</d><b>预订</b></l><l><s>商务座</s><d>1873</d><b>抢票</b></l></u></d><d><d><d>07:00</d><d>北京南</d></d><d><d>4时29分</d><d>G1</d></d><d><d>11:29</d><d>上海</d></d><d><d>667</d><u><l>二等座有票</l><l>一等座18张</l><l>优选一等座3张</l><l>商务座9张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>667</d><b>预订</b></l><l><s>一等座</s><d>1067</d><b>预订</b></l><l><s>优选一等座</s><d>1467</d><b>预订</b></l><l><s>商务座</s><d>2331</d><b>预订</b></l></u></d><d><d><d>07:17</d><d>北京南</d></d><d><d>5时46分</d><d>G105</d></d><d><d>13:03</d><d>上海虹桥</d></d><d><d>553</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座3张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>553</d><b>预订</b></l><l><s>一等座</s><d>930</d><b>预订</b></l><l><s>商务座</s><d>1873</d><b>预订</b></l></u></d><d><d><d>07:25</d><d>北京南</d></d><d><d>5时47分</d><d>G107</d></d><d><d>13:12</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座<s>(抢)</s></l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>抢票</b></l><l><s>商务座</s><d>2158</d><b>抢票</b></l></u></d><d><d><d>07:40</d><d>北京</d></d><d><d>4时52分</d><d>G3</d></d><d><d>12:32</d><d>上海</d></d><d><d>671</d><u><l>二等座有票</l><l>一等座<s>(抢)</s></l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>671</d><b>预订</b></l><l><s>一等座</s><d>1073</d><b>抢票</b></l><l><s>商务座</s><d>2344</d><b>抢票</b></l></u></d><d><d><d>07:45</d><d>北京南</d></d><d><d>6时4分</d><d>G109</d></d><d><d>13:49</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座17张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>2158</d><b>预订</b></l></u></d><d><d>抢票成功率：较高</d><d><d><d>08:00</d><d>北京南</d></d><d><d>4时32分</d><d>G3</d></d><d><d>12:32</d><d>上海</d></d><d><d>667</d><u><l><s>暂无余票，建议抢票，抢票成功率较高</s></l></u></d><b>抢</b></d></d><d><u><l><s>二等座</s><d>667</d><b>抢票</b></l><l><s>一等座</s><d>1067</d><b>抢票</b></l><l><s>商务座</s><d>2331</d><b>抢票</b></l></u></d><d><d><d>08:16</d><d>北京南</d></d><d><d>5时55分</d><d>G111</d></d><d><d>14:11</d><d>上海虹桥</d></d><d><d>576</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座10张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>576</d><b>预订</b></l><l><s>一等座</s><d>969</d><b>预订</b></l><l><s>商务座</s><d>1998</d><b>预订</b></l></u></d><d><d><d>08:39</d><d>北京南</d></d><d><d>6时22分</d><d>G113</d></d><d><d>15:01</d><d>上海虹桥</d></d><d><d>534</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座8张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>534</d><b>预订</b></l><l><s>一等座</s><d>887</d><b>预订</b></l><l><s>商务座</s><d>1855</d><b>预订</b></l></u></d><d><d>抢票成功率：较高</d><d><d><d>09:00</d><d>北京南</d></d><d><d>4时37分</d><d>G5</d></d><d><d>13:37</d><d>上海虹桥</d></d><d><d>662</d><u><l>二等座<s>(抢)</s></l><l>一等座1张</l><l>优选一等座<s>(抢)</s></l><l>商务座1张</l></u></d><b>订</b></d></d><d><u><l><s>二等座</s><d>662</d><b>抢票</b></l><l><s>一等座</s><d>1060</d><b>预订</b></l><l><s>优选一等座</s><d>1457</d><b>抢票</b></l><l><s>商务座</s><d>2318</d><b>预订</b></l></u></d><d><d><d>09:10</d><d>北京南</d></d><d><d>5时38分</d><d>G115</d></d><d><d>14:48</d><d>上海虹桥</d></d><d><d>626</d><u><l>二等座有票</l><l>一等座15张</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>626</d><b>预订</b></l><l><s>一等座</s><d>1035</d><b>预订</b></l><l><s>商务座</s><d>2318</d><b>抢票</b></l></u></d><d><d><d>09:20</d><d>北京南</d></d><d><d>5时35分</d><d>G117</d></d><d><d>14:55</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座2张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>2158</d><b>预订</b></l></u></d><d><d><d>09:24</d><d>北京南</d></d><d><d>6时7分</d><d>G119</d></d><d><d>15:31</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座9张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>2158</d><b>预订</b></l></u></d><d><d>抢票成功率：较高</d><d><d><d>10:00</d><d>北京南</d></d><d><d>4时35分</d><d>G7</d></d><d><d>14:35</d><d>上海虹桥</d></d><d><d>662</d><u><l><s>暂无余票，建议抢票，抢票成功率较高</s></l></u></d><b>抢</b></d></d><d><u><l><s>二等座</s><d>662</d><b>抢票</b></l><l><s>一等座</s><d>1060</d><b>抢票</b></l><l><s>优选一等座</s><d>1457</d><b>抢票</b></l><l><s>商务座</s><d>2318</d><b>抢票</b></l></u></d><d><d><d>10:05</d><d>北京南</d></d><d><d>5时37分</d><d>G121</d></d><d><d>15:42</d><d>上海虹桥</d></d><d><d>626</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座10张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>626</d><b>预订</b></l><l><s>一等座</s><d>1035</d><b>预订</b></l><l><s>商务座</s><d>2318</d><b>预订</b></l></u></d><d><d><d>10:14</d><d>北京南</d></d><d><d>6时12分</d><d>G123</d></d><d><d>16:26</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>2158</d><b>抢票</b></l></u></d><d><d><d>10:48</d><d>北京南</d></d><d><d>6时2分</d><d>G125</d></d><d><d>16:50</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座12张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>2158</d><b>预订</b></l></u></d><d><d><d>11:00</d><d>北京南</d></d><d><d>4时37分</d><d>G9</d></d><d><d>15:37</d><d>上海虹桥</d></d><d><d>662</d><u><l>二等座有票</l><l>一等座<s>(抢)</s></l><l>优选一等座<s>(抢)</s></l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>662</d><b>预订</b></l><l><s>一等座</s><d>1060</d><b>抢票</b></l><l><s>优选一等座</s><d>1457</d><b>抢票</b></l><l><s>商务座</s><d>2318</d><b>抢票</b></l></u></d><d><d><d>11:05</d><d>北京南</d></d><d><d>6时3分</d><d>G127</d></d><d><d>17:08</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>2158</d><b>抢票</b></l></u></d><d><d><d>11:18</d><d>北京南</d></d><d><d>6时20分</d><d>G129</d></d><d><d>17:38</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座20张</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>1998</d><b>抢票</b></l></u></d><d><d><d>11:27</d><d>北京南</d></d><d><d>5时55分</d><d>G131</d></d><d><d>17:22</d><d>上海虹桥</d></d><d><d>626</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座12张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>626</d><b>预订</b></l><l><s>一等座</s><d>1035</d><b>预订</b></l><l><s>商务座</s><d>2318</d><b>预订</b></l></u></d><d><d><d>11:50</d><d>北京南</d></d><d><d>6时12分</d><d>G133</d></d><d><d>18:02</d><d>上海</d></d><d><d>604</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座10张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>604</d><b>预订</b></l><l><s>一等座</s><d>1013</d><b>预订</b></l><l><s>商务座</s><d>2013</d><b>预订</b></l></u></d><d><d><d>12:00</d><d>北京南</d></d><d><d>4时38分</d><d>G11</d></d><d><d>16:38</d><d>上海虹桥</d></d><d><d>662</d><u><l>二等座有票</l><l>一等座1张</l><l>优选一等座2张</l><l>商务座1张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>662</d><b>预订</b></l><l><s>一等座</s><d>1060</d><b>预订</b></l><l><s>优选一等座</s><d>1457</d><b>预订</b></l><l><s>商务座</s><d>2318</d><b>预订</b></l></u></d><d><d><d>12:08</d><d>北京南</d></d><d><d>7时50分</d><d>G2573</d></d><d><d>19:58</d><d>上海虹桥</d></d><d><d>581</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>581</d><b>预订</b></l><l><s>一等座</s><d>952</d><b>预订</b></l><l><s>商务座</s><d>1845</d><b>抢票</b></l></u></d><d><d>抢票成功率：较高</d><d><d><d>12:10</d><d>北京</d></d><d><d>18时35分</d><d>1461</d></d><d><d>06:45<e>+1</e></d><d>上海</d></d><d><d>156.5</d><u><l>硬座有票</l><l>硬卧<s>(抢)</s></l><l>软卧<s>(抢)</s></l><l>无座有票</l></u></d><b>订</b></d></d><d><u><l><s>硬座</s><d>156.5</d><b>预订</b></l><l><s>硬卧</s><d>283.5</d><b>抢票</b></l><l><s>软卧</s><d>455.5</d><b>抢票</b></l><l><s>无座</s><d>156.5</d><b>预订</b></l></u></d><d><d><d>12:12</d><d>北京南</d></d><d><d>6时9分</d><d>G135</d></d><d><d>18:21</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座11张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>1998</d><b>预订</b></l></u></d><d><d><d>12:47</d><d>北京南</d></d><d><d>6时12分</d><d>G137</d></d><d><d>18:59</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座20张</l><l>商务座<s>(抢)</s></l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>1998</d><b>抢票</b></l></u></d><d><d><d>13:00</d><d>北京南</d></d><d><d>4时35分</d><d>G13</d></d><d><d>17:35</d><d>上海</d></d><d><d>667</d><u><l>二等座有票</l><l>一等座<s>(抢)</s></l><l>优选一等座<s>(抢)</s></l><l>商务座8张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>667</d><b>预订</b></l><l><s>一等座</s><d>1067</d><b>抢票</b></l><l><s>优选一等座</s><d>1467</d><b>抢票</b></l><l><s>商务座</s><d>2331</d><b>预订</b></l></u></d><d><d><d>13:04</d><d>北京南</d></d><d><d>6时2分</d><d>G139</d></d><d><d>19:06</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座18张</l><l>商务座1张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>1998</d><b>预订</b></l></u></d><d><d><d>13:34</d><d>北京南</d></d><d><d>6时34分</d><d>G141</d></d><d><d>20:08</d><d>上海虹桥</d></d><d><d>612</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座9张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>612</d><b>预订</b></l><l><s>一等座</s><d>1016</d><b>预订</b></l><l><s>商务座</s><d>2110</d><b>预订</b></l></u></d><d><d><d>14:00</d><d>北京南</d></d><d><d>4时32分</d><d>G15</d></d><d><d>18:32</d><d>上海虹桥</d></d><d><d>662</d><u><l>二等座有票</l><l>一等座<s>(抢)</s></l><l>优选一等座<s>(抢)</s></l><l>商务座9张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>662</d><b>预订</b></l><l><s>一等座</s><d>1060</d><b>抢票</b></l><l><s>优选一等座</s><d>1457</d><b>抢票</b></l><l><s>商务座</s><d>2318</d><b>预订</b></l></u></d><d><d><d>14:09</d><d>北京南</d></d><d><d>5时58分</d><d>G143</d></d><d><d>20:07</d><d>上海虹桥</d></d><d><d>598</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座10张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>598</d><b>预订</b></l><l><s>一等座</s><d>1006</d><b>预订</b></l><l><s>商务座</s><d>1998</d><b>预订</b></l></u></d><d><d><d>14:14</d><d>北京南</d></d><d><d>5时58分</d><d>G145</d></d><d><d>20:12</d><d>上海虹桥</d></d><d><d>576</d><u><l>二等座有票</l><l>一等座有票</l><l>商务座11张</l></u></d><b>订</b></d><d><u><l><s>二等座</s><d>576</d><b>预订</b></l><l><s>一等座</s><d>969</d><b>预订</b></l><l><s>商务座</s><d>1873</d><b>预订</b></l></u></d><d><d><d>14:27</d><d>北京南</d></d><d><d>6时16分</d><d>G147</d></d><d><d>20:43</d><d>上海虹桥</d></d><d><d>576</d><u><l>二等�""",
                )
            ),
        )

        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("OUTDOOR_NAVIGATE_START", result.plan.content)

    async def test_qa_in_english(self):
        q = AgentParameter(
            query="オリオンスターのキャリーボットを紹介します。",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_qa_in_english"
            ),
            robot=Robot(
                interface_state=InterfaceState(app_id=Launcher_App_Id), language="ja_JP"
            ),
        )
        agent_setting.region_version = "oversea"
        agent_setting.embedding_model = "text-embedding-3-small"
        agent_setting.embedding_model_base_url = "http://proxy-ai.smartsales.vip/v1"
        agent_setting.embedding_api_key = "********************************************************************************************************************************************************************"

        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("QA", result.plan.content)

    async def test_to_say_not_guide(self):
        q = AgentParameter(
            query="给我介绍一下上海",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_to_say_not_guide"
            ),
        )

        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "帮我推荐上海三天的旅游路径"
        result = await SingleActionAgent.a_invoke(q)
        self.assertNotIn("RECOMMEND", result.plan.content)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

        q.query = "介绍一下成都武侯祠"
        result = await SingleActionAgent.a_invoke(q)
        self.assertNotIn("RECOMMEND", result.plan.content)
        self.assertIn("KNOWLEDGE_QA", result.plan.content)

    async def test_guide_op(self):
        q = AgentParameter(
            query="别走了",
            candidate_actions=candidate_actions,
            memory=Memory(redis_client=testcase_redis_client, device_id="guide_op"),
            robot=Robot(
                interface_state=InterfaceState(
                    app_id=Opk_Guide_App_Id,
                )
            ),
        )
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("COMMON_PAUSE", result.plan.content)

        q.query = "继续走"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("MULTIMEDIA_PLAY", result.plan.content)

        q.query = "继续服务"
        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("MULTIMEDIA_PLAY", result.plan.content)

    async def test_web_search(self):
        q = AgentParameter(
            query="搜一下马斯克有几个女儿",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client, device_id="test_web_search"
            ),
        )

        result = await SingleActionAgent.a_invoke(q)
        self.assertIn("OPEN_WEB_URL", result.plan.content)

    async def test_vision_qa(self):
        q = AgentParameter(
            query="你现在在回答一下他们什么关系",
            candidate_actions=candidate_actions,
            memory=Memory(redis_client=testcase_redis_client, device_id="test_vision"),
            robot=Robot(
                interface_state=InterfaceState(
                    interface_info="<b> <i>\\ue610</i> 百度首页 设置 登录 <b>网页</b> 图片 资讯 视频 笔记 地图 贴吧 文库 更多 <t><t><t><t> DeepSeek-R1 帮你解答 <i>\\ue687</i><i>\\ue613</i> <i>\\ue619</i> 换一换 热搜榜 北京榜 民生榜 财经榜 <i>\\ue662</i> “铁杆朋友”的温暖相册 1 中方驳斥乌总统称中国向俄提供武器 热 2 黄仁勋说了一句意味深长的大实话 3 特斯拉无人车零件被关税挡住了 4 海口美兰区区长吴升娇去世 终年41岁 新 5 男子先后3次中百万大奖 热 6 公安机关公布10起网络谣言案件 7 中国被曝已不再生产出口美国的玩具 热 8 国常会：要持续稳定股市 新 9 三个维度看一季度中国经济数据 10 父亲回应女婴被饿成皮包骨进ICU 11 女子因孕吐在医院住院时去世 新 12 钓鱼佬钓到蛇吓唬同伴 13 北影节今晚启幕 14 孙杨空降西双版纳女粉丝激动追 15 叶童当面拒绝陈德容加入 </t></t></t></t> <i>\\ue60c</i>收起工具 时间不限<i>\\ue615</i> 所有网页和文件 <i>\\ue615</i> 站点内检索 <i>\\ue615</i> <i>\\ue641</i>搜索工具 百度为您找到以下结果 <p>\\u200c 傅盛和孙明焱的关系是商业合作伙伴。 \\u200c</p><p>孙明焱目前担任北京聚云科技有限公司和北京聚云立方科技有限公司等4家企业的法定代表人，同时在7家企业担... 1 。</p> <h> 猎豹总裁徐鸣卸任 傅盛 称其不仅是搭档更像是兄弟 - TechWeb </h> 2018年6月21日 猎豹总裁徐鸣卸任 傅盛 称其不仅是搭档更像是兄弟 【TechWeb报道】6月21日消息，今日晚间，猎豹CEO 傅盛 发布内部信称，公司总裁徐鸣因个人原因将于2018年7月21日起卸任公司总裁... TechWeb <i>\\ue62b</i> <h> 孙明焱 - 南京擎盾信息科技有限公司 - 法定代表人/高管/股... </h> 2025年4月7日 二、 孙明焱 投资情况: 孙明焱 间接持股企业6家,包括投资青岛猎豹投资管理有限公司、投资占比达1%,玖壹叁陆零医学科技(杭州)有限公... 孙明焱 投资杭州聚合... 爱企查 <i>\\ue62b</i> <h> 大模型“炼丹”容易“修仙”难:猎户星空跨越AI应用鸿沟|甲... </h> 2024年11月28日 之所以造成这些问题,和出海企业不了解用户需求、以及模型能力不足、数据质量不够有很大 关系 。 在猎豹移动董事长兼CEO、猎户星空董事长 傅盛 看来,大模型之间的... 财经头条 <i>\\ue62b</i> <h> 关于大模型竞争, 傅盛 捅破的不只有数据壁垒|傅盛_新浪财经_... </h> 2024年12月2日 这些标注相对来说比较明确,比如依照视频找物体,根据语音找文字。但在大模型时代,企业应用是多种多样的”... 孙明焱 向北京商报记者举例,“比如... 新浪财经 <i>\\ue62b</i> <h> 猎豹移动面向海外用户发布区块链安全钱包SafeWallet </h> 2018年2月1日 傅盛 认为，AI提升生产力，区块链改变生产 关系 。AI是提高代码本身的效率，比如人脸识别率从70%到90%，这就是生产力的提升。区块链是生产关系。生... 雷递 <i>\\ue62b</i> 大家还在搜 傅盛是哪里人 傅山集团是干什么的 傅盛投资了多少家公司 傅国强个人简介资料 傅斌星和傅梅城的关系 傅国胜全国排名 傅方俊是哪家公司的 傅千万是谁 <h> (cmcm)股票公司高管_美股_新浪财经_新浪网 </h> 傅盛 傅盛先生自2018年3月起担任公司董事长,自2010年11月起担任公司首席执行官和董事。傅先生自20... 新浪网 <i>\\ue62b</i> <h> 猎豹移动 傅盛 :AI大模型竞争,本质上是数据的竞争-新闻频道-... </h> 2024年12月3日 “猎豹是行业内唯一训练过大模型,把数据标注能力和数据服务能力开放出来的公司,这是目前公司在行业中的独... 傅盛 及猎豹移动高级副总裁 孙明焱 ... 和讯新闻 <i>\\ue62b</i> <h> 孙明焱 - 北京聚云科技有限公司 - 企查查 </h> 2025年4月6日 孙 sūn míng yàn 孙明焱的 合作伙伴有:吴炜炜、肖洁、任今涛等,他(她)在北京聚云科技有限公司、杭州聚合云成科技有限公司、北京聚... 企查查 <i>\\ue62b</i> <h> 猎豹移动与生态伙伴共建,旗下猎户星空助力深圳安吉诺智能... </h> 2025年3月28日 在发布仪式环节，猎户星空战略合作伙伴金承诺国际集团有限公司董事长叶明武、金承诺旗下安吉诺智能机器人有... 傅盛 、猎豹移动高级副总裁 孙明焱 ... 千龙网 <i>\\ue62b</i> 相关搜索 <t><t><t><t> 傅盛投资了多少家公司 </t><t> 傅国胜全国排名 </t></t><t><t> 傅国强个人简介资料 </t><t> 傅盛是哪里人 </t></t><t><t> 傅方俊是哪家公司的 </t><t> 傅山集团是干什么的 </t></t><t><t> 傅元中央音乐学院 </t><t> 傅斌星和傅梅城的关系 </t></t><t><t> 傅千万是谁 </t><t> 傅修延与詹艾斌 </t></t></t></t> 1 2 3 4 5 6 7 8 9 10 下一页 > 帮助 举报 用户反馈 企业推广 </b>"
                )
            ),
        )
        result = await SingleActionAgent.a_invoke(q)
        self.assertNotIn("VISION", result.plan.content)


if __name__ == "__main__":
    import asyncio
    import sys
    import unittest

    # 获取要运行的测试方法名
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
    else:
        test_name = "test_speak"  # 默认运行的测试方法

    # 异步运行单个测试方法
    async def run_single_test():
        test = TestSingleAgent(test_name)
        try:
            # 手动调用setUp（如果有）
            if hasattr(test, "asyncSetUp"):
                await test.asyncSetUp()
            # 运行测试方法
            test_method = getattr(test, test_name)
            await test_method()
            print(f"✅ 测试 {test_name} 成功")
        except Exception as e:
            print(f"❌ 测试 {test_name} 失败: {e}")
            import traceback

            traceback.print_exc()
        finally:
            # 手动调用tearDown（如果有）
            if hasattr(test, "asyncTearDown"):
                await test.asyncTearDown()

    # 运行测试
    asyncio.run(run_single_test())
