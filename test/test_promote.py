import json
import os

os.environ["OPENAI_API_KEY"] = "***************************************************"

import unittest

from redis import Redis

from src.action.action_version.version_manager import ActionVersionManager
from src.action.actions import Opk_Promote_App_Id
from src.action.retrieve import MaterialRetriever
from src.action.server_function import (
    case_introduction,
    home,
    production_function,
    record_feedback,
    sales_pitch,
)
from src.agent_core.models.model import AgentParameter
from src.agent_core.single_action import SingleActionAgent
from src.common.agent_config import PROMOTE_AGENT_ID
from src.session_manager.chat_context import (
    ChatAction,
    ChatEvent,
    ChatParameter,
    ChatResult,
)
from src.session_manager.memory import Memory
from src.session_manager.robot import InterfaceState, Robot
from src.settings import agent_setting
from src.utils.llm import LLMConfig, LLMManager

candidate_actions = ActionVersionManager().fetch_actions_by_version("draft")

testcase_redis_client = Redis(
    host=agent_setting.redis_host,
    port=agent_setting.redis_port,
    db=agent_setting.test_case_redis_db,
)


class TestPromoteAgent(unittest.IsolatedAsyncioTestCase):
    def tearDown(self):
        MaterialRetriever.reload_qdrant_client()

    async def test_communication(self):
        # 1. 用户初次见面 --> 首页
        p = AgentParameter(
            query="你好，你们都有啥东西",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_communication",
            ),
            robot=Robot(
                agent_id=PROMOTE_AGENT_ID,
                interface_state=InterfaceState(
                    app_id=Opk_Promote_App_Id,
                ),
            ),
        )
        p.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(p)

        self.assertTrue("HOME" in result.plan.content or "SAY" in result.plan.content)
        home_result = await home(
            __robot=p.robot,
            __memory=p.memory,
        )
        p.memory.commit_chat_user_message(text="你好，你们都有啥东西")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                display_name="首页",
                name="HOME",
                result=[
                    ChatResult(
                        name="text_content",
                        value=home_result.result["text_content"],
                        type="page_info",
                    )
                ],
            ),
        )

        p.query = "我想看看你们那个梯控功能"
        result = await SingleActionAgent.a_invoke(p)
        # 2. 用户询问产品功能 --> 产品功能介绍
        self.assertTrue("PRODUCT_FUNCTION" in result.plan.content)

        function_result = await production_function(
            __robot=p.robot,
            __memory=p.memory,
            _USER_QUERY="我想看看你们那个梯控功能",
            production_function="豹小秘2-梯控功能",
        )
        print(f"query:我想看看你们那个梯控功能. audio text:{function_result.audio}")
        # model_result = await LLMManager.a_call_openai_api(
        #     messages=function_result.audio_result.messages,
        #     model=LLMConfig(**function_result.audio_result.llm_config),
        # )
        p.memory.commit_chat_user_message(text="我想看看你们那个梯控功能")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="PRODUCT_FUNCTION",
                parameters=[
                    ChatParameter(
                        name="production_function",
                        value="豹小秘2-梯控功能",
                    ),
                ],
                result=[
                    ChatResult(
                        name="title",
                        value=function_result.result["title"],
                        type="string",
                    ),
                    ChatResult(
                        name="url", value=function_result.result["url"], type="HttpUrl"
                    ),
                    ChatResult(
                        name="type", value=function_result.result["type"], type="string"
                    ),
                ],
            )
        )
        # p.memory.commit_chat_assistant_message(text=model_result.content)

        p.query = ""
        p.memory.commit_chat_user_message(event=ChatEvent(desc="用户长时间无交互"))
        result = await SingleActionAgent.a_invoke(p)
        # 3.用户无反馈 --> 推荐
        self.assertTrue("SALES_PITCH" in result.plan.content)

        result = await sales_pitch(
            __memory=p.memory,
            __robot=p.robot,
        )

        model_result = await LLMManager._a_call_openai_api(
            messages=result.audio_result.messages,
            model=LLMConfig(**result.audio_result.llm_config),
        )
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                display_name="说",
                name="SALES_PITCH",
            )
        )
        p.memory.commit_chat_assistant_message(text=model_result.content)

        p.query = "你知道豹小秘15吗"
        result = await SingleActionAgent.a_invoke(p)
        # 4. 用户追问 --> 回答用户问题
        self.assertTrue("SALES_PITCH" in result.plan.content)

        result = await sales_pitch(
            __memory=p.memory,
            __robot=p.robot,
        )

        model_result = await LLMManager._a_call_openai_api(
            messages=result.audio_result.messages,
            model=LLMConfig(**result.audio_result.llm_config),
        )
        p.memory.commit_chat_user_message(text="你知道豹小秘15吗")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="SALES_PITCH",
                display_name="推销",
            )
        )
        p.memory.commit_chat_assistant_message(text=model_result.content)

        p.query = "行吧，上面那个走楼梯的功能看起来还不错，就是不实用"
        # 5. 用户反馈 --> 记录反馈
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "RECORD_FEEDBACK" in result.plan.content
            or "SALES_PITCH" in result.plan.content
        )

        await record_feedback(
            production_function="豹小秘2-梯控功能",
            user_review="行吧，上面那个走楼梯的功能看起来还不错，就是不实用",
        )
        p.memory.commit_chat_user_message(
            text="行吧，上面那个走楼梯的功能看起来还不错，就是不实用"
        )

        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="RECORD_FEEDBACK",
                display_name="记录反馈",
                parameters=[
                    ChatParameter(
                        name="production_function",
                        value="豹小秘2-梯控功能",
                    ),
                    ChatParameter(
                        name="user_review",
                        value="行吧，上面那个走楼梯的功能看起来还不错，就是不实用",
                    ),
                ],
            )
        )

        # 6.询问价格 -> 展示联系方式
        p.query = "你们那个豹小秘多少钱"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SHOW_CONTACT_INFORMATION" in result.plan.content)
        p.memory.commit_chat_user_message(text="你们那个豹小秘多少钱")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="SHOW_CONTACT_INFORMATION", display_name="展示联系方式"
            )
        )

        # 7. 用户结束对话
        p.query = "好的，谢谢"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)
        p.memory.commit_chat_user_message(text="好的，谢谢")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(display_name="感谢体验", name="SAY")
        )

    async def test_answer_user_question(self):
        interface_info = [
            {
                "id": 1,
                "title": "豹小秘2",
                "subtitle": "大模型时代专属你的AI机器人数字讲解员",
                "description": "软硬件功能：软硬件深度融合，实现高效稳定的系统运行。\n开箱即用：无需复杂配置，通电即可投入使用。\n梯控功能：支持电梯联动，实现跨楼层自主移动。\n数字讲解：提供清晰生动的语音和屏幕展示讲解，尽显企业智能化。\n硬件介绍：搭载高性能硬件，确保流畅高效的任务执行。",
            },
            {
                "id": 2,
                "title": "豹厂通",
                "subtitle": "AI时代工厂的搬运担当",
                "description": "跟随配货：智能识别并跟随目标，跟随工人检货并自动完成物品配送。\n避障功能：实时感知环境，自主避开障碍物，确保安全运行。",
            },
            {
                "id": 3,
                "title": "招财豹",
                "subtitle": "AI时代餐厅的效率担当",
                "description": "硬件介绍：搭载高性能硬件，确保稳定高效的运行体验。\n智能营销：结合场景互动，实现精准信息传递和用户引导。\n多机协作：多台机器人协同作业，提升整体工作效率。\n智能避让：实时感知动态环境，灵活避让行人和障碍物。\n自动矫正巡线路径：自主识别偏差，实时调整并优化巡线路径。",
            },
        ]

        chat_history = """<Me> SAY '你好'
<User> SAY '这三号'
<Robot> Executed Action DONE. PRODUCT_DETAIL(production=招财豹) Result:{"product's description is": 'The product detail data', "product_detail's description is": 'The Robotdia resources of the product'}
<Robot> SAY '欢迎体验招财豹，餐厅效率新高度！'
<Robot> SAY '嘿！这款招财豹，餐厅效率神器！屏幕上都有详细信息，快来看看吧！'
<Robot> Executed Action DONE. SALES_PITCH() Result:{}
<User> SAY '这三个...屏幕上这三个机器人在功能上有什么差别或者硬件上有什么差别吗'"""
        p = AgentParameter(
            query=chat_history,
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_answer_user_question",
            ),
            robot=Robot(
                agent_id=PROMOTE_AGENT_ID,
                interface_state=InterfaceState(
                    app_id=Opk_Promote_App_Id,
                    interface_info=json.dumps(
                        interface_info, ensure_ascii=False, indent=2
                    ),
                ),
            ),
        )
        p.memory.clear_chat_history()

        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)

    async def test_promote_recommend(self):
        chat_history = """
<Robot> SAY '你好'
<User> SAY '你好'
<Robot> SAY '嘿嘿，我是豹小秘2！开箱即用，轻松实现智能讲解，快来看看吧！'
<Robot> Executed Action. SALES_PITCH() Result:{}
<User> SAY '你们都有什么产品'
<Robot> Executed Action. HOME() Result:{'data': '...'}
<Robot> SAY '嘿！还有豹厂通和招财豹，都是AI时代的好帮手哦！快来了解一下吧！'
<Robot> Executed Action. SALES_PITCH() Result:{}
<User> Triggered Event [desc='用户不说话4s']
<User> SAY '你们这三款产品有什么区别吗'"""
        screen_info = """[{\"id\": 1, \"title\": \"豹小秘2\", \"subtitle\": \"大模型时代专属你的AI机器人数字讲解员\", \"description\": \"软硬件功能：软硬件深度融合，实现高效稳定的系统运行。\\n开箱即用：无需复杂配置，通电即可投入使用。\\n梯控功能：支持电梯联动，实现跨楼层自主移动。\\n数字讲解：提供清晰生动的语音和屏幕展示讲解，尽显企业智能化。\\n硬件介绍：搭载高性能硬件，确保流畅高效的任务执行。\"}, {\"id\": 2, \"title\": \"豹厂通\", \"subtitle\": \"AI时代工厂的搬运担当\", \"description\": \"跟随配货：智能识别并跟随目标，跟随工人检货并自动完成物品配送。\\n避障功能：实时感知环境，自主避开障碍物，确保安全运行。\"}, {\"id\": 3, \"title\": \"招财豹\", \"subtitle\": \"AI时代餐厅的效率担当\", \"description\": \"硬件介绍：搭载高性能硬件，确保稳定高效的运行体验。\\n智能营销：结合场景互动，实现精准信息传递和用户引导。\\n多机协作：多台机器人协同作业，提升整体工作效率。\\n智能避让：实时感知动态环境，灵活避让行人和障碍物。\\n自动矫正巡线路径：自主识别偏差，实时调整并优化巡线路径。\"}]"""

        p = AgentParameter(
            query=chat_history,
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_promote_recommend",
            ),
            robot=Robot(
                agent_id=PROMOTE_AGENT_ID,
                interface_state=InterfaceState(
                    app_id=Opk_Promote_App_Id,
                    interface_info=screen_info,
                ),
            ),
        )
        p.memory.clear_chat_history()
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)

    async def test_sense(self):
        p = AgentParameter(
            query="我是商品储运的，有什么推荐的产品吗",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_sense",
            ),
            robot=Robot(
                agent_id=PROMOTE_AGENT_ID,
                interface_state=InterfaceState(
                    app_id=Opk_Promote_App_Id,
                ),
            ),
        )
        p.memory.clear_chat_history()
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)
        p.memory.commit_chat_user_message(text="我是商品储运的，有什么推荐的产品吗")

        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="SALES_PITCH", display_name="推销")
        )
        sales_pitch_result = await sales_pitch(
            __memory=p.memory,
            __robot=p.robot,
        )
        model_result = await LLMManager._a_call_openai_api(
            messages=sales_pitch_result.audio_result.messages,
            model=LLMConfig(**sales_pitch_result.audio_result.llm_config),
        )
        p.memory.commit_chat_assistant_message(text=model_result.content)

        p.query = "开超市的呢"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)

    async def test_experience(self):
        p = AgentParameter(
            query="你们有那些机器人？",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_experience",
            ),
            robot=Robot(
                agent_id=PROMOTE_AGENT_ID,
                interface_state=InterfaceState(
                    app_id=Opk_Promote_App_Id,
                ),
            ),
        )

        p.memory.clear_chat_history()
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("HOME" in result.plan.content)

        p.memory.commit_chat_user_message(text="你们有那些机器人？")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="HOME", display_name="首页")
        )

        p.query = "介绍一下招财豹"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "PRODUCT_DETAIL" in result.plan.content
            or "SALES_PITCH" in result.plan.content
        )

        p.memory.commit_chat_user_message(text="介绍一下招财豹")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="PRODUCT_DETAIL", display_name="产品详情")
        )

        p.query = "有什么可以体验的吗？"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)

        p.memory.commit_chat_user_message(text="有什么可以体验的吗？")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="SALES_PITCH", display_name="推销")
        )

        p.query = "ok,帮我查一下去杭州的机票吧"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("OPEN_WEB_URL" in result.plan.content)
        self.assertTrue("flights.ctrip.com/online/list/oneway" in result.plan.content)
        p.memory.commit_chat_user_message(text="ok,帮我查一下去杭州的机票吧")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="OPEN_WEB_URL",
                display_name="打开网页",
                parameters=[
                    ChatParameter(
                        name="url", value="flights.ctrip.com/online/list/oneway-PEK-HGH"
                    ),
                ],
            )
        )

        p.query = "挺不错的"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("RECORD_FEEDBACK" in result.plan.content)

        p.memory.commit_chat_user_message(text="挺不错的")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="RECORD_FEEDBACK", display_name="记录反馈")
        )

        p.query = "帮我送个快递吧"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)

        p.memory.commit_chat_user_message(text="帮我送个快递吧")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="SALES_PITCH", display_name="推销")
        )
        p.memory.commit_chat_assistant_message(
            text="您好，我还不能帮您送快递，请您稍后再试。"
        )

        p.query = "那我走了"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "SALES_PITCH" in result.plan.content or "HOME" in result.plan.content
        )
        p.memory.commit_chat_user_message(text="那我走了")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(name="SALES_PITCH", display_name="推销")
        )
        p.memory.commit_chat_assistant_message(
            text="不过，我有其他产品可以推荐给您，请稍等。"
        )
        p.robot.interface_state.interface_info = json.dumps(
            {
                "id": 1,
                "title": "豹小秘2",
                "subtitle": "大模型时代专属你的AI机器人数字讲解员",
                "description": "软硬件功能：软硬件深度融合，实现高效稳定的系统运行。\n开箱即用：无需复杂配置，通电即可投入使用。\n梯控功能：支持电梯联动，实现跨楼层自主移动。\n数字讲解：提供清晰生动的语音和屏幕展示讲解，尽显企业智能化。\n硬件介绍：搭载高性能硬件，确保流畅高效的任务执行。",
            },
            ensure_ascii=False,
            indent=2,
        )
        p.query = "播放视频"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SALES_PITCH" in result.plan.content)

        p.robot.interface_state.interface_info = json.dumps(
            {
                "status": "视频窗: 等待用户点击播放按钮",
                "content": {
                    "id": 1,
                    "title": "豹小秘2",
                    "subtitle": "大模型时代专属你的AI机器人数字讲解员",
                    "description": "软硬件功能：软硬件深度融合，实现高效稳定的系统运行。\n开箱即用：无需复杂配置，通电即可投入使用。\n梯控功能：支持电梯联动，实现跨楼层自主移动。\n数字讲解：提供清晰生动的语音和屏幕展示讲解，尽显企业智能化。\n硬件介绍：搭载高性能硬件，确保流畅高效的任务执行。",
                },
            },
            ensure_ascii=False,
            indent=2,
        )
        p.query = "播放视频"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("MULTIMEDIA_PLAY" in result.plan.content)

        p.query = "介绍一个图书馆场景的应用案例"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue(
            "PRODUCT_FUNCTION" in result.plan.content and "case" in result.plan.content
        )

        p.memory.commit_chat_user_message(text="介绍一个图书馆场景的应用案例")

        case_result = await case_introduction(__memory=p.memory, __robot=p.robot)
        self.assertTrue("图书馆" in case_result.audio_result.text)

    async def test_play_video(self):
        p = AgentParameter(
            query="带我看视频",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_play_video",
            ),
            robot=Robot(
                agent_id=PROMOTE_AGENT_ID,
                interface_state=InterfaceState(
                    app_id=Opk_Promote_App_Id,
                ),
            ),
        )

        p.memory.commit_chat_assistant_message(
            text="豹厂通的最大载重是100公斤，包含托盘或货架的重量。了解更多关于工厂场景应用的视频，您可以查看“豹厂通-工厂场景案例视频”。如果您对产品规格感兴趣，我可以提供详细的规格图供您参考。"
        )
        p.memory.commit_chat_user_message(text="他最快的行进速度是多少")
        p.memory.commit_chat_assistant_message(
            text="豹厂通的最快行进速度是1.5米每秒，确保高效的物流操作。对于详细的产品规格，您可以查看“豹厂通规格图”。这将帮助您更好地了解豹厂通在工厂中的应用潜力。"
        )
        p.memory.commit_chat_user_message(event=ChatEvent(desc="用户不说话4s"))
        p.memory.commit_chat_user_message(text="他总是这个算就是个force")
        p.memory.commit_chat_assistant_message(
            text="豹厂通以其卓越的智能化交互和灵活部署能力，"
        )
        p.memory.commit_chat_user_message(text="我要看豹厂通...我要看豹厂通那个什么图")
        p.memory.commit_chat_assistant_message(text="您可以查看豹厂通的产品参数规格。")
        p.memory.commit_chat_assistant_message(
            action=ChatAction(
                name="PRODUCT_FUNCTION",
                display_name="产品规格",
                parameters=[
                    ChatParameter(name="production_function", value="豹厂通-产品规格")
                ],
                result=[
                    ChatResult(
                        name="title", value="豹厂通-产品参数规格", type="string"
                    ),
                    ChatResult(name="url", value="https://te...", type="HttpUrl"),
                    ChatResult(name="type", value="image", type="string"),
                ],
            )
        )
        p.memory.commit_chat_assistant_message(
            text="您对豹厂通的兴趣让我感到兴奋！为了更深入了解它在工厂中的实际应用，我建议您观看“豹厂通-工厂场景案例视频”。这个视频将展示它如何在复杂环境中高效运作，把握机会，进一步探索它为您企业带来的潜在价值吧！"
        )
        p.robot.interface_state.interface_info = """{\"status\":\"图片展示\",\"content\":{\"title\":\"豹厂通-产品参数规格\",\"text_content\":\"产品参数\\n产品最终参数以出厂规格书为准\\n豹厂通 标准版\\n 机身尺寸：650 × 525 × 1377mm\\n 货架尺寸：-\\n 产品净重：48kg\\n 承重：100kg\\n豹厂通 托盘版\\n 机身尺寸：650 × 525 × 1377mm\\n 货架尺寸：-\\n 产品净重：58kg\\n 承重：每托盘30公斤，共100公斤（含托盘）\\n豹厂通 货架版\\n 机身尺寸：650 × 525 × 1377mm\\n 货架尺寸：405 × 825 × 937mm\\n 产品净重：67kg（机器本体48kg + 货架19kg）\\n 承重：100kg（含货架）\\n屏幕尺寸：14英寸，全高清1080p\\n 导航技术：VSLAM+\\n Mic阵列：6麦克全域收音与降噪，360°音源定位\\n 硬件平台：高通骁龙芯片 + 工业级MCU\\n 续航时间：13h以上（空载，大面积地图）\\n 充电时间：4.5小时\\n 操作系统：基于Android9.0深度定制的RobotOS机器人操作系统\\n 网络支持：4G，WIFI支持2.4G/5G\\n 导航传感器：激光雷达 × 1、深度视觉传感器 × 3、鱼眼摄像头 × 2、红外摄像头 × 2、里程计、惯性测量单元 × 1\\n\"}}"""
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("CASE_INTRODUCTION" in result.plan.content)

    async def test_find_person(self):
        p = AgentParameter(
            query="找一下孙名言",
            candidate_actions=candidate_actions,
            memory=Memory(
                redis_client=testcase_redis_client,
                device_id="test_find_person",
            ),
        )
        p.memory.clear_chat_history()
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SEND_MESSAGE" in result.plan.content)

        p.query = "把张兰叫来"
        result = await SingleActionAgent.a_invoke(p)
        self.assertTrue("SEND_MESSAGE" in result.plan.content)


if __name__ == "__main__":
    unittest.main()
