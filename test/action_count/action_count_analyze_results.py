import pandas as pd
from pathlib import Path


def analyze_test_results(csv_path):
    """分析测试结果并生成报告"""
    # 读取CSV文件
    df = pd.read_csv(csv_path)

    # 按action_count分组计算统计数据
    grouped_stats = (
        df.groupby("action_count")
        .agg(
            {
                "execution_time": [
                    "mean",
                    "std",
                    "min",
                    "max",
                    lambda x: x.quantile(0.90),  # P90
                    lambda x: x.quantile(0.95),  # P95
                ],
                "input_tokens": [
                    "mean",
                    "std",
                    "min",
                    "max",
                    lambda x: x.quantile(0.90),  # P90
                    lambda x: x.quantile(0.95),  # P95
                ],
                "output_tokens": [
                    "mean",
                    "std",
                    "min",
                    "max",
                    lambda x: x.quantile(0.90),  # P90
                    lambda x: x.quantile(0.95),  # P95
                ],
                "total_tokens": [
                    "mean",
                    "std",
                    "min",
                    "max",
                    lambda x: x.quantile(0.90),  # P90
                    lambda x: x.quantile(0.95),  # P95
                ],
                "cache_tokens": ["mean", "sum"],
                "response_length": [
                    "mean",
                    "std",
                    "min",
                    "max",
                    lambda x: x.quantile(0.90),  # P90
                    lambda x: x.quantile(0.95),  # P95
                ],
            }
        )
        .round(2)
    )

    # 重命名P90和P95列
    grouped_stats.columns = [
        f"{col[0]}_{col[1]}"
        if col[1] != "<lambda_0>" and col[1] != "<lambda_1>"
        else f"{col[0]}_p90"
        if col[1] == "<lambda_0>"
        else f"{col[0]}_p95"
        for col in grouped_stats.columns
    ]

    # 创建输出目录
    output_dir = Path("test_results")
    output_dir.mkdir(exist_ok=True)

    # 保存统计结果
    grouped_stats.to_csv(output_dir / "statistics_summary.csv")

    # 生成文本报告
    with open(output_dir / "analysis_report.txt", "w", encoding="utf-8") as f:
        f.write("测试结果分析报告\n")
        f.write("=" * 50 + "\n\n")

        f.write("1. 执行时间分析\n")
        f.write("-" * 20 + "\n")
        for action_count in df["action_count"].unique():
            subset = df[df["action_count"] == action_count]
            f.write(f"\n{action_count}个动作:\n")
            f.write(f"  平均执行时间: {subset['execution_time'].mean():.2f}秒\n")
            f.write(f"  标准差: {subset['execution_time'].std():.2f}秒\n")
            f.write(f"  最小值: {subset['execution_time'].min():.2f}秒\n")
            f.write(f"  最大值: {subset['execution_time'].max():.2f}秒\n")
            f.write(f"  P90: {subset['execution_time'].quantile(0.90):.2f}秒\n")
            f.write(f"  P95: {subset['execution_time'].quantile(0.95):.2f}秒\n")

        f.write("\n2. Token使用情况\n")
        f.write("-" * 20 + "\n")
        for action_count in df["action_count"].unique():
            subset = df[df["action_count"] == action_count]
            f.write(f"\n{action_count}个动作:\n")
            f.write(
                f"  输入tokens - 平均: {subset['input_tokens'].mean():.2f}, P90: {subset['input_tokens'].quantile(0.90):.2f}, P95: {subset['input_tokens'].quantile(0.95):.2f}\n"
            )
            f.write(
                f"  输出tokens - 平均: {subset['output_tokens'].mean():.2f}, P90: {subset['output_tokens'].quantile(0.90):.2f}, P95: {subset['output_tokens'].quantile(0.95):.2f}\n"
            )
            f.write(
                f"  总tokens - 平均: {subset['total_tokens'].mean():.2f}, P90: {subset['total_tokens'].quantile(0.90):.2f}, P95: {subset['total_tokens'].quantile(0.95):.2f}\n"
            )

        f.write("\n3. 响应长度分析\n")
        f.write("-" * 20 + "\n")
        for action_count in df["action_count"].unique():
            subset = df[df["action_count"] == action_count]
            f.write(f"\n{action_count}个动作:\n")
            f.write(f"  平均响应长度: {subset['response_length'].mean():.2f}字符\n")
            f.write(f"  最短响应: {subset['response_length'].min()}字符\n")
            f.write(f"  最长响应: {subset['response_length'].max()}字符\n")
            f.write(f"  P90: {subset['response_length'].quantile(0.90):.2f}字符\n")
            f.write(f"  P95: {subset['response_length'].quantile(0.95):.2f}字符\n")

        f.write("\n4. 缓存效果分析\n")
        f.write("-" * 20 + "\n")
        f.write(f"缓存命中次数: {len(df[df['cache_tokens'] > 0])}\n")
        f.write(f"总测试次数: {len(df)}\n")
        f.write(f"缓存命中率: {len(df[df['cache_tokens'] > 0]) / len(df) * 100:.2f}%\n")

        # 打印详细的分组统计
        f.write("\n5. 详细统计数据\n")
        f.write("-" * 20 + "\n")
        f.write("\n按动作数量分组的统计：\n")
        f.write(grouped_stats.to_string())


if __name__ == "__main__":
    # 使用最新的测试结果文件
    latest_csv = Path("test_results") / "combined_results.csv"
    analyze_test_results(latest_csv)
    print("分析完成！请查看 test_results 目录下的报告。")
