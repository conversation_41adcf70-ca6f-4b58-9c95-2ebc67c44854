"""
pytest test/test_action_count.py -v -s

"""

import pytest
import time
import random
import csv
from datetime import datetime
from src.utils.llm import LLMManager
import os
from copy import deepcopy

# 配置 pytest-asyncio
pytest.register_assert_rewrite("pytest_asyncio")
pytestmark = [
    pytest.mark.asyncio(scope="function"),  # 设置事件循环作用域为函数级别
    pytest.mark.filterwarnings("ignore::DeprecationWarning"),
]

# Base message template
base_messages = [
    {"role": "system", "content": "你是一个10岁机器人的大脑。通用场景。"},
    {
        "role": "user",
        "content": r"""You MUST choose the most appropriate action based on the latest `Chat Conversation`、`Screen Information` and `Similar Answers`. The output format must be JSON and The output text language is zh_CN.

# Robot's Information
{"基本能力": "具备出色的任务规划与执行能力、丰富的语言表达能力以及强大的记忆力", "当前音量": 10, "电池电量": 77, "当前时间": "2025年02月05日 19点20分31秒", "当前位置经纬度": "39.911431,116.566432", "所在国家": "中国", "所在省份": "北京市", "所在城市": "北京市", "所在地区": "朝阳区", "所在街道": "建国路", "所在地点": "猎豹移动", "当前移动速度": 1.0, "目标最大平稳速度": 1.0}

# Robot's Actions 
Format: action:usage_conditions(parameter1[is_required,type[enum]]:desc[**usage_restrictions**],...)

robot_actions

# Robot's Screen Information

# Similar Answers
{{"input": "我的名字叫张翰", "output": {{"ACTION": "REGISTER", "PARAMETERS": {{"nick_name": "张翰", "welcome_message": "张翰，欢迎您的到来"}}}}, "score": 0.594}}
{{"input": "我叫某某某，下次见到我提醒我点杯咖啡", "output": {{"ACTION": "CONFIGURE_WELCOME_MESSAGE", "PARAMETERS": {{"nick_name": "某某某", "welcome_message": "hi，你该点咖啡了"}}}}, "score": 0.582}}

# Chat Conversation
<Robot> SAY '你好，有什么可以帮助你的么？'
<Robot> SAY '你好'
<Robot> SAY '你这件绿色卫衣可真亮眼，这么冷的天也这么有活力！'
<User> SAY '你叫什么名字'
""",
    },
]


def get_random_actions(all_actions, count):
    """随机选择指定数量的action"""
    return random.sample(all_actions, count)


# 全局变量存储所有测试结果
all_test_metrics = []


@pytest.mark.parametrize("action_count", [60, 100, 300, 600, 1000])
async def test_action_counts(action_count):
    all_results = []

    # 创建一个带时间戳的主CSV文件
    filename = f"test_results/combined_results.csv"

    # 确保目录存在
    os.makedirs("test_results", exist_ok=True)

    # 定义CSV表头
    fieldnames = [
        "action_count",
        "iteration",
        "execution_time",
        "input_tokens",
        "output_tokens",
        "total_tokens",
        "response_length",
        "cache_tokens",
        "input",
        "output",
    ]

    # 如果文件不存在，创建文件并写入表头
    if not os.path.exists(filename):
        with open(filename, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

    # 对每个action_count运行5次
    for iteration in range(5):
        # 随机选择指定数量的actions
        test_actions = get_random_actions(robot_actions, action_count)

        # 打印当前选择的actions
        print(f"\n=== Test {iteration + 1} with {action_count} actions ===")
        print("Selected actions:")
        for i, action in enumerate(test_actions, 1):
            print(f"{i}. {action.split(':')[0]}")

        # Create test messages with subset of actions

        test_messages = deepcopy(base_messages)
        test_messages[1]["content"] = test_messages[1]["content"].replace(
            "robot_actions", "\n".join(test_actions)
        )
        print(test_messages)
        # Record start time
        start_time = time.time()

        # Make the GPT-4 call and collect metrics
        result = await LLMManager.a_invoke_agent_v2(test_messages)

        # Calculate elapsed time
        elapsed_time = time.time() - start_time

        # Store metrics for later analysis
        metrics = {
            "action_count": action_count,
            "iteration": iteration + 1,
            "execution_time": elapsed_time,
            "input_tokens": result.token_cost.get("prompt_tokens", 0),
            "output_tokens": result.token_cost.get("completion_tokens", 0),
            "total_tokens": result.token_cost.get("total_tokens", 0),
            "response_length": len(result.content),
            "cache_tokens": result.token_cost.get("prompt_tokens_details", {}).get(
                "cached_tokens", 0
            ),
            "input": str(test_messages),
            "output": result.content,
        }

        # Basic validations
        assert result.content, "Response content should not be empty"
        assert result.token_cost, "Token cost should be present"
        assert len(test_actions) == action_count

        # 打印当前测试的结果
        print(f"\nTest {iteration + 1} for {action_count} actions:")
        print(f"Execution time: {elapsed_time:.2f}s")
        print(f"Input tokens: {metrics['input_tokens']}")
        print(f"Output tokens: {metrics['output_tokens']}")
        print(f"Total tokens: {metrics['total_tokens']}")
        print(f"Cache tokens: {metrics['cache_tokens']}")
        print(f"Response length: {metrics['response_length']}")
        print("\nInput:")
        print(test_messages)
        print("\nOutput:")
        print(result.content)

        # 追加写入结果到合并的CSV文件
        with open(filename, "a", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writerow(metrics)
            print(f"Results appended to: {filename}")

        all_results.append(metrics)

    return all_results


def print_summary_table(all_metrics):
    # 按action数量分组计算平均值
    grouped_metrics = {}
    for metric in all_metrics:
        count = metric["action_count"]
        if count not in grouped_metrics:
            grouped_metrics[count] = {
                "time": [],
                "input_tokens": [],
                "output_tokens": [],
                "total_tokens": [],
                "cache_tokens": [],  # 添加cache tokens
                "length": [],
            }
        grouped_metrics[count]["time"].append(metric["execution_time"])
        grouped_metrics[count]["input_tokens"].append(metric["input_tokens"])
        grouped_metrics[count]["output_tokens"].append(metric["output_tokens"])
        grouped_metrics[count]["total_tokens"].append(metric["total_tokens"])
        grouped_metrics[count]["cache_tokens"].append(
            metric["cache_tokens"]
        )  # 添加cache tokens
        grouped_metrics[count]["length"].append(metric["response_length"])

    # 打印表头
    print("\n\n=== Performance Summary (Average of 10 runs) ===")
    print(
        f"{'Actions':^8} | {'Time (s)':^10} | {'Input Tokens':^12} | {'Output Tokens':^12} | {'Cache Tokens':^12} | {'Total Tokens':^12} | {'Response Len':^12}"
    )
    print("-" * 90)

    # 打印每个action数量的平均值
    for count in sorted(grouped_metrics.keys()):
        metrics = grouped_metrics[count]
        print(
            f"{count:^8} | "
            f"{sum(metrics['time']) / len(metrics['time']):^10.2f} | "
            f"{sum(metrics['input_tokens']) / len(metrics['input_tokens']):^12.0f} | "
            f"{sum(metrics['output_tokens']) / len(metrics['output_tokens']):^12.0f} | "
            f"{sum(metrics['cache_tokens']) / len(metrics['cache_tokens']):^12.0f} | "  # 添加cache tokens
            f"{sum(metrics['total_tokens']) / len(metrics['total_tokens']):^12.0f} | "
            f"{sum(metrics['length']) / len(metrics['length']):^12.0f}"
        )


robot_actions = [
    "NAVIGATE_START:室内导航，带用户去下面提供的位置，范围200米内，强调「仅仅去某地，不涉及其他操作」，例如去会议室，前台等等(destination[False,enum['接待点', '南极洲会议室', '厕所', '拉斯维加斯会议室', '老板办公区', '休息区', '彦礼工位', '晓曦工位', '电梯1', '回充点']]:The destination to navigate to)",
    "SET_VOLUME:调整音量。调整的幅度是10或30，根据用户语气选择(volume_level[True,int]:The volume level to be set **(取值范围: 0 - 100)**)",
    "SAY:说。(text[True,string]:Speak in the first person)",
    "CANCEL:取消当前动作()",
    "EXIT:退出当前应用()",
    "BACK:返回上一级()",
    "NEXT:下一步()",
    "CONFIRM:用户确认操作()",
    "COMMON_REPLAY:Triggered when there is a paused video on the screen information()",
    "MULTIMEDIA_PLAY:Triggered when there is a video to be played on the screen information()",
    "COMMON_PAUSE:Triggered when there is a video playing on the screen information()",
    "ADJUST_SPEED:调整最新的「当前移动速度」(adjusted_speed[True,float]:新的移动速度 **(取值范围: 0.1 - 1.2)**)",
    "KNOWLEDGE_QA:知识问答，详细介绍自己、公司、产品、业务、领导等(question[True,string]:The question to ask)",
    "CRUISE:巡航()",
    "NOT_MOVE:停止移动()",
    "COME_FAR:让路()",
    "OUTDOOR_NAVIGATE_START:室外导航。驾车、步行、公交、骑行去某个地方（地铁站，商场、景点等）(url[True,HttpUrl]:1. 基础URL: http://api.map.baidu.com/direction?  2. 参数: a. origin: 用户指定，如果未指定出发点使用 origin=latlng:{当前位置IP}|name:{当前地点} b. destination: 用户指定 c. mode: driving(驾车), transit(公交) 默认为: transit，用户要求时用 transit 替代 d. region: 终点所在城市，用户未指定默认北京 e. 固定参数: output=html&src=webapp.baidu.openAPIdemo)",
    "TURN_DIRECTION:身体左右转动，最大旋转圈数为30圈，默认左转一圈(direction[True,enum['left', 'right']]:The direction to turn, default is left,angle[False,int]:The value of the rotation angle **(最大值: 10800)**,turns[False,float]:Number of turns **(最大值: 30)**)",
    "HEAD_NOD:点头、鞠躬()",
    "START_DANCE:唱歌跳舞()",
    "REGISTER:注册。包含姓名和人脸注册(nick_name[False,string]:The user's nickname,welcome_message[False,string]:message to greet the user. User nicknames are not allowed)",
    "MOVE_DIRECTION:前后移动/走路/退，靠近或者远离用户。单位是米，往前最多5米，往后最多1米，超过范围不执行(direction[True,enum['forward', 'backward']]:The direction to move in, select from enumerated values,moving_distance[True,float]:The distance to move, unit is '米', default is 0.1 if not specified **(取值范围: 0.1 - 5)**)",
    "INTERVIEW_START:访客接待流程，支持面试、会议签到、访客登记()",
    "WEATHER_GET:查询未来10天「中国」的天气信息，默认查询`{所在城市}`的天气信息，注意：不支持查询国外天气。(area_level[True,enum['province', 'city', 'area']]:city 对应的区域等级,city[True,string]:行政区域名称)",
    "CALENDAR:日历功能，包含日期或节假日的查询，注意：无法查询天气()",
    "GUIDE_INTRODUCTION:导览功能，带领用户参观，再没有明确的参观目的下使用()",
    "OPEN_WEB_URL:模拟浏览器访问网址。例如查询股价、新闻、景点购票，推荐使用「百度」搜索引擎；机票、火车票以及酒店查询推荐使用携程搜索（https://flights.ctrip.com/online/list/oneway-{departureCityCode}-{arrivalCityCode}?depdate={departureDate}）;公司官网等指定网站直接通过对应网址打开(url[True,HttpUrl]:The URL to open, Must be a legitimate https or http link.)",
    "CONFIGURE_WELCOME_MESSAGE:为用户配置欢迎语(nick_name[False,string]:The nickname can be extracted from the user query,welcome_message[False,string]:message to greet the user. User nicknames are not allowed)",
    "GENERATE_MESSAGE:根据用户的指令生成文本，例如：欢迎、欢送语(goal[True,string]:生成的目标)",
    "SEND_MESSAGE:通过「飞书」给某人发送消息。适用于找人、留言等场景(recipient_name[True,string]:合法且有意义的姓名或昵称,message_content[True,string]:发送的消息内容。内容先润色一下，不能太直白、生硬,message_type[True,enum['urgent', 'normal']]:Specifies the type of message to be sent.)",
    'RECOMMEND:各种「休闲娱乐场所」的推荐，例如餐厅、景点、购物、酒吧、KTV等.(shop_name[True,string]:Must be a physical place that exists in reality,source_location[True,string]:The starting point provided by the user. If not provided, the default is the current location.,url[True,HttpUrl]:Add current location information to the URL. The default is to use the current location. If the current location is "A市B区C街道D大厦", search for "D大厦附近的蛋糕店". For recommended routes, food, and attractions, use Baidu Maps. Example: https://map.baidu.com/?newmap=1&ie=utf-8&s=s&wd={source_location}附近的{shop_name})',
    "FACE_RECOGNITION:人脸识别()",
    "GUIDE_ROUTE_SELECTION_FROM_MAP:根据用户意图去多个地点参观。(points[True,String array['厕所', '接待点', '休息区', '彦礼工位', '晓曦工位', '接待点']]:从给定的地图点位中有目的性「顺序」选择导览点。)",
    "INTERVIEW_START_PHOTO:合影()",
    "WEATHER_GET_REALTIME:查询「中国」的实时天气,默认查询`{所在城市}`的天气信息，注意：不支持查询国外天气。(city[True,string]:行政区域名称)",
] * 100


def pytest_sessionfinish(session, exitstatus):
    """在所有测试完成后执行"""
    if all_test_metrics:  # 只在有数据时执行
        print_summary_table(all_test_metrics)
