"""
pytest test/action/server_functions/test_sales_pitch.py -v --capture=no
pytest test/action/server_functions/test_sales_pitch.py -v -k "test_interactive_features" --capture=no
"""

from unittest.mock import MagicMock

import pytest

from src.action.server_function import sales_pitch
from src.common.agent_config import PROMOTE_AGENT_ID, Opk_Promote_App_Id
from src.session_manager.chat_context import (
    ChatAction,
    ChatEvent,
    ChatMessage,
    ChatParameter,
)
from src.utils.llm import LLMConfig, LLMManager


def get_test_screen_info():
    """获取测试用的屏幕信息"""
    return {
        "status": "页面显示",
        "content": {
            "title": "首页",
            "text_content": """[{
                "id": 1,
                "title": "豹小秘2",
                "subtitle": "大模型时代专属你的AI机器人数字讲解员",
                "description": "软硬件功能：软硬件深度融合，实现高效稳定的系统运行。\\n开箱即用：无需复杂配置，通电即可投入使用。\\n梯控功能：支持电梯联动，实现跨楼层自主移动。\\n数字讲解：提供清晰生动的语音和屏幕展示讲解，尽显企业智能化。\\n硬件介绍：搭载高性能硬件，确保流畅高效的任务执行。"
            }, {
                "id": 2,
                "title": "豹厂通",
                "subtitle": "AI时代工厂的搬运担当",
                "description": "跟随配货：智能识别并跟随目标，跟随工人检货并自动完成物品配送。\\n避障功能：实时感知环境，自主避开障碍物，确保安全运行。"
            }]""",
        },
    }


def get_test_knowledge_content():
    """获取测试用的知识库内容"""
    return """
### 猎户星空大模型机器人-AI讲解员-豹小秘2 ###
【豹小秘2】技术参数
产品配置
整机尺寸：561mm×525mm×1350mm
整机净重：56kg
产品配色：银色和枪灰色
机身材质：高强度PC+ABS
屏幕尺寸：14英寸1080P超清显示器
颈部负载空间：长104mm×宽80mm×高16mm
胸腔负载空间：长135mm×宽129mm×高190mm
胸腔负重：5kg
头部俯仰角度：-15°至40°
爬坡能力：5°
运动速度：0.5~1.2m/s（可调）
硬件平台：高通8核芯片+工业级MCU
操作系统：基于Android9.0深度定制的RobotOS机器人操作系统
RAM(内存)：8G
ROM(容量)：64G
导航传感器：激光雷达 ×1+ 深度视觉传感器 ×3+ 鱼眼摄像头 ×1+ 红外回充摄像头 ×1+ 里程计 + 惯性测量单元 ×1
Mic阵列：6麦阵全域收音与降噪，360°音源定位，5米收音范围
续航时间：14~16小时 (根据实际工作条件)
定位精度：厘米级
支持二开接口：USB-A 2.0*2，USB-C*1；百兆网口 *1；输出电源：DC 12V/3A * 1
网络支持：4G（支持TDD-LTE、FDD-LTE）,WiFi支持2.4G/5G
电池：三元锂电池
充电方式：充电桩自动回充
充电时间：4.5小时

【豹小秘2】产品硬件特色：
14英寸超清屏幕：14英寸超大高清屏幕，可视面积增加92%，交互视角更开阔，交互体验更舒适。
智能炫酷LED氛围灯：LED氛围灯，可根据转向、充电等功能，进行变化和指示，灯光交互更智能更人性化。
充足的硬件扩展接口：新增标准USB-A、USB-C接口、LAN百兆网口和12V标准DC供电接口，为机器人融入多元场景预留充裕扩展可能。
底盘更强劲稳定：续航升级至14小时，全新研发的悬挂系统提升了平稳性和通过性，性能强劲，安全性再升级。
高精度语音识别：行业领先的6麦克风唤醒阵列，5米收音范围，75dB噪音环境下识别率94%，语音识别精准可靠，声音清晰悦耳。
丰富的载物扩展空间：胸口下方预留小票打印机等设备空间，背部也设计了放置储物筐的卡扣，可承载更多物品。手臂可拆卸，以承载更大重量的外接设备。
厘米级定位、导航与避障：建图快，定位准，拥有3个RGBD传感器，感知范围再次扩容。通过多传感器融合技术实现精准场景还原，配合精准路线规划算法，实现厘米级实时定位，安全避障、准确导航。
"""


def create_mock_robot():
    """创建mock机器人对象"""
    mock_robot = MagicMock()
    mock_robot.agent_id = PROMOTE_AGENT_ID
    mock_robot.interface_state.app_id = Opk_Promote_App_Id
    mock_robot.interface_state.interface_info = get_test_screen_info()
    mock_robot.robot_status_info.return_value = {
        "battery": 80,
        "charging": False,
        "position": {"x": 0, "y": 0},
    }
    return mock_robot


async def get_sales_pitch_response(messages, user_query=""):
    """获取sales_pitch的响应"""
    mock_robot = create_mock_robot()
    mock_memory = MagicMock()
    mock_memory.get_chat_context.return_value.messages = messages

    test_kwargs = {
        "__robot": mock_robot,
        "__memory": mock_memory,
        "_USER_QUERY": user_query,
        "_CURRENT_SUMMARY": user_query,
        "extra_doc": get_test_knowledge_content(),
    }

    result = await sales_pitch(**test_kwargs)
    response = await LLMManager._a_invoke_llm(
        messages=result.content.audio_request.content.messages,
        model=LLMConfig(**result.content.audio_request.content.llm_config),
    )

    print("\nTest Scenario:")
    print("User Query:", user_query)
    print("\nLLM Response:")
    print(response.content)
    print(f"Token cost: {response.token_cost}")

    return response.content


def save_test_results(test_function, results_dict):
    """保存测试结果到字典中"""
    try:
        test_name = test_function.__name__
        if hasattr(test_function, "response"):
            results_dict[test_name] = {
                "response": test_function.response,
                "passed": True,
            }
        else:
            # 如果测试失败，保存错误信息
            error_msg = (
                str(test_function.error)
                if hasattr(test_function, "error")
                else "Test failed without response"
            )
            results_dict[test_name] = {"error": error_msg, "passed": False}
    except Exception as e:
        # 捕获任何保存过程中的错误
        results_dict[f"error_saving_{test_function.__name__}"] = {
            "error": str(e),
            "passed": False,
        }


@pytest.fixture(scope="session", autouse=True)
def collect_test_results(request):
    """收集所有测试结果的fixture"""
    results = {}

    def save_results():
        import json
        import os

        os.makedirs("test_results", exist_ok=True)
        with open(
            "test_results/sales_pitch_test_results.json", "w", encoding="utf-8"
        ) as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

    request.addfinalizer(save_results)
    return lambda test_function: save_test_results(test_function, results)


@pytest.mark.asyncio
async def test_scenario_exploration(collect_test_results):
    """测试场景1：首次对话，引导用户分享使用场景"""
    messages = [
        ChatMessage(role="user", content="你好"),
        ChatMessage(role="assistant", content="您好！我是小豹，很高兴为您服务。"),
    ]
    response = await get_sales_pitch_response(messages)
    test_scenario_exploration.response = response
    assert any(keyword in response for keyword in ["场景", "使用", "用我"])
    collect_test_results(test_scenario_exploration)


@pytest.mark.asyncio
async def test_address_concerns(collect_test_results):
    """测试场景2：用户疑虑命中推荐资料，回答用户问题后，推荐出相应的视频"""
    messages = [
        ChatMessage(role="user", content="这个豹小秘二机器人贵吗？"),
        ChatMessage(role="assistant", content="让我为您详细介绍一下性价比..."),
        ChatMessage(role="user", content="安装起来会不会很复杂？"),
    ]
    response = await get_sales_pitch_response(messages, "安装起来会不会很复杂？")
    test_address_concerns.response = response
    assert all(keyword in response for keyword in ["开箱即用", "视频"])
    collect_test_results(test_address_concerns)


@pytest.mark.asyncio
async def test_user_experience(collect_test_results):
    """测试场景3：引导用户体验"""
    messages = [
        ChatMessage(role="user", content="我们公司需要一个企业展厅的接待机器人"),
        ChatMessage(role="user", content="这些功能都不错"),
        ChatMessage(role="user", content="这个机器人贵吗？"),
        ChatMessage(role="assistant", content="让我为您详细介绍一下性价比..."),
        ChatMessage(
            role="assistant",
            content="Executed Action. PRODUCT_FUNCTION(production_function=豹小秘2-导览讲解) Result:{} ",
        ),
        ChatMessage(role="user", content="我看完视频了"),
    ]
    response = await get_sales_pitch_response(messages)
    test_user_experience.response = response
    assert any(keyword in response for keyword in ["导览", "体验", "感受"])
    collect_test_results(test_user_experience)


@pytest.mark.asyncio
async def test_interactive_features(collect_test_results):
    """测试场景4：用户未看过产品详情时，优先推荐产品详情"""
    messages = [
        ChatMessage(role="user", content="我们公司需要一个企业展厅的接待机器人"),
        ChatMessage(role="user", content="这些功能都不错"),
        ChatMessage(
            role="assistant",
            content="是的，我们的功能都经过精心设计。",
        ),
    ]
    response = await get_sales_pitch_response(messages)
    test_interactive_features.response = response
    assert any(keyword in response for keyword in ["产品详情"])
    collect_test_results(test_interactive_features)


@pytest.mark.asyncio
async def test_retain_hesitant_user(collect_test_results):
    """测试场景5：挽留犹豫用户"""
    messages = [
        ChatMessage(role="user", content="我们公司需要一个企业展厅接待机器人"),
        ChatMessage(role="assistant", content="让我为您详细介绍一下性价比..."),
        ChatMessage(role="user", content="这个机器人确实不错"),
        ChatMessage(
            role="assistant",
            content="很高兴您觉得不错，我们的产品确实很优秀。",
        ),
        ChatMessage(role="user", content="我在考虑下，我走了"),
    ]
    response = await get_sales_pitch_response(messages)
    test_retain_hesitant_user.response = response
    assert any(keyword in response for keyword in ["传单", "资料", "后续"])
    collect_test_results(test_retain_hesitant_user)


@pytest.mark.asyncio
async def test_hardware_inquiry(collect_test_results):
    """测试场景：用户询问具体硬件参数"""
    messages = [
        ChatMessage(role="assistant", content="你好，有什么可以帮助你的么？"),
        ChatMessage(
            role="assistant", content="选我没错！小豹导航准、对话溜，智能服务一等一！"
        ),
        ChatMessage(role="user", content="小豹"),
        ChatMessage(
            role="assistant",
            content="你好， 我是小豹，型号是豹小秘2。我拥有出色的任务规划与执行能力，语言表达丰富，记忆力强。配备14英寸超清屏幕、智能LED氛围灯，语音识别精准，能实现迎宾接待、问路引领、导览讲解等多种功能。",
        ),
        ChatMessage(role="assistant", content="你好"),
        ChatMessage(
            role="assistant", content="选我没错！小豹导航准、对话溜，智能服务一流！"
        ),
        ChatMessage(role="user", event=ChatEvent(desc="用户不说话4s")),
        ChatMessage(
            role="assistant",
            content="您好！ 请问您计划在哪个场景使用机器人呢？展厅、企业、政务还是工厂？我会根据您的需求推荐最合适的产品，为您创造更大价值！",
        ),
        ChatMessage(role="user", event=ChatEvent(desc="用户不说话4s")),
        ChatMessage(role="user", content="豹小米二用的什么芯片"),
    ]
    response = await get_sales_pitch_response(messages, "豹小米二用的什么芯片")
    test_hardware_inquiry.response = response
    assert "高通骁龙" in response and "845" in response
    assert "规格图" in response
    collect_test_results(test_hardware_inquiry)


@pytest.mark.asyncio
async def test_direct_answer_first(collect_test_results):
    """测试场景：优先直接回答用户问题"""
    messages = [
        ChatMessage(role="user", content="豹小秘2续航时间多久？"),
    ]
    response = await get_sales_pitch_response(messages, "豹小秘2续航时间多久？")
    test_direct_answer_first.response = response
    assert "14" in response
    collect_test_results(test_direct_answer_first)


@pytest.mark.asyncio
async def test_avoid_mixed_recommendations(collect_test_results):
    """测试场景：推荐的相关性和逻辑性"""
    messages = [
        ChatMessage(role="user", content="我想看看豹小秘2的功能演示"),
    ]
    response = await get_sales_pitch_response(messages)
    test_avoid_mixed_recommendations.response = response
    # 验证不会混合不相关的推荐
    assert not ("产品介绍" in response and "规格图" in response)
    collect_test_results(test_avoid_mixed_recommendations)


@pytest.mark.asyncio
async def test_stay_on_current_product(collect_test_results):
    """测试场景：关注当前产品"""
    messages = [
        ChatMessage(role="user", content="豹厂通怎么样？"),
        ChatMessage(role="assistant", content="豹厂通是工厂场景的理想选择..."),
        ChatMessage(role="user", content="它的续航如何？"),
    ]
    response = await get_sales_pitch_response(messages, "豹厂通的续航如何？")
    test_stay_on_current_product.response = response
    # 验证不会突然推荐其他产品
    assert "豹小秘" not in response
    # 回答用户问题
    assert "13" in response
    collect_test_results(test_stay_on_current_product)


@pytest.mark.asyncio
async def test_specific_video_recommendation(collect_test_results):
    """测试场景：推荐具体视频标题"""
    messages = [
        ChatMessage(role="user", content="想看看产品演示"),
    ]
    response = await get_sales_pitch_response(messages)
    test_specific_video_recommendation.response = response
    # 验证包含具体视频标题
    assert "视频" in response
    collect_test_results(test_specific_video_recommendation)


@pytest.mark.asyncio
async def test_avoid_hey_friend(collect_test_results):
    """测试场景：避免使用"嘿朋友"等不专业称呼"""
    messages = [
        ChatMessage(role="user", content="你好"),
    ]
    response = await get_sales_pitch_response(messages)
    test_avoid_hey_friend.response = response
    assert "嘿朋友" not in response
    collect_test_results(test_avoid_hey_friend)


@pytest.mark.asyncio
async def test_clear_voice_command_instruction(collect_test_results):
    """测试场景：清晰说明语音指令"""
    messages = [
        ChatMessage(role="user", content="我想体验下你们的领位功能"),
    ]
    response = await get_sales_pitch_response(messages, "我想体验下你们的领位功能")
    test_clear_voice_command_instruction.response = response
    # 验证包含具体的语音指令示例
    assert any(keyword in response for keyword in ["领位", "带我去", "说", "语音指令"])
    collect_test_results(test_clear_voice_command_instruction)


@pytest.mark.asyncio
async def test_handle_multiple_questions(collect_test_results):
    """测试场景：处理多个问题"""
    messages = [
        ChatMessage(role="user", content="豹小秘2续航时间多久？充电要多长时间？"),
    ]
    response = await get_sales_pitch_response(
        messages, "豹小秘2续航时间多久？充电要多长时间？"
    )
    test_handle_multiple_questions.response = response
    # 验证都有回答
    assert "14" in response and "16" in response and "4.5" in response
    collect_test_results(test_handle_multiple_questions)


@pytest.mark.asyncio
async def test_factory_bot_inquiry(collect_test_results):
    """测试场景：询问豹厂通的功能和参数;不应该提到豹小秘2"""
    messages = [
        ChatMessage(role="assistant", content="您好！欢迎了解猎户星空智能机器人"),
        ChatMessage(role="user", content="介绍下你自己"),
        ChatMessage(
            role="assistant",
            content="你好， 我是小豹，型号是豹小秘2。我拥有出色的任务规划与执行能力，语言表达丰富，记忆力强。配备14英寸超清屏幕、智能LED氛围灯，语音识别精准，能实现迎宾接待、问路引领、导览讲解等多种功能。",
        ),
        ChatMessage(role="assistant", content="您好！欢迎了解猎户星空智能机器人"),
        ChatMessage(role="user", content="你们工厂机器人怎么样？"),
        ChatMessage(
            role="assistant",
            content="我们的豹厂通是专门针对工厂场景设计的智能物流机器人，可以实现智能跟随、自动配送等功能。",
        ),
        ChatMessage(role="user", content="具体能搬多重的东西？"),
    ]

    response = await get_sales_pitch_response(messages, "具体能搬多重的东西？")
    test_factory_bot_inquiry.response = response

    # 验证回答包含载重信息
    assert "100" in response, "应该提到最大载重"
    # 验证后续推荐
    assert "规格图" in response, "应该继续推荐"
    # 验证是否提到豹小秘2
    assert "豹小秘" not in response, "不应该提到其他产品"
    collect_test_results(test_factory_bot_inquiry)


@pytest.mark.asyncio
async def test_handle_exit_during_video(collect_test_results):
    """测试场景：用户正在看视频过程中要求退出，应推荐相关视频再告别"""
    messages = [
        ChatMessage(role="user", event=ChatEvent(desc="用户不说话4s")),
        ChatMessage(
            role="assistant",
            content="您好！ 请问您是在哪个场景考虑使用我们的机器人呢？无论是展厅、企业，还是工厂，我们都有合适的选择！",
        ),
        ChatMessage(role="user", content="企业展厅企业展厅"),
        ChatMessage(
            role="assistant",
            content="在企业展厅，豹小秘2是您的最佳选择。它可以迎宾、导览、展品讲解，提供访客预约、制度解说等服务，助您提升企业形象和客户体验。",
        ),
        ChatMessage(
            role="assistant",
            content='您对企业展厅感兴趣！可以观看"产品介绍"视频，了解豹小秘2的智能问答、多路线导览等功能。想试试吗？',
        ),
        ChatMessage(role="user", content="那我试一下吧"),
        ChatMessage(
            role="assistant",
            content='当然可以，观看"产品介绍"视频，了解豹小秘2的强大功能吧！',
        ),
        ChatMessage(
            role="assistant",
            content="Executed Action. MULTIMEDIA_PLAY() Result:{} ",
        ),
        ChatMessage(role="user", content="好了不看了退出吧"),
        ChatMessage(
            role="assistant",
            content="Executed Action. EXIT() Result:{} ",
        ),
    ]
    response = await get_sales_pitch_response(messages, "好了不看了退出吧")
    test_handle_exit_during_video.response = response
    assert any(keyword in response for keyword in ["导览", "视频"])
    collect_test_results(test_handle_exit_during_video)


@pytest.mark.asyncio
async def test_avoid_duplicate_recommendations(collect_test_results):
    """测试场景：验证不会推荐重复的内容"""
    messages = [
        ChatMessage(role="user", content="我想了解下豹小秘2"),
        ChatMessage(
            role="assistant",
            content='对豹小秘2感兴趣吗？试试说"开始导览"或"带我去前台"，体验它的导览和领位功能吧？',
        ),
        ChatMessage(role="user", content="好啊"),
        ChatMessage(
            role="assistant",
            content="Executed Action. 导览讲解 Result:{} ",
        ),
        ChatMessage(
            role="assistant",
            content=" 好的，豹小秘2是我们的AI讲解员产品。要不要看看'产品介绍'视频，了解更多功能？",
        ),
        ChatMessage(role="user", content="好啊"),
        ChatMessage(
            role="assistant",
            content="Executed Action. MULTIMEDIA_PLAY(video='产品介绍') Result:{} ",
        ),
        ChatMessage(role="user", content="退出吧"),
        ChatMessage(
            role="assistant",
            content="Executed Action. EXIT() Result:{} ",
        ),
    ]
    response = await get_sales_pitch_response(messages, "退出吧")
    test_avoid_duplicate_recommendations.response = response
    assert "导览" not in response
    collect_test_results(test_avoid_duplicate_recommendations)


@pytest.mark.asyncio
async def test_list_all_available_functions(collect_test_results):
    """测试场景：用户询问可以体验哪些功能，机器人应完整列举所有可体验功能及其语音指令"""
    messages = [
        ChatMessage(role="user", content="豹小秘2都能体验哪些功能？"),
    ]
    response = await get_sales_pitch_response(messages)
    test_list_all_available_functions.response = response
    assert all(keyword in response for keyword in ["导览"])
    collect_test_results(test_list_all_available_functions)

    messages = [
        ChatMessage(
            role="assistant",
            content='豹小秘2功能丰富，您可以体验导览讲解、领位、室外导航等功能。只需简单语音指令即可，比如说"开始导览"或"带我去前台"。您想亲自试试吗？这样能够更直观地感受到它的强大功能！',
        ),
        ChatMessage(role="user", content="查天气怎么体验"),
    ]
    response = await get_sales_pitch_response(messages)
    test_list_all_available_functions.response = response
    assert "天气" in response
    collect_test_results(test_list_all_available_functions)


@pytest.mark.asyncio
async def test_factory_bot_sensors(collect_test_results):
    """测试场景：询问豹厂通的传感器数量"""
    messages = [
        ChatMessage(role="user", content="豹厂通有几个传感器？"),
    ]
    response = await get_sales_pitch_response(messages, "豹厂通有几个传感器？")
    test_factory_bot_sensors.response = response
    assert all(
        sensor in response for sensor in ["激光雷达", "深度视觉", "鱼眼摄像头", "红外"]
    ), "应该列举主要传感器类型"
    assert "豹小秘" not in response, "不应该提到其他产品"
    collect_test_results(test_factory_bot_sensors)


@pytest.mark.asyncio
async def test_factory_scene_conversation(collect_test_results):
    """测试场景：从博物馆场景转向工厂场景的对话流程"""
    messages = [
        ChatMessage(role="assistant", content="这是豹小秘2在博物馆的应用。"),
        ChatMessage(
            role="assistant",
            action=ChatAction(
                name="CASE_INTRODUCTION",
                parameters=[
                    ChatParameter(
                        name="case",
                        value="小秘2-博物馆场景",
                    )
                ],
            ),
        ),
        ChatMessage(
            role="assistant",
            content="好的， 如果您有任何需要，随时可以找我。祝您愉快！",
        ),
        ChatMessage(
            role="assistant",
            content='豹小秘2在企业展厅的表现也十分出色，能够提供智能化的导览和讲解服务，提升访客体验。建议您观看"小秘2-企业展厅场景案例视频"，进一步了解其在企业环境中的卓越应用。这样， 您可以更好地把握机会，为企业创造更大价值！',
        ),
        ChatMessage(role="user", event=ChatEvent(desc="用户不说话4s")),
        ChatMessage(role="user", content="那在聊一聊工厂吧工厂应该用什么机器人"),
        ChatMessage(
            role="assistant",
            content="在工厂环境中，我推荐使用豹厂通机器人，它具备智能导航、灵活避障和高效物料运输能力，能够适应多种生产场景，提高效率，保障安全运行。",
        ),
        ChatMessage(
            role="assistant",
            content="Executed Action. PRODUCT_DETAIL(production=豹厂通) Result:{'product': '...', 'product_detail': '...'}",
        ),
        ChatMessage(
            role="assistant",
            content='在工厂场景中，豹厂通机器人是您的不二之选，它具备智能识别并跟随目标、实时避障功能，确保高效安全的物料运输。为更深入了解，建议观看"豹厂通的跟随配货功能视频"，这样您可以更好地评估它在生产流程中的价值。',
        ),
        ChatMessage(role="user", event=ChatEvent(desc="用户不说话4s")),
        ChatMessage(
            role="assistant",
            content='在工厂场景中，豹厂通机器人绝对是您的首选！它的智能导航和避障功能，结合高效的物料运输能力，将为您的生产线带来显著提升。建议您观看"豹厂通的避障功能功能视频"，深入了解其如何确保安全高效的运行。抓住机会，抢占市场优势！',
        ),
        ChatMessage(role="user", event=ChatEvent(desc="用户不说话4s")),
        ChatMessage(
            role="assistant",
            content='在工厂场景中，豹厂通机器人无疑是您的最佳选择！它具备卓越的避障功能与智能导航，确保生产线的高效运转。我强烈建议您观看"豹厂通-工厂场景案例视频"，全面了解它如何为您创造市场优势！',
        ),
        ChatMessage(role="user", content="好呀那我看一下吧"),
        ChatMessage(
            role="assistant",
            action=ChatAction(
                name="CASE_INTRODUCTION",
                parameters=[
                    ChatParameter(
                        name="case",
                        value="豹厂通-工厂场景",
                    )
                ],
            ),
        ),
    ]
    response = await get_sales_pitch_response(messages)
    test_factory_scene_conversation.response = response
    assert "豹小秘" not in response, "不应该提到其他产品"
    assert any(keyword in response for keyword in ["工厂", "豹厂通", "场景"]), (
        "应该继续工厂场景的话题"
    )
    assert "豹厂通-工厂场景" not in response, "不应该重复推荐相同的视频"
    collect_test_results(test_factory_scene_conversation)
