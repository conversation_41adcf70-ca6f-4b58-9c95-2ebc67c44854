import asyncio
import json
import traceback
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, <PERSON>ple

import mem0
import pandas as pd
from loguru import logger

from src.assistant.planner.recommend import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WelcomeGenerator
from src.assistant.planner.recommend_prompts import *
from src.settings import agent_setting
from src.utils.general_environment_sense import CalendarInfo, WeatherInfo
from src.utils.mem0_adapter import _mem0_embedding_adapter

TEST_IMAGES = [
    "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1111/c/4/0/9/c409fca43e7e63591f24e8efa80c30e8.jpeg",
    # "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1107/3/7/3/0/37301c448456c70d1a787cc3e2d25a85.jpeg",
    "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1113/7/8/1/c/781cdf5cb16ef560f4a19078c24d1463.jpeg",
    "https://beta-jiedai.ainirobot.com/orics/down/aios001_20241011_43dfc781ee299cfc49e7a0f51a277def.jpeg",
    "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1114/c/7/5/7/c7572297722d6c70e07b10b859567a0b.jpeg",
    "https://robot-jiedai.s3.cn-northwest-1.amazonaws.com.cn/account/orics/download/pub/aios_rpim_image/ori/2024/1114/d/a/3/c/da3c833ac780ad45dddb4096b5c67399.jpeg",
]

# 添加用户历史记录场景配置
USER_HISTORY_SCENARIOS = [
    {
        "name": "无人脸识别结果",
        "previous_descriptions": [],
        "face_info": FaceInfoResult(
            face_id="",  # 置空
            user_name="",  # 置空
            personal_welcome_message="",  # 置空
        ),
    },
    # {
    #     "name": "有用户名",
    #     "previous_descriptions": [],
    #     "face_info": FaceInfoResult(
    #         face_id="test_user",
    #         user_name="测试用户",
    #         personal_welcome_message=""  # 无个性化欢迎语
    #     ),
    # },
    {
        "name": "有欢迎语",
        "previous_descriptions": [
            "记录时间：2024-01-01 外貌特点：白色上衣, 戴着黑框眼镜，表情平静"
        ],
        "face_info": FaceInfoResult(
            face_id="test_user",
            user_name="测试用户",
            personal_welcome_message="记得吃饭",
        ),
    },
]

# 添加普通场景
TEST_SCENARIOS = [
    # 普通场景
    {
        "name": "普通工作日上午",
        "category": "工作日时段",
        "datetime": datetime(2024, 1, 2, 10, 10),  # 普通工作日上午
        "weather": WeatherInfo(weather="晴", temperature=15, wind_speed=2),
        "calendar": CalendarInfo(
            festival="",  # 无节日
            lunar="腊月十一",
            solarterm="",
            week="星期二",
            motto="",
            date="2024-01-02",
        ),
    },
    # 节日场景
    {
        "name": "元旦场景",
        "category": "节日",
        "datetime": datetime(2024, 1, 1, 10, 10),
        "weather": WeatherInfo(weather="晴", temperature=5, wind_speed=3),
        "calendar": CalendarInfo(
            festival="元旦",
            lunar="腊月初十",
            solarterm="小寒",
            week="星期一",
            motto="新年新气象",
            date="2024-01-01",
        ),
    },
    # {
    #     "name": "平安夜场景",
    #     "category": "节日",
    #     "datetime": datetime(2023, 12, 24, 19, 30),
    #     "weather": WeatherInfo(weather="小雪", temperature=-2, wind_speed=2),
    #     "calendar": CalendarInfo(
    #         festival="平安夜",
    #         lunar="冬月初一",
    #         solarterm="冬至",
    #         week="星期日",
    #         motto="平安喜乐",
    #         date="2023-12-24",
    #     ),
    # },
    # {
    #     "name": "圣诞节场景",
    #     "category": "节日",
    #     "datetime": datetime(2023, 12, 25, 14, 30),
    #     "weather": WeatherInfo(weather="阴", temperature=0, wind_speed=1),
    #     "calendar": CalendarInfo(
    #         festival="圣诞节",
    #         lunar="冬月初二",
    #         solarterm="冬至",
    #         week="星期一",
    #         motto="圣诞快乐",
    #         date="2023-12-25",
    #     ),
    # },
    # 极端天气场景
    # {
    #     "name": "暴雪场景",
    #     "category": "极端天气",
    #     "datetime": datetime(2023, 12, 26, 14, 30),
    #     "weather": WeatherInfo(weather="暴雪", temperature=-8, wind_speed=5),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初三",
    #         solarterm="冬至",
    #         week="星期二",
    #         motto="风雪无阻",
    #         date="2023-12-26",
    #     ),
    # },
    {
        "name": "暴雨场景",
        "category": "极端天气",
        "datetime": datetime(2023, 12, 27, 16, 30),
        "weather": WeatherInfo(weather="暴雨", temperature=25, wind_speed=4),
        "calendar": CalendarInfo(
            festival="",
            lunar="冬月初四",
            solarterm="冬至",
            week="星期三",
            motto="雨过天晴",
            date="2023-12-27",
        ),
    },
    # {
    #     "name": "重度霾场景",
    #     "category": "极端天气",
    #     "datetime": datetime(2023, 12, 29, 13, 30),
    #     "weather": WeatherInfo(weather="重度霾", temperature=15, wind_speed=1),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初六",
    #         solarterm="冬至",
    #         week="星期五",
    #         motto="雾霾终将散去",
    #         date="2023-12-29",
    #     ),
    # },
    # {
    #     "name": "酷暑场景",
    #     "category": "极端天气",
    #     "datetime": datetime(2023, 12, 30, 14, 30),
    #     "weather": WeatherInfo(weather="晴", temperature=38, wind_speed=2),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初七",
    #         solarterm="冬至",
    #         week="星期六",
    #         motto="炎炎夏日",
    #         date="2023-12-30",
    #     ),
    # },
    # {
    #     "name": "严寒场景",
    #     "category": "极端天气",
    #     "datetime": datetime(2023, 12, 31, 10, 30),
    #     "weather": WeatherInfo(weather="晴", temperature=-15, wind_speed=3),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初八",
    #         solarterm="冬至",
    #         week="星期日",
    #         motto="寒冬腊月",
    #         date="2023-12-31",
    #     ),
    # },
    # 工作日不同时间
    # {
    #     "name": "工作日早高峰",
    #     "category": "工作日时段",
    #     "datetime": datetime(2023, 12, 26, 8, 30),
    #     "weather": WeatherInfo(weather="晴", temperature=5, wind_speed=2),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初三",
    #         solarterm="冬至",
    #         week="星期二",
    #         motto="早起的鸟儿有虫吃",
    #         date="2023-12-26",
    #     ),
    # },
    # {
    #     "name": "工作日午餐",
    #     "category": "工作日时段",
    #     "datetime": datetime(2023, 12, 26, 12, 30),
    #     "weather": WeatherInfo(weather="大雨", temperature=8, wind_speed=3),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初三",
    #         solarterm="冬至",
    #         week="星期二",
    #         motto="享受美食时光",
    #         date="2023-12-26",
    #     ),
    # },
    # {
    #     "name": "工作日下午茶",
    #     "category": "工作日时段",
    #     "datetime": datetime(2023, 12, 26, 15, 30),
    #     "weather": WeatherInfo(weather="霾", temperature=10, wind_speed=1),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初三",
    #         solarterm="冬至",
    #         week="星期二",
    #         motto="下午茶时光",
    #         date="2023-12-26",
    #     ),
    # },
    # {
    #     "name": "工作日晚高峰",
    #     "category": "工作日时段",
    #     "datetime": datetime(2023, 12, 26, 18, 30),
    #     "weather": WeatherInfo(weather="大风", temperature=3, wind_speed=8),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初三",
    #         solarterm="冬至",
    #         week="星期二",
    #         motto="平安归家",
    #         date="2023-12-26",
    #     ),
    # },
    {
        "name": "深夜场景",
        "category": "工作日时段",
        "datetime": datetime(2023, 12, 26, 23, 30),
        "weather": WeatherInfo(weather="晴", temperature=0, wind_speed=2),
        "calendar": CalendarInfo(
            festival="",
            lunar="冬月初三",
            solarterm="冬至",
            week="星期二",
            motto="夜深人静",
            date="2023-12-26",
        ),
    },
    # 周末场景
    {
        "name": "周六上午",
        "category": "周末",
        "datetime": datetime(2023, 12, 23, 10, 30),
        "weather": WeatherInfo(weather="晴", temperature=15, wind_speed=2),
        "calendar": CalendarInfo(
            festival="",
            lunar="冬月初一",
            solarterm="冬至",
            week="星期六",
            motto="周末愉快",
            date="2023-12-23",
        ),
    },
    # {
    #     "name": "周日下午",
    #     "category": "周末",
    #     "datetime": datetime(2023, 12, 24, 15, 30),
    #     "weather": WeatherInfo(weather="小雨", temperature=12, wind_speed=3),
    #     "calendar": CalendarInfo(
    #         festival="",
    #         lunar="冬月初二",
    #         solarterm="冬至",
    #         week="星期日",
    #         motto="享受周末",
    #         date="2023-12-24",
    #     ),
    # },
]


class MockRedis:
    """Mock Redis client for testing"""

    def hget(self, key: str, field: str) -> Optional[str]:
        return None

    def expire(self, key: str, time: int) -> bool:
        return True


async def run_single_test(
    mem0_client: mem0.Memory,
    image_url: str,
    scenario: Dict,
    use_gpt4: bool,
    scenario_start_time: datetime,
    user_history_scenario: Dict,
    mock_prompts: Optional[Dict] = None,
    prompts_config: Optional[Dict] = None,
) -> Dict:
    """运行单个测试用例"""
    try:
        image_info = {
            "image_id": str(uuid.uuid4()),
            "image_url": image_url,
        }
        if not mock_prompts:
            mock_prompts = {
                "weekend_prompts": WEEKEND_PROMPTS,
                "weather_prompts": WEATHER_PROMPTS,
                "time_prompts": TIME_PROMPTS,
                "location_prompts": LOCATION_PROMPTS,
                "holiday_prompts": HOLIDAY_PROMPTS,
                "single_person_examples": SINGLE_PERSON_EXAMPLES,
                "multi_person_examples": MULTI_PERSON_EXAMPLES,
                "base_requirements": BASE_REQUIREMENTS,
                "vision_prompt": VISION_PROMPT,
            }

        generator = WelcomeGenerator(
            redis_client=MockRedis(),
            mem0_client=mem0_client,
            image_info=image_info,
            location="前台接待点",
            geo_location="北京",
            use_history=True,
            use_gpt4=use_gpt4,
            device_id="G" * 9,
            mock=True,
            mock_datetime=scenario["datetime"],
            mock_weather=scenario["weather"],
            mock_calendar=scenario["calendar"],
            mock_previous_descriptions=user_history_scenario["previous_descriptions"],
            mock_face_info=user_history_scenario["face_info"],
            # Use mock prompts if available, otherwise use defaults
            weekend_prompts=mock_prompts.get("weekend_prompts", WEEKEND_PROMPTS),
            weather_prompts=mock_prompts.get("weather_prompts", WEATHER_PROMPTS),
            time_prompts=mock_prompts.get("time_prompts", TIME_PROMPTS),
            location_prompts=mock_prompts.get("location_prompts", LOCATION_PROMPTS),
            holiday_prompts=mock_prompts.get("holiday_prompts", HOLIDAY_PROMPTS),
            single_person_examples=mock_prompts.get(
                "single_person_examples", SINGLE_PERSON_EXAMPLES
            ),
            multi_person_examples=mock_prompts.get(
                "multi_person_examples", MULTI_PERSON_EXAMPLES
            ),
            base_requirements=mock_prompts.get("base_requirements", BASE_REQUIREMENTS),
            vision_prompt=mock_prompts.get("vision_prompt", VISION_PROMPT),
        )

        try:
            welcome_text = await generator.run()
            scenario_end_time = datetime.now()
            scenario_duration = (
                scenario_end_time - scenario_start_time
            ).total_seconds()

            result = {
                "场景名称": scenario["name"],
                "场景类别": scenario["category"],
                "历史记录场景": user_history_scenario["name"],
                "图片链接": image_url,
                "日期时间": scenario["datetime"].strftime("%Y-%m-%d %H:%M"),
                "天气": scenario["weather"].weather,
                "温度": scenario["weather"].temperature,
                "风速": scenario["weather"].wind_speed,
                "节日": scenario["calendar"].festival,
                "农历": scenario["calendar"].lunar,
                "节气": scenario["calendar"].solarterm,
                "星期": scenario["calendar"].week,
                "格言": scenario["calendar"].motto,
                "使用模型": "GPT4-O" if use_gpt4 else "qwen",
                "欢迎语": welcome_text,
                "总耗时": generator.elapse_info.get("total_time", 0),
                "外观描述耗时": generator.elapse_info.get("multimodal_time", 0),
                "外观描述": generator.debug_info.get("appearance_model_result", {}),
                "天气和人脸信息耗时": generator.elapse_info.get(
                    "face_and_weather_time", 0
                ),
                "欢迎语生成耗时": generator.elapse_info.get("generate_welcome_time", 0),
                "场景执行开始时间": scenario_start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "场景执行结束时间": scenario_end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "场景总耗时(秒)": scenario_duration,
                "Prompts配置": prompts_config["config_name"]
                if prompts_config
                else "默认",
            }

            return result

        except Exception as e:
            scenario_end_time = datetime.now()
            scenario_duration = (
                scenario_end_time - scenario_start_time
            ).total_seconds()

            return {
                "场景名称": scenario["name"],
                "场景类别": scenario["category"],
                "历史记录场景": user_history_scenario["name"],
                "图片链接": image_url,
                "日期时间": scenario["datetime"].strftime("%Y-%m-%d %H:%M"),
                "天气": scenario["weather"].weather,
                "温度": scenario["weather"].temperature,
                "风速": scenario["weather"].wind_speed,
                "节日": scenario["calendar"].festival,
                "农历": scenario["calendar"].lunar,
                "节气": scenario["calendar"].solarterm,
                "星期": scenario["calendar"].week,
                "格言": scenario["calendar"].motto,
                "使用模型": "GPT4-O" if use_gpt4 else "qwen",
                "欢迎语": f"错误: {str(e)}",
                "总耗时": -1,
                "外观描述耗时": -1,
                "外观描述": {},
                "天气和人脸信息耗时": -1,
                "欢迎语生成耗时": -1,
                "场景执行开始时间": scenario_start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "场景执行结束时间": scenario_end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "场景总耗时(秒)": scenario_duration,
                "Prompts配置": prompts_config["config_name"]
                if prompts_config
                else "默认",
            }

    except Exception as e:
        logger.error(f"Test failed: {str(e)} {traceback.format_exc()}")
        return None


async def test_welcome_generation(
    compare_models: bool = False,
) -> Tuple[List[pd.DataFrame], Dict]:
    """Test welcome text generation with different scenarios and models"""
    all_results = []
    all_dfs = []

    test_start_time = datetime.now()
    logger.info(f"Test started at: {test_start_time}")
    logger.info(f"Model comparison mode: {'enabled' if compare_models else 'disabled'}")

    # Define test configurations
    test_configs = [
        {"config_name": "默认Prompts", "mock_prompts": None, "file_path": None},
        # {"config_name": "Mock Prompts 1", "file_path": "test/welcome/mock_prompts_1.json"},
        # {"config_name": "Mock Prompts 2", "file_path": "test/welcome/mock_prompts_2.json"},
    ]

    for test_config in test_configs:
        results = []

        # 获取 mock prompts
        mock_prompts = None
        if test_config.get("file_path"):
            try:
                with open(test_config["file_path"], "r", encoding="utf-8") as f:
                    mock_prompts = json.load(f)
            except Exception as e:
                logger.error(
                    f"Failed to load mock prompts from {test_config['file_path']}: {e}"
                )

        # Initialize mem0 client
        mem0_config = {
            "version": "v1.1",
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "collection_name": f"{agent_setting.env}_mem0_embeddings_bge_1024",
                    "host": agent_setting.qdrant_host,
                    "port": 6333,
                    "embedding_model_dims": 1024,
                },
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o",
                    "temperature": 0.2,
                    "max_tokens": 1500,
                },
            },
        }

        mem0_client = mem0.Memory.from_config(config_dict=mem0_config)
        mem0_client.embedding_model = _mem0_embedding_adapter()

        for image_url in TEST_IMAGES:
            image_start_time = datetime.now()
            logger.info(
                f"\n\n=== Testing image: {image_url} at {image_start_time} with {test_config['config_name']} ==="
            )

            # 测试场景
            for scenario in TEST_SCENARIOS:  # 测试其他场景（节日、极端天气等）
                for user_history_scenario in USER_HISTORY_SCENARIOS:
                    if compare_models:
                        scenario_start_time = datetime.now()
                        result = await run_single_test(
                            mem0_client,
                            image_url,
                            scenario,
                            True,
                            scenario_start_time,
                            user_history_scenario=user_history_scenario,
                            mock_prompts=mock_prompts,
                            prompts_config=test_config,
                        )
                        results.append(result)
                        await asyncio.sleep(1)

                    scenario_start_time = datetime.now()
                    result = await run_single_test(
                        mem0_client,
                        image_url,
                        scenario,
                        False,
                        scenario_start_time,
                        user_history_scenario=user_history_scenario,
                        mock_prompts=mock_prompts,
                        prompts_config=test_config,
                    )
                    results.append(result)
                    await asyncio.sleep(1)

        # Convert results to DataFrame for current config
        df = pd.DataFrame(results)
        df["prompts_config"] = test_config["config_name"]
        df["prompts_file"] = test_config.get("file_path", "default")
        all_dfs.append(df)
        all_results.extend(results)

    test_end_time = datetime.now()

    # Save and analyze results
    save_test_results(all_dfs, test_start_time, test_end_time, compare_models)

    return all_dfs, calculate_summary_info(
        all_dfs, test_start_time, test_end_time, compare_models
    )


def save_test_results(
    all_dfs: List[pd.DataFrame],
    test_start_time: datetime,
    test_end_time: datetime,
    compare_models: bool = False,
):
    """Save test results with comparison between different prompt configurations"""
    results_dir = Path("test_results")
    results_dir.mkdir(exist_ok=True)

    timestamp = test_start_time.strftime("%Y%m%d_%H%M%S")
    excel_path = results_dir / f"welcome_test_results_{timestamp}.xlsx"

    # 合并所有数据框
    combined_df = pd.concat(all_dfs, ignore_index=True)

    with pd.ExcelWriter(excel_path, engine="openpyxl") as writer:
        # 将所有结果保存到一个sheet中
        combined_df.to_excel(writer, sheet_name="详细结果", index=False)

        # Create and save overall comparison
        comparison_df = create_overall_comparison(all_dfs, compare_models)
        comparison_df.to_excel(writer, sheet_name="配置对比", index=True)

        # Save model comparison only if compare_models is True
        if compare_models:
            model_comparison = create_model_comparison_df(combined_df)
            model_comparison.to_excel(writer, sheet_name="模型对比", index=False)

    # Save combined CSV
    csv_path = results_dir / f"welcome_test_results_{timestamp}.csv"
    combined_df.to_csv(csv_path, index=False, encoding="utf-8-sig")

    logger.info(f"Results saved to {excel_path} and {csv_path}")


def create_overall_comparison(
    all_dfs: List[pd.DataFrame], compare_models: bool = False
) -> pd.DataFrame:
    """Create comparison DataFrame between different prompt configurations"""
    comparison_data = []

    for df in all_dfs:
        config_name = df["prompts_config"].iloc[0]
        data = {
            "配置名称": config_name,
            "总用例数": len(df),
            "节日场景数": len(df[df["场景类别"] == "节日"]),
            "极端天气场景数": len(df[df["场景类别"] == "极端天气"]),
            "工作日时段场景数": len(df[df["场景类别"] == "工作日时段"]),
            "周末场景数": len(df[df["场景类别"] == "周末"]),
            "无人脸识别结果数": len(df[df["历史记录场景"] == "无人脸识别结果"]),
            "有用户名数": len(df[df["历史记录场景"] == "有用户名"]),
            "有欢迎语数": len(df[df["历史记录场景"] == "有欢迎语"]),
            "平均欢迎语生成耗时": df["欢迎语生成耗时"].mean(),
        }

        if compare_models:
            gpt4_df = df[df["使用模型"] == "GPT4-O"]
            qwen_df = df[df["使用模型"] == "qwen"]
            data.update(
                {
                    "GPT4-O平均响应时间": gpt4_df["总耗时"].mean(),
                    "qwen平均响应时间": qwen_df["总耗时"].mean(),
                    "GPT4-O失败率": len(
                        gpt4_df[gpt4_df["欢迎语"].str.contains("错误:", na=False)]
                    )
                    / len(gpt4_df)
                    if len(gpt4_df) > 0
                    else 0,
                    "qwen失败率": len(
                        qwen_df[qwen_df["欢迎语"].str.contains("错误:", na=False)]
                    )
                    / len(qwen_df)
                    if len(qwen_df) > 0
                    else 0,
                }
            )
        else:
            data.update(
                {
                    "qwen平均响应时间": df["总耗时"].mean(),
                    "qwen失败率": len(df[df["欢迎语"].str.contains("错误:", na=False)])
                    / len(df)
                    if len(df) > 0
                    else 0,
                }
            )

        comparison_data.append(data)

    return pd.DataFrame(comparison_data).set_index("配置名称")


def calculate_summary_info(
    all_dfs: List[pd.DataFrame],
    test_start_time: datetime,
    test_end_time: datetime,
    compare_models: bool = False,
) -> Dict:
    """Calculate summary information for all test results"""
    combined_df = pd.concat(all_dfs, ignore_index=True)

    summary = {
        "测试开始时间": test_start_time.strftime("%Y-%m-%d %H:%M:%S"),
        "测试结束时间": test_end_time.strftime("%Y-%m-%d %H:%M:%S"),
        "总测试时长(秒)": (test_end_time - test_start_time).total_seconds(),
        "测试用例总数": len(combined_df),
        "qwen平均响应时间": combined_df[combined_df["使用模型"] == "qwen"][
            "总耗时"
        ].mean(),
        "qwen失败用例数": len(
            combined_df[
                (combined_df["使用模型"] == "qwen")
                & (combined_df["欢迎语"].str.contains("错误:", na=False))
            ]
        ),
    }

    if compare_models:
        summary.update(
            {
                "GPT4-O平均响应时间": combined_df[combined_df["使用模型"] == "GPT4-O"][
                    "总耗时"
                ].mean(),
                "GPT4-O失败用例数": len(
                    combined_df[
                        (combined_df["使用模型"] == "GPT4-O")
                        & (combined_df["欢迎语"].str.contains("错误:", na=False))
                    ]
                ),
            }
        )

    return summary


def create_model_comparison_df(df: pd.DataFrame) -> pd.DataFrame:
    """Create model comparison DataFrame"""
    gpt4_df = df[df["使用模型"] == "GPT4-O"]
    custom_df = df[df["使用模型"] == "qwen"]

    return pd.DataFrame(
        {
            "指标": ["平均响应时间", "失败用例数", "成功用例数"],
            "GPT4-O": [
                gpt4_df["欢迎语生成耗时"].mean(),
                len(gpt4_df[gpt4_df["欢迎语"].str.contains("错误:", na=False)]),
                len(gpt4_df[~gpt4_df["欢迎语"].str.contains("错误:", na=False)]),
            ],
            "qwen": [
                custom_df["欢迎语生成耗时"].mean(),
                len(custom_df[custom_df["欢迎语"].str.contains("错误:", na=False)]),
                len(custom_df[~custom_df["欢迎语"].str.contains("错误:", na=False)]),
            ],
        }
    )


if __name__ == "__main__":
    # 只测试 qwen
    # python test_welcome.py

    # 同时测试 qwen 和 GPT4-O
    # python test_welcome.py --compare-models

    import argparse
    import os

    parser = argparse.ArgumentParser(description="Run welcome text generation tests")
    parser.add_argument(
        "--compare-models",
        action="store_true",
        help="Enable model comparison (test both GPT4-O and qwen)",
    )
    args = parser.parse_args()

    os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key
    all_dfs, summary = asyncio.run(
        test_welcome_generation(compare_models=args.compare_models)
    )

    # Print summary
    print("\n=== Test Summary ===")
    for key, value in summary.items():
        print(f"{key}: {value}")
