import asyncio
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict

import pandas as pd
from loguru import logger


class WelcomeScorer:
    SCORING_PROMPT = """你是一个专业的AI助手欢迎语评估专家。请对以下欢迎语进行评分，满分100分。

    评分标准：
    1. 趣味性 (35分)：
       - 是否包含幽默或调侃元素
       - 是否有创意的比喻或联想
       - 是否能让人会心一笑
    
    2. 个性化互动 (30分)：
       - 是否针对用户外表、穿着等特征进行互动
       - 是否自然地融入场景元素（天气、节日等）
    
    3. 语气把控 (20分)：
       - 是否保持轻松友好的语气
       - 调侃是否恰到好处，不过分
       - 是否避免过度夸张或尴尬
    
    4. 对话流畅度 (15分)：
       - 是否符合口语表达习惯
       - 是否有互动感和对话感
       - 是否自然地引起互动兴趣
       - 可以使用类似“老妹儿、小接接”等方言性质的称呼
    
    欢迎语内容：
    {welcome_text}

    场景信息：
    时间：{datetime}
    天气：{weather}
    节日：{festival}
    
    请按以下格式输出评分：
    {{
        "趣味性": {{
            "score": <分数>,
            "reason": "<评分理由>"
        }},
        "个性化互动": {{
            "score": <分数>,
            "reason": "<评分理由>"
        }},
        "语气把控": {{
            "score": <分数>,
            "reason": "<评分理由>"
        }},
        "对话流畅度": {{
            "score": <分数>,
            "reason": "<评分理由>"
        }},
        "总分": <总分>,
        "总体评价": "<整体评价>"
    }}
    """

    def __init__(self, openai_client):
        self.client = openai_client

    async def score_welcome(self, welcome_data: Dict) -> Dict:
        """对单条欢迎语进行评分"""
        try:
            prompt = self.SCORING_PROMPT.format(
                welcome_text=welcome_data["欢迎语"],
                datetime=welcome_data["日期时间"],
                weather=f"{welcome_data['天气']} {welcome_data['温度']}℃",
                festival=welcome_data["节日"] or "无",
            )

            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
            )

            return json.loads(response.choices[0].message.content)
        except Exception as e:
            logger.error(f"Scoring failed: {str(e)} {traceback.format_exc()}")
            return None


async def evaluate_welcome_results(results_file: str, openai_client) -> pd.DataFrame:
    """评估欢迎语测试结果"""
    try:
        # 读取Excel文件
        df = pd.read_excel(results_file, sheet_name="详细结果")
        logger.info(f"Available columns: {df.columns.tolist()}")

        # 检查必要的列名
        required_columns = ["欢迎语", "prompts_config", "场景类别"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            logger.error(f"Available columns: {df.columns.tolist()}")
            return None

        logger.info(
            f"Loaded {len(df)} rows with configurations: {df['prompts_config'].unique()}"
        )

    except Exception as e:
        logger.error(f"Failed to read file: {str(e)}")
        return None

    scorer = WelcomeScorer(openai_client)

    all_scores = []
    for _, row in df.iterrows():
        if "错误" in str(row["欢迎语"]):
            continue

        logger.info(
            f"Scoring welcome text: '{row['欢迎语']}' for config: {row['prompts_config']}"
        )
        score_result = await scorer.score_welcome(
            {
                "欢迎语": row["欢迎语"],
                "日期时间": row.get("日期时间", None),
                "天气": row.get("天气", None),
                "温度": row.get("温度", None),
                "节日": row.get("节日", None),
            }
        )

        if score_result:
            score_result.update(
                {
                    "prompts_config": row["prompts_config"],
                    "欢迎语": row["欢迎语"],
                    "场景类别": row["场景类别"],  # 添加场景类别字段
                }
            )
            all_scores.append(score_result)
            logger.info(f"Score generated: {score_result.get('总分', 'N/A')}")
        await asyncio.sleep(1)

    if not all_scores:
        logger.error("No valid scores generated")
        return None

    # 转换为DataFrame
    scores_df = pd.DataFrame(all_scores)

    # 保存详细评分结果
    results_dir = Path("test_results")
    results_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_path = results_dir / f"welcome_scores_{timestamp}.xlsx"

    with pd.ExcelWriter(excel_path, engine="openpyxl") as writer:
        # 详细评分表
        scores_df.to_excel(writer, sheet_name="评分详情", index=False)

        # 创建汇总
        summary_dfs = create_scoring_summary(scores_df)
        summary_dfs["基础汇总"].to_excel(writer, sheet_name="评分汇总", index=False)
        summary_dfs["场景汇总"].to_excel(writer, sheet_name="场景评分汇总", index=False)

    logger.info(f"Results saved to {excel_path}")
    return scores_df


def create_scoring_summary(scores_df: pd.DataFrame) -> pd.DataFrame:
    """创建评分汇总"""
    # 基础配置汇总
    summary_data = []
    scene_summary_data = []

    for config in scores_df["prompts_config"].unique():
        config_df = scores_df[scores_df["prompts_config"] == config]

        # 基础汇总统计
        summary = {
            "配置名称": config,
            "样本数": len(config_df),
            "平均总分": config_df["总分"].mean(),
            "趣味性平均分": config_df["趣味性"]
            .apply(lambda x: x.get("score") if isinstance(x, dict) else x)
            .mean(),
            "个性化互动平均分": config_df["个性化互动"]
            .apply(lambda x: x.get("score") if isinstance(x, dict) else x)
            .mean(),
            "语气把控平均分": config_df["语气把控"]
            .apply(lambda x: x.get("score") if isinstance(x, dict) else x)
            .mean(),
            "对话流畅度平均分": config_df["对话流畅度"]
            .apply(lambda x: x.get("score") if isinstance(x, dict) else x)
            .mean(),
            "最高分": config_df["总分"].max(),
            "最低分": config_df["总分"].min(),
            "标准差": config_df["总分"].std(),
        }
        summary_data.append(summary)

        # 场景维度统计 - 只根据"场景类别"字段
        scene_groups = config_df.groupby("场景类别")
        for scene_type, group in scene_groups:
            scene_summary = {
                "配置名称": config,
                "场景类型": scene_type,  # 节日/极端天气/工作日时段/周末
                "样本数": len(group),
                "平均总分": group["总分"].mean(),
                "最高分": group["总分"].max(),
                "最低分": group["总分"].min(),
                "标准差": group["总分"].std(),
            }
            scene_summary_data.append(scene_summary)

    # 创建多个DataFrame并返回字典
    base_summary_df = pd.DataFrame(summary_data)
    scene_summary_df = pd.DataFrame(scene_summary_data)

    # 写入不同sheet
    return {"基础汇总": base_summary_df, "场景汇总": scene_summary_df}


if __name__ == "__main__":
    import os
    from datetime import datetime

    from openai import AsyncOpenAI

    from src.settings import agent_setting

    # 设置 OpenAI API key
    os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key

    # 获取最新的测试结果文件
    results_dir = Path("test_results")
    if not results_dir.exists():
        print("No test results directory found")
        exit(1)

    # 查找最新的测试结果文件
    result_files = list(results_dir.glob("welcome_test_*.xlsx"))
    if not result_files:
        print("No test result files found")
        exit(1)

    latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
    print(f"Using latest test result file: {latest_file}")

    # 运行评分
    client = AsyncOpenAI()
    asyncio.run(evaluate_welcome_results(str(latest_file), client))
