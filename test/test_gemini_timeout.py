#!/usr/bin/env python3
"""
测试Gemini API超时控制效果的脚本
"""

import asyncio
import time
import sys
import os
import traceback

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from src.utils.llm import LLMManager, LLMConfig
from src.settings import agent_setting
from loguru import logger

# 配置日志
logger.remove()
logger.add(sys.stdout, level="INFO", format="{time} | {level} | {message}")


async def test_gemini_normal_call():
    """测试正常的Gemini调用（较长超时时间）"""
    print("\n=== 测试1: 正常Gemini调用 ===")

    llm_config = LLMConfig(
        llm_model_name="gemini-2.5-flash-lite-preview-06-17",
        timeout=60,  # 60秒超时
        temperature=0.7,
        max_tokens=100,
    )

    messages = [{"role": "user", "content": "请简单介绍一下人工智能的发展历史"}]

    start_time = time.time()
    try:
        result = await LLMManager._a_call_gemini_api(
            model=llm_config, messages=messages, _logger=logger
        )
        elapsed = time.time() - start_time

        print(f"✅ 调用成功")
        print(f"📊 耗时: {elapsed:.2f}秒")
        print(f"📝 内容长度: {len(result.content) if result.content else 0}")
        print(f"🔧 错误信息: {result.error}")
        if result.content:
            print(f"📄 返回内容前100字符: {result.content[:100]}...")

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 调用失败: {e}")
        print(f"📊 耗时: {elapsed:.2f}秒")


async def test_gemini_timeout_call():
    """测试Gemini超时调用（很短超时时间）"""
    print("\n=== 测试2: Gemini超时调用 ===")

    llm_config = LLMConfig(
        llm_model_name="gemini-2.5-flash-lite-preview-06-17",
        timeout=2,  # 2秒超时，很可能超时
        temperature=0.7,
        max_tokens=1000,  # 较大的token数，增加超时概率
    )

    messages = [
        {
            "role": "user",
            "content": "请详细写一篇关于机器学习算法的长文章，包括监督学习、无监督学习、强化学习等各个方面，每个部分都要详细说明其原理、应用场景和具体算法实例。",
        }
    ]

    start_time = time.time()
    try:
        result = await LLMManager._a_call_gemini_api(
            model=llm_config, messages=messages, _logger=logger
        )
        elapsed = time.time() - start_time

        if result.error:
            print(f"⏰ 超时控制生效")
            print(f"📊 实际耗时: {elapsed:.2f}秒")
            print(f"⚙️ 设置超时: {llm_config.timeout}秒")
            print(f"🔧 错误信息: {result.error}")
        else:
            print(f"✅ 调用成功（未超时）")
            print(f"📊 耗时: {elapsed:.2f}秒")
            print(f"📝 内容长度: {len(result.content) if result.content else 0}")

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 调用异常: {e} {traceback.format_exc()}")
        print(f"📊 耗时: {elapsed:.2f}秒")


async def test_gemini_stream_normal():
    """测试正常的Gemini流式调用"""
    print("\n=== 测试3: Gemini流式调用（正常） ===")

    llm_config = LLMConfig(
        llm_model_name="gemini-2.5-flash-lite-preview-06-17",
        timeout=30,  # 30秒超时
        temperature=0.7,
        max_tokens=200,
    )

    messages = [{"role": "user", "content": "请简单介绍一下Python编程语言的特点, 纯文本"}]

    start_time = time.time()
    content_chunks = []
    chunk_count = 0

    try:
        async for chunk in LLMManager._a_invoke_gemini_llm_stream(
            model=llm_config,
            messages=messages,
        ):
            if chunk:
                content_chunks.append(chunk)
                chunk_count += 1
                if chunk_count == 1:
                    first_chunk_time = time.time() - start_time
                    print(f"🚀 首个数据块耗时: {first_chunk_time:.2f}秒")

        elapsed = time.time() - start_time
        total_content = "".join(content_chunks)

        print(f"✅ 流式调用成功")
        print(f"📊 总耗时: {elapsed:.2f}秒")
        print(f"📦 数据块数量: {chunk_count}")
        print(f"📝 内容长度: {len(total_content)}")
        if total_content:
            print(f"📄 内容前100字符: {total_content[:100]}...")

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 流式调用失败: {e}")
        print(f"📊 耗时: {elapsed:.2f}秒")


async def test_gemini_stream_timeout():
    """测试Gemini流式调用超时"""
    print("\n=== 测试4: Gemini流式调用（超时） ===")

    llm_config = LLMConfig(
        llm_model_name="gemini-2.5-flash-lite-preview-06-17",
        timeout=2,  # 2秒超时
        temperature=0.7,
        max_tokens=2000,  # 大token数增加超时概率
    )

    messages = [
        {
            "role": "user",
            "content": "请写一篇非常详细的关于深度学习的技术文章",
        }
    ]

    start_time = time.time()
    content_chunks = []
    chunk_count = 0

    try:
        async for chunk in LLMManager._a_invoke_gemini_llm_stream(
            model=llm_config,
            messages=messages,
        ):
            if chunk:
                content_chunks.append(chunk)
                chunk_count += 1
                if chunk_count == 1:
                    first_chunk_time = time.time() - start_time
                    print(f"🚀 首个数据块耗时: {first_chunk_time:.2f}秒")

        elapsed = time.time() - start_time
        total_content = "".join(content_chunks)

        if elapsed >= llm_config.timeout - 0.5:  # 允许小误差
            print(f"⏰ 流式超时控制生效")
        else:
            print(f"✅ 流式调用成功（未超时）")

        print(f"📊 总耗时: {elapsed:.2f}秒")
        print(f"⚙️ 设置超时: {llm_config.timeout}秒")
        print(f"📦 数据块数量: {chunk_count}")
        print(f"📝 内容长度: {len(total_content)}")

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 流式调用异常: {e}")
        print(f"📊 耗时: {elapsed:.2f}秒")


async def main():
    """主函数"""
    print("🧪 开始测试Gemini API超时控制效果")
    print("=" * 50)

    # 检查Gemini客户端是否可用
    try:
        from src.settings import gemini_client

        if not gemini_client:
            print("❌ Gemini客户端未配置，请检查settings配置")
            return
    except Exception as e:
        print(f"❌ 无法导入Gemini客户端: {e}")
        return

    # 执行测试
    await test_gemini_normal_call()
    await asyncio.sleep(1)  # 间隔1秒

    await test_gemini_timeout_call()
    await asyncio.sleep(1)

    await test_gemini_stream_normal()
    await asyncio.sleep(1)

    await test_gemini_stream_timeout()

    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n📋 测试说明:")
    print("- 测试1和3应该正常完成")
    print("- 测试2和4应该触发超时控制")
    print("- 如果测试2或4的实际耗时明显超过设置的超时时间，说明超时控制未生效")
    print("- 如果实际耗时接近设置的超时时间，说明超时控制正常工作")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
