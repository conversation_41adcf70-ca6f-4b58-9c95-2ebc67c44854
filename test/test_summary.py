import unittest

from src.common.toolkit import LLMToolKit


class TestSummary(unittest.IsolatedAsyncioTestCase):
    async def test_summary(self):
        # 1.对于代词的替换
        chat_history = [
            "User SAY '你好，我们都有哪些产品'",
            "Robot EXECUTED ACTION. HOME() Result:{}",
            "Robot SAY '你好，我们有豹小秘2，豹厂通，招财豹'",
            "User SAY '介绍一下招财豹吧'",
            "Robot EXECUTED ACTION. PRODUCT_DETAIL(production=招财豹) Result:{}",
            "Robot SAY '欢迎体验招财豹，餐厅效率新高度！'",
        ]
        follow_up_input = "这三款产品有什么区别吗"
        summary_result = await LLMToolKit.summary(
            "\n".join(chat_history), follow_up_input
        )

        # 关键信息必须存在
        self.assertTrue(
            "豹小秘2" in summary_result.content
            and "豹厂通" in summary_result.content
            and "招财豹" in summary_result.content
        )

        chat_history = [
            "User SAY '你好，我们都有哪些产品'",
            "Robot EXECUTED ACTION. HOME() Result:{}",
            "Robot SAY '你好，我们有豹小秘2，豹厂通，招财豹'",
            "User SAY '我打算春节的时候去你们那里买几台'",
            "Robot SAY '好的，我帮你查一下'",
            "Robot SAY '您好，我查到您可以购买豹小秘2，豹厂通，招财豹'",
        ]

        summary_result = await LLMToolKit.summary(
            "\n".join(chat_history), "那我去的时候，北京天气怎么样"
        )
        print(summary_result.content)
        self.assertTrue("春节" in summary_result.content)

        chat_history = [
            "<Robot> SAY '为了让您充分体验豹小秘2的智能化魅力，我建议您亲身试试导览功能！直接说“开始导览”，让这款机器人为您的展厅增添智慧色彩！绝对是提升客户体验的绝佳选择！'",
            "<User> Triggered Event [desc='用户不说话4s']",
            "<Robot> SAY '豹小秘2的无缝语音交互和数字讲解功能让您在展厅、企业和政务场景中如虎添翼！我建议您观看“豹小秘2的产品介绍功能视频”，全面感受其卓越性能！千万别错过这个提升客户体验的良机！'",
        ]
        summary_result = await LLMToolKit.summary(
            "\n".join(chat_history),
            "或者改成跟没路由器一样的他连上那个号space服务以后...再去做自己的操作才行",
        )
        print(summary_result.content)
        self.assertTrue("豹小秘2" in summary_result.content)

        chat_history = """<Robot> SAY '你好'
<Robot> SAY '选我没错！小豹导航准、对话溜，智能服务一流！'
<User> Triggered Event [desc='用户不说话4s']
<User> SAY '了解一下工厂机器人'
<Robot> Executed Action. PRODUCT_DETAIL(production=豹厂通) Result:{'product': '...', 'product_detail': '...'} 
<Robot> SAY '豹厂通是工厂的搬运担当，具备智能导航、柔性部署、开箱即用等特点。支持多种运载方式，精准避障，适应多种生产场景。国际认证，品质保证，是提高效率的理想选择。'
<User> Triggered Event [desc='用户不说话4s']
<User> SAY '豹厂通的有什么版本'
<Robot> Executed Action. PRODUCT_FUNCTION(production_function=豹厂通-产品规格) Result:{'title': '豹厂通-产品参数规格', 'url': 'https://re...', 'type': 'image'} 
<Robot> SAY '豹厂通有多种版本，标准版、托盘版和货架版，均具备高效的导航与避障能力，适合不同的生产需求。想进一步了解它在实际工厂中的表现吗？我建议您观看“豹厂通-工厂场景案例视频”，这会让您更直观地感受其出色的工作能力！'
"""

        summary_result = await LLMToolKit.summary(chat_history, "那我看一下吧")
        print(summary_result.content)
        self.assertTrue(
            "豹厂通" in summary_result.content and "案例" in summary_result.content
        )

        chat_history = """User> SAY '看一下企业展厅场景案例视频'
<Robot> Executed Action. PRODUCT_FUNCTION(production_function=豹小秘2-数字讲解) Result:{'title': '豹厂通-产品参数规格', 'url': 'https://re...', 'type': 'image'} 
<Robot> SAY '观看“小秘2-企业展厅场景案例视频”后，'
<User> SAY '啥情况'
<User> SAY '你这不是企业展厅视频视频'
<Robot> SAY '看来我们的视频链接出现了一些问题，我为您推荐最相关的“小秘2-企业展厅场景案例视频”。这将帮助您深入了解豹小秘2在企业展厅中的实际应用，绝对值得一看！观看后， 您会发现豹小秘2的无限潜力！'
"""

        summary_result = await LLMToolKit.summary(chat_history, "还有别的案例吗")
        print(summary_result.content)
        # 还有除了小秘2-企业展厅场景案例视频之外的其他企业展厅场景案例视频吗？
        self.assertTrue(
            "企业展厅" in summary_result.content
            and ("除了" in summary_result.content or "还有" in summary_result.content)
        )


if __name__ == "__main__":
    unittest.main()
