import asyncio
import json
import os
import unittest
from unittest.mock import Magic<PERSON>ock

import mem0
from livekit.agents.llm import ChatContext
from loguru import logger

from src.session_manager.memory import Memory
from src.settings import agent_setting
from src.utils.mem0_adapter import _mem0_embedding_adapter


class TestMemory(unittest.TestCase):
    def setUp(self):
        # Mock the Redis client
        self.redis_client = MagicMock()
        self.memory = Memory(redis_client=self.redis_client)
        self.memory.device_id = "test_device"

    def test_device_id_setter_getter(self):
        self.memory.device_id = "new_device"
        self.assertEqual(self.memory.device_id, "new_device")

    def test_chat_context_prefix(self):
        expected_prefix = f"{self.memory.key_prefix}_test_device_chat_context"
        self.assertEqual(self.memory.chat_context_prefix, expected_prefix)

    def test_plan_context_prefix(self):
        expected_prefix = f"{self.memory.key_prefix}_test_device_plan_context"
        self.assertEqual(self.memory.plan_context_prefix, expected_prefix)

    def test_clear_chat_history(self):
        self.memory.clear_chat_history()
        self.redis_client.expire.assert_any_call(self.memory.chat_context_prefix, 0)
        self.redis_client.expire.assert_any_call(self.memory.plan_context_prefix, 0)

    def test_commit_chat_assistant_message(self):
        self.memory.get_chat_context = MagicMock(return_value=ChatContext())
        self.memory.update_context = MagicMock()

        self.memory.commit_chat_assistant_message(text="Hello", images=[])

        self.memory.update_context.assert_called_once()
        updated_context = self.memory.update_context.call_args[0][0]
        self.assertEqual(updated_context.messages[-1].content, "Hello")
        self.assertEqual(updated_context.messages[-1].role, "assistant")

    def test_commit_chat_user_message(self):
        self.memory.get_chat_context = MagicMock(return_value=ChatContext())
        self.memory.update_context = MagicMock()

        self.memory.commit_chat_user_message(text="Hi", images=[])

        self.memory.update_context.assert_called_once()
        updated_context = self.memory.update_context.call_args[0][0]
        self.assertEqual(updated_context.messages[-1].content, "Hi")
        self.assertEqual(updated_context.messages[-1].role, "user")

    def test_update_context_with_max_chat_history(self):
        context = ChatContext()
        for i in range(10):  # Add more messages than max_chat_history
            context.append(text=f"Message {i}", role="user")

        self.memory.update_context(context, self.memory.chat_context_prefix)

        self.redis_client.setex.assert_called_once()
        args, kwargs = self.redis_client.setex.call_args
        self.assertEqual(args[0], self.memory.chat_context_prefix)
        serialized_messages = json.loads(args[2])
        self.assertEqual(len(serialized_messages), self.memory.max_chat_history)
        self.assertEqual(serialized_messages[-1]["content"], "Message 9")

    def test_update_context_with_max_plan_history(self):
        context = ChatContext()
        for i in range(10):  # Add more messages than max_plan_history
            context.append(text=f"Plan {i}", role="user")

        self.memory.update_context(context, self.memory.plan_context_prefix)

        self.redis_client.setex.assert_called_once()
        args, kwargs = self.redis_client.setex.call_args
        self.assertEqual(args[0], self.memory.plan_context_prefix)
        serialized_messages = json.loads(args[2])
        self.assertEqual(len(serialized_messages), self.memory.max_plan_history)
        self.assertEqual(serialized_messages[-1]["content"], "Plan 9")


async def test_get_memories():
    """Test memory retrieval functionality"""

    # 初始化 Memory 类
    config = {
        "version": "v1.1",
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": f"{agent_setting.env}_mem0_embeddings_bge_1024",
                "host": agent_setting.qdrant_host,
                "port": 6333,
                "embedding_model_dims": 1024,
            },
        },
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o",
                "temperature": 0.2,
                "max_tokens": 1500,
            },
        },
    }

    mem0_client = mem0.Memory.from_config(config_dict=config)
    mem0_client.embedding_model = _mem0_embedding_adapter()

    memory = Memory(mem0_client)

    # 测试数据
    test_cases = [
        {
            "name": "正常查询",
            "query": "用户外观",
            "user_id": "test_user_1",
            "max_records": 3,
        },
        {
            "name": "空用户查询",
            "query": "用户外观",
            "user_id": "non_existent_user",
            "max_records": 3,
        },
        {
            "name": "大量记录查询",
            "query": "用户外观",
            "user_id": "test_user_1",
            "max_records": 10,
        },
    ]

    # 运行测试
    for test_case in test_cases:
        logger.info(f"\n=== 测试用例: {test_case['name']} ===")
        try:
            results = await memory.get_memories(
                query=test_case["query"],
                user_id=test_case["user_id"],
                max_records=test_case["max_records"],
            )

            logger.info("查询参数:")
            logger.info(f"  - Query: {test_case['query']}")
            logger.info(f"  - User ID: {test_case['user_id']}")
            logger.info(f"  - Max Records: {test_case['max_records']}")

            logger.info("查询结果:")
            if results:
                for i, result in enumerate(results, 1):
                    logger.info(f"  {i}. {result}")
            else:
                logger.info("  无结果")

            logger.info(f"结果数量: {len(results)}")

        except Exception as e:
            logger.error(f"测试失败: {e}")


async def test_add_and_retrieve():
    """Test adding and then retrieving memories"""

    # 初始化配置
    config = {
        "version": "v1.1",
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": f"{agent_setting.env}_mem0_embeddings_bge_1024",
                "host": agent_setting.qdrant_host,
                "port": 6333,
                "embedding_model_dims": 1024,
            },
        },
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o",
                "temperature": 0.2,
                "max_tokens": 1500,
            },
        },
    }

    mem0_client = mem0.Memory.from_config(config_dict=config)
    mem0_client.embedding_model = _mem0_embedding_adapter()

    # 创建测试用户ID
    test_user_id = "cici"

    # 添加一些测试数据
    test_memories = [
        "黑色长发女生，金框眼镜，白衬衫黑裤，微笑",
        "棕色短发女生，戴银项链，红色连衣裙，开心",
        "黑色短发男生，蓝色西装，严肃表情",
    ]

    logger.info("=== 开始测试添加和检索记忆 ===")
    logger.info(f"测试用户 ID: {test_user_id}")

    try:
        # 添加记忆
        logger.info("添加测试记忆...")
        for memory_text in test_memories:
            result = mem0_client.add(
                memory_text,
                user_id=test_user_id,
                metadata={"category": "appearance"},
                filters={"category": "appearance"},
            )
            logger.info(f"添加记忆: {memory_text}")
            logger.info(f"添加结果: {result}")

        # 等待一小段时间确保数据已保存
        await asyncio.sleep(2)

        # 检索记忆
        logger.info("\n检索记忆...")
        memory = Memory(mem0_client)
        results = await memory.get_memories(
            query="用户外观", user_id=test_user_id, max_records=5
        )

        logger.info("检索结果:")
        for i, result in enumerate(results, 1):
            logger.info(f"  {i}. {result}")

    except Exception as e:
        logger.error(f"测试失败: {e}")


if __name__ == "__main__":
    # unittest.main()
    # 设置环境变量
    os.environ["OPENAI_API_KEY"] = agent_setting.plan_model_api_key

    # 运行测试
    asyncio.run(test_get_memories())
    logger.info("\n" + "=" * 50 + "\n")
    asyncio.run(test_add_and_retrieve())
