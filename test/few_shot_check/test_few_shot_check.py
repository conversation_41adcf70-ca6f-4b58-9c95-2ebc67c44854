import asyncio
import yaml
import pandas as pd
from loguru import logger
from src.action.retrieve import MaterialRetriever
from src.common.toolkit import LLMToolKit
from src.session_manager.chat_context import ChatMessage
from src.agent_core.single_action import SingleActionAgent
from src.common.constant import Area, LanguageEnum
import json
import csv

FEW_SHOT_SCORE_THRESHOLD = 0.35
async def get_intent_by_summary(
    history_messages: list[ChatMessage], 
    final_query: str,
    language: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    使用LLMToolKit.summary方法获取搜索查询并查找相关few-shots
    """
    try:
        # 构建conversation_progress
        conversation_progress = []
        for msg in history_messages:
            if msg.content:
                conversation_progress.append(msg.content)
        
        # 使用summary方法获取搜索查询
        summary_result = await LLMToolKit.summary(
            "\n".join(conversation_progress[-3:]),
            final_query,
        )
        
        # 解析结果，添加错误处理
        try:
            if isinstance(summary_result.content, str):
                search_query = summary_result.content
            else:
                parsed_result = LLMToolKit.parse_yaml(summary_result.content)
                search_query = str(parsed_result.get("FinalAnswer", summary_result.content))
        except Exception as e:
            logger.warning(f"解析summary结果失败: {e}, 使用原始内容")
            search_query = str(summary_result.content)
        
        # 使用搜索查询获取few-shots
        materials = await MaterialRetriever.retrieve(
            search_query,
            filter_action_names=[a["name"] for a in (actions or [])],
            few_shot_limit=2,
            language=language,
            few_shot_score_threshold = FEW_SHOT_SCORE_THRESHOLD,
            _logger=logger,
        )
        
        # 提取few_shots的input和action
        few_score = []
        few_shot_input = []
        few_shot_action = []
        few_output = []
        for shot in materials.few_shots:
            few_score.append(shot.get('score', ''))
            few_shot_input.append(shot.get('Input', ''))
            few_shot_action.append(shot.get('Output', {}).get('action', ''))
            few_output.append(shot.get('Output', {}))

        
        return {
            'search_query': search_query,
            'few_score': few_score,
            'few_shot_input': few_shot_input,
            'few_shot_action': few_shot_action,
            'few_output': few_output
        }
    except Exception as e:
        logger.error(f"get_intent_by_summary 执行失败: {e}")
        return {
            'search_query': final_query,  # 失败时使用原始查询
            'few_score': [],
            'few_shot_input': [],
            'few_shot_action': [],
            'few_output': []
        }

async def get_intent_by_build_query(
    history_messages: list[ChatMessage], 
    final_query: str,
    language: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    使用build_search_query方法获取搜索查询并查找相关few-shots
    """
    # 使用build_search_query方法获取搜索查询
    search_query = SingleActionAgent.build_search_query(
        history_messages, 
        final_query, 
        limit=2
    )
    
    # 使用搜索查询获取few-shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        filter_action_names=[a["name"] for a in (actions or [])],
        few_shot_limit=2,
        language=language,
        few_shot_score_threshold=FEW_SHOT_SCORE_THRESHOLD,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_score = []
    few_shot_input = []
    few_shot_action = []
    few_output = []
    for shot in materials.few_shots:
        few_score.append(shot.get('score', ''))
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
        few_output.append(shot.get('Output', {}))

    return {
        'search_query': search_query,
        'few_score': few_score,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action,
        'few_output': few_output
    }

async def get_intent_by_discussion_summary(
    history_messages: list[ChatMessage], 
    final_query: str,
    language: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    通过对话总结获取完整的用户意图
    
    Args:
        history_messages: 历史对话消息
        final_query: 最终用户查询
        logger: 日志记录器
    
    Returns:
        dict: 包含处理结果的字典，格式如下：
        {
            'discussion_summary': str,              # 对话总结
            'search_query': str,                    # 完整意图作为搜索查询
            'few_shot_input': list[str],           # few-shot的输入列表
            'few_shot_action': list[str],          # few-shot的动作列表
        }
    """
    # 获取对话总结
    summary_result = await LLMToolKit.summarize_discussion_intent(history_messages)

    # 使用complete_user_intent获取完整意图
    # complete_intent_result = await LLMToolKit.complete_user_intent(
    #     summary=summary_result.content,
    #     current_question=final_query
    # )

    # 将summary_result和final_query拼接
    search_query = f"{summary_result.content} | {final_query}"
    
    
    # 使用完整意图查询few_shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        few_shot_limit=2,
        language=language,
        few_shot_score_threshold=FEW_SHOT_SCORE_THRESHOLD,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_score = []
    few_shot_input = []
    few_shot_action = []
    few_output = []
    for shot in materials.few_shots:
        few_score.append(shot.get('score', ''))
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
        few_output.append(shot.get('Output', {}))
    

    return {
        'discussion_summary': summary_result.content,
        'search_query': search_query,
        'few_score': few_score,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action,
        'few_output': few_output
    }

async def test_summarize_discussion_intent():
    # 读取测试数据
    try:
        df = pd.read_excel('english_check.xlsx')
    except Exception as e:
        logger.error(f"读取excel文件失败: {e}")
        return

    # 为三种方法分别准备结果列表
    results = {
        'summary': [],
        'build_query': [],
        'async_summary': []
    }
    
    # 遍历每一行进行测试
    for index, row in df.iterrows():
        try:
            query = row['query']
            expected_action = row['Action']
            
            # 构造测试消息
            test_message = ChatMessage(role="user", content=query)
            
            # 测试三种方法
            methods = {
                'summary': get_intent_by_summary,
                'build_query': get_intent_by_build_query,
                'async_summary': get_intent_by_discussion_summary
            }
            
            for method_name, method_func in methods.items():
                try:
                    # 获取预测结果
                    result = await method_func(
                        history_messages=[test_message],
                        final_query=query,
                        language=LanguageEnum.en
                    )
                    
                    # 检查是否命中
                    predicted_actions = result['few_shot_action']
                    is_hit = any(action == expected_action for action in predicted_actions)
                    
                    # 检查是否有few-shots数据
                    if not result.get('few_shot_input'):
                        few_shot_pairs = []
                        record = {
                            'query': query,
                            'expected_action': expected_action,
                            'predicted_actions': 'No predictions',
                            'few_shot_pairs': 'No few-shots',
                            'search_query': result.get('search_query', ''),
                            'is_hit': is_hit
                        }
                    else:
                        few_shot_pairs = []
                        try:
                            for input_text, action, score, output in zip(
                                result.get('few_shot_input', []), 
                                result.get('few_shot_action', []),
                                result.get('few_score', []),
                                result.get('few_output', [])
                            ):
                                few_shot_pairs.append(
                                    json.dumps({
                                        "score": score,
                                        "input": input_text,
                                        "action": action,
                                        "output": output
                                    }, ensure_ascii=False)
                                )
                        except Exception as e:
                            logger.error(f"处理few-shot pairs时发生错误: {e}")
                            few_shot_pairs = ['Error processing few-shots']
                        
                        record = {
                            'query': query,
                            'expected_action': expected_action,
                            'predicted_actions': ' | '.join(result.get('few_shot_action', [])) if result.get('few_shot_action') else 'No predictions',
                            'few_shot_pairs': ' || '.join(few_shot_pairs) if few_shot_pairs else 'No few-shots',
                            'search_query': result.get('search_query', ''),
                            'is_hit': is_hit
                        }
                    
                    # 只有discussion方法有discussion_summary
                    if method_name == 'discussion':
                        record['discussion_summary'] = result['discussion_summary']
                        
                    results[method_name].append(record)
                        
                except Exception as e:
                    logger.error(f"{method_name}方法处理行 {index} 时发生错误: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"处理行 {index} 时发生错误: {e}")
            continue

        # if index == 1:
        #     break
    # 输出每种方法的结果到不同的excel文件
    total = len(df)
    for method_name, all_results in results.items():
        if all_results:
            output_df = pd.DataFrame(all_results)
            output_file = f'predictions_{method_name}.xlsx'
            try:
                output_df.to_excel(output_file, index=False)
                hits = sum(1 for r in all_results if r['is_hit'])
                accuracy = hits / total * 100
                logger.info(f"{method_name}方法结果: 总数 {total}, 命中数 {hits}, 准确率 {accuracy:.2f}%")
                logger.info(f"{method_name}方法的预测结果已写入 {output_file}")
            except Exception as e:
                logger.error(f"写入{method_name}结果文件失败: {e}")

    # 收集所有未命中的案例
    all_misses = []
    for method_name, all_results in results.items():
        for result in all_results:
            if not result['is_hit']:
                miss_record = {
                    'query': result['query'],
                    'expected_action': result['expected_action'],
                    'method': method_name,
                    'predicted_actions': result['predicted_actions'],
                    'few_shot_pairs': result['few_shot_pairs'] if result['few_shot_pairs'] else 'No few-shots',
                    'search_query': result['search_query']
                }
                if method_name == 'discussion':
                    miss_record['discussion_summary'] = result['discussion_summary']
                all_misses.append(miss_record)
    
    # 将所有未命中案例写入单独的文件
    if all_misses:
        miss_df = pd.DataFrame(all_misses)
        miss_file = 'all_mismatched_cases.xlsx'
        try:
            # 按query和method排序，保持所有信息
            miss_df_sorted = miss_df.sort_values(['query', 'method'])
            miss_df_sorted.to_excel(miss_file, index=False)
            
            logger.info(f"所有未命中案例已写入 {miss_file}")
            logger.info(f"总共有 {len(miss_df_sorted)} 个未命中记录")
            logger.info(f"涉及 {len(miss_df_sorted['query'].unique())} 个独特的查询")
        except Exception as e:
            logger.error(f"写入未命中案例文件失败: {e}")
            logger.error(f"可用的列: {miss_df.columns.tolist()}")

if __name__ == "__main__":
    logger.info("开始测试 summarize_discussion_intent 方法...")
    asyncio.run(test_summarize_discussion_intent())
    logger.info("测试完成")