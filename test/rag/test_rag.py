"""
Author: <PERSON><PERSON><PERSON><PERSON>
LastEditors: <PERSON><PERSON>
email: <PERSON><PERSON><PERSON><PERSON>@orionstar.com
github:
Date: 2025-05-21 14:01:28
LastEditTime: 2025-06-16 15:07:05
motto: Still water run deep
Description: Modify here please
FilePath: /easyNLP/test/rag/test_rag.py
"""

import asyncio
import pandas as pd
from pathlib import Path
from loguru import logger
import json
from src.utils.llm import LLMManager, LLMConfig
from src.settings import agent_setting
import ast
import time
import argparse
import urllib.parse
import aiohttp

# 线上环境
ENTERPRISE_ID = "orion.ovs.entprise.1429922673"
ROBOT_OPENAPI_HOST = "https://openapi.orionstar.com"
ROBOT_OPENAPI_KEY = "34b44257d6a129cbf79c14f4d420f722"

# 测试集环境
# ENTERPRISE_ID = "orion.ovs.entprise.9945420568"
# ROBOT_OPENAPI_HOST = "https://test-openapi.orionstar.com"
# ROBOT_OPENAPI_KEY = "7ef1f8909e3da64c67f3e7e839070b6b"


async def decontextualize_query(query: str, previous_queries: list = None) -> dict:
    """
    对query进行去上下文操作，确保查询是完整和独立的

    Args:
        query: 当前查询
        previous_queries: 之前的查询列表

    Returns:
        dict: 包含是否需要去上下文化和去上下文化后的查询，以及耗时
    """
    start_time = time.perf_counter()

    if not previous_queries:
        previous_queries = []

    # 构建prompt
    prompt = f"""Analyze whether the query contains ambiguous references that require conversation history to be understood.

STRICT CRITERIA - Mark "True" ONLY if the query contains:
1. Ambiguous pronouns that refer to something from history ("it", "that", "this", "they", "which one")
2. Unclear demonstratives without clear context ("that", "this", "here", "over there")
3. Incomplete comparative or continuation references ("what about...", "and also...", "in addition...")
4. Missing essential subjects that are only identifiable from conversation history

DO NOT mark "True" for:
- Complete action commands and control instructions
- Complete statements with clear standalone meaning
- Emotional expressions and exclamations
- Self-contained questions or requests
- Commands that include all necessary context
- Status descriptions or observations

When decontextualizing (if True):
- Use MINIMAL necessary information from history
- Preserve original tone, style, and intent exactly
- Do NOT add explanatory words or extra descriptions
- Do NOT answer the current query
- Only fill in the missing essential information

Query: "{query}"
History: {previous_queries}

Return JSON only (ensure both fields are always present and non-empty):
{{
    "requires_decontextualization": "True or False",
    "decontextualized_query": "Complete standalone query with original meaning"
}}"""

    try:
        # 调用大模型进行去上下文化
        llm_config = LLMConfig(
            base_url=agent_setting.generate_text_model_base_url,
            llm_model_name=agent_setting.generate_text_model,
            api_key=agent_setting.generate_text_model_api_key,
            temperature=0.0,
            timeout=10,
        )

        messages = [{"role": "user", "content": prompt}]
        result = await LLMManager.invoke_generate_text_model(
            messages, llm_config=llm_config
        )

        # 解析返回结果
        try:
            response_data = json.loads(result.content)
            end_time = time.perf_counter()
            decontext_time = round(
                (end_time - start_time) * 1000, 3
            )  # 转换为毫秒，保留3位小数
            response_data["decontextualization_time_ms"] = decontext_time
            logger.info(f"去上下文化结果: {response_data}, 耗时: {decontext_time}ms")
            return response_data
        except json.JSONDecodeError as e:
            end_time = time.perf_counter()
            decontext_time = round((end_time - start_time) * 1000, 3)
            logger.error(f"解析去上下文化结果失败: {e}, 原始内容: {result.content}")
            # 如果解析失败，返回原始查询
            return {
                "requires_decontextualization": "False",
                "decontextualized_query": query,
                "decontextualization_time_ms": decontext_time,
            }

    except Exception as e:
        end_time = time.perf_counter()
        decontext_time = round((end_time - start_time) * 1000, 3)
        logger.error(f"去上下文化过程中出错: {str(e)}")
        # 如果出错，返回原始查询
        return {
            "requires_decontextualization": "False",
            "decontextualized_query": query,
            "decontextualization_time_ms": decontext_time,
        }


async def fetch_knowledge_content(
    query: str, knowledge_base_id: str, enterprise_id: str
):
    agent_setting.robot_openapi_host = ROBOT_OPENAPI_HOST
    agent_setting.robot_openapi_key = ROBOT_OPENAPI_KEY

    start_time = time.time()
    try:
        knowledge_content_url = (
            urllib.parse.urljoin(
                agent_setting.robot_openapi_host, agent_setting.knowledge_content_path
            )
            + f"?chatmax_api=ctai_search_ctai_doc&ov_corp_id={enterprise_id}"
        )

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=knowledge_content_url,
                headers={
                    "orionstar-api-key": agent_setting.robot_openapi_key,
                    "Content-Type": "application/json",
                },
                json={
                    "query_text": query,
                    "retrieval_strategy": "v2",
                    "pad_ctai_doc": 0,
                },
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"Failed to fetch knowledge content: {response.status}"
                    )
                    return ""

                data = await response.json()
                request_time = time.time() - start_time
                logger.debug(
                    f"ChatMax API request time: {request_time:.2f} seconds {data}"
                )
                return data
    except Exception as e:
        logger.error(f"获取知识内容失败: {str(e)}")
        return ""


async def get_knowledge_content_single(query: str, previous_queries: list = None):
    """单个query的知识检索，包含去上下文化处理"""
    total_start_time = time.perf_counter()

    # 先进行去上下文化处理
    decontext_result = await decontextualize_query(query, previous_queries)
    final_query = decontext_result.get("decontextualized_query", "")

    logger.info(f"原始查询: {query}, 历史查询: {previous_queries}")
    logger.info(
        f"是否需要去上下文化: {decontext_result.get('requires_decontextualization', 'False')}"
    )
    logger.info(f"最终查询: {final_query}")

    if final_query == "":
        exit()

    # 使用去上下文化后的查询进行知识检索
    knowledge_start_time = time.perf_counter()

    raw_data = await fetch_knowledge_content(
        query=final_query,
        knowledge_base_id="",
        enterprise_id=ENTERPRISE_ID,
    )
    knowledge_end_time = time.perf_counter()
    knowledge_time = round((knowledge_end_time - knowledge_start_time) * 1000, 3)

    total_end_time = time.perf_counter()
    total_time = round((total_end_time - total_start_time) * 1000, 3)

    # 添加耗时信息到结果中
    decontext_result["knowledge_retrieval_time_ms"] = knowledge_time
    decontext_result["total_time_ms"] = total_time

    logger.info(f"知识检索耗时: {knowledge_time}ms, 总耗时: {total_time}ms")

    return raw_data, decontext_result


async def get_knowledge_content():
    """原始的单个测试函数，现在包含去上下文化功能"""
    processed_content, raw_data, decontext_result = await get_knowledge_content_single(
        "公司谁最能喝酒"
    )
    logger.info(f"===去上下文化结果: {decontext_result}===")
    logger.info(f"===processed_content: {processed_content}===")
    logger.info(f"===raw_data: {raw_data}===")
    return processed_content, raw_data


async def batch_test_from_xlsx(
    input_file: str,
    output_file: str = None,
    case_ids: list = None,
    start_row: int = None,
):
    """从xlsx文件批量测试query，包含去上下文化功能

    Args:
        input_file: 输入的xlsx文件路径
        output_file: 输出文件路径，如果为None则自动生成
        case_ids: 指定要运行的case ID列表，如果为None则运行所有case
        start_row: 从指定行开始测试（1-based，即第1行为数据的第一行），如果为None则从第一行开始
    """
    batch_start_time = time.perf_counter()
    input_path = Path(input_file)

    # 检查输入文件是否存在
    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return

    # 设置输出文件路径
    if output_file is None:
        filename_parts = []
        if case_ids:
            case_ids_str = "_".join(map(str, case_ids))
            filename_parts.append(f"cases_{case_ids_str}")
        if start_row:
            filename_parts.append(f"from_row_{start_row}")

        if filename_parts:
            suffix = "_" + "_".join(filename_parts)
            output_file = input_path.parent / f"{input_path.stem}_results{suffix}.xlsx"
        else:
            output_file = input_path.parent / f"{input_path.stem}_results.xlsx"

    try:
        # 读取xlsx文件
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共{len(df)}行数据")

        # 检查是否有query列
        if "query_list" not in df.columns:
            logger.error("xlsx文件中未找到'query_list'列，请确保文件包含query_list列")
            return

        # 如果指定了case_ids，过滤数据
        if case_ids:
            # 检查是否有case_id列或id列
            id_column = "caseID"

            # 过滤指定的case_ids
            original_count = len(df)
            df = df[df[id_column].isin(case_ids)]
            filtered_count = len(df)

            logger.info(f"指定case_ids: {case_ids}")
            logger.info(f"原始数据: {original_count}行, 过滤后: {filtered_count}行")

            if filtered_count == 0:
                logger.warning("没有找到匹配的case ID，请检查输入的case_ids是否正确")
                return

        # 准备结果列表和查询历史
        results = []

        # 应用start_row过滤（如果指定）
        if start_row:
            original_count = len(df)
            # start_row是1-based，转换为0-based索引
            df = df.iloc[start_row - 1 :]
            filtered_count = len(df)
            logger.info(f"指定从第{start_row}行开始")
            logger.info(
                f"原始数据: {original_count}行, 跳过前{start_row - 1}行后: {filtered_count}行"
            )

        # 逐个处理query
        for index, row in df.iterrows():
            query_list = row["query_list"]
            if pd.isna(query_list) or query_list.strip() == "":
                logger.warning(f"第{index + 1}行query为空，跳过")
                continue

            logger.info(f"正在处理第{index + 1}行,query_list: {query_list}")
            query_list = ast.literal_eval(query_list)
            query = query_list[-1]
            previous_queries = query_list[:-1]
            try:
                # 调用知识检索（包含去上下文化）
                raw_data, decontext_result = await get_knowledge_content_single(
                    query, previous_queries.copy()
                )

                # 相似片段最高分
                sections = raw_data.get("data", {}).get("section_list", [])

                max_section_score = 0
                for section in sections:
                    content = section.get("content", "")
                    doc_name = section.get("doc_name")
                    if content and doc_name:
                        max_section_score = section.get("score", 0)
                        break

                # 相似summary最高分
                summaries = raw_data.get("data", {}).get("summary_list", [])
                max_summary_score = 0
                if summaries:
                    max_summary_score = summaries[0].get("score", 0)

                # 记录结果
                result = {
                    "requires_decontextualization": decontext_result.get(
                        "requires_decontextualization", "False"
                    ),
                    "decontextualized_query": decontext_result.get(
                        "decontextualized_query", query
                    ),
                    "retrieved_content": str(raw_data),  # 转换为字符串以便存储
                    "max_section_score": max_section_score,
                    "max_summary_score": max_summary_score,
                    "decontextualization_time_ms": decontext_result.get(
                        "decontextualization_time_ms", 0
                    ),
                    "knowledge_retrieval_time_ms": decontext_result.get(
                        "knowledge_retrieval_time_ms", 0
                    ),
                    "total_time_ms": decontext_result.get("total_time_ms", 0),
                }
                print(result)

                # 如果原始数据中有其他列，也保留
                for col in df.columns:
                    result[col] = row[col]

                results.append(result)

                logger.info(f"第{index + 1}行处理完成")

            except Exception as e:
                logger.error(f"处理第{index + 1}行时出错: {str(e)}")
                # 记录错误结果
                result = {
                    "query": query,
                    "requires_decontextualization": "Error",
                    "decontextualized_query": query,
                    "retrieved_content": str(e),
                    "max_section_score": 0,
                    "max_summary_score": 0,
                    "decontextualization_time_ms": 0,
                    "knowledge_retrieval_time_ms": 0,
                    "total_time_ms": 0,
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    if col != "query_list":
                        result[col] = row[col]

                results.append(result)

        # 保存结果到xlsx文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel(output_file, index=False)

            batch_end_time = time.perf_counter()
            batch_total_time = round((batch_end_time - batch_start_time) * 1000, 3)

            # 计算平均耗时
            avg_total_time = round(
                sum(r.get("total_time_ms", 0) for r in results) / len(results), 3
            )
            avg_decontext_time = round(
                sum(r.get("decontextualization_time_ms", 0) for r in results)
                / len(results),
                3,
            )
            avg_knowledge_time = round(
                sum(r.get("knowledge_retrieval_time_ms", 0) for r in results)
                / len(results),
                3,
            )

            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"共处理{len(results)}条query")
            logger.info(f"批量处理总耗时: {batch_total_time}ms")
            logger.info(f"平均单个query耗时: {avg_total_time}ms")
            logger.info(f"平均去上下文化耗时: {avg_decontext_time}ms")
            logger.info(f"平均知识检索耗时: {avg_knowledge_time}ms")
        else:
            logger.warning("没有有效的结果可保存")

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


if __name__ == "__main__":
    import sys

    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="RAG测试脚本")
    parser.add_argument("input_file", nargs="?", help="输入的xlsx文件路径")
    parser.add_argument("output_file", nargs="?", help="输出文件路径（可选）")
    parser.add_argument(
        "--case-ids",
        "-c",
        nargs="+",
        type=int,
        help="指定要运行的case ID列表，支持多个ID，例如: --case-ids 1 2 3",
    )
    parser.add_argument(
        "--start-row",
        "-s",
        type=int,
        help="从指定行开始测试（从1开始计数），例如: --start-row 10",
    )

    args = parser.parse_args()

    if args.input_file:
        # 批量测试模式
        logger.info(f"开始批量测试，输入文件: {args.input_file}")
        if args.output_file:
            logger.info(f"输出文件: {args.output_file}")
        if args.case_ids:
            logger.info(f"指定运行case IDs: {args.case_ids}")
        if args.start_row:
            logger.info(f"从第{args.start_row}行开始测试")

        # 运行批量测试
        asyncio.run(
            batch_test_from_xlsx(
                args.input_file, args.output_file, args.case_ids, args.start_row
            )
        )
    else:
        # 单个测试模式（原始功能）
        logger.info("运行单个测试模式")
        asyncio.run(get_knowledge_content())
