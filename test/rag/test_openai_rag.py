"""
Author: <PERSON><PERSON><PERSON><PERSON>
LastEditors: <PERSON><PERSON>
email: <PERSON><PERSON><PERSON><PERSON>@orionstar.com
github:
Date: 2025-01-18 10:00:00
LastEditTime: 2025-01-18 10:00:00
motto: Still water run deep
Description: OpenAI Vector Store RAG implementation
FilePath: /easyNLP/test/rag/test_openai_vector_store.py
"""

import argparse
import asyncio
import os
import sys
import time
from pathlib import Path

import pandas as pd
from loguru import logger
from openai import OpenAI
import aiohttp


class OpenAIVectorStoreManager:
    """OpenAI Vector Store管理器"""

    def __init__(self, api_key: str = None, base_url: str = None):
        """
        初始化OpenAI Vector Store管理器

        Args:
            api_key: OpenAI API密钥
            base_url: OpenAI API基础URL
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = base_url or os.getenv(
            "OPENAI_BASE_URL", "https://api.openai.com/v1"
        )

        if not self.api_key:
            raise ValueError(
                "OpenAI API密钥未设置，请设置OPENAI_API_KEY环境变量或传入api_key参数"
            )

        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)

    def create_vector_store(self, name: str) -> str:
        """
        创建Vector Store

        Args:
            name: Vector Store名称

        Returns:
            str: Vector Store ID
        """
        try:
            vector_store = self.client.vector_stores.create(name=name)
            logger.info(f"成功创建Vector Store: {name}, ID: {vector_store.id}")
            return vector_store.id
        except Exception as e:
            logger.error(f"创建Vector Store失败: {str(e)}")
            raise

    def upload_files_to_vector_store(
        self, vector_store_id: str, file_paths: list
    ) -> dict:
        """
        上传文件到Vector Store

        Args:
            vector_store_id: Vector Store ID
            file_paths: 文件路径列表

        Returns:
            dict: 上传结果信息
        """
        try:
            # 准备文件流
            file_streams = []
            for path in file_paths:
                if os.path.exists(path):
                    file_streams.append(Path(path))
                else:
                    logger.warning(f"文件不存在: {path}")

            if not file_streams:
                raise ValueError("没有有效的文件可上传")

            # 使用SDK上传文件
            file_batch = self.client.vector_stores.file_batches.upload_and_poll(
                vector_store_id=vector_store_id,
                files=file_streams,
                max_concurrency=5,
                chunking_strategy={
                    "type": "auto",
                },
            )

            logger.info(f"文件上传状态: {file_batch.status}")
            logger.info(f"文件数量统计: {file_batch.file_counts}")

            return {
                "status": file_batch.status,
                "file_counts": file_batch.file_counts,
                "batch_id": file_batch.id,
            }

        except Exception as e:
            logger.error(f"上传文件到Vector Store失败: {str(e)}")
            raise

    async def search_vector_store(
        self, vector_store_id: str, query: str, filters: dict = None
    ) -> dict:
        """
        搜索Vector Store

        Args:
            vector_store_id: Vector Store ID
            query: 查询文本
            filters: 过滤条件

        Returns:
            dict: 搜索结果
        """
        try:
            search_url = f"{self.base_url}/vector_stores/{vector_store_id}/search"

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            payload = {
                "query": query,
            }

            if filters:
                payload["filters"] = filters

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    search_url, headers=headers, json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(
                            f"搜索成功，返回{len(result.get('data', []))}个结果"
                        )
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"搜索失败: HTTP {response.status}, {error_text}")
                        return {"error": f"HTTP {response.status}: {error_text}"}

        except Exception as e:
            logger.error(f"搜索Vector Store失败: {str(e)}")
            return {"error": str(e)}

    def list_vector_stores(self) -> list:
        """
        列出所有Vector Store

        Returns:
            list: Vector Store列表
        """
        try:
            vector_stores = self.client.vector_stores.list()
            logger.info(f"共找到{len(vector_stores.data)}个Vector Store")
            return vector_stores.data
        except Exception as e:
            logger.error(f"列出Vector Store失败: {str(e)}")
            return []

    def delete_vector_store(self, vector_store_id: str) -> bool:
        """
        删除Vector Store

        Args:
            vector_store_id: Vector Store ID

        Returns:
            bool: 是否删除成功
        """
        try:
            self.client.vector_stores.delete(vector_store_id)
            logger.info(f"成功删除Vector Store: {vector_store_id}")
            return True
        except Exception as e:
            logger.error(f"删除Vector Store失败: {str(e)}")
            return False


async def single_query_test(
    query: str, vector_store_id: str = None, api_key: str = None, base_url: str = None
) -> dict:
    """
    单个query的OpenAI Vector Store检索测试

    Args:
        query: 查询文本
        vector_store_id: Vector Store ID
        api_key: OpenAI API密钥
        base_url: OpenAI API基础URL

    Returns:
        dict: 包含检索结果和耗时信息
    """
    start_time = time.perf_counter()

    try:
        # 初始化管理器
        manager = OpenAIVectorStoreManager(api_key=api_key, base_url=base_url)

        # 如果没有指定vector_store_id，尝试使用默认的或创建一个
        if not vector_store_id:
            vector_stores = manager.list_vector_stores()
            if vector_stores:
                vector_store_id = vector_stores[0].id
                logger.info(f"使用已存在的Vector Store: {vector_store_id}")
            else:
                logger.warning("没有找到可用的Vector Store，请先创建并上传文件")
                return {
                    "query": query,
                    "response": "没有找到可用的Vector Store",
                    "retrieval_time_ms": 0,
                    "success": False,
                }

        # 执行搜索
        response = await manager.search_vector_store(vector_store_id, query)

        end_time = time.perf_counter()
        retrieval_time = round((end_time - start_time) * 1000, 3)  # 转换为毫秒

        logger.info(f"检索结果: {response}")
        logger.info(f"检索耗时: {retrieval_time}ms")

        # 解析搜索结果
        max_score = 0
        doc_count = 0
        success = False

        if "error" not in response:
            success = True
            data = response.get("data", [])
            doc_count = len(data)

            if data:
                # 获取最高分数
                scores = [item.get("score", 0) for item in data]
                if scores:
                    max_score = max(scores)

        return {
            "query": query,
            "response": response,
            "retrieval_time_ms": retrieval_time,
            "success": success,
            "max_score": max_score,
            "doc_count": doc_count,
        }

    except Exception as e:
        end_time = time.perf_counter()
        retrieval_time = round((end_time - start_time) * 1000, 3)

        logger.error(f"检索过程中出错: {str(e)}")
        return {
            "query": query,
            "response": str(e),
            "retrieval_time_ms": retrieval_time,
            "success": False,
            "max_score": 0,
            "doc_count": 0,
        }


async def batch_test_from_xlsx(
    input_file: str,
    output_file: str = None,
    vector_store_id: str = None,
    case_ids: list = None,
    start_row: int = None,
    api_key: str = None,
    base_url: str = None,
):
    """
    从xlsx文件批量测试query，使用OpenAI Vector Store检索

    Args:
        input_file: 输入的xlsx文件路径
        output_file: 输出文件路径，如果为None则自动生成
        vector_store_id: Vector Store ID
        case_ids: 指定要运行的case ID列表，如果为None则运行所有case
        start_row: 从指定行开始测试（1-based），如果为None则从第一行开始
        api_key: OpenAI API密钥
        base_url: OpenAI API基础URL
    """
    batch_start_time = time.perf_counter()
    input_path = Path(input_file)

    # 检查输入文件是否存在
    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return

    # 设置输出文件路径
    if output_file is None:
        filename_parts = []
        if case_ids:
            case_ids_str = "_".join(map(str, case_ids))
            filename_parts.append(f"cases_{case_ids_str}")
        if start_row:
            filename_parts.append(f"from_row_{start_row}")

        if filename_parts:
            suffix = "_" + "_".join(filename_parts)
            output_file = (
                input_path.parent / f"{input_path.stem}_openai_results{suffix}.xlsx"
            )
        else:
            output_file = input_path.parent / f"{input_path.stem}_openai_results.xlsx"

    try:
        # 读取xlsx文件
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共{len(df)}行数据")

        # 如果指定了case_ids，过滤数据
        if case_ids:
            original_count = len(df)
            df = df[df["caseID"].isin(case_ids)]
            filtered_count = len(df)

            logger.info(f"指定case_ids: {case_ids}")
            logger.info(f"原始数据: {original_count}行, 过滤后: {filtered_count}行")

            if filtered_count == 0:
                logger.warning("没有找到匹配的case ID，请检查输入的case_ids是否正确")
                return

        # 应用start_row过滤（如果指定）
        if start_row:
            original_count = len(df)
            df = df.iloc[start_row - 1 :]
            filtered_count = len(df)
            logger.info(f"指定从第{start_row}行开始")
            logger.info(
                f"原始数据: {original_count}行, 跳过前{start_row - 1}行后: {filtered_count}行"
            )

        # 准备结果列表
        results = []

        # 逐个处理query
        for index, row in df.iterrows():
            query = row["decontextualized_query"]
            logger.info(f"正在处理第{index + 1}行, query: {query}")

            try:
                # 调用OpenAI Vector Store检索
                result = await single_query_test(
                    query,
                    vector_store_id=vector_store_id,
                    api_key=api_key,
                    base_url=base_url,
                )

                # 记录结果
                result_record = {
                    "openai_query": query,
                    "openai_success": result.get("success", False),
                    "openai_max_score": result.get("max_score", 0),
                    "openai_doc_count": result.get("doc_count", 0),
                    "openai_retrieval_time_ms": result.get("retrieval_time_ms", 0),
                    "openai_response": str(result.get("response", "")),
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

                logger.info(
                    f"第{index + 1}行处理完成, 成功: {result.get('success')}, "
                    f"最高分: {result.get('max_score', 0)}, 文档数: {result.get('doc_count', 0)}"
                )

            except Exception as e:
                logger.error(f"处理第{index + 1}行时出错: {str(e)}")
                # 记录错误结果
                result_record = {
                    "openai_query": query,
                    "openai_success": False,
                    "openai_max_score": 0,
                    "openai_doc_count": 0,
                    "openai_retrieval_time_ms": 0,
                    "openai_response": str(e),
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

        # 保存结果到xlsx文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel(output_file, index=False)

            batch_end_time = time.perf_counter()
            batch_total_time = round((batch_end_time - batch_start_time) * 1000, 3)

            # 计算统计信息
            total_queries = len(results)
            successful_queries = sum(
                1 for r in results if r.get("openai_success", False)
            )
            avg_retrieval_time = round(
                sum(r.get("openai_retrieval_time_ms", 0) for r in results)
                / len(results),
                3,
            )

            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"共处理{total_queries}条query")
            logger.info(f"成功处理{successful_queries}条query")
            logger.info(f"成功率: {successful_queries / total_queries * 100:.2f}%")
            logger.info(f"批量处理总耗时: {batch_total_time}ms")
            logger.info(f"平均单个query检索耗时: {avg_retrieval_time}ms")
        else:
            logger.warning("没有有效的结果可保存")

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


def upload_files_demo(files_dir: str, api_key: str, base_url: str = None):
    """文件上传示例"""
    logger.info("开始文件上传示例")

    try:
        # 初始化管理器
        manager = OpenAIVectorStoreManager(api_key=api_key, base_url=base_url)

        # 创建Vector Store
        vector_store_id = manager.create_vector_store("AosKnowledgeBase2")

        file_paths = [os.path.join(files_dir, f) for f in os.listdir(files_dir)]

        print("file_paths: ", file_paths)
        # 检查文件是否存在
        existing_files = [path for path in file_paths if os.path.exists(path)]
        if not existing_files:
            logger.warning("没有找到可上传的文件，请检查文件路径")
            logger.info("如果要测试上传功能，请准备一些文档文件并更新file_paths列表")
            return vector_store_id

        # 上传文件
        upload_result = manager.upload_files_to_vector_store(
            vector_store_id, existing_files
        )
        logger.info(f"上传结果: {upload_result}")

        return vector_store_id

    except Exception as e:
        logger.error(f"文件上传示例失败: {str(e)}")
        return None


def print_vector_store_info(api_key: str, base_url: str = None):
    """打印vector store信息"""
    logger.info("开始打印vector store信息")
    try:
        # 初始化管理器
        manager = OpenAIVectorStoreManager(api_key=api_key, base_url=base_url)
        # 创建Vector Store
        vector_stores = manager.list_vector_stores()

        logger.info(f"vector_stores: {vector_stores}")

    except Exception as e:
        logger.error(f"文件上传示例失败: {str(e)}")


# AosKnowledgeBase , ID: "vs_685274de66b081919e1d4ac5bfeb4cec"
# AosKnowledgeBase2, ID: vs_6852ab975f088191a265035e5361ab53


def main():
    """主函数，支持命令行参数和单个测试"""

    # 配置日志
    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="OpenAI Vector Store RAG测试脚本")
    parser.add_argument("input_file", nargs="?", help="输入的xlsx文件路径")
    parser.add_argument("output_file", nargs="?", help="输出文件路径（可选）")
    parser.add_argument(
        "--vector-store-id",
        "-v",
        help="Vector Store ID",
        default="vs_6852ab975f088191a265035e5361ab53",
    )
    parser.add_argument("--api-key", "-k", help="OpenAI API密钥")
    parser.add_argument(
        "--base-url",
        "-b",
        help="OpenAI API基础URL",
        default="https://api.openai.com/v1",
    )
    parser.add_argument(
        "--case-ids",
        "-c",
        nargs="+",
        type=int,
        help="指定要运行的case ID列表，支持多个ID，例如: --case-ids 1 2 3",
    )
    parser.add_argument(
        "--start-row",
        "-s",
        type=int,
        help="从指定行开始测试（从1开始计数），例如: --start-row 10",
    )
    parser.add_argument(
        "--files-dir",
        "-f",
        help="文件目录",
    )
    parser.add_argument(
        "--print-vector-store-info",
        "-p",
        help="打印vector store信息",
    )
    args = parser.parse_args()

    if args.print_vector_store_info:
        print_vector_store_info(args.api_key, args.base_url)
        exit()

    if args.files_dir:
        vector_store_id = upload_files_demo(args.files_dir, args.api_key, args.base_url)
        print("vector_store_id: ", vector_store_id)

    elif args.input_file:
        # 批量测试模式
        logger.info(f"开始批量测试，输入文件: {args.input_file}")
        if args.output_file:
            logger.info(f"输出文件: {args.output_file}")
        if args.vector_store_id:
            logger.info(f"Vector Store ID: {args.vector_store_id}")
        if args.case_ids:
            logger.info(f"指定运行case IDs: {args.case_ids}")
        if args.start_row:
            logger.info(f"从第{args.start_row}行开始测试")

        # 运行批量测试
        asyncio.run(
            batch_test_from_xlsx(
                args.input_file,
                args.output_file,
                args.vector_store_id,
                args.case_ids,
                args.start_row,
                args.api_key,
                args.base_url,
            )
        )
    else:
        # 单个测试模式
        logger.info("运行单个测试模式")
        query = "介绍下普渡科技"

        async def run_single_test():
            result = await single_query_test(
                query,
                vector_store_id=args.vector_store_id,
                api_key=args.api_key,
                base_url=args.base_url,
            )
            logger.info(f"测试结果: {result}")

        asyncio.run(run_single_test())


if __name__ == "__main__":
    main()
