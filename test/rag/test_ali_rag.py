"""
Author: <PERSON><PERSON><PERSON><PERSON>
LastEditors: <PERSON><PERSON>
email: <PERSON><PERSON><PERSON><PERSON>@orionstar.com
github:
Date: 2025-06-16 14:37:48
LastEditTime: 2025-06-16 21:08:16
motto: Still water run deep
Description: Modify here please
FilePath: /easyNLP/test/rag/test_ali_rag.py
"""

import argparse
import asyncio
import os
import sys
import time
from pathlib import Path

import pandas as pd
from loguru import logger

from alibabacloud_bailian20231229.client import Client as bailian20231229Client
from alibabacloud_bailian20231229 import models as bailian_20231229_models
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models

# pip install alibabacloud_bailian20231229==2.0.8
# export ALIBABA_CLOUD_ACCESS_KEY_ID='LTAI5tMyRAPsoFxpxsWejFoT'
# export ALIBABA_CLOUD_ACCESS_KEY_SECRET='******************************'
# export WORKSPACE_ID='您的阿里云百炼业务空间ID'


os.environ["ALIBABA_CLOUD_ACCESS_KEY_ID"] = "LTAI5tMyRAPsoFxpxsWejFoT"
os.environ["ALIBABA_CLOUD_ACCESS_KEY_SECRET"] = "******************************"
os.environ["WORKSPACE_ID"] = "llm-mryehziqllw4ffu3"


def create_client() -> bailian20231229Client:
    """
    创建并配置客户端（Client）。

    返回:
        bailian20231229Client: 配置好的客户端（Client）。
    """
    config = open_api_models.Config(
        access_key_id=os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_ID"),
        access_key_secret=os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_SECRET"),
    )
    # 下方接入地址以公有云的公网接入地址为例，可按需更换接入地址。
    config.endpoint = "bailian.cn-beijing.aliyuncs.com"
    return bailian20231229Client(config)


def retrieve_index(client, workspace_id, index_id, query):
    """
    在指定的知识库中检索信息。

    参数:
        client (bailian20231229Client): 客户端（Client）。
        workspace_id (str): 业务空间ID。
        index_id (str): 知识库ID。
        query (str): 原始输入prompt。

    返回:
        阿里云百炼服务的响应。
    """
    headers = {}
    retrieve_request = bailian_20231229_models.RetrieveRequest(
        index_id=index_id, query=query
    )
    runtime = util_models.RuntimeOptions()
    return client.retrieve_with_options(
        workspace_id, retrieve_request, headers, runtime
    )


def single_query_test(
    query: str, client=None, workspace_id: str = None, index_id: str = None
):
    """
    单个query的阿里云百炼检索测试

    Args:
        query: 查询文本
        client: 阿里云百炼客户端
        workspace_id: 业务空间ID
        index_id: 知识库ID

    Returns:
        dict: 包含检索结果和耗时信息
    """
    start_time = time.perf_counter()

    try:
        if client is None:
            client = create_client()
        if workspace_id is None:
            workspace_id = os.environ.get("WORKSPACE_ID")
        if index_id is None:
            index_id = "lwa7iuioz3"  # 默认知识库ID

        # 执行检索
        response = retrieve_index(client, workspace_id, index_id, query)

        end_time = time.perf_counter()
        retrieval_time = round((end_time - start_time) * 1000, 3)  # 转换为毫秒

        logger.info(f"检索耗时: {retrieval_time}ms")

        # 解析响应结果
        response_dict = (
            response.to_map() if hasattr(response, "to_map") else str(response)
        )

        return {
            "response": response_dict,
            "retrieval_time_ms": retrieval_time,
            "success": True,
        }

    except Exception as e:
        end_time = time.perf_counter()
        retrieval_time = round((end_time - start_time) * 1000, 3)

        logger.error(f"检索过程中出错: {str(e)}")
        return {
            "query": query,
            "response": str(e),
            "retrieval_time_ms": retrieval_time,
            "success": False,
        }


async def batch_test_from_xlsx(
    input_file: str,
    output_file: str = None,
    case_ids: list = None,
    start_row: int = None,
):
    """
    从xlsx文件批量测试query，使用阿里云百炼检索

    Args:
        input_file: 输入的xlsx文件路径
        output_file: 输出文件路径，如果为None则自动生成
        case_ids: 指定要运行的case ID列表，如果为None则运行所有case
        start_row: 从指定行开始测试（1-based，即第1行为数据的第一行），如果为None则从第一行开始
    """
    batch_start_time = time.perf_counter()
    input_path = Path(input_file)

    # 检查输入文件是否存在
    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return

    # 设置输出文件路径
    if output_file is None:
        filename_parts = []
        if case_ids:
            case_ids_str = "_".join(map(str, case_ids))
            filename_parts.append(f"cases_{case_ids_str}")
        if start_row:
            filename_parts.append(f"from_row_{start_row}")

        if filename_parts:
            suffix = "_" + "_".join(filename_parts)
            output_file = (
                input_path.parent / f"{input_path.stem}_ali_results{suffix}.xlsx"
            )
        else:
            output_file = input_path.parent / f"{input_path.stem}_ali_results.xlsx"

    try:
        # 读取xlsx文件
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共{len(df)}行数据")

        # 如果指定了case_ids，过滤数据
        if case_ids:
            # 过滤指定的case_ids
            original_count = len(df)
            df = df[df["caseID"].isin(case_ids)]
            filtered_count = len(df)

            logger.info(f"指定case_ids: {case_ids}")
            logger.info(f"原始数据: {original_count}行, 过滤后: {filtered_count}行")

            if filtered_count == 0:
                logger.warning("没有找到匹配的case ID，请检查输入的case_ids是否正确")
                return

        # 应用start_row过滤（如果指定）
        if start_row:
            original_count = len(df)
            # start_row是1-based，转换为0-based索引
            df = df.iloc[start_row - 1 :]
            filtered_count = len(df)
            logger.info(f"指定从第{start_row}行开始")
            logger.info(
                f"原始数据: {original_count}行, 跳过前{start_row - 1}行后: {filtered_count}行"
            )

        # 准备结果列表
        results = []

        # 创建阿里云百炼客户端
        client = create_client()
        workspace_id = os.environ.get("WORKSPACE_ID")
        index_id = "lwa7iuioz3"  # 知识库ID

        # 逐个处理query
        for index, row in df.iterrows():
            query = row["decontextualized_query"]
            logger.info(f"正在处理第{index + 1}行, query: {query}")

            try:
                # 调用阿里云百炼检索
                result = single_query_test(query, client, workspace_id, index_id)

                # 解析响应结果，提取分数等信息
                response = result.get("response", {})
                # print(result)

                # 尝试从响应中提取相关分数（根据阿里云百炼的响应格式调整）
                max_score = 0
                doc_count = 0

                success = "Failed"
                if isinstance(response, dict):
                    # 根据实际的阿里云百炼响应格式提取信息
                    body = response.get("body", {})
                    if isinstance(body, dict):
                        success = body.get("Code", "Failed")
                        data = body.get("Data", {})
                        # print(data)
                        if isinstance(data, dict):
                            nodes = data.get("Nodes", [])
                            print(nodes)
                            if nodes:
                                doc_count = len(nodes)
                                # 获取最高分数
                                scores = [
                                    node.get("Score", 0)
                                    for node in nodes
                                    if isinstance(node, dict)
                                ]
                                if scores:
                                    max_score = max(scores)

                # 记录结果
                result_record = {
                    "ali_query": query,
                    "ali_success": success,
                    "ali_max_score": max_score,
                    "ali_doc_count": doc_count,
                    "ali_retrieval_time_ms": result.get("retrieval_time_ms", 0),
                    "ali_response": str(response),  # 转换为字符串以便存储
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

                logger.info(
                    f"第{index + 1}行处理完成, 成功: {success}, 最高分: {max_score}, 文档数: {doc_count}"
                )

            except Exception as e:
                logger.error(f"处理第{index + 1}行时出错: {str(e)}")
                # 记录错误结果
                result_record = {
                    "ali_query": query,
                    "ali_success": False,
                    "ali_max_score": 0,
                    "ali_doc_count": 0,
                    "ali_retrieval_time_ms": 0,
                    "ali_response": str(e),
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

        # 保存结果到xlsx文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel(output_file, index=False)

            batch_end_time = time.perf_counter()
            batch_total_time = round((batch_end_time - batch_start_time) * 1000, 3)

            # 计算统计信息
            total_queries = len(results)
            successful_queries = sum(1 for r in results if r.get("success", False))
            avg_retrieval_time = round(
                sum(r.get("retrieval_time_ms", 0) for r in results) / len(results), 3
            )

            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"共处理{total_queries}条query")
            logger.info(f"成功处理{successful_queries}条query")
            logger.info(f"成功率: {successful_queries / total_queries * 100:.2f}%")
            logger.info(f"批量处理总耗时: {batch_total_time}ms")
            logger.info(f"平均单个query检索耗时: {avg_retrieval_time}ms")
        else:
            logger.warning("没有有效的结果可保存")

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


def main():
    """主函数，支持命令行参数和单个测试"""

    # 配置日志
    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="阿里云百炼RAG测试脚本")
    parser.add_argument("input_file", nargs="?", help="输入的xlsx文件路径")
    parser.add_argument("output_file", nargs="?", help="输出文件路径（可选）")
    parser.add_argument(
        "--case-ids",
        "-c",
        nargs="+",
        type=int,
        help="指定要运行的case ID列表，支持多个ID，例如: --case-ids 1 2 3",
    )
    parser.add_argument(
        "--start-row",
        "-s",
        type=int,
        help="从指定行开始测试（从1开始计数），例如: --start-row 10",
    )

    args = parser.parse_args()

    if args.input_file:
        # 批量测试模式
        logger.info(f"开始批量测试，输入文件: {args.input_file}")
        if args.output_file:
            logger.info(f"输出文件: {args.output_file}")
        if args.case_ids:
            logger.info(f"指定运行case IDs: {args.case_ids}")
        if args.start_row:
            logger.info(f"从第{args.start_row}行开始测试")

        # 运行批量测试
        asyncio.run(
            batch_test_from_xlsx(
                args.input_file, args.output_file, args.case_ids, args.start_row
            )
        )
    else:
        # 单个测试模式（原始功能）
        logger.info("运行单个测试模式")
        client = create_client()
        index_id = "lwa7iuioz3"  # 知识库ID
        query = "介绍下普渡科技"

        result = single_query_test(
            query, client, os.environ.get("WORKSPACE_ID"), index_id
        )
        logger.info(f"测试结果: {result}")


if __name__ == "__main__":
    main()
