"""
Author: <PERSON><PERSON><PERSON><PERSON>
LastEditors: <PERSON><PERSON>
email: <PERSON><PERSON><PERSON><PERSON>@orionstar.com
github:
Date: 2025-01-18 10:00:00
LastEditTime: 2025-01-18 10:00:00
motto: Still water run deep
Description: Google Vertex RAG implementation
FilePath: /easyNLP/test/rag/test_vertex_rag.py
"""

import argparse
import asyncio
import os
import sys
import time
import ast
import json
from pathlib import Path

import pandas as pd
import numpy as np
from loguru import logger
from google.cloud import storage
import vertexai
from vertexai import rag
from vertexai.generative_models import GenerativeModel, Tool
from src.utils.llm import LLMManager, LLMConfig
from src.settings import agent_setting


# current_dir = os.path.dirname(os.path.abspath(__file__))
# # 构建JSON文件的完整路径
# json_file_path = os.path.join(current_dir, "orionstar-ai-gemini-807c6141e429.json")
# os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = json_file_path


async def decontextualize_query(query: str, previous_queries: list = None) -> dict:
    """
    对query进行去上下文操作，确保查询是完整和独立的

    Args:
        query: 当前查询
        previous_queries: 之前的查询列表

    Returns:
        dict: 包含是否需要去上下文化和去上下文化后的查询，以及耗时
    """
    start_time = time.perf_counter()

    if not previous_queries:
        previous_queries = []

    # 构建prompt
    prompt = f"""Analyze whether the query contains ambiguous references that require conversation history to be understood.

STRICT CRITERIA - Mark "True" ONLY if the query contains:
1. Ambiguous pronouns that refer to something from history ("it", "that", "this", "they", "which one")
2. Unclear demonstratives without clear context ("that", "this", "here", "over there")
3. Incomplete comparative or continuation references ("what about...", "and also...", "in addition...")
4. Missing essential subjects that are only identifiable from conversation history

DO NOT mark "True" for:
- Complete action commands and control instructions
- Complete statements with clear standalone meaning
- Emotional expressions and exclamations
- Self-contained questions or requests
- Commands that include all necessary context
- Status descriptions or observations

When decontextualizing (if True):
- Use MINIMAL necessary information from history
- Preserve original tone, style, and intent exactly
- Do NOT add explanatory words or extra descriptions
- Do NOT answer the current query
- Only fill in the missing essential information

Query: "{query}"
History: {previous_queries}

Return JSON only (ensure both fields are always present and non-empty):
{{
    "requires_decontextualization": "True or False",
    "decontextualized_query": "Complete standalone query with original meaning"
}}"""

    try:
        # 调用大模型进行去上下文化
        llm_config = LLMConfig(
            base_url=agent_setting.generate_text_model_base_url,
            llm_model_name=agent_setting.generate_text_model,
            api_key=agent_setting.generate_text_model_api_key,
            temperature=0.0,
            timeout=10,
        )

        messages = [{"role": "user", "content": prompt}]
        result = await LLMManager.invoke_generate_text_model(
            messages, llm_config=llm_config
        )

        # 解析返回结果
        try:
            response_data = json.loads(result.content)
            end_time = time.perf_counter()
            decontext_time = round(
                (end_time - start_time) * 1000, 3
            )  # 转换为毫秒，保留3位小数
            response_data["decontextualization_time_ms"] = decontext_time
            logger.info(f"去上下文化结果: {response_data}, 耗时: {decontext_time}ms")
            return response_data
        except json.JSONDecodeError as e:
            end_time = time.perf_counter()
            decontext_time = round((end_time - start_time) * 1000, 3)
            logger.error(f"解析去上下文化结果失败: {e}, 原始内容: {result.content}")
            # 如果解析失败，返回原始查询
            return {
                "requires_decontextualization": "False",
                "decontextualized_query": query,
                "decontextualization_time_ms": decontext_time,
            }

    except Exception as e:
        end_time = time.perf_counter()
        decontext_time = round((end_time - start_time) * 1000, 3)
        logger.error(f"去上下文化过程中出错: {str(e)}")
        # 如果出错，返回原始查询
        return {
            "requires_decontextualization": "False",
            "decontextualized_query": query,
            "decontextualization_time_ms": decontext_time,
        }


class VertexRAGManager:
    """Google Vertex RAG管理器"""

    def __init__(self, project_id: str = None, location: str = "us-central1"):
        """
        初始化Google Vertex RAG管理器

        Args:
            project_id: Google Cloud项目ID
            location: Google Cloud区域，默认us-central1
        """
        self.project_id = project_id or os.getenv("GOOGLE_CLOUD_PROJECT")
        self.location = location

        if not self.project_id:
            raise ValueError(
                "Google Cloud项目ID未设置，请设置GOOGLE_CLOUD_PROJECT环境变量或传入project_id参数"
            )

        # 初始化Vertex AI
        vertexai.init(project=self.project_id, location=self.location)
        logger.info(f"初始化Vertex AI: 项目={self.project_id}, 区域={self.location}")

    def create_rag_corpus(self, display_name: str, description: str = None) -> str:
        """
        创建RAG Corpus

        Args:
            display_name: Corpus显示名称
            description: Corpus描述

        Returns:
            str: Corpus资源名称
        """
        try:
            # 配置embedding模型
            embedding_model_config = rag.RagEmbeddingModelConfig(
                vertex_prediction_endpoint=rag.VertexPredictionEndpoint(
                    publisher_model="publishers/google/models/text-multilingual-embedding-002"
                )
            )

            # 创建RAG Corpus
            rag_corpus = rag.create_corpus(
                display_name=display_name,
                description=description or f"RAG Corpus: {display_name}",
                backend_config=rag.RagVectorDbConfig(
                    rag_embedding_model_config=embedding_model_config
                ),
            )

            logger.info(
                f"成功创建RAG Corpus: {display_name}, 资源名称: {rag_corpus.name}"
            )
            return rag_corpus.name
        except Exception as e:
            logger.error(f"创建RAG Corpus失败: {str(e)}")
            raise

    def upload_files_to_corpus(
        self,
        corpus_name: str,
        file_paths: list,
        chunk_size: int = 512,
        chunk_overlap: int = 100,
    ) -> dict:
        """
        上传文件到RAG Corpus

        Args:
            corpus_name: Corpus资源名称
            file_paths: 文件路径列表 (支持gs://, Google Drive链接, 本地文件)
            chunk_size: 文档分块大小
            chunk_overlap: 分块重叠大小

        Returns:
            dict: 上传结果信息
        """
        try:
            # 处理本地文件路径，转换为有效的路径
            valid_paths = []
            for path in file_paths:
                if path.startswith(("gs://", "https://drive.google.com/")):
                    valid_paths.append(path)
                elif os.path.exists(path):
                    # 对于本地文件，需要先上传到GCS或使用其他支持的方式
                    logger.warning(
                        f"本地文件路径 {path} 需要先上传到Google Cloud Storage"
                    )
                    continue
                else:
                    logger.warning(f"文件不存在或不支持的路径: {path}")
                    continue

            if not valid_paths:
                raise ValueError("没有有效的文件路径可上传")

            # 导入文件到RAG Corpus
            response = rag.import_files(
                corpus_name=corpus_name,
                paths=valid_paths,
                transformation_config=rag.TransformationConfig(
                    chunking_config=rag.ChunkingConfig(
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap,
                    ),
                ),
                max_embedding_requests_per_min=1000,  # 可选的速率限制
            )

            logger.info(
                f"文件导入完成，导入文件数量: {response.imported_rag_files_count}"
            )

            return {
                "imported_files_count": response.imported_rag_files_count,
                "corpus_name": corpus_name,
            }

        except Exception as e:
            logger.error(f"上传文件到RAG Corpus失败: {str(e)}")
            raise

    async def search_corpus(
        self,
        corpus_name: str,
        query: str,
        top_k: int = 10,
        vector_distance_threshold: float = 0.3,
    ) -> dict:
        """
        搜索RAG Corpus

        Args:
            corpus_name: Corpus资源名称
            query: 查询文本
            top_k: 返回结果数量
            distance_threshold: 向量距离阈值

        Returns:
            dict: 搜索结果
        """
        try:
            # 配置检索参数
            rag_retrieval_config = rag.RagRetrievalConfig(
                top_k=top_k,
                filter=rag.Filter(vector_distance_threshold=vector_distance_threshold),
            )

            # 执行检索查询
            response = rag.retrieval_query(
                rag_resources=[
                    rag.RagResource(
                        rag_corpus=corpus_name,
                        # 可选: 指定特定的rag_file_ids
                        # rag_file_ids=["rag-file-1", "rag-file-2", ...],
                    )
                ],
                text=query,
                rag_retrieval_config=rag_retrieval_config,
            )

            logger.info(f"检索成功，返回{len(response.contexts.contexts)}个结果")

            # 解析结果
            results = []
            for context in response.contexts.contexts:
                # Google Vertex AI RAG 默认使用 COSINE_DISTANCE，返回的是距离值 [0, 2]
                if hasattr(context, "score"):
                    score = context.score
                    similarity_score = 1 - (score / 2)
                else:
                    score = 0
                    similarity_score = 0

                results.append(
                    {
                        "text": context.text,
                        "source_uri": context.source_uri,
                        "score": score,  # 原始分数
                        "similarity_score": similarity_score,  # 转换后的相似度分数
                    }
                )

            return {
                "query": query,
                "results": results,
                "success": True,
                "result_count": len(results),
            }

        except Exception as e:
            logger.error(f"搜索RAG Corpus失败: {str(e)}")
            return {
                "query": query,
                "results": [],
                "success": False,
                "error": str(e),
                "result_count": 0,
            }

    def list_corpora(self) -> list:
        """
        列出所有RAG Corpora

        Returns:
            list: Corpus列表
        """
        try:
            # 注意：这个API可能需要使用REST API调用
            # 这里提供一个基本的实现框架
            logger.info("列出RAG Corpora功能需要通过REST API实现")
            return []
        except Exception as e:
            logger.error(f"列出RAG Corpora失败: {str(e)}")
            return []

    def delete_corpus(self, corpus_name: str) -> bool:
        """
        删除RAG Corpus

        Args:
            corpus_name: Corpus资源名称

        Returns:
            bool: 是否删除成功
        """
        try:
            # 注意：删除功能需要通过REST API实现
            logger.info(f"删除RAG Corpus功能需要通过REST API实现: {corpus_name}")
            return True
        except Exception as e:
            logger.error(f"删除RAG Corpus失败: {str(e)}")
            return False

    def generate_with_rag(
        self,
        corpus_name: str,
        query: str,
        model_name: str = "gemini-2.0-flash-001",
        top_k: int = 10,
        distance_threshold: float = 0.5,
    ) -> str:
        """
        使用RAG增强的生成

        Args:
            corpus_name: Corpus资源名称
            query: 查询文本
            model_name: 生成模型名称
            top_k: 检索结果数量
            distance_threshold: 向量距离阈值

        Returns:
            str: 生成的回答
        """
        try:
            # 创建RAG检索工具
            rag_retrieval_tool = Tool.from_retrieval(
                retrieval=rag.Retrieval(
                    source=rag.VertexRagStore(
                        rag_resources=[
                            rag.RagResource(
                                rag_corpus=corpus_name,
                            )
                        ],
                        rag_retrieval_config=rag.RagRetrievalConfig(
                            top_k=top_k,
                            filter=rag.Filter(
                                vector_distance_threshold=distance_threshold
                            ),
                        ),
                    ),
                )
            )

            # 创建生成模型
            rag_model = GenerativeModel(
                model_name=model_name, tools=[rag_retrieval_tool]
            )

            # 生成回答
            response = rag_model.generate_content(query)
            return response.text

        except Exception as e:
            logger.error(f"RAG增强生成失败: {str(e)}")
            raise


async def batch_test_from_xlsx(
    input_file: str,
    output_file: str = None,
    corpus_name: str = None,
    case_ids: list = None,
    start_row: int = None,
    project_id: str = None,
    location: str = "us-central1",
):
    """
    从xlsx文件批量测试query，使用Google Vertex RAG检索

    Args:
        input_file: 输入的xlsx文件路径
        output_file: 输出文件路径，如果为None则自动生成
        corpus_name: Corpus资源名称
        case_ids: 指定要运行的case ID列表，如果为None则运行所有case
        start_row: 从指定行开始测试（1-based），如果为None则从第一行开始
        project_id: Google Cloud项目ID
        location: Google Cloud区域
    """
    batch_start_time = time.perf_counter()
    input_path = Path(input_file)

    # 检查输入文件是否存在
    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return

    # 设置输出文件路径
    if output_file is None:
        filename_parts = []
        if case_ids:
            case_ids_str = "_".join(map(str, case_ids))
            filename_parts.append(f"cases_{case_ids_str}")
        if start_row:
            filename_parts.append(f"from_row_{start_row}")

        if filename_parts:
            suffix = "_" + "_".join(filename_parts)
            output_file = (
                input_path.parent / f"{input_path.stem}_vertex_rag_results{suffix}.xlsx"
            )
        else:
            output_file = (
                input_path.parent / f"{input_path.stem}_vertex_rag_results.xlsx"
            )

    try:
        # 读取xlsx文件
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共{len(df)}行数据")

        # 如果指定了case_ids，过滤数据
        if case_ids:
            original_count = len(df)
            df = df[df["caseID"].isin(case_ids)]
            filtered_count = len(df)

            logger.info(f"指定case_ids: {case_ids}")
            logger.info(f"原始数据: {original_count}行, 过滤后: {filtered_count}行")

            if filtered_count == 0:
                logger.warning("没有找到匹配的case ID，请检查输入的case_ids是否正确")
                return

        # 应用start_row过滤（如果指定）
        if start_row:
            original_count = len(df)
            df = df.iloc[start_row - 1 :]
            filtered_count = len(df)
            logger.info(f"指定从第{start_row}行开始")
            logger.info(
                f"原始数据: {original_count}行, 跳过前{start_row - 1}行后: {filtered_count}行"
            )

        # 准备结果列表
        results = []

        # 初始化管理器
        manager = VertexRAGManager(project_id=project_id, location=location)
        # 如果没有指定corpus_name，需要用户提供
        if not corpus_name:
            logger.warning("没有指定Corpus资源名称，请提供有效的corpus_name")
            exit(1)

        # 逐个处理query
        for index, row in df.iterrows():
            if "decontextualized_query" in row:
                query = row["decontextualized_query"]
            else:
                # 使用原始问题测试
                # query = row["query"]
                # decontextualized_query_result = {}

                # 使用rewrite query测试
                query_lst = ast.literal_eval(row["query"])
                decontextualized_query_result = await decontextualize_query(query_lst)
                query = decontextualized_query_result["decontextualized_query"]

            logger.info(f"正在处理第{index + 1}行, query: {query}")

            try:
                start_time = time.perf_counter()
                # 执行搜索
                response = await manager.search_corpus(corpus_name, query)
                end_time = time.perf_counter()
                retrieval_time = round((end_time - start_time) * 1000, 3)  # 转换为毫秒

                logger.info(f"检索结果: {response}")
                logger.info(f"检索耗时: {retrieval_time}ms")

                # 解析搜索结果
                max_score = 0
                doc_count = 0
                success = response.get("success", False)
                if success and response.get("results"):
                    doc_count = len(response["results"])
                    scores = [
                        item.get("similarity_score", 0) for item in response["results"]
                    ]
                    if scores:
                        max_score = max(scores)

                # 记录结果
                result_record = {
                    "vertex_rag_query": query,
                    "vertex_rag_success": success,
                    "vertex_rag_max_score": max_score,
                    "vertex_rag_doc_count": doc_count,
                    "vertex_rag_retrieval_time_ms": retrieval_time,
                    "vertex_rag_response": str(response),
                }

                result_record = {**result_record, **decontextualized_query_result}
                print(result_record)

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

                logger.info(
                    f"第{index + 1}行处理完成, 成功: {success}, "
                    f"最高分: {max_score}, 文档数: {doc_count}"
                )

            except Exception as e:
                logger.error(f"处理第{index + 1}行时出错: {str(e)}")
                # 记录错误结果
                result_record = {
                    "vertex_rag_query": query,
                    "vertex_rag_success": False,
                    "vertex_rag_max_score": 0,
                    "vertex_rag_doc_count": 0,
                    "vertex_rag_retrieval_time_ms": 0,
                    "vertex_rag_response": str(e),
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

        # 保存结果到xlsx文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel(output_file, index=False)

            batch_end_time = time.perf_counter()
            batch_total_time = round((batch_end_time - batch_start_time) * 1000, 3)

            # 计算统计信息
            total_queries = len(results)
            successful_queries = sum(
                1 for r in results if r.get("vertex_rag_success", False)
            )

            # 计算检索时间统计
            retrieval_times = [r.get("vertex_rag_retrieval_time_ms", 0) for r in results]
            avg_retrieval_time = round(
                sum(retrieval_times) / len(retrieval_times),
                3,
            )
            p90_retrieval_time = round(np.percentile(retrieval_times, 90), 3)

            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"共处理{total_queries}条query")
            logger.info(f"成功处理{successful_queries}条query")
            logger.info(f"成功率: {successful_queries / total_queries * 100:.2f}%")
            logger.info(f"批量处理总耗时: {batch_total_time}ms")
            logger.info(f"平均单个query检索耗时(Avg): {avg_retrieval_time}ms")
            logger.info(f"P90单个query检索耗时(P90): {p90_retrieval_time}ms")
        else:
            logger.warning("没有有效的结果可保存")

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


def upload_local_files_to_gcs(files_dir: str, project_id: str, bucket_name: str):
    """将本地文件上传到Google Cloud Storage"""
    logger.info("开始将本地文件上传到Google Cloud Storage")
    try:
        # 检查文件目录是否存在
        if not os.path.exists(files_dir):
            logger.error(f"文件目录不存在: {files_dir}")
            return []

        # 检查认证设置和存储桶
        try:
            # 初始化GCS客户端
            storage_client = storage.Client(project=project_id)
            bucket = storage_client.bucket(bucket_name)

            # 检查存储桶是否存在
            if not bucket.exists():
                logger.warning(f"存储桶 {bucket_name} 不存在，正在创建...")
                try:
                    # 创建存储桶
                    bucket = storage_client.create_bucket(
                        bucket_name, location="us-central1"
                    )
                    logger.info(f"成功创建存储桶: {bucket_name}")
                except Exception as create_error:
                    logger.error(f"创建存储桶失败: {str(create_error)}")
                    logger.error("请确保您有创建存储桶的权限，或手动创建存储桶")
                    return []
            else:
                logger.info(f"存储桶 {bucket_name} 已存在")

        except Exception as auth_error:
            logger.error("Google Cloud Storage认证失败")
            logger.error("请按照以下步骤设置认证:")
            logger.error("1. 安装Google Cloud SDK: sudo snap install google-cloud-cli")
            logger.error("2. 初始化: gcloud init")
            logger.error("3. 设置应用默认凭据: gcloud auth application-default login")
            logger.error(
                "或者设置服务账号密钥: export GOOGLE_APPLICATION_CREDENTIALS='/path/to/key.json'"
            )
            logger.error(f"详细错误信息: {str(auth_error)}")
            return []

        # 获取文件目录中的所有文件
        uploaded_files = []
        supported_extensions = [".txt", ".pdf", ".docx", ".doc", ".md", ".json"]

        for filename in os.listdir(files_dir):
            file_path = os.path.join(files_dir, filename)

            # 检查是否为文件且支持的格式
            if os.path.isfile(file_path):
                file_extension = os.path.splitext(filename)[1].lower()
                if file_extension in supported_extensions:
                    logger.info(f"准备上传文件: {filename}")

                    try:
                        # 上传到GCS
                        blob_name = f"rag_documents/{filename}"
                        blob = bucket.blob(blob_name)

                        # 上传文件
                        blob.upload_from_filename(file_path)

                        # 构造GCS路径
                        gcs_path = f"gs://{bucket_name}/{blob_name}"

                        logger.info(f"文件 {filename} 已上传到: {gcs_path}")

                        # 记录文件信息
                        uploaded_files.append(
                            {
                                "filename": filename,
                                "local_path": file_path,
                                "gcs_path": gcs_path,
                                "size": os.path.getsize(file_path),
                                "extension": file_extension,
                                "status": "uploaded",
                            }
                        )

                    except Exception as file_error:
                        logger.error(f"上传文件 {filename} 时出错: {str(file_error)}")
                        uploaded_files.append(
                            {
                                "filename": filename,
                                "local_path": file_path,
                                "status": "error",
                                "error": str(file_error),
                            }
                        )
                else:
                    logger.warning(
                        f"不支持的文件格式: {filename} (扩展名: {file_extension})"
                    )
            else:
                logger.info(f"跳过非文件项: {filename}")

        # 打印上传摘要
        successful_uploads = [f for f in uploaded_files if f["status"] == "uploaded"]
        logger.info(f"上传完成，共上传 {len(successful_uploads)} 个文件")

        # 返回GCS路径列表
        gcs_paths = [f["gcs_path"] for f in successful_uploads]
        return gcs_paths

    except Exception as e:
        logger.error(f"上传过程中发生错误: {str(e)}")
        return []


def upload_rag_corpus(file_paths: list, project_id: str, location: str = "us-central1"):
    """文件上传示例"""
    logger.info("开始文件上传示例")
    try:
        # 初始化管理器
        manager = VertexRAGManager(project_id=project_id, location=location)
        # 创建RAG Corpus
        corpus_name = manager.create_rag_corpus("TestKnowledgeBase", "测试知识库")
        # corpus_name = manager.create_rag_corpus("TestSpeedTest", "测试接口速度")

        if not file_paths:
            logger.warning("没有找到可上传的文件路径")
            logger.info("请提供GCS路径(gs://)或Google Drive链接来测试上传功能")
            return corpus_name

        # 上传文件
        upload_result = manager.upload_files_to_corpus(corpus_name, file_paths)
        logger.info(f"上传结果: {upload_result}")

        return corpus_name

    except Exception as e:
        logger.error(f"文件上传示例失败: {str(e)}")
        return None


def print_corpus_info(project_id: str, location: str = "us-central1"):
    """打印corpus信息"""
    logger.info("开始打印Corpus信息")
    try:
        # 初始化管理器
        manager = VertexRAGManager(project_id=project_id, location=location)
        # 列出Corpus
        corpora = manager.list_corpora()
        logger.info(f"找到的Corpus: {corpora}")

    except Exception as e:
        logger.error(f"获取Corpus信息失败: {str(e)}")


def distance_to_similarity(file_path: str):
    """读取Excel文件并返回数据"""
    try:
        df = pd.read_excel(file_path)
        results = []
        for idx, row in df.iterrows():
            raw_response = ast.literal_eval(row["vertex_rag_response"])
            distances = [item["score"] for item in raw_response["results"]]
            min_distance = min(distances) if distances else 2
            # 分数转换: 将余弦距离 [0, 2] 转换为相似度分数 [1, 0]
            result_record = {
                "min_distance": min_distance,
                "similarity_score": 1 - (min_distance / 2),
            }
            # 保留原始数据的其他列
            for col in df.columns:
                result_record[col] = row[col]

            results.append(result_record)

        # save results to excel
        results_df = pd.DataFrame(results)
        results_df.to_excel(file_path.replace(".xlsx", "_converted.xlsx"), index=False)

    except Exception as e:
        logger.error(f"读取Excel文件失败: {str(e)}")


BUCKET_NAME = "aos-test-documents"
PROJECT_ID = "orionstar-ai-gemini"
LOCATION = "us-central1"  # asia-east2 us-central1  global

# us-central1
FILE_PATHS = [
    "gs://aos-test-documents/rag_documents/cd59e5b9f5dd7c6.pdf",
    "gs://aos-test-documents/rag_documents/6b44298c491202cc.pdf",
    "gs://aos-test-documents/rag_documents/592a1748f2a93637.pdf",
    "gs://aos-test-documents/rag_documents/73e72a89cace01ed.pdf",
    "gs://aos-test-documents/rag_documents/61eaaab4e43de14c.pdf",
    "gs://aos-test-documents/rag_documents/271b4c4ee8c45b4e.pdf",
    "gs://aos-test-documents/rag_documents/4822aa8cdfd35b3a.pdf",
    "gs://aos-test-documents/rag_documents/285c013515509b80.pdf",
]

# Norwegian Maritime Museum files
NORSK_MARITIMT_MUSEUM_FILE_PATHS = [
    "gs://aos-test-documents/rag_documents/Du snakker alle disse språkene.docx",
    "gs://aos-test-documents/rag_documents/Samlet tekst NMM.docx",
    "gs://aos-test-documents/rag_documents/Bathallen_NMM_Knowledge_FAQ_NO_EN.txt",
]

us_central1_corpus_name = (
    "projects/613914673221/locations/us-central1/ragCorpora/4611686018427387904"
)

# Norwegian Maritime Museum corpus
norsk_maritimt_museum_corpus_name = (
    "projects/613914673221/locations/us-central1/ragCorpora/9144559043375792128"
)


def main():
    """主函数，支持命令行参数和单个测试"""

    # 配置日志
    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="Google Vertex RAG测试脚本")
    parser.add_argument("input_file", nargs="?", help="输入的xlsx文件路径")
    parser.add_argument("output_file", nargs="?", help="输出文件路径（可选）")
    parser.add_argument(
        "--corpus-name",
        "-c",
        default=us_central1_corpus_name,
        help="Corpus资源名称，格式: projects/{PROJECT_ID}/locations/{LOCATION}/ragCorpora/{CORPUS_ID}",
    )
    parser.add_argument(
        "--project-id", "-p", help="Google Cloud项目ID", default=PROJECT_ID
    )
    parser.add_argument(
        "--location",
        "-l",
        help="Google Cloud区域",
        default=LOCATION,
    )
    parser.add_argument(
        "--case-ids",
        "-i",
        nargs="+",
        type=int,
        help="指定要运行的case ID列表，支持多个ID，例如: --case-ids 1 2 3",
    )
    parser.add_argument(
        "--start-row",
        "-s",
        type=int,
        help="从指定行开始测试（从1开始计数），例如: --start-row 10",
    )
    parser.add_argument(
        "--files-dir",
        "-f",
        help="文件目录（用于上传测试）",
    )
    parser.add_argument(
        "--upload-to-gcs",
        "-g",
        action="store_true",
        help="上传文件到Google Cloud Storage",
    )
    parser.add_argument(
        "--upload-rag-corpus",
        "-r",
        action="store_true",
        help="上传文件到RAG Corpus",
    )
    parser.add_argument(
        "--print-corpus-info",
        "-n",
        action="store_true",
        help="打印corpus信息",
    )
    args = parser.parse_args()

    if args.print_corpus_info:
        print_corpus_info(args.project_id, args.location)
        exit()

    # if args.input_file:
    #     distance_to_similarity(args.input_file)
    #     exit()

    if args.upload_to_gcs:
        if args.files_dir:
            # 上传到Google Cloud Storage
            logger.info(f"开始上传文件到GCS存储桶: {BUCKET_NAME}")
            gcs_paths = upload_local_files_to_gcs(
                args.files_dir,
                args.project_id,
                BUCKET_NAME,
            )

            if gcs_paths:
                logger.info(f"成功上传 {len(gcs_paths)} 个文件到GCS")
                print("GCS路径列表：")
                for path in gcs_paths:
                    print(f"  - {path}")
            else:
                logger.error("没有文件成功上传到GCS")
        else:
            logger.error("没有文件目录")
    elif args.upload_rag_corpus:
        corpus_name = upload_rag_corpus(FILE_PATHS, args.project_id, args.location)
        print("corpus_name: ", corpus_name)

    elif args.input_file:
        # 批量测试模式
        logger.info(f"开始批量测试，输入文件: {args.input_file}")
        if args.output_file:
            logger.info(f"输出文件: {args.output_file}")
        if args.corpus_name:
            logger.info(f"Corpus名称: {args.corpus_name}")
        if args.case_ids:
            logger.info(f"指定运行case IDs: {args.case_ids}")
        if args.start_row:
            logger.info(f"从第{args.start_row}行开始测试")

        # 运行批量测试
        asyncio.run(
            batch_test_from_xlsx(
                args.input_file,
                args.output_file,
                args.corpus_name,
                args.case_ids,
                args.start_row,
                args.project_id,
                args.location,
            )
        )


if __name__ == "__main__":
    main()
