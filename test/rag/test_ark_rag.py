"""
pip install volcengine
pip install pandas openpyxl loguru
"""

import json
import requests
import pandas as pd
import time
import argparse
from pathlib import Path
from loguru import logger
import sys

from volcengine.auth.SignerV4 import SignerV4
from volcengine.base.Request import Request
from volcengine.Credentials import Credentials

collection_name = "aos"  # test_RAG
project_name = "default"
query = "介绍下山西老陈醋"
ak = "AKLTOWM0Nzg1ODA1YTY2NGQwM2JlNTE3YjExMmZjZWIxZDU"
sk = "TjJJell6aGlNek13Tm1JeE5ETXdaVGs1WXpobE9EVmpZVFU0WXpObE1XVQ=="
g_knowledge_base_domain = "api-knowledgebase.mlp.cn-beijing.volces.com"
account_id = "**********"


def prepare_request(method, path, params=None, data=None, doseq=0):
    if params:
        for key in params:
            if (
                isinstance(params[key], int)
                or isinstance(params[key], float)
                or isinstance(params[key], bool)
            ):
                params[key] = str(params[key])
            elif isinstance(params[key], list):
                if not doseq:
                    params[key] = ",".join(params[key])
    r = Request()
    r.set_shema("http")
    r.set_method(method)
    r.set_connection_timeout(10)
    r.set_socket_timeout(10)
    mheaders = {
        "Accept": "application/json",
        "Content-Type": "application/json; charset=utf-8",
        "Host": g_knowledge_base_domain,
        "V-Account-Id": account_id,
    }
    r.set_headers(mheaders)
    if params:
        r.set_query(params)
    r.set_host(g_knowledge_base_domain)
    r.set_path(path)
    if data is not None:
        r.set_body(json.dumps(data))

    # 生成签名
    credentials = Credentials(ak, sk, "air", "cn-north-1")
    SignerV4.sign(r, credentials)
    return r


def search_knowledge_single(query_text: str):
    """单个查询的知识检索函数"""
    start_time = time.perf_counter()

    method = "POST"
    path = "/api/knowledge/collection/search_knowledge"
    request_params = {
        "project": project_name,
        "name": collection_name,
        "query": query_text,
        "limit": 10,
        "pre_processing": {
            "need_instruction": True,
            "return_token_usage": True,
            "messages": [{"role": "system", "content": ""}, {"role": "user"}],
        },
        "dense_weight": 0.5,
        "post_processing": {
            "get_attachment_link": True,
            "rerank_only_chunk": False,
            "rerank_switch": True,
        },
    }

    info_req = prepare_request(method=method, path=path, data=request_params)
    rsp = requests.request(
        method=info_req.method,
        url="http://{}{}".format(g_knowledge_base_domain, info_req.path),
        headers=info_req.headers,
        data=info_req.body,
    )

    end_time = time.perf_counter()
    request_time = round((end_time - start_time) * 1000, 3)  # 转换为毫秒

    try:
        result_data = json.loads(rsp.text)
    except json.JSONDecodeError:
        result_data = {"error": "Invalid JSON response", "raw_response": rsp.text}

    return result_data, request_time


async def batch_test_from_xlsx(
    input_file: str,
    output_file: str = None,
    case_ids: list = None,
    start_row: int = None,
):
    """从xlsx文件批量测试query，适配火山引擎知识库API

    Args:
        input_file: 输入的xlsx文件路径
        output_file: 输出文件路径，如果为None则自动生成
        case_ids: 指定要运行的case ID列表，如果为None则运行所有case
        start_row: 从指定行开始测试（1-based，即第1行为数据的第一行），如果为None则从第一行开始
    """
    batch_start_time = time.perf_counter()
    input_path = Path(input_file)

    # 检查输入文件是否存在
    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return

    # 设置输出文件路径
    if output_file is None:
        filename_parts = []
        if case_ids:
            case_ids_str = "_".join(map(str, case_ids))
            filename_parts.append(f"cases_{case_ids_str}")
        if start_row:
            filename_parts.append(f"from_row_{start_row}")

        if filename_parts:
            suffix = "_" + "_".join(filename_parts)
            output_file = (
                input_path.parent / f"{input_path.stem}_ark_results{suffix}.xlsx"
            )
        else:
            output_file = input_path.parent / f"{input_path.stem}_ark_results.xlsx"

    try:
        # 读取xlsx文件
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共{len(df)}行数据")

        # 如果指定了case_ids，过滤数据
        if case_ids:
            # 过滤指定的case_ids
            original_count = len(df)
            df = df[df["caseID"].isin(case_ids)]
            filtered_count = len(df)

            logger.info(f"指定case_ids: {case_ids}")
            logger.info(f"原始数据: {original_count}行, 过滤后: {filtered_count}行")

            if filtered_count == 0:
                logger.warning("没有找到匹配的case ID，请检查输入的case_ids是否正确")
                return

        # 应用start_row过滤（如果指定）
        if start_row:
            original_count = len(df)
            # start_row是1-based，转换为0-based索引
            df = df.iloc[start_row - 1 :]
            filtered_count = len(df)
            logger.info(f"指定从第{start_row}行开始")
            logger.info(
                f"原始数据: {original_count}行, 跳过前{start_row - 1}行后: {filtered_count}行"
            )

        # 准备结果列表
        results = []

        # 逐个处理query
        for index, row in df.iterrows():
            final_query = row["decontextualized_query"]
            logger.info(f"正在处理第{index + 1}行, query: {final_query}")

            try:
                # 调用火山引擎知识检索
                result_data, request_time = search_knowledge_single(final_query)

                # 解析结果，提取关键信息
                max_score = 0
                total_results = 0
                error_message = ""

                if "error" in result_data:
                    error_message = result_data.get("error", "")
                elif "data" in result_data:
                    # 假设API返回的数据结构中包含结果列表和分数
                    data = result_data.get("data", {})
                    print(data)
                    if "result_list" in data:
                        results_list = data["result_list"]
                        total_results = len(results_list)
                        if results_list:
                            # 获取最高分数
                            scores = [
                                item.get("score", 0)
                                for item in results_list
                                if "score" in item
                            ]
                            if scores:
                                max_score = max(scores)

                # 记录结果
                result = {
                    "ark_query": final_query,
                    "ark_max_score": max_score,
                    "ark_total_results": total_results,
                    "ark_request_time_ms": request_time,
                    "ark_retrieved_content": str(result_data),  # 转换为字符串以便存储
                    "ark_error_message": error_message,
                    "ark_status": "success" if not error_message else "error",
                }

                # 如果原始数据中有其他列，也保留
                for col in df.columns:
                    if col not in result:  # 避免覆盖已有的结果列
                        result[col] = row[col]

                results.append(result)
                logger.info(
                    f"第{index + 1}行处理完成，耗时: {request_time}ms，最高分数: {max_score}"
                )

            except Exception as e:
                logger.error(f"处理第{index + 1}行时出错: {str(e)}")
                # 记录错误结果
                result = {
                    "ark_query": final_query,
                    "ark_max_score": 0,
                    "ark_total_results": 0,
                    "ark_request_time_ms": 0,
                    "ark_retrieved_content": "",
                    "ark_error_message": str(e),
                    "ark_status": "error",
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    if col not in result:
                        result[col] = row[col]

                results.append(result)

        # 保存结果到xlsx文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel(output_file, index=False)

            batch_end_time = time.perf_counter()
            batch_total_time = round((batch_end_time - batch_start_time) * 1000, 3)

            # 计算统计信息
            successful_requests = [r for r in results if r["ark_status"] == "success"]
            avg_request_time = (
                round(
                    sum(r["ark_request_time_ms"] for r in successful_requests)
                    / len(successful_requests),
                    3,
                )
                if successful_requests
                else 0
            )

            logger.info(f"结果已保存到: {output_file}")
            logger.info(
                f"共处理{len(results)}条query，成功{len(successful_requests)}条"
            )
            logger.info(f"批量处理总耗时: {batch_total_time}ms")
            logger.info(f"平均单个请求耗时: {avg_request_time}ms")
        else:
            logger.warning("没有有效的结果可保存")

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


def batch_test_from_xlsx_sync(
    input_file: str,
    output_file: str = None,
    case_ids: list = None,
    start_row: int = None,
):
    """同步版本的批量测试函数"""
    import asyncio

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(
            batch_test_from_xlsx(input_file, output_file, case_ids, start_row)
        )
    finally:
        loop.close()


if __name__ == "__main__":
    # 配置日志
    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="火山引擎知识库RAG测试脚本")
    parser.add_argument("input_file", nargs="?", help="输入的xlsx文件路径")
    parser.add_argument("output_file", nargs="?", help="输出文件路径（可选）")
    parser.add_argument(
        "--case-ids",
        "-c",
        nargs="+",
        type=int,
        help="指定要运行的case ID列表，支持多个ID，例如: --case-ids 1 2 3",
    )
    parser.add_argument(
        "--start-row",
        "-s",
        type=int,
        help="从指定行开始测试（从1开始计数），例如: --start-row 10",
    )

    args = parser.parse_args()

    if args.input_file:
        # 批量测试模式
        logger.info(f"开始批量测试，输入文件: {args.input_file}")
        if args.output_file:
            logger.info(f"输出文件: {args.output_file}")
        if args.case_ids:
            logger.info(f"指定运行case IDs: {args.case_ids}")
        if args.start_row:
            logger.info(f"从第{args.start_row}行开始测试")

        # 运行批量测试
        batch_test_from_xlsx_sync(
            args.input_file, args.output_file, args.case_ids, args.start_row
        )
    else:
        # 单个测试模式（原始功能）
        logger.info("运行单个测试模式")
        search_knowledge_single(query)
