"""
Author: <PERSON><PERSON><PERSON><PERSON>
LastEditors: <PERSON><PERSON>
email: <PERSON><PERSON><PERSON><PERSON>@orionstar.com
github:
Date: 2025-06-16 14:37:48
LastEditTime: 2025-01-19 14:00:00
motto: Still water run deep
Description: 百度千帆知识库RAG测试脚本
FilePath: /easyNLP/test/rag/test_baidu_rag.py
"""

import argparse
import asyncio
import os
import sys
import time
from pathlib import Path

import pandas as pd
import requests
from loguru import logger


# 开发测试环境变量设置（请替换为实际的API密钥和知识库ID）
os.environ["BAIDU_APPBUILDER_API_KEY"] = (
    "bce-v3/ALTAK-bdJNoaLbqllfIaRtVqIlE/574ca44692af6e953435a4981312ddbbc64843d5"
)
os.environ["BAIDU_KNOWLEDGEBASE_ID"] = "006ae37f-cd9b-4569-807a-3d4733af1e05"


def create_headers():
    """
    创建百度千帆API请求头。

    返回:
        dict: 请求头字典。
    """
    api_key = os.environ.get("BAIDU_APPBUILDER_API_KEY")
    if not api_key:
        raise ValueError("请设置环境变量 BAIDU_APPBUILDER_API_KEY")

    return {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}


def retrieve_knowledge_base(
    query, knowledgebase_id=None, top=6, rank_score_threshold=0.2
):
    """
    在百度千帆知识库中检索信息。

    参数:
        query (str): 查询文本。
        knowledgebase_id (str): 知识库ID，如果为None则使用环境变量。
        top (int): 返回结果数量。
        rank_score_threshold (float): 排序分数阈值。

    返回:
        dict: 百度千帆API的响应结果。
    """
    if knowledgebase_id is None:
        knowledgebase_id = os.environ.get("BAIDU_KNOWLEDGEBASE_ID")

    if not knowledgebase_id:
        raise ValueError(
            "请设置环境变量 BAIDU_KNOWLEDGEBASE_ID 或传入 knowledgebase_id 参数"
        )

    headers = create_headers()
    url = "https://qianfan.baidubce.com/v2/knowledgebases/query"

    # 构建请求数据
    data = {
        "type": "hybrid",  # fulltext, hybrid,semantic
        "query": query,
        "knowledgebase_ids": [knowledgebase_id],
        # "pipeline_config": {
        #     "id": "pipeline_001",
        #     "pipeline": [
        #         {
        #             "name": "step1",
        #             "type": "elastic_search",
        #             "threshold": 0.1,
        #             "top": 400,
        #             "pre_ranking": {
        #                 "bm25_weight": 0.25,
        #                 "vec_weight": 0.75,
        #                 "bm25_b": 0.75,
        #                 "bm25_k1": 1.5,
        #                 "bm25_max_score": 50
        #             }
        #         },
        #         {
        #             "name": "step2",
        #             "type": "ranking",
        #             "inputs": ["step1"],
        #             "model_name": "ranker-v1",
        #             "top": 20
        #         },
        #         {
        #             "name": "step3",
        #             "type": "small_to_big"
        #         }
        #     ]
        # },
        "top": top,
        "skip": 0,
        "rank_score_threshold": rank_score_threshold,
    }

    # 发送POST请求
    response = requests.post(url, headers=headers, json=data)

    # 检查响应状态
    if response.status_code != 200:
        raise Exception(
            f"请求失败，状态码: {response.status_code}, 响应: {response.text}"
        )

    return response.json()


def single_query_test(
    query: str,
    knowledgebase_id: str = None,
    top: int = 6,
    rank_score_threshold: float = 0.5,
):
    """
    单个query的百度千帆知识库检索测试

    Args:
        query: 查询文本
        knowledgebase_id: 知识库ID
        top: 返回结果数量
        rank_score_threshold: 排序分数阈值

    Returns:
        dict: 包含检索结果和耗时信息
    """
    start_time = time.perf_counter()

    try:
        if knowledgebase_id is None:
            knowledgebase_id = os.environ.get("BAIDU_KNOWLEDGEBASE_ID")

        # 执行检索
        response = retrieve_knowledge_base(
            query, knowledgebase_id, top, rank_score_threshold
        )

        end_time = time.perf_counter()
        retrieval_time = round((end_time - start_time) * 1000, 3)  # 转换为毫秒

        logger.info(f"检索耗时: {retrieval_time}ms")

        return {
            "response": response,
            "retrieval_time_ms": retrieval_time,
            "success": True,
        }

    except Exception as e:
        end_time = time.perf_counter()
        retrieval_time = round((end_time - start_time) * 1000, 3)

        logger.error(f"检索过程中出错: {str(e)}")
        return {
            "response": str(e),
            "retrieval_time_ms": retrieval_time,
            "success": False,
        }


async def batch_test_from_xlsx(
    input_file: str,
    output_file: str = None,
    case_ids: list = None,
    start_row: int = None,
):
    """
    从xlsx文件批量测试query，使用百度千帆知识库检索

    Args:
        input_file: 输入的xlsx文件路径
        output_file: 输出文件路径，如果为None则自动生成
        case_ids: 指定要运行的case ID列表，如果为None则运行所有case
        start_row: 从指定行开始测试（1-based，即第1行为数据的第一行），如果为None则从第一行开始
    """
    batch_start_time = time.perf_counter()
    input_path = Path(input_file)

    # 检查输入文件是否存在
    if not input_path.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return

    # 设置输出文件路径
    if output_file is None:
        filename_parts = []
        if case_ids:
            case_ids_str = "_".join(map(str, case_ids))
            filename_parts.append(f"cases_{case_ids_str}")
        if start_row:
            filename_parts.append(f"from_row_{start_row}")

        if filename_parts:
            suffix = "_" + "_".join(filename_parts)
            output_file = (
                input_path.parent / f"{input_path.stem}_baidu_results{suffix}.xlsx"
            )
        else:
            output_file = input_path.parent / f"{input_path.stem}_baidu_results.xlsx"

    try:
        # 读取xlsx文件
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共{len(df)}行数据")

        # 如果指定了case_ids，过滤数据
        if case_ids:
            # 过滤指定的case_ids
            original_count = len(df)
            df = df[df["caseID"].isin(case_ids)]
            filtered_count = len(df)

            logger.info(f"指定case_ids: {case_ids}")
            logger.info(f"原始数据: {original_count}行, 过滤后: {filtered_count}行")

            if filtered_count == 0:
                logger.warning("没有找到匹配的case ID，请检查输入的case_ids是否正确")
                return

        # 应用start_row过滤（如果指定）
        if start_row:
            original_count = len(df)
            # start_row是1-based，转换为0-based索引
            df = df.iloc[start_row - 1 :]
            filtered_count = len(df)
            logger.info(f"指定从第{start_row}行开始")
            logger.info(
                f"原始数据: {original_count}行, 跳过前{start_row - 1}行后: {filtered_count}行"
            )

        # 准备结果列表
        results = []

        # 获取百度千帆知识库ID
        knowledgebase_id = os.environ.get("BAIDU_KNOWLEDGEBASE_ID")

        # 逐个处理query
        for index, row in df.iterrows():
            query = row["decontextualized_query"]
            logger.info(f"正在处理第{index + 1}行, query: {query}")

            try:
                # 调用百度千帆知识库检索
                result = single_query_test(query, knowledgebase_id)

                # 解析响应结果，提取分数等信息
                response = result.get("response", {})
                print(result)

                # 尝试从响应中提取相关分数（根据百度千帆的响应格式调整）
                max_score = 0
                doc_count = 0
                total_count = 0

                success = result.get("success", False)
                if isinstance(response, dict) and success:
                    # 根据百度千帆响应格式提取信息
                    chunks = response.get("chunks", [])
                    total_count = response.get("total_count", 0)

                    if chunks:
                        doc_count = len(chunks)
                        # 获取最高rank_score
                        rank_scores = [
                            chunk.get("rank_score", 0)
                            for chunk in chunks
                            if isinstance(chunk, dict)
                        ]
                        if rank_scores:
                            max_score = max(rank_scores)

                        print(f"找到 {doc_count} 个chunks, 最高分数: {max_score}")

                # 记录结果
                result_record = {
                    "baidu_query": query,
                    "baidu_success": success,
                    "baidu_max_score": max_score,
                    "baidu_doc_count": doc_count,
                    "baidu_total_count": total_count,
                    "baidu_retrieval_time_ms": result.get("retrieval_time_ms", 0),
                    "baidu_response": str(response),  # 转换为字符串以便存储
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

                logger.info(
                    f"第{index + 1}行处理完成, 成功: {success}, 最高分: {max_score}, 文档数: {doc_count}, 总数: {total_count}"
                )

            except Exception as e:
                logger.error(f"处理第{index + 1}行时出错: {str(e)}")
                # 记录错误结果
                result_record = {
                    "baidu_query": query,
                    "baidu_success": False,
                    "baidu_max_score": 0,
                    "baidu_doc_count": 0,
                    "baidu_total_count": 0,
                    "baidu_retrieval_time_ms": 0,
                    "baidu_response": str(e),
                }

                # 保留原始数据的其他列
                for col in df.columns:
                    result_record[col] = row[col]

                results.append(result_record)

        # 保存结果到xlsx文件
        if results:
            results_df = pd.DataFrame(results)
            results_df.to_excel(output_file, index=False)

            batch_end_time = time.perf_counter()
            batch_total_time = round((batch_end_time - batch_start_time) * 1000, 3)

            # 计算统计信息
            total_queries = len(results)
            successful_queries = sum(1 for r in results if r.get("success", False))
            avg_retrieval_time = round(
                sum(r.get("retrieval_time_ms", 0) for r in results) / len(results), 3
            )

            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"共处理{total_queries}条query")
            logger.info(f"成功处理{successful_queries}条query")
            logger.info(f"成功率: {successful_queries / total_queries * 100:.2f}%")
            logger.info(f"批量处理总耗时: {batch_total_time}ms")
            logger.info(f"平均单个query检索耗时: {avg_retrieval_time}ms")
        else:
            logger.warning("没有有效的结果可保存")

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")


def main():
    """主函数，支持命令行参数和单个测试"""

    # 配置日志
    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="百度千帆知识库RAG测试脚本")
    parser.add_argument("input_file", nargs="?", help="输入的xlsx文件路径")
    parser.add_argument("output_file", nargs="?", help="输出文件路径（可选）")
    parser.add_argument(
        "--case-ids",
        "-c",
        nargs="+",
        type=int,
        help="指定要运行的case ID列表，支持多个ID，例如: --case-ids 1 2 3",
    )
    parser.add_argument(
        "--start-row",
        "-s",
        type=int,
        help="从指定行开始测试（从1开始计数），例如: --start-row 10",
    )

    args = parser.parse_args()

    if args.input_file:
        # 批量测试模式
        logger.info(f"开始批量测试，输入文件: {args.input_file}")
        if args.output_file:
            logger.info(f"输出文件: {args.output_file}")
        if args.case_ids:
            logger.info(f"指定运行case IDs: {args.case_ids}")
        if args.start_row:
            logger.info(f"从第{args.start_row}行开始测试")

        # 运行批量测试
        asyncio.run(
            batch_test_from_xlsx(
                args.input_file, args.output_file, args.case_ids, args.start_row
            )
        )
    else:
        # 单个测试模式（原始功能）
        logger.info("运行单个测试模式")
        knowledgebase_id = os.environ.get("BAIDU_KNOWLEDGEBASE_ID")
        query = "党的二十大报告讲了哪些内容"

        result = single_query_test(query, knowledgebase_id)
        logger.info(f"测试结果: {result}")


if __name__ == "__main__":
    main()
