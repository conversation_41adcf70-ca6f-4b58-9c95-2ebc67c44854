import pandas as pd
import numpy as np
from sklearn.metrics import precision_recall_curve, average_precision_score, auc
import plotly.graph_objects as go
import argparse
import os

# Plotly 原生支持中文，无需特殊字体配置


def load_data_from_excel(file_path, sheet_name=None):
    """
    从Excel文件中读取数据

    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称，如果为None则读取第一个工作表

    Returns:
        DataFrame: 包含数据的DataFrame
    """
    try:
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)

        print(f"成功读取Excel文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")

        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None


def validate_data(df, rag_col="rag", score_col="max_section_score"):
    """
    验证数据的有效性

    Args:
        df: 数据DataFrame
        rag_col: ground truth标签列名
        score_col: 预测分数列名

    Returns:
        bool: 数据是否有效
    """
    if df is None:
        return False

    # 检查必需的列是否存在
    if rag_col not in df.columns:
        print(f"错误: 找不到列 '{rag_col}'")
        return False

    if score_col not in df.columns:
        print(f"错误: 找不到列 '{score_col}'")
        return False

    # 检查rag列的值是否为0或1
    unique_rag_values = df[rag_col].unique()
    if not all(val in [0, 1] for val in unique_rag_values if not pd.isna(val)):
        print(f"警告: rag列包含非0/1值: {unique_rag_values}")

    # 检查score列的值是否在0-1范围内
    score_min, score_max = df[score_col].min(), df[score_col].max()
    if score_min < 0 or score_max > 1:
        print(
            f"警告: max_section_score列的值超出0-1范围: min={score_min}, max={score_max}"
        )

    # 检查缺失值
    rag_na = df[rag_col].isna().sum()
    score_na = df[score_col].isna().sum()

    if rag_na > 0:
        print(f"警告: rag列有 {rag_na} 个缺失值")

    if score_na > 0:
        print(f"警告: max_section_score列有 {score_na} 个缺失值")

    return True


def calculate_pr_curve(y_true, y_scores):
    """
    计算PR曲线

    Args:
        y_true: 真实标签数组
        y_scores: 预测分数数组

    Returns:
        tuple: (precision, recall, thresholds, ap_score, auc_score)
    """
    # 计算precision-recall曲线
    precision, recall, thresholds = precision_recall_curve(y_true, y_scores)

    # 计算平均精度分数 (Average Precision)
    ap_score = average_precision_score(y_true, y_scores)

    # 计算AUC-PR
    auc_score = auc(recall, precision)

    return precision, recall, thresholds, ap_score, auc_score


def save_pr_data_to_excel(
    precision, recall, thresholds, ap_score, auc_score, output_path
):
    """
    将PR曲线数据保存到Excel文件

    Args:
        precision: 精度数组
        recall: 召回率数组
        thresholds: 阈值数组
        ap_score: 平均精度分数
        auc_score: AUC-PR分数
        output_path: 输出Excel文件路径
    """
    try:
        # 安全处理数组长度不一致的问题
        min_length = min(len(precision), len(recall), len(thresholds))

        if min_length == 0:
            print("错误: 数组为空，无法保存数据")
            return

        # 确保数组长度一致
        if len(precision) > len(thresholds):
            precision_data = precision[: len(thresholds)]
        else:
            precision_data = precision

        if len(recall) > len(thresholds):
            recall_data = recall[: len(thresholds)]
        else:
            recall_data = recall

        # 处理长度仍不匹配的情况
        data_length = min(len(precision_data), len(recall_data), len(thresholds))

        # 创建主数据表
        pr_data = pd.DataFrame(
            {
                "threshold": thresholds[:data_length],
                "precision": precision_data[:data_length],
                "recall": recall_data[:data_length],
            }
        )

        # 计算F1分数，避免除零错误
        denominator = pr_data["precision"] + pr_data["recall"]
        pr_data["f1_score"] = np.where(
            denominator > 1e-8,
            2 * (pr_data["precision"] * pr_data["recall"]) / denominator,
            0,
        )

        # 找出最佳阈值点
        if len(pr_data) > 0:
            best_f1_idx = pr_data["f1_score"].idxmax()

            # 创建汇总统计表
            summary_data = pd.DataFrame(
                {
                    "指标": [
                        "平均精度 (AP)",
                        "AUC-PR",
                        "最佳阈值",
                        "最佳精度",
                        "最佳召回率",
                        "最佳F1分数",
                    ],
                    "数值": [
                        ap_score,
                        auc_score,
                        pr_data.loc[best_f1_idx, "threshold"],
                        pr_data.loc[best_f1_idx, "precision"],
                        pr_data.loc[best_f1_idx, "recall"],
                        pr_data.loc[best_f1_idx, "f1_score"],
                    ],
                }
            )
        else:
            # 如果没有有效数据，创建基本统计表
            summary_data = pd.DataFrame(
                {"指标": ["平均精度 (AP)", "AUC-PR"], "数值": [ap_score, auc_score]}
            )

        # 使用ExcelWriter保存多个工作表
        with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
            # 保存PR曲线详细数据
            pr_data.to_excel(writer, sheet_name="PR曲线数据", index=False)

            # 保存汇总统计
            summary_data.to_excel(writer, sheet_name="汇总统计", index=False)

        print(f"PR曲线数据已成功保存到Excel文件: {output_path}")
        print(f"- 工作表1: 'PR曲线数据' (包含{len(pr_data)}个数据点)")
        print("- 工作表2: '汇总统计' (包含关键性能指标)")

    except Exception as e:
        print(f"保存Excel文件时出错: {e}")


def _validate_indices(precision, recall, thresholds):
    """验证数组长度并返回有效的处理长度"""
    n = len(thresholds)
    if len(precision) > n:
        precision = precision[:-1]
    if len(recall) > n:
        recall = recall[:-1]

    if n < 6:  # 数据点太少，无法可靠检测拐点
        return None, None, None

    return precision, recall, n


def _light_smooth(data, window=3):
    """轻微平滑减少噪声"""
    smoothed = np.copy(data).astype(float)
    for i in range(len(data)):
        start = max(0, i - window // 2)
        end = min(len(data), i + window // 2 + 1)
        smoothed[i] = np.mean(data[start:end])
    return smoothed


def _is_valid_index(idx, precision, recall, thresholds):
    """检查索引是否有效"""
    return 0 <= idx < len(thresholds) and idx < len(precision) and idx < len(recall)


def _detect_slope_changes(precision_smooth, recall_smooth, thresholds):
    """方法1: 基于一阶导数符号变化检测拐点"""
    inflection_points = []

    # 计算一阶导数
    first_derivatives = []
    for i in range(1, len(recall_smooth) - 1):
        dr = recall_smooth[i + 1] - recall_smooth[i - 1]
        dp = precision_smooth[i + 1] - precision_smooth[i - 1]

        if abs(dr) > 1e-10:
            derivative = dp / dr
        else:
            # 回退到前向差分
            dr_forward = (
                recall_smooth[i + 1] - recall_smooth[i]
                if i < len(recall_smooth) - 1
                else 0
            )
            dp_forward = (
                precision_smooth[i + 1] - precision_smooth[i]
                if i < len(precision_smooth) - 1
                else 0
            )
            derivative = dp_forward / dr_forward if abs(dr_forward) > 1e-10 else 0

        first_derivatives.append(derivative)

    # 检测斜率变化显著的点
    if len(first_derivatives) > 2:
        for i in range(1, len(first_derivatives) - 1):
            prev_slope = first_derivatives[i - 1]
            curr_slope = first_derivatives[i]
            next_slope = first_derivatives[i + 1]

            slope_change = abs(next_slope - prev_slope)
            if slope_change > 0.5:  # 斜率变化阈值
                original_idx = i + 1

                if _is_valid_index(
                    original_idx, precision_smooth, recall_smooth, thresholds
                ):
                    # 判断变化类型
                    if prev_slope > curr_slope and curr_slope > next_slope:
                        inflection_type = "下降拐点"
                        symbol, color = "triangle-down", "red"
                    elif prev_slope < curr_slope and curr_slope < next_slope:
                        inflection_type = "上升拐点"
                        symbol, color = "triangle-up", "blue"
                    elif prev_slope > 0 > next_slope or prev_slope < 0 < next_slope:
                        inflection_type = "转折拐点"
                        symbol, color = "diamond", "purple"
                    else:
                        continue

                    inflection_points.append(
                        {
                            "idx": original_idx,
                            "type": inflection_type,
                            "symbol": symbol,
                            "color": color,
                            "change_magnitude": slope_change,
                            "precision": precision_smooth[original_idx],
                            "recall": recall_smooth[original_idx],
                            "threshold": thresholds[original_idx],
                            "slope_before": prev_slope,
                            "slope_current": curr_slope,
                            "slope_after": next_slope,
                        }
                    )

    return inflection_points, first_derivatives


def _detect_curvature_changes(first_derivatives, precision, recall, thresholds):
    """方法2: 基于二阶导数检测曲率变化点"""
    inflection_points = []

    if len(first_derivatives) > 2:
        second_derivatives = []
        for i in range(1, len(first_derivatives) - 1):
            d2 = first_derivatives[i + 1] - first_derivatives[i - 1]
            second_derivatives.append(d2)

        # 检测二阶导数符号变化
        for i in range(1, len(second_derivatives) - 1):
            prev_d2 = second_derivatives[i - 1]
            curr_d2 = second_derivatives[i]
            next_d2 = second_derivatives[i + 1]

            # 检测二阶导数符号变化
            if (prev_d2 > 0 > next_d2 or prev_d2 < 0 < next_d2) and abs(curr_d2) < max(
                abs(prev_d2), abs(next_d2)
            ):
                original_idx = i + 2  # 调整索引偏移

                if _is_valid_index(original_idx, precision, recall, thresholds):
                    # 判断曲率变化类型
                    if prev_d2 > 0 > next_d2:
                        inflection_type = "凸拐点"
                        symbol, color = "triangle-down", "darkred"
                    else:
                        inflection_type = "凹拐点"
                        symbol, color = "triangle-up", "darkblue"

                    curvature_change = abs(next_d2 - prev_d2)

                    inflection_points.append(
                        {
                            "idx": original_idx,
                            "type": inflection_type,
                            "symbol": symbol,
                            "color": color,
                            "change_magnitude": curvature_change,
                            "precision": precision[original_idx],
                            "recall": recall[original_idx],
                            "threshold": thresholds[original_idx],
                            "curvature_before": prev_d2,
                            "curvature_after": next_d2,
                        }
                    )

    return inflection_points


def _detect_local_extrema(precision_smooth, recall_smooth, thresholds):
    """方法3: 基于局部极值检测拐点"""
    inflection_points = []

    for i in range(2, len(precision_smooth) - 2):
        # 检测precision的局部极值
        if (
            precision_smooth[i] > precision_smooth[i - 1]
            and precision_smooth[i] > precision_smooth[i + 1]
            and precision_smooth[i] > precision_smooth[i - 2]
            and precision_smooth[i] > precision_smooth[i + 2]
        ) or (
            precision_smooth[i] < precision_smooth[i - 1]
            and precision_smooth[i] < precision_smooth[i + 1]
            and precision_smooth[i] < precision_smooth[i - 2]
            and precision_smooth[i] < precision_smooth[i + 2]
        ):
            if i < len(thresholds):
                # 计算变化幅度
                max_nearby = max(
                    precision_smooth[max(0, i - 2) : min(len(precision_smooth), i + 3)]
                )
                min_nearby = min(
                    precision_smooth[max(0, i - 2) : min(len(precision_smooth), i + 3)]
                )
                change_magnitude = max_nearby - min_nearby

                if change_magnitude > 0.05:  # 变化幅度阈值
                    inflection_points.append(
                        {
                            "idx": i,
                            "type": "局部极值",
                            "symbol": "star",
                            "color": "orange",
                            "change_magnitude": change_magnitude,
                            "precision": precision_smooth[i],
                            "recall": recall_smooth[i],
                            "threshold": thresholds[i],
                        }
                    )

    return inflection_points


def _filter_duplicate_points(inflection_points, min_distance=3):
    """去除重复和过近的拐点"""
    if len(inflection_points) <= 1:
        return inflection_points

    # 按变化幅度排序
    inflection_points.sort(key=lambda x: x.get("change_magnitude", 0), reverse=True)

    # 移除距离过近的拐点
    filtered_points = []
    for point in inflection_points:
        is_far_enough = True
        for existing_point in filtered_points:
            if abs(point["idx"] - existing_point["idx"]) < min_distance:
                is_far_enough = False
                break

        if is_far_enough:
            filtered_points.append(point)

        # 限制拐点数量，避免图形过于复杂
        if len(filtered_points) >= 12:
            break

    # 按索引排序以便绘图
    filtered_points.sort(key=lambda x: x["idx"])
    return filtered_points


def detect_inflection_points(precision, recall, thresholds, min_distance=3):
    """
    使用多种方法检测PR曲线的拐点，包括斜率变化点和曲率变化点

    Args:
        precision: 精度数组
        recall: 召回率数组
        thresholds: 阈值数组
        min_distance: 拐点之间的最小距离

    Returns:
        list: 拐点信息列表，包含索引、类型、坐标等
    """
    # 验证和预处理数据
    precision, recall, n = _validate_indices(precision, recall, thresholds)
    if precision is None:
        return []

    # 轻微平滑减少噪声
    precision_smooth = _light_smooth(precision, window=3)
    recall_smooth = _light_smooth(recall, window=3)

    inflection_points = []

    # 方法1: 检测斜率变化点
    slope_points, first_derivatives = _detect_slope_changes(
        precision_smooth, recall_smooth, thresholds
    )
    inflection_points.extend(slope_points)

    # 方法2: 检测曲率变化点
    curvature_points = _detect_curvature_changes(
        first_derivatives, precision, recall, thresholds
    )
    inflection_points.extend(curvature_points)

    # 方法3: 检测局部极值
    extrema_points = _detect_local_extrema(precision_smooth, recall_smooth, thresholds)
    inflection_points.extend(extrema_points)

    # 过滤重复点
    return _filter_duplicate_points(inflection_points, min_distance)


def _create_hover_text(point, threshold_val, recall_val, precision_val, f1_val):
    """创建悬停信息文本"""
    base_info = (
        f"{point['name']}<br>"
        f"阈值: {threshold_val:.3f}<br>"
        f"召回率: {recall_val:.3f}<br>"
        f"精度: {precision_val:.3f}<br>"
        f"F1: {f1_val:.3f}"
    )

    # 根据拐点类型添加特定信息
    if point["name"] in ["凸拐点", "凹拐点"]:
        curvature_before = point.get("curvature_before", 0)
        curvature_after = point.get("curvature_after", 0)
        change_magnitude = point.get("change_magnitude", 0)

        return (
            f"{base_info}<br>"
            f"曲率变化: {curvature_before:.3f}→{curvature_after:.3f}<br>"
            f"变化幅度: {change_magnitude:.3f}"
        )

    elif point["name"] in ["下降拐点", "上升拐点", "转折拐点"]:
        slope_before = point.get("slope_before", 0)
        slope_current = point.get("slope_current", 0)
        slope_after = point.get("slope_after", 0)
        change_magnitude = point.get("change_magnitude", 0)

        return (
            f"{base_info}<br>"
            f"斜率变化: {slope_before:.3f}→{slope_current:.3f}→{slope_after:.3f}<br>"
            f"变化幅度: {change_magnitude:.3f}"
        )

    elif point["name"] == "局部极值":
        change_magnitude = point.get("change_magnitude", 0)
        return f"{base_info}<br>变化幅度: {change_magnitude:.3f}"

    return base_info


def _get_key_threshold_points(precision, recall, thresholds):
    """获取关键阈值点"""
    if len(thresholds) == 0:
        return []

    f1_scores = (
        2 * (precision[:-1] * recall[:-1]) / (precision[:-1] + recall[:-1] + 1e-8)
    )
    key_points = []

    # 1. 最佳F1分数点
    best_f1_idx = np.argmax(f1_scores)
    key_points.append(
        {
            "idx": best_f1_idx,
            "name": "最佳F1",
            "color": "red",
            "symbol": "circle",
            "size": 12,
        }
    )

    # 2. 高精度点（精度>=0.9的点中召回率最高的）
    high_precision_mask = precision[:-1] >= 0.9
    if np.any(high_precision_mask):
        high_precision_idx = np.where(high_precision_mask)[0]
        best_high_prec_idx = high_precision_idx[
            np.argmax(recall[:-1][high_precision_mask])
        ]
        key_points.append(
            {
                "idx": best_high_prec_idx,
                "name": "高精度点",
                "color": "green",
                "symbol": "square",
                "size": 10,
            }
        )

    # 3. 高召回率点（召回率>=0.9的点中精度最高的）
    high_recall_mask = recall[:-1] >= 0.9
    if np.any(high_recall_mask):
        high_recall_idx = np.where(high_recall_mask)[0]
        best_high_recall_idx = high_recall_idx[
            np.argmax(precision[:-1][high_recall_mask])
        ]
        key_points.append(
            {
                "idx": best_high_recall_idx,
                "name": "高召回率点",
                "color": "orange",
                "symbol": "triangle-up",
                "size": 10,
            }
        )

    return key_points


def _add_point_to_figure(fig, point, idx, precision, recall, thresholds, f1_scores):
    """向图形添加单个关键点"""
    if idx >= len(recall) - 1 or idx >= len(precision) - 1:  # 确保索引有效
        return

    threshold_val = thresholds[idx]
    precision_val = precision[idx]
    recall_val = recall[idx]
    f1_val = f1_scores[idx] if idx < len(f1_scores) else 0

    # 创建悬停信息
    hover_text = _create_hover_text(
        point, threshold_val, recall_val, precision_val, f1_val
    )

    # 添加关键点
    fig.add_trace(
        go.Scatter(
            x=[recall_val],
            y=[precision_val],
            mode="markers+text",
            name=point["name"],
            marker=dict(
                color=point["color"],
                size=point["size"],
                symbol=point["symbol"],
                line=dict(color="black", width=1),
            ),
            text=[f"T:{threshold_val:.3f}"],
            textposition="top center",
            textfont=dict(size=10, color=point["color"]),
            hovertemplate=hover_text + "<extra></extra>",
            showlegend=True,
        )
    )


def plot_pr_curve(precision, recall, thresholds, ap_score, auc_score, save_path=None):
    """
    使用Plotly绘制PR曲线，显示多个关键阈值点

    Args:
        precision: 精度数组
        recall: 召回率数组
        thresholds: 阈值数组
        ap_score: 平均精度分数
        auc_score: AUC-PR分数
        save_path: 保存图片的路径
    """
    # 创建plotly图形
    fig = go.Figure()

    # 为主PR曲线准备threshold数据
    # precision和recall比thresholds长1，需要处理这个问题
    if len(thresholds) > 0:
        # 创建与precision和recall长度一致的threshold数组
        thresholds_extended = np.append(
            thresholds, thresholds[-1]
        )  # 最后一个threshold重复
        # threshold_text = [f"阈值: {t:.3f}" for t in thresholds_extended]
    else:
        # threshold_text = ["阈值: N/A"] * len(precision)
        thresholds_extended = [None] * len(precision)

    # 绘制PR曲线
    fig.add_trace(
        go.Scatter(
            x=recall,
            y=precision,
            mode="lines",
            name=f"PR曲线 (AP = {ap_score:.3f}, AUC = {auc_score:.3f})",
            line=dict(color="blue", width=3),
            customdata=thresholds_extended
            if len(thresholds) > 0
            else [None] * len(precision),
            hovertemplate="召回率: %{x:.3f}<br>精度: %{y:.3f}<br>阈值: %{customdata:.3f}<extra></extra>",
        )
    )

    # 计算并添加关键阈值点
    if len(thresholds) > 0:
        f1_scores = (
            2 * (precision[:-1] * recall[:-1]) / (precision[:-1] + recall[:-1] + 1e-8)
        )

        # 获取关键阈值点
        key_points = _get_key_threshold_points(precision, recall, thresholds)

        # # 使用改进的拐点检测算法
        # inflection_points = detect_inflection_points(precision, recall, thresholds)

        # # 添加检测到的拐点
        # for inflection_point in inflection_points:
        #     point_info = {
        #         'idx': inflection_point['idx'],
        #         'name': inflection_point['type'],
        #         'color': inflection_point['color'],
        #         'symbol': inflection_point['symbol'],
        #         'size': 10,
        #         'change_magnitude': inflection_point.get('change_magnitude', 0)
        #     }

        #     # 根据拐点类型添加特定信息
        #     if inflection_point['type'] in ['凸拐点', '凹拐点']:
        #         point_info['curvature_before'] = inflection_point.get('curvature_before', 0)
        #         point_info['curvature_after'] = inflection_point.get('curvature_after', 0)
        #     elif inflection_point['type'] in ['下降拐点', '上升拐点', '转折拐点']:
        #         point_info['slope_before'] = inflection_point.get('slope_before', 0)
        #         point_info['slope_current'] = inflection_point.get('slope_current', 0)
        #         point_info['slope_after'] = inflection_point.get('slope_after', 0)

        #     key_points.append(point_info)

        # 绘制所有关键点
        for point in key_points:
            _add_point_to_figure(
                fig, point, point["idx"], precision, recall, thresholds, f1_scores
            )

    # 添加基线（随机分类器）
    # 随机分类器的平均精度等于正样本比例
    if len(precision) > 1:
        positive_ratio = precision[-1] if precision[-1] > 0 else 0.5
    else:
        positive_ratio = 0.5

    fig.add_hline(
        y=positive_ratio,
        line_dash="dash",
        line_color="gray",
        annotation_text=f"随机基线 (AP ≈ {positive_ratio:.3f})",
        annotation_position="bottom right",
    )

    # 设置布局
    fig.update_layout(
        title={"text": "PR曲线", "x": 0.5, "font": {"size": 18}},
        xaxis_title="召回率 (Recall)",
        yaxis_title="精度 (Precision)",
        xaxis=dict(range=[0, 1], gridcolor="lightgray"),
        yaxis=dict(range=[0, 1.05], gridcolor="lightgray"),
        plot_bgcolor="white",
        width=1000,
        height=700,
        font=dict(family="Arial, sans-serif", size=12),
        hovermode="closest",
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="right",
            x=0.99,
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="black",
            borderwidth=1,
        ),
    )

    # 保存和显示图形
    if save_path:
        # 支持多种格式
        if save_path.endswith(".html"):
            fig.write_html(save_path)
            print(f"交互式PR曲线图已保存到: {save_path}")
        else:
            # 静态图片格式
            fig.write_image(save_path, width=1000, height=700, scale=2)
            print(f"PR曲线图已保存到: {save_path}")

    # 显示图形
    fig.show()


def print_summary_statistics(
    y_true, y_scores, precision, recall, thresholds, ap_score, auc_score
):
    """
    打印汇总统计信息
    """
    print("\n" + "=" * 50)
    print("PR曲线分析结果汇总")
    print("=" * 50)

    # 基本统计
    total_samples = len(y_true)
    positive_samples = sum(y_true)
    negative_samples = total_samples - positive_samples

    print(f"总样本数: {total_samples}")
    print(
        f"正样本数: {positive_samples} ({positive_samples / total_samples * 100:.1f}%)"
    )
    print(
        f"负样本数: {negative_samples} ({negative_samples / total_samples * 100:.1f}%)"
    )

    print("\n性能指标:")
    print(f"平均精度 (Average Precision): {ap_score:.4f}")
    print(f"AUC-PR: {auc_score:.4f}")

    # 计算F1分数 - 使用与其他函数一致的逻辑
    if (
        len(thresholds) > 0
        and len(precision) > len(thresholds)
        and len(recall) > len(thresholds)
    ):
        # 处理precision和recall比thresholds长1的情况
        precision_for_f1 = precision[:-1]
        recall_for_f1 = recall[:-1]
    else:
        # 使用相同长度的数组
        min_len = min(len(precision), len(recall), len(thresholds))
        precision_for_f1 = precision[:min_len]
        recall_for_f1 = recall[:min_len]

    # 安全计算F1分数，避免除零错误
    denominator = precision_for_f1 + recall_for_f1
    f1_scores = np.where(
        denominator > 1e-8, 2 * (precision_for_f1 * recall_for_f1) / denominator, 0
    )

    if len(f1_scores) > 0:
        best_f1_idx = np.argmax(f1_scores)
        best_threshold = thresholds[best_f1_idx]
        best_precision = precision_for_f1[best_f1_idx]
        best_recall = recall_for_f1[best_f1_idx]
        best_f1 = f1_scores[best_f1_idx]

        print("\n最佳阈值 (基于F1分数):")
        print(f"阈值: {best_threshold:.4f}")
        print(f"精度: {best_precision:.4f}")
        print(f"召回率: {best_recall:.4f}")
        print(f"F1分数: {best_f1:.4f}")
    else:
        print("\n警告: 无法计算最佳阈值，数据不足")


def main():
    parser = argparse.ArgumentParser(
        description="从Excel文件计算PR曲线 (使用Plotly绘图)"
    )
    parser.add_argument("--file", "-f", type=str, required=True, help="Excel文件路径")
    parser.add_argument(
        "--sheet", "-s", type=str, default=None, help="工作表名称（可选）"
    )
    parser.add_argument(
        "--rag_col", type=str, default="rag", help="ground truth标签列名（默认: rag）"
    )
    parser.add_argument(
        "--score_col",
        type=str,
        default="max_section_score",
        help="预测分数列名（默认: max_section_score）",
    )
    parser.add_argument(
        "--save_plot",
        type=str,
        default=None,
        help="保存PR曲线图的路径（支持.html/.png/.pdf/.svg等格式）",
    )
    parser.add_argument(
        "--save_data",
        type=str,
        default=None,
        help="保存PR曲线数据的Excel文件路径（可选）",
    )

    args = parser.parse_args()

    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"错误: 文件不存在: {args.file}")
        return

    # 读取数据
    print(f"正在读取Excel文件: {args.file}")
    df = load_data_from_excel(args.file, args.sheet)

    if df is None:
        return

    # 验证数据
    if not validate_data(df, args.rag_col, args.score_col):
        return

    # 删除包含缺失值的行
    original_length = len(df)
    df_clean = df[[args.rag_col, args.score_col]].dropna()

    if len(df_clean) < original_length:
        print(f"已删除 {original_length - len(df_clean)} 行包含缺失值的数据")

    if len(df_clean) == 0:
        print("错误: 清理后没有有效数据")
        return

    # 提取数据
    y_true = df_clean[args.rag_col].values
    # max (max_summary_score, max_section_score)
    y_scores = df_clean[args.score_col].values
    # y_scores = np.maximum(
    #     df_clean[args.score_col].values, df_clean["max_summary_score"].values
    # )

    print(f"有效数据点数量: {len(y_true)}")

    # 计算PR曲线
    print("正在计算PR曲线...")
    precision, recall, thresholds, ap_score, auc_score = calculate_pr_curve(
        y_true, y_scores
    )

    # 打印统计信息
    print_summary_statistics(
        y_true, y_scores, precision, recall, thresholds, ap_score, auc_score
    )

    # 绘制PR曲线
    print("正在绘制PR曲线...")
    plot_pr_curve(precision, recall, thresholds, ap_score, auc_score, args.save_plot)

    # 保存PR曲线数据到Excel文件
    if args.save_data:
        print("正在保存PR曲线数据到Excel文件...")
        save_pr_data_to_excel(
            precision, recall, thresholds, ap_score, auc_score, args.save_data
        )

    print("\nPR曲线分析完成！")


if __name__ == "__main__":
    main()
