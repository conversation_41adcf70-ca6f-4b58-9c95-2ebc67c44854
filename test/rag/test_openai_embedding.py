"""
Author: <PERSON><PERSON><PERSON><PERSON>
LastEditors: <PERSON><PERSON>
email: <PERSON><PERSON><PERSON><PERSON>@orionstar.com
github:
Date: 2025-06-18 19:08:12
LastEditTime: 2025-06-18 19:41:27
motto: Still water run deep
Description: Modify here please
FilePath: /easyNLP/test/rag/test_openai_embedding.py
"""

from openai import OpenAI
import numpy as np


def openai_embedding(
    text: str, model: str = "text-embedding-3-large", dimension: int = 3072
):
    client = OpenAI()
    response = client.embeddings.create(
        model=model, input=text, encoding_format="float"
    )
    return response.data[0].embedding


def embedding_similarity(embedding_1, embedding_2):
    return np.dot(np.array(embedding_1), np.array(embedding_2))


def test_openai_embedding():
    text = "AgentOS是什么？"
    embedding_256 = openai_embedding(
        text, model="text-embedding-3-large", dimension=256
    )
    embedding_1024 = openai_embedding(
        text, model="text-embedding-3-large", dimension=1024
    )
    embedding_3072 = openai_embedding(
        text, model="text-embedding-3-large", dimension=3072
    )

    data = {
        "object": "vector_store.search_results.page",
        "search_query": ["AgentOS是什么？"],
        "data": [
            {
                "file_id": "file-DTtjT3myJQsdABCdf99MH4",
                "filename": "AgentOS能力介绍.docx",
                "score": 0.03278688524590164,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "AgentOS\n\n一、AgentOS正式介绍 \n\n1.1 产品介绍\n\n         Agent是一种能够自主执行任务的软件实体，通常用于自动化、人工智能和机器学习领域。而Agentos是一个用于运行、管理、调度智能代理(Intelligent Agents)的平台和框架。在我们的机器人场景中，我们把实体机器人的各项能力都可以定义为一项Agent，比如有硬件能力相关的导航能力Navi的Agent，说话能力TTS Agent，语音识别的能力ASR Agent，视觉识别Agent和激光雷达Agent，以及机械臂控制Agent等等，也有软件能力相关的，比如可以调用API查询天气，查询日历，查询新闻，设置闹钟等等。\n\n        总而言之，对于用户对机器人发出的指令，AgentOS会基于用户的指令、环境的感知，对原始信息进行意图分析，任务拆解，规划编排执行计划，并调度Agent的执行，以协助和指挥机器人完成用户的任务。\n\n\n\n1.2 原理\n\n核心思想\n\n我们把AgentOS拆解成三个主要的组成部分：大脑负责决策、感知获取信息、行动用户执行。他们之间相互协作，各司其职，一起完成最终的任务。\n\n1. 控制端 Brain\n\n拥有LLMs作为整个系统的大脑\n\n拥有海量的知识，超强的语义理解能力，以及逻辑推理能力。结合长期记忆/短期记忆，以及Knowledge知识库的加持。\n\n\n\n2. 感知端 Perception\n\n充当了大模型的耳朵和眼睛，可以感知环境的信息。\n\n通过对环境信息的采集和分析，用来辅助大模型的大脑决策和推理。\n\n\n\n3. 行动端 Action\n\n充当了大模型的身体，比如手和脚。\n\n可以把大模型的文本生成能力落地到具体的执行实际任务中，既可通过Action操纵机器人的硬件能力组件，比如导航能力，语音播放能力，机械臂，视觉识别和激光雷达等，也可以通过一系列软件能力组件API获取如天气信息、日历信息、搜索网络知识等外部或实时知识。这也是Embodied Robotic的早期形态。\n\n\n\n1.3 工作流程\n\n\n\n1.4 里程碑-关键节点\n\n2024.08.01 - 项目正式启动。",
                    }
                ],
            },
            {
                "file_id": "file-DTtjT3myJQsdABCdf99MH4",
                "filename": "AgentOS能力介绍.docx",
                "score": 0.03200204813108039,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "2025.02.01 - 公司内部进行alpha版本的体验。    \n\n2025.03.01 - 推销智能体试点\n\n2025.06.01 - 构建出智能体应用开发平台\n\n\n\n\n\n二、语音场景PMF探索方式\n\n首次会议时，我们会对PMF探索方式进行宣讲，这是未来很长时间我们共同协作的模式。\n\n注：目前我们正在进行孵化阶段，最重要的命题是：收集客户反馈、便于发现真需求。\n\n\n\n\n\n\n\n三、产研进度汇报\n\nAgentOS现状\n\n1. 功能介绍\n\n功能名称\n\n功能介绍\n\n视频\n\n信息查询功能\n\n支持多种类型的查询，包括地图路线、美食推荐、机票查询、新闻资讯等。无论是导航出行、寻找美食，还是获取实时新闻，都能为您提供快捷、精准的帮助，让信息触手可得，方便每一次需求。\n\n\n\n[信息查询功能.mp4]\n\n信息通知功能\n\n当有访客到访或需要发送重要提醒时，机器人可通过飞书即时通知相关人员，实现高效、智能的沟通，提升前台服务的响应速度和整体协作效率。\n\n\n\n[飞书发消息功能.mp4]\n\n知识库记忆功能\n\n能够自动存储和调用重要信息，形成智能化的知识库。在与用户互动时，可快速查找并提供相关内容，帮助解答常见问题、支持多轮对话和知识更新，提升信息传递的精准性和服务体验的连续性。\n\n\n\n[知识库记忆.mp4]\n\n个性化打招呼\n\n\n\n当检测到熟悉的面孔时，机器人会主动调用对应的用户信息，以个性化的方式进行问候，如称呼姓名或根据用户偏好调整欢迎词，带来更贴心、更智能的互动体验。\n\n[个性化打招呼.mp4]\n\n随机点位导览\n\n\n\n具备随机指定点位的导览讲解功能，用户可随机选择特定点位，无需重复配置导览路线，机器人将根据指定位置进行相应讲解。无论哪些点位，机器人都能精准呈现丰富内容，为用户带来个性化的导览体验，满足多样化的探索需求。\n\n[指定点位导览讲解.mp4]\n\n2. 使用手册",
                    }
                ],
            },
            {
                "file_id": "file-X7kBEBzh4zsujnHGwxAK7y",
                "filename": "推销机器人介绍.txt",
                "score": 0.03200204813108039,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": '推销机器人介绍\n\n通过AgentOS基础能力，构建出来一个智能体应用，目前内部规划的第一个场景是展会推销，担任推销员的角色。\\\n支持体验的功能如下：\n\n 1. 合影\n 2. 导览讲解：你可以试着对我说"开始导览讲解"。\n 3. 领位：你可以试着对我说"带我去接待点"。\n 4. 路线规划、地图查询\n 5. 网页搜索：你可以试着对我说"今天A股表现怎么样"。\n 6. 查日历\n 7. 查机票：\n 8. 查天气：你可以试着对我说"帮我查查今天天气"。\n 9. 转圈\n10. 跳舞\n11. 点头\n12. 前进/后退：你可以试着对我说"前进一步"、"后退一步"。',
                    }
                ],
            },
            {
                "file_id": "file-DTtjT3myJQsdABCdf99MH4",
                "filename": "AgentOS能力介绍.docx",
                "score": 0.03125763125763126,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "1. 控制端 Brain\n\n拥有LLMs作为整个系统的大脑\n\n拥有海量的知识，超强的语义理解能力，以及逻辑推理能力。结合长期记忆/短期记忆，以及Knowledge知识库的加持。\n\n\n\n2. 感知端 Perception\n\n充当了大模型的耳朵和眼睛，可以感知环境的信息。\n\n通过对环境信息的采集和分析，用来辅助大模型的大脑决策和推理。\n\n\n\n3. 行动端 Action\n\n充当了大模型的身体，比如手和脚。\n\n可以把大模型的文本生成能力落地到具体的执行实际任务中，既可通过Action操纵机器人的硬件能力组件，比如导航能力，语音播放能力，机械臂，视觉识别和激光雷达等，也可以通过一系列软件能力组件API获取如天气信息、日历信息、搜索网络知识等外部或实时知识。这也是Embodied Robotic的早期形态。\n\n\n\n1.3 工作流程\n\n\n\n1.4 里程碑-关键节点\n\n2024.08.01 - 项目正式启动。\n\n2024.11.01  - 机器人支持免配置使用（取代nlp和QA配置）。\n\n2024.12.31  - 完成自动任务规划能力。\n\n2025.02.01 - 公司内部进行alpha版本的体验。    \n\n2025.03.01 - 推销智能体试点\n\n2025.06.01 - 构建出智能体应用开发平台\n\n\n\n\n\n二、语音场景PMF探索方式\n\n首次会议时，我们会对PMF探索方式进行宣讲，这是未来很长时间我们共同协作的模式。\n\n注：目前我们正在进行孵化阶段，最重要的命题是：收集客户反馈、便于发现真需求。\n\n\n\n\n\n\n\n三、产研进度汇报\n\nAgentOS现状\n\n1. 功能介绍\n\n功能名称\n\n功能介绍\n\n视频\n\n信息查询功能\n\n支持多种类型的查询，包括地图路线、美食推荐、机票查询、新闻资讯等。无论是导航出行、寻找美食，还是获取实时新闻，都能为您提供快捷、精准的帮助，让信息触手可得，方便每一次需求。\n\n\n\n[信息查询功能.mp4]\n\n信息通知功能\n\n当有访客到访或需要发送重要提醒时，机器人可通过飞书即时通知相关人员，实现高效、智能的沟通，提升前台服务的响应速度和整体协作效率。",
                    }
                ],
            },
            {
                "file_id": "file-5hMDDrWxEvRVT8qXw6GLBP",
                "filename": "猎户星空公司相关简介.txt",
                "score": 0.0293236301369863,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "5s平均响应速度、5m远场交互\r\n行业争相使用，猎户星空语音OS支撑了超过30%的中国智能音箱产品市场份额\r\n截至目前，猎户星空语音OS每天线上语音指令超过3000万次，拥有上百万小时的远场语音数据积累。作为应用最广的中文语音合成技术，猎户星空语音OS累计激活设备超过1个亿\r\n\r\n猎户星空人工智能服务机器人操作系统 Robot OS\r\n标准化：标准化平台，支持不同形态机器人、同样代码直接支持多种机器人形态\r\n真开放：核心业务代码授权开源、支持合作伙伴快速落地、Android 体系应用无缝接入\r\n模板化：AI功能模板化支持、不懂AI也能迅速上手\r\n\r\n全自研：\r\n标准化硬件研发体系\r\n完备的系统硬件开发体系\r\n完整的系统质量控制体系\r\n多产品验证的硬件研发能力\r\n稳定品质，全栈硬件设计制造能力\r\n\r\n全自研带来的三大优势：\r\n成本低：业内唯一全系产品运用单845芯片方案、行业领先的低成本多模态导航技术、最具价格竞争力的机械臂协作应用方案、具备成熟供应链持续降本能力\r\n性能好：数据闭环、迭代速度快、信息安全、隐私保护、产品思维、注重用户体验\r\n场景进入快：硬件可模块化自由组合、软件统一平台、从立项到量产速度快（如：餐厅服务机器人从立项到量产共5个月）\r\n\r\n猎户星空重要荣誉一览\r\n北京冬奥服务型机器人创新测评大赛，猎户星空五项产品入选，独占半壁。猎户星空是唯一入选公寓入住接待机器人。猎户星空是唯一入选导览翻译服务机器人\r\n\r\n猎户星空全力支持冬奥，收到官方感谢信\r\n\r\n猎户星空相关数据介绍：\r\n机器人落地数据：猎户星空拥有完备的机器人生产流水线，贯穿整个供应链的端到端能力，截至2023年6月，已有50000台机器人雇员上岗。",
                    }
                ],
            },
            {
                "file_id": "file-X6zGmPajwkeLSMMqd35bCv",
                "filename": "猎户星空大模型机器人-AI配货员-豹厂通 (1).txt",
                "score": 0.02813852813852814,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "智能化交互\r\n· 多机协同，自动规划线路\r\n· 支持呼叫铃控制及智能跟随设置\r\n· 14英寸超大屏易操作\r\n3.开箱即用\r\n·  快速部署，当天完成，即刻上岗\r\n· 单机建图智能共享\r\n· 无需PC，内置建图软件，提效3倍\r\n4.一厘米定位精度\r\n全球数万客户信赖之选\r\n· 通过多项国际权威安全认证\r\n· 平均无故障运行时间达4万小时\r\n· 全系产品连续8年累计出货6万+台\r\n· 服务全球数万家客户的售后体系\r\n5.智能二开系统无缝对接\r\n· 500+API端口\r\n· 3个硬件扩展接口\r\n· 平均7天快速定制开发\r\n6.人机协作无压力\r\n· 5重安全护航\r\n· 65CM窄道通行无阻\r\n· 效率提升3-6倍\r\n\r\n\r\n【豹厂通】主要功能\r\n适配多种承载方式：多种运载方式，无论是松散的零件还是包装盒，机器人都可以精确完成每一次配送\r\n\r\n自动驾驶级视觉导航，无惧生产环境变化：携手全球数万家客户，共同打磨VSLAM+ 技术，无需定位标签和设施改造，即可快速适应生产布局的变化，降低成本，大幅降低了部署人工成本和环境改造费用\r\n\r\n多机协同工作，自动线路规划：当多个机器人在岔路口相遇时，会按照机器人编号的优先级智能行驶，自主避让，无需人工干预，复杂环境下，机器人能够自主导航，灵活绕过障碍物，并实现精确定位，确保更安全、更高效地完成配送任务\r\n，多机器人协同工作，可显著提高效率\r\n\r\n开箱即用， 快速部署，当天完成，即刻上岗\r\n部署时间从传统AGV的动辄数周，缩短至最快到货当天即可完成交付，豹厂通无需使用电脑，机器人内置建图软件，建图效率提升3倍\r\n\r\n智能二开，支持系统无缝对接\r\n支持API调用，二次开发便捷友好，平均7天完成定制开发\r\n\r\n5重安全防护,",
                    }
                ],
            },
            {
                "file_id": "file-G3wdMaGDAyMaZVHBfqVURQ",
                "filename": "猎豹移动介绍.txt",
                "score": 0.026687875574407917,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "猎豹移动（Cheetah Mobile）——全球领先的AI与工具应用创新者\r\n猎豹移动是一家以技术驱动为核心的中国互联网科技公司，深耕全球化市场十余年，通过工具类应用积累亿级用户，并成功向人工智能（AI）及机器人领域拓展，致力于以创新科技赋能全球用户与企业。\r\n\r\n核心亮点与成就\r\n全球化影响力\r\n\r\n旗下产品覆盖全球200+国家及地区，累计用户超10亿，尤其在欧美、东南亚、印度等市场占据领先地位。\r\n\r\nClean Master（猎豹清理大师）长期稳居Google Play全球工具类应用下载榜前列，被誉为“国民级手机优化工具”。\r\n\r\nAI与机器人技术领航者\r\n\r\n2018年成立全资子公司猎户星空（Orion Star），自主研发全链条AI技术（语音识别、视觉导航、机械臂控制等），推出多款智能服务机器人：\r\n\r\n豹小秘：商场、医院导览机器人，服务超千家客户，日均交互量百万次。\r\n\r\n递茶机器人：融合机械臂技术，落地知名茶饮品牌门店。\r\n\r\n技术合作：与小米、喜马拉雅等企业深度合作，提供语音交互及AIoT解决方案。\r\n\r\n商业模式创新\r\n\r\n广告生态：与Google、Facebook等全球头部平台建立长期合作，精准匹配用户与广告主需求。\r\n\r\n企业服务升级：通过AI机器人、智慧营销系统等B端产品，助力零售、医疗、政务等领域数字化转型。\r\n\r\n技术实力与行业认可\r\n研发投入：每年将营收的20%以上投入AI核心技术研发，专利储备超千项。\r\n\r\n国际奖项：多次获评Google Play“顶尖开发者”（Top Developer）、CES创新奖等荣誉。\r\n\r\n权威背书：入选《麻省理工科技评论》“全球50家聪明公司”榜单（2020）。\r\n\r\n未来愿景\r\n猎豹移动以“用科技让世界更聪明”为使命，持续探索AI、机器人技术与场景化应用的深度融合，目标成为全球智能服务机器人领域的标杆企业，推动人类生活与商业效率的革新。",
                    }
                ],
            },
            {
                "file_id": "file-YN48Q4ksjGuRt1qNctr7sA",
                "filename": "木偶头雕刻.pdf",
                "score": 0.026137303556658397,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "雕刻。后来，他特意在英语方面下功夫，他说：“选择英语，是希望以后向外国人推广泉州\n\n的木偶头雕刻艺术。”\n\n记者采访江东林时，他正在雕刻木偶头。但见他一刀一刻间，关公、项羽、美猴王、雷\n\n公……一个个木偶形象完美呈现，活灵活现，惟妙惟肖。\n\n“除了吃饭和睡觉，我们很少出门，每天就是坐在这里和父亲雕刻木偶。”江东林表示。\n\n但他并没有觉得长时间的创作会枯燥无味，相反，他觉得每天的时间都不够用，因为，他希\n\n望自己能尽快赶上父亲的步伐。\n\n采访中，他拿了一个“七头七丑”的木雕雕刻作品给记者看。只见这个木偶头有 7 个头，\n\n而 7 个头上的眼睛能够同时转动，嘴巴可以开合。可想而知，做这个的难度相当大，同时也\n\n是一大创新和挑战。江东林说，这是他的一个创新作品，他觉得颇有成就感。2017 年，江\n\n东林参加第 6 届成都非遗节并现场展示技艺，被评为“最佳新人”。\n\n“越钻研，你就会发现自己懂得的越少。”江东林谦逊地说。\n\n“你才学 20 年，才刚开始，你懂得的还只是一小部分。”每每父亲这样“嫌弃”他时，江\n\n东林就会更加努力给自己充电。\n\n如今，工作之余，江东林在做一件事情：整理江加走木偶艺术的各种照片、书籍、资料。\n\n江东林表示，此举是为了让江加走木偶头雕刻更加为人们所熟悉，方便后人研究泉州的木偶\n\n头雕刻文化。",
                    }
                ],
            },
            {
                "file_id": "file-5hMDDrWxEvRVT8qXw6GLBP",
                "filename": "猎户星空公司相关简介.txt",
                "score": 0.025793650793650792,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "猎户星空是唯一入选公寓入住接待机器人。猎户星空是唯一入选导览翻译服务机器人\r\n\r\n猎户星空全力支持冬奥，收到官方感谢信\r\n\r\n猎户星空相关数据介绍：\r\n机器人落地数据：猎户星空拥有完备的机器人生产流水线，贯穿整个供应链的端到端能力，截至2023年6月，已有50000台机器人雇员上岗。\r\n猎户星空全自研高精度语音识别系统，语音识别精准可靠，日均语音交互频次超1700万次\r\n猎户星空机器人拥有人体识别免唤醒功能，主动为用户提供帮助，总服务人次近5亿\r\n猎户星空机器人已在政务、餐饮、医疗机构、大型商场、法院、展馆、图书馆、企业、会展、酒店、地产、学校等20多个行业场景落地\r\n猎户星空业务已经覆盖全球40多个国家和地区，其中包括美国、日本、韩国、泰国等国家\r\n猎户星空以优质的服务，完善的代理商支持政策，已获得上万家客户的认可\r\n\r\n如果您需要销售联系方式或者要联系销售\r\n猎户星空的联系方式可以在公司官网中找到，包括公司地址、电话、邮箱等信息。您可以访问公司官网，了解更多关于猎户星空的信息和产品介绍。公司官网链接为：https://www.orionstar.com/。如果您有其他问题需要咨询，也可以拨打400-898-7779电话咨询，我们的客服团队会为您提供详细的解答和帮助。\r\n猎户星空的客服电话在线时间为周一至周日9:00-18:00\r\n\r\n猎户星空公司官方邮箱地址：<EMAIL>，欢迎您给我们发邮件咨询。",
                    }
                ],
            },
            {
                "file_id": "file-YN48Q4ksjGuRt1qNctr7sA",
                "filename": "木偶头雕刻.pdf",
                "score": 0.025460793502030614,
                "attributes": {},
                "content": [
                    {
                        "type": "text",
                        "text": "据江碧峰（江朝弦之子）回忆，父亲一直谨记爷爷江加走的教诲，对自己非常严苛。\n\n“木偶头雕刻是个细致活儿。只要哪里不满意，我父亲就一定要重新做，所以，他经常\n\n加班到深夜。由于当时照明条件差，又经常加班到深夜，他的右眼后来就失明了。”\n\n\n\n江朝铉认为，追求创新才能卓越。于是，他汲取和借鉴其他戏剧和民间艺术的精华，加\n\n以提炼，塑造出了各种富有独特性格和色彩的木偶头像。比如，他发展和创作出了 285 种不\n\n同性格的木偶头像，其中 250 种都有称谓；新编梳十余种不同式样的头髻和发辫；雕刻和粉\n\n彩的木偶头像达万余件之多，等等。\n\n江朝铉还根据父亲留下的《东海龙王》木偶形象，进一步创作升华，他先后创作了西海、\n\n南海、北海龙王，四大金刚、孙悟空以及难度极高的《封神演义》中三头六臂的吕岳形象。\n\n江碧峰 14 岁随父亲学习木偶头雕刻艺术。其所创作的木偶头雕刻精细，造型生动，颜\n\n色鲜明、线条流畅、除了全面传承了祖父江加走和父亲江朝铉独特的艺术特点外，又进一步\n\n创新发展，深受国内外艺术家和收藏家的推崇和喜爱。2008 年，江加走木偶头雕刻经申请\n\n被评为中国非物质文化遗产，江碧峰被评为国家级“非遗”项目木偶头雕刻艺术传承人。\n\n江朝铉逝世后，其子江碧峰又承继父业，并继续传承给下一代江东林，至今，江氏木偶\n\n头雕刻已传至第五代。\n\n1978 年，江家第五代传人江东林出生。从 1997 年起，他主动跟爷爷江朝铉学习木偶头",
                    }
                ],
            },
        ],
        "has_more": False,
        "next_page": None,
    }
    # data = ast.literal_eval(data_str)
    for i in data["data"]:
        print(i["filename"])

        doc_embedding_256 = openai_embedding(
            i["content"][0]["text"], model="text-embedding-3-large", dimension=256
        )
        doc_embedding_1024 = openai_embedding(
            i["content"][0]["text"], model="text-embedding-3-large", dimension=1024
        )
        doc_embedding_3072 = openai_embedding(
            i["content"][0]["text"], model="text-embedding-3-large", dimension=3072
        )

        score_256 = embedding_similarity(embedding_256, doc_embedding_256)
        score_1024 = embedding_similarity(embedding_1024, doc_embedding_1024)
        score_3072 = embedding_similarity(embedding_3072, doc_embedding_3072)
        print(
            "score",
            i["score"],
            "score_256:",
            score_256,
            "score_1024:",
            score_1024,
            "score_3072:",
            score_3072,
        )


if __name__ == "__main__":
    test_openai_embedding()
