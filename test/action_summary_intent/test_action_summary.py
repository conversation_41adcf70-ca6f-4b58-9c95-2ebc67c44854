import asyncio
import yaml
import pandas as pd
from loguru import logger
from src.action.retrieve import MaterialRetriever
from src.common.toolkit import LLMToolKit
from src.session_manager.chat_context import ChatMessage
from src.agent_core.single_action import SingleActionAgent
import json
import csv

async def get_intent_by_summary(
    history_messages: list[ChatMessage], 
    final_query: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    使用LLMToolKit.summary方法获取搜索查询并查找相关few-shots
    """
    try:
        # 构建conversation_progress
        conversation_progress = []
        for msg in history_messages:
            if msg.content:
                conversation_progress.append(msg.content)
        
        # 使用summary方法获取搜索查询
        summary_result = await LLMToolKit.summary(
            "\n".join(conversation_progress[-3:]),
            final_query,
        )
        
        # 解析结果，添加错误处理
        try:
            if isinstance(summary_result.content, str):
                search_query = summary_result.content
            else:
                parsed_result = LLMToolKit.parse_yaml(summary_result.content)
                search_query = str(parsed_result.get("FinalAnswer", summary_result.content))
        except Exception as e:
            logger.warning(f"解析summary结果失败: {e}, 使用原始内容")
            search_query = str(summary_result.content)
        
        # 使用搜索查询获取few-shots
        materials = await MaterialRetriever.retrieve(
            search_query,
            filter_action_names=[a["name"] for a in (actions or [])],
            few_shot_limit=2,
            _logger=logger,
        )
        
        # 提取few_shots的input和action
        few_shot_input = []
        few_shot_action = []
        for shot in materials.few_shots:
            few_shot_input.append(shot.get('Input', ''))
            few_shot_action.append(shot.get('Output', {}).get('action', ''))
        
        return {
            'search_query': search_query,
            'few_shot_input': few_shot_input,
            'few_shot_action': few_shot_action
        }
    except Exception as e:
        logger.error(f"get_intent_by_summary 执行失败: {e}")
        return {
            'search_query': final_query,  # 失败时使用原始查询
            'few_shot_input': [],
            'few_shot_action': []
        }

async def get_intent_by_build_query(
    history_messages: list[ChatMessage], 
    final_query: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    使用build_search_query方法获取搜索查询并查找相关few-shots
    """
    # 使用build_search_query方法获取搜索查询
    search_query = SingleActionAgent.build_search_query(
        history_messages, 
        final_query, 
        limit=2
    )
    
    # 使用搜索查询获取few-shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        filter_action_names=[a["name"] for a in (actions or [])],
        few_shot_limit=2,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_shot_input = []
    few_shot_action = []
    for shot in materials.few_shots:
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
    
    return {
        'search_query': search_query,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action
    }

async def get_intent_by_discussion_summary(
    history_messages: list[ChatMessage], 
    final_query: str,
    logger=logger
) -> dict:
    """
    通过对话总结获取完整的用户意图
    
    Args:
        history_messages: 历史对话消息
        final_query: 最终用户查询
        logger: 日志记录器
    
    Returns:
        dict: 包含处理结果的字典，格式如下：
        {
            'discussion_summary': str,              # 对话总结
            'search_query': str,                    # 完整意图作为搜索查询
            'few_shot_input': list[str],           # few-shot的输入列表
            'few_shot_action': list[str],          # few-shot的动作列表
        }
    """
    # 获取对话总结
    summary_result = await LLMToolKit.summarize_discussion_intent(history_messages)

    # 使用complete_user_intent获取完整意图
    # complete_intent_result = await LLMToolKit.complete_user_intent(
    #     summary=summary_result.content,
    #     current_question=final_query
    # )

    # 将summary_result和final_query拼接
    search_query = f"{summary_result.content} | {final_query}"
    
    
    # 使用完整意图查询few_shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        few_shot_limit=2,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_shot_input = []
    few_shot_action = []
    for shot in materials.few_shots:
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
    
    return {
        'discussion_summary': summary_result.content,
        'search_query': search_query,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action
    }

async def test_summarize_discussion_intent():
    # 使用pandas读取CSV文件，设置更宽松的参数
    df = pd.read_csv('test/action_summary_intent/test_query_400.csv', 
                     encoding='utf-8',
                     quoting=csv.QUOTE_ALL,      # 处理引号内的内容
                     quotechar='"',              # 指定引号字符
                     escapechar='\\',            # 处理转义字符
                     dtype=str,                  # 所有列都作为字符串处理
                     on_bad_lines='skip',        # 跳过有问题的行
                     delimiter=',',              # 明确指定分隔符
                     header=0,                   # 第一行是标题
                     low_memory=False)           # 禁用低内存模式
    
    # 只保留非空的行（通常筛选后的行会保留值，而被筛选掉的行是空的）
    df = df.dropna(how='all')  # 删除全为空值的行
    df = df[df.iloc[:, 0].notna()]  # 确保第一列（query）不为空
    
    print(len(df))  # 打印实际读取的行数
    
    test_cases = []
    count = 0
    for index, row in df.iterrows():
        try:
            query = row[0]
            case_id = row[1]
            action = row[2]
            chat_text = row[3] if len(row) > 3 else ""
            
            # 跳过空值
            if pd.isna(query) or pd.isna(case_id) or pd.isna(action):
                continue
                
            # 从User Prompt2字段提取对话历史
            chat_messages = []
            if isinstance(chat_text, str) and '# CHAT CONVERSATION' in chat_text:
                chat_section = chat_text.split('# CHAT CONVERSATION')[1]
                if '#' in chat_section:
                    chat_section = chat_section.split('#')[0]
                
                for chat_line in chat_section.strip().split('\n'):
                    chat_line = chat_line.strip()
                    if chat_line.startswith('<Robot>') or chat_line.startswith('<User>'):
                        msg_parts = chat_line.split(' SAY ', 1)
                        if len(msg_parts) == 2:
                            role = 'assistant' if msg_parts[0].strip() == '<Robot>' else 'user'
                            content = msg_parts[1].strip().strip("'")
                            chat_messages.append({'role': role, 'content': content})
            
            test_case = {
                'name': f'Case_{case_id}',
                'messages': chat_messages,
                'final_query': query,
                'case_id': case_id,
                'expected': action
            }

            test_cases.append(test_case)

            # count = count + 1
            # if count % 50 == 0:
            #     logger.info(f"正在处理第{count}行")
            #     break
            
        except Exception as e:
            logger.error(f"解析CSV行时出错: {e}")
            continue

    
    # 创建结果列表和计数器
    results_data = []
    match_counts = {
        '方法1_命中数': 0,
        '方法2_命中数': 0,
        '方法3_命中数': 0
    }

    try:
        count = 0
        for case in test_cases:
            logger.info(f"\n测试用例: {case['name']}")
            count = count + 1
            if count % 10 == 0:
                logger.info(f"正在处理第{count}行")
            
            # 获取所有消息
            # messages = [ChatMessage(role=msg['role'], content=msg['content']) 
            #            for msg in case['messages']]
            
             # 获取所有消息，但去掉最后一个用户消息
            messages = []
            logger.info(f"原始消息列表: {case['messages']}")  # 打印原始消息
            
            for i, msg in enumerate(case['messages']):
                # 如果是最后一条消息且是用户消息，则跳过
                if i == len(case['messages']) - 1 and msg['role'] == 'user':
                    continue
                messages.append(ChatMessage(role=msg['role'], content=msg['content']))
                logger.info(f"添加消息: {msg['role']} - {msg['content']}")  # 打印每条添加的消息

            final_user_query = case['final_query']
            
            # 打印测试用例的对话内容
            logger.info("历史对话:")
            for msg in messages:
                logger.info(f"{msg.role}: {msg.content}")
            logger.info(f"\n最终用户查询: {final_user_query}")
            
            # 使用三种不同方法获取结果
            result1 = await get_intent_by_discussion_summary(
                history_messages=messages,
                final_query=final_user_query
            )
            
            result2 = await get_intent_by_summary(
                history_messages=messages,
                final_query=final_user_query
            )
            
            result3 = await get_intent_by_build_query(
                history_messages=messages,
                final_query=final_user_query
            )
            
            # 收集当前测试用例的结果
            expected_action = str(case.get('expected', ''))  # 确保是字符串
            
            # 检查每个方法的命中情况并更新计数
            method1_hit = any(str(expected_action) in str(action) for action in result1['few_shot_action'])
            method2_hit = any(str(expected_action) in str(action) for action in result2['few_shot_action'])
            method3_hit = any(str(expected_action) in str(action) for action in result3['few_shot_action'])
            
            if method1_hit:
                match_counts['方法1_命中数'] += 1
            if method2_hit:
                match_counts['方法2_命中数'] += 1
            if method3_hit:
                match_counts['方法3_命中数'] += 1
            
            case_result = {
                '测试用例': case['name'],
                '历史对话': '\n'.join([f"{msg.role}: {msg.content}" for msg in messages]),
                '最终用户查询': final_user_query,
                '预期结果': expected_action,
                
                '方法1_对话总结+完整意图': result1['search_query'],
                '方法1_Few-shot输入': '\n'.join(str(x) for x in result1['few_shot_input']),
                '方法1_Few-shot动作': '\n'.join(str(x) for x in result1['few_shot_action']),
                '方法1_命中结果': method1_hit,
                
                '方法2_最近三轮总结': result2['search_query'],
                '方法2_Few-shot输入': '\n'.join(str(x) for x in result2['few_shot_input']),
                '方法2_Few-shot动作': '\n'.join(str(x) for x in result2['few_shot_action']),
                '方法2_命中结果': method2_hit,
                
                '方法3_直接拼接': result3['search_query'],
                '方法3_Few-shot输入': '\n'.join(str(x) for x in result3['few_shot_input']),
                '方法3_Few-shot动作': '\n'.join(str(x) for x in result3['few_shot_action']),
                '方法3_命中结果': method3_hit,
            }
            
            results_data.append(case_result)
            
        # 创建主要结果的DataFrame
        df_results = pd.DataFrame(results_data[:-1])  # 不包含最后的统计行
        
        # 创建统计结果的DataFrame，只包含有用的字段
        summary_data = {
            '总用例数': len(test_cases),
            '方法1_命中数': match_counts['方法1_命中数'],
            '方法1_命中率': f"{(match_counts['方法1_命中数'] / len(test_cases)) * 100:.2f}%",
            '方法2_命中数': match_counts['方法2_命中数'],
            '方法2_命中率': f"{(match_counts['方法2_命中数'] / len(test_cases)) * 100:.2f}%",
            '方法3_命中数': match_counts['方法3_命中数'],
            '方法3_命中率': f"{(match_counts['方法3_命中数'] / len(test_cases)) * 100:.2f}%"
        }
        df_summary = pd.DataFrame([summary_data])
        
        # 使用ExcelWriter保存到不同的sheet
        excel_path = 'test/action_summary_intent/test_results.xlsx'
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df_results.to_excel(writer, sheet_name='测试结果', index=False)
            df_summary.to_excel(writer, sheet_name='统计结果', index=False)
        
        logger.info(f"测试结果已保存到: {excel_path}")
        
        # 打印统计结果
        logger.info("\n统计结果:")
        logger.info(f"总用例数: {len(test_cases)}")
        for method_num in range(1, 4):
            hits = match_counts[f'方法{method_num}_命中数']
            rate = (hits / len(test_cases)) * 100
            logger.info(f"方法{method_num}命中数: {hits}, 命中率: {rate:.2f}%")

    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    logger.info("开始测试 summarize_discussion_intent 方法...")
    asyncio.run(test_summarize_discussion_intent())
    logger.info("测试完成")