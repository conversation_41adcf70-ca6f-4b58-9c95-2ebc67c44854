import pytest
from src.common.toolkit import LLMToolKit
from src.session_manager.chat_context import ChatMessage
import asyncio
from loguru import logger

@pytest.mark.asyncio
async def test_summarize_discussion_intent():
    # 准备测试数据
    # test_messages = [
    #     ChatMessage(role="user", content="我想买一部新手机"),
    #     ChatMessage(role="assistant", content="好的，您对手机有什么具体要求吗？比如预算、品牌偏好等？"),
    #     ChatMessage(role="user", content="预算5000左右，想要拍照好的"),
    #     ChatMessage(role="assistant", content="明白了，我为您推荐几款拍照性能出色、5000元价位的手机：\n1. 手机A\n2. 手机B\n3. 手机C"),
    #     ChatMessage(role="user", content="这些手机的电池续航怎么样？")
    # ]
    test_messages = [
        ChatMessage(role="user", content="今天天气怎么样"),
        Chat<PERSON><PERSON><PERSON>(role="assistant", content="北京今天天气良好，是个适合出行的日期哦"),
        ChatMessage(role="user", content="那上海呢"),
        ChatMessage(role="assistant", content="上海多雨"),
        ChatMessage(role="user", content="天津呢")
    ]

    # 测试完整对话场景
    result = await LLMToolKit.summarize_discussion_intent(test_messages)
    assert result.content is not None
    assert isinstance(result.content, str)
    assert len(result.content) > 0
    assert result.elapsed_time > 0

    # 测试空消息列表场景
    empty_result = await LLMToolKit.summarize_discussion_intent([])
    assert empty_result.content == ""
    assert empty_result.elapsed_time == 0

    # 测试单条消息场景
    single_message = [ChatMessage(role="user", content="推荐一款游戏手机")]
    single_result = await LLMToolKit.summarize_discussion_intent(single_message)
    assert single_result.content is not None
    assert isinstance(single_result.content, str)
    assert len(single_result.content) > 0

    # 测试history_turns参数
    limited_result = await LLMToolKit.summarize_discussion_intent(test_messages, history_turns=2)
    assert limited_result.content is not None
    assert isinstance(limited_result.content, str)
    assert len(limited_result.content) > 0

    # 测试包含action的消息
    action_message = ChatMessage(
        role="assistant",
        action={
            "name": "search_phones",
            "parameters": [{"name": "price", "value": "5000"}],
            "result": [{"name": "phones", "type": "list", "value": ["Phone A", "Phone B"]}]
        }
    )
    action_messages = test_messages + [action_message]
    action_result = await LLMToolKit.summarize_discussion_intent(action_messages)
    assert action_result.content is not None
    assert isinstance(action_result.content, str)
    assert len(action_result.content) > 0

    # 测试英语对话场景
    english_messages = [
        ChatMessage(role="user", content="What's the weather like in London?"),
        ChatMessage(role="assistant", content="It's cloudy with light rain in London today."),
        ChatMessage(role="user", content="How about Manchester?"),
        ChatMessage(role="assistant", content="Manchester is experiencing sunny intervals."),
        ChatMessage(role="user", content="And Birmingham?")
    ]
    english_result = await LLMToolKit.summarize_discussion_intent(english_messages)
    assert english_result.content is not None
    assert isinstance(english_result.content, str)
    assert len(english_result.content) > 0

    # 测试德语对话场景
    german_messages = [
        ChatMessage(role="user", content="Wie ist das Wetter in Berlin?"),
        ChatMessage(role="assistant", content="In Berlin ist es heute sonnig und warm."),
        ChatMessage(role="user", content="Und in München?"),
        ChatMessage(role="assistant", content="In München regnet es leicht."),
        ChatMessage(role="user", content="Was ist mit Hamburg?")
    ]
    german_result = await LLMToolKit.summarize_discussion_intent(german_messages)
    assert german_result.content is not None
    assert isinstance(german_result.content, str)
    assert len(german_result.content) > 0

if __name__ == "__main__":
    logger.info("开始测试 summarize_discussion_intent 方法...")
    asyncio.run(test_summarize_discussion_intent())
    logger.info("测试完成")