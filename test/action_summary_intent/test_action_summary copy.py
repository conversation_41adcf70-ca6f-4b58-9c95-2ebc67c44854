import asyncio
import yaml
import pandas as pd
from loguru import logger
from src.action.retrieve import MaterialRetriever
from src.common.toolkit import LLMToolKit
from src.session_manager.chat_context import ChatMessage
from src.agent_core.single_action import SingleActionAgent
import json

async def get_intent_by_summary(
    history_messages: list[ChatMessage], 
    final_query: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    使用LLMToolKit.summary方法获取搜索查询并查找相关few-shots
    """
    # 构建conversation_progress
    conversation_progress = []
    for msg in history_messages:
        if msg.content:
            conversation_progress.append(msg.content)
    
    # 使用summary方法获取搜索查询
    summary_result = await LLMToolKit.summary(
        "\n".join(conversation_progress[-3:]),
        final_query,
    )
    search_query = summary_result.content
    
    # 使用搜索查询获取few-shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        filter_action_names=[a["name"] for a in (actions or [])],
        few_shot_limit=2,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_shot_input = []
    few_shot_action = []
    for shot in materials.few_shots:
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
    
    return {
        'search_query': search_query,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action
    }

async def get_intent_by_build_query(
    history_messages: list[ChatMessage], 
    final_query: str,
    actions: list[dict] = None,
    logger=logger
) -> dict:
    """
    使用build_search_query方法获取搜索查询并查找相关few-shots
    """
    # 使用build_search_query方法获取搜索查询
    search_query = SingleActionAgent.build_search_query(
        history_messages, 
        final_query, 
        limit=2
    )
    
    # 使用搜索查询获取few-shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        filter_action_names=[a["name"] for a in (actions or [])],
        few_shot_limit=2,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_shot_input = []
    few_shot_action = []
    for shot in materials.few_shots:
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
    
    return {
        'search_query': search_query,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action
    }

async def get_intent_by_discussion_summary(
    history_messages: list[ChatMessage], 
    final_query: str,
    logger=logger
) -> dict:
    """
    通过对话总结获取完整的用户意图
    
    Args:
        history_messages: 历史对话消息
        final_query: 最终用户查询
        logger: 日志记录器
    
    Returns:
        dict: 包含处理结果的字典，格式如下：
        {
            'discussion_summary': str,              # 对话总结
            'search_query': str,                    # 完整意图作为搜索查询
            'few_shot_input': list[str],           # few-shot的输入列表
            'few_shot_action': list[str],          # few-shot的动作列表
        }
    """
    # 获取对话总结
    summary_result = await LLMToolKit.summarize_discussion_intent(history_messages)

    # 使用complete_user_intent获取完整意图
    # complete_intent_result = await LLMToolKit.complete_user_intent(
    #     summary=summary_result.content,
    #     current_question=final_query
    # )

    # 将summary_result和final_query拼接
    search_query = f"{summary_result.content} | {final_query}"
    
    
    # 使用完整意图查询few_shots
    materials = await MaterialRetriever.retrieve(
        search_query,
        few_shot_limit=2,
        _logger=logger,
    )
    
    # 提取few_shots的input和action
    few_shot_input = []
    few_shot_action = []
    for shot in materials.few_shots:
        few_shot_input.append(shot.get('Input', ''))
        few_shot_action.append(shot.get('Output', {}).get('action', ''))
    
    return {
        'discussion_summary': summary_result.content,
        'search_query': search_query,
        'few_shot_input': few_shot_input,
        'few_shot_action': few_shot_action
    }

async def test_summarize_discussion_intent():
    # 从YAML文件加载测试用例
    with open('test/action_summary_intent/dialogue_test_cases.yaml', 'r', encoding='utf-8') as file:
        test_data = yaml.safe_load(file)
    
    # 创建结果列表用于存储所有测试用例的结果
    results_data = []
    
    try:
        for case in test_data['test_cases']:
            logger.info(f"\n测试用例: {case['name']}")
            
            # 获取所有消息
            messages = [ChatMessage(role=msg['role'], content=msg['content']) 
                       for msg in case['messages']]
            
            # 找到最后一个用户消息的索引
            last_user_index = -1
            for i in range(len(messages)-1, -1, -1):
                if messages[i].role == 'user':
                    last_user_index = i
                    break
            
            if last_user_index == -1:
                logger.warning("未找到用户消息")
                continue
            
            # 分离历史消息和最后的用户查询
            history_messages = messages[:last_user_index]
            final_user_query = messages[last_user_index].content
            
            # 打印测试用例的对话内容
            logger.info("历史对话:")
            for msg in history_messages:
                logger.info(f"{msg.role}: {msg.content}")
            logger.info(f"\n最终用户查询: {final_user_query}")
            
            # 使用三种不同方法获取结果
            result1 = await get_intent_by_discussion_summary(
                history_messages=history_messages,
                final_query=final_user_query
            )
            
            result2 = await get_intent_by_summary(
                history_messages=history_messages,
                final_query=final_user_query
            )
            
            result3 = await get_intent_by_build_query(
                history_messages=history_messages,
                final_query=final_user_query
            )
            
            
            # 收集当前测试用例的结果
            case_result = {
                '测试用例': case['name'],
                '历史对话': '\n'.join([f"{msg.role}: {msg.content}" for msg in history_messages]),
                '最终用户查询': final_user_query,
                '预期结果': case.get('expected', ''),
                
                '方法1_对话总结+完整意图': result1['search_query'],
                '方法1_Few-shot输入': '\n'.join(result1['few_shot_input']),
                '方法1_Few-shot动作': '\n'.join(result1['few_shot_action']),
                
                '方法2_最近三轮总结': result2['search_query'],
                '方法2_Few-shot输入': '\n'.join(result2['few_shot_input']),
                '方法2_Few-shot动作': '\n'.join(result2['few_shot_action']),
                
                '方法3_直接拼接': result3['search_query'],
                '方法3_Few-shot输入': '\n'.join(result3['few_shot_input']),
                '方法3_Few-shot动作': '\n'.join(result3['few_shot_action']),
            }
            
            results_data.append(case_result)
            
            # 打印日志...（保持原有的日志输出）
            
        # 创建DataFrame并保存到Excel
        df = pd.DataFrame(results_data)
        excel_path = 'test/action_summary_intent/test_results.xlsx'
        df.to_excel(excel_path, index=False, engine='openpyxl')
        logger.info(f"测试结果已保存到: {excel_path}")

    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    logger.info("开始测试 summarize_discussion_intent 方法...")
    asyncio.run(test_summarize_discussion_intent())
    logger.info("测试完成")