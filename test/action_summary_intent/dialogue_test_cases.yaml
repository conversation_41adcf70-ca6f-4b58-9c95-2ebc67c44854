test_cases:
  - name: "示例1-天气温度查询"
    messages:
      - role: "user"
        content: "北京今天气温多少？"
      - role: "assistant"
        content: "最高温度15°C，最低温度8°C"
      - role: "user"
        content: "那上海呢？"
    expected: "上海今天气温多少？"

  - name: "示例2-天气状况查询"
    messages:
      - role: "user"
        content: "查一下北京的天气"
      - role: "assistant"
        content: "北京今天晴转阴，气温20°C"
      - role: "user"
        content: "那上海呢？"
    expected: "查一下上海的天气"

  - name: "示例3-诗歌生成"
    messages:
      - role: "user"
        content: "生成一段五言绝句欢送一下刘总"
      - role: "assistant"
        content: "生成了一段五言绝句，送别刘总：\n春风送暖去，\n柳垂新绿枝。\n离别总难舍，\n共盼重逢时。"
      - role: "user"
        content: "再搞个七言律诗吧"
    expected: "生成一段七言律诗送别王总"

  - name: "示例4-空气质量查询"
    messages:
      - role: "user"
        content: "查一下杭州的空气质量"
      - role: "assistant"
        content: "杭州今天的空气质量指数为75，属于良好。"
      - role: "user"
        content: "北京呢？"
    expected: "查一下北京的空气质量"

  - name: "示例5-知识问答"
    messages:
      - role: "user"
        content: "你知道张大千吗？"
      - role: "assistant"
        content: "张大千是20世纪著名的中国画家。"
      - role: "user"
        content: "那李可染呢？"
    expected: "你知道李可染吗？"

  - name: "示例6-下雪预报"
    messages:
      - role: "user"
        content: "什么时候下雪呢？"
      - role: "assistant"
        content: "北京预计在明天早晨有小雪。"
      - role: "user"
        content: "上海呢？"
    expected: "上海什么时候下雪？"

  - name: "示例7-消息通知"
    messages:
      - role: "user"
        content: "给张三发消息，告诉他明天早点来公司"
      - role: "assistant"
        content: "消息已发送给张三，内容是：明天早点来公司。"
      - role: "user"
        content: "那也通知下李四"
    expected: "给李四发消息，告诉他明天早点来公司"

  - name: "示例8-穿衣建议"
    messages:
      - role: "user"
        content: "我去深圳要穿什么衣服？"
      - role: "assistant"
        content: "深圳天气温暖，建议穿轻便衣物。"
      - role: "user"
        content: "那去广州呢？"
    expected: "我去广州要穿什么衣服？"

  - name: "示例9-前台接待"
    messages:
      - role: "user"
        content: "我在前台，要找A部门的B同事"
      - role: "assistant"
        content: "已经通知B同事来前台接待您。"
      - role: "user"
        content: "那再找下D同事"
    expected: "我在前台，要找D同事"

  - name: "示例10-公司调查"
    messages:
      - role: "user"
        content: "你能告诉我公司里谁最能喝酒吗？"
      - role: "assistant"
        content: "根据公司数据，张三是最能喝酒的。"
      - role: "user"
        content: "那谁最能吃辣？"
    expected: "公司里谁最能吃辣？"