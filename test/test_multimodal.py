import asyncio
import json
import traceback
from typing import Dict, Optional
from urllib.parse import urljoin

import aiohttp
from loguru import logger

from src.assistant.planner.recommend_prompts import VISION_PROMPT
from src.settings import agent_setting


async def test_multimodal_model(
    image_url: str, use_openai: bool = False, verbose: bool = True
) -> Optional[Dict]:
    """Test multimodal model with given image URL

    Args:
        image_url: URL of the image to analyze
        use_openai: Whether to use OpenAI API (True) or local model (False)
        verbose: Whether to print detailed logs
    """
    url = f"{agent_setting.vision_base_url}/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {agent_setting.vision_api_key}",
    }

    if use_openai:
        # For OpenAI API
        headers["Authorization"] = f"Bearer {agent_setting.plan_model_api_key}"
        model_name = agent_setting.generate_text_model
    else:
        # For local model
        model_name = agent_setting.vision_model

    payload = {
        "model": model_name,
        "temperature": 0.0,
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "image_url", "image_url": {"url": image_url}},
                    {"type": "text", "text": VISION_PROMPT},
                ],
            }
        ],
    }

    if verbose:
        logger.info(f"Testing image: {image_url}")
        logger.info(f"Using model: {model_name}")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                response_data = await response.json()

                if verbose:
                    logger.info(f"Raw response: {response_data}")

                if not response_data.get("choices"):
                    logger.error("No choices in response")
                    return None

                result = response_data["choices"][0]["message"]["content"]

                # Clean up the result
                prefixes_to_remove = [
                    "```json",
                    "```",
                    "单人示例：",
                    "多人示例：",
                    "单人示例:",
                    "多人示例:",
                ]

                if result.endswith("```"):
                    result = result[:-3]

                for prefix in prefixes_to_remove:
                    if result.startswith(prefix):
                        result = result[len(prefix) :]
                        break

                result = result.strip()
                parsed_result = json.loads(result)

                if verbose:
                    logger.info(
                        f"Parsed result: {json.dumps(parsed_result, ensure_ascii=False, indent=2)}"
                    )

                return parsed_result

    except Exception as e:
        logger.error(f"Error testing multimodal model: {e} {traceback.format_exc()}")
        return None


async def main():
    images = [
        "aios001_20250722_5520c2f9cef00f8aef4f405d06122821.jpeg",
        "aios001_20250722_0240573a23ad1a531788ff7158fda0b1.jpeg",
        "aios001_20250722_a1f357e7558dae481003d1715e4bc091.jpeg",
        "aios001_20250722_5a79a48b7f57d6aa9fa4ae41529bdabe.jpeg",
        "aios001_20250722_a794f274a85c5910d859eda4c925bc1e.jpeg",
        "aios001_20250722_bacef2f9b1e19b18dd9dd722545b4624.jpeg",
        "aios001_20250722_d30531d02f7bdc882070fca636e01796.jpeg",
        "aios001_20250722_86ec872c79d0adf5e5befb203e05cde5.jpeg",
        "aios001_20250722_f312cdbbf24a41a11b32d7b040422473.jpeg",
        "aios001_20250722_8ca2f6ad180197baccbcf02b7f1f152b.jpeg",
        "aios001_20250722_8b08822b20af35ca382c112822719e0b.jpeg",
        "aios001_20250722_d440586fc5c80577492d957924c03412.jpeg",
        "aios001_20250722_0921998b764757c4b0af09780d47a301.jpeg",
        "aios001_20250722_c8949560bb9ba5d862444eb9344b428f.jpeg",
        "aios001_20250722_242ab56006064432ad8a42af40aa2d94.jpeg",
        "aios001_20250722_3d4302dccbaab9ecb55db3772113def9.jpeg",
        "aios001_20250722_df3ba965fb76983721b08143ca2907ef.jpeg",
        "aios001_20250722_ce150804f2071b94b6366767ea07cd8a.jpeg",
        "aios001_20250722_a2e4b790ea3408c4f1c453156c6b33e5.jpeg",
    ]
    base_url = "https://jiedai.ainirobot.com/orics/down/"

    # Test images - include both single person and multiple person scenarios
    test_images = [urljoin(base_url, image) for image in images]

    for image_url in test_images:
        logger.info("=" * 50)
        logger.info(f"Testing image: {image_url}")

        # Test with local model
        logger.info("\nTesting with local model:")
        result = await test_multimodal_model(image_url, use_openai=False)
        logger.info(f"result: {result}")

        # Test with OpenAI (optional)
        # logger.info("\nTesting with OpenAI:")
        # result = await test_multimodal_model(image_url, use_openai=True)


if __name__ == "__main__":
    asyncio.run(main())
