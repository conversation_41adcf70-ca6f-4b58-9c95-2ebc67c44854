import pytest
from unittest.mock import MagicMock
from src.action.post_processing import convert_element_id


class MockRobot:
    def __init__(self, clickable_elements=None):
        self.interface_state = MagicMock()
        self.interface_state.clickable_elements = clickable_elements or []


class MockAgentParameters:
    def __init__(self, clickable_elements=None):
        self.robot = MockRobot(clickable_elements)


@pytest.mark.asyncio
async def test_convert_element_id_normal_case():
    """测试正常情况下的元素转换"""
    clickable_elements = [
        '<a id="1">酒店</a>',
        '<a id="2">机票</a>',
        '<a id="3">火车票</a>',
    ]
    agent_parameters = MockAgentParameters(clickable_elements)

    # 测试匹配成功的情况
    result = await convert_element_id({"element_tag": "酒店"}, agent_parameters)
    assert result == {"element_tag": "酒店", "element_id": "1"}

    # 测试匹配失败的情况
    result = await convert_element_id({"element_tag": "不存在的标签"}, agent_parameters)
    assert result == {"element_tag": "不存在的标签", "element_id": ""}


@pytest.mark.asyncio
async def test_convert_element_id_with_empty_input():
    """测试空输入的情况"""
    agent_parameters = MockAgentParameters([])

    # 测试空element_tag
    result = await convert_element_id({}, agent_parameters)
    assert result == {}

    # 测试空clickable_elements
    result = await convert_element_id({"element_tag": "测试"}, agent_parameters)
    assert result == {"element_tag": "测试", "element_id": ""}


@pytest.mark.asyncio
async def test_convert_element_id_with_multiline_content():
    """测试包含多行内容的情况"""
    clickable_elements = [
        """<a id="91">



        00:40</a>""",
        """<a id="92">


        下一站


        </a>""",
    ]
    agent_parameters = MockAgentParameters(clickable_elements)

    # 测试多行空白的情况
    result = await convert_element_id({"element_tag": "00:40"}, agent_parameters)
    assert result == {"element_tag": "00:40", "element_id": "91"}

    # 测试多行内容的情况
    result = await convert_element_id({"element_tag": "下一站"}, agent_parameters)
    assert result == {"element_tag": "下一站", "element_id": "92"}


@pytest.mark.asyncio
async def test_convert_element_id_with_special_cases():
    """测试特殊情况"""
    clickable_elements = [
        '<a id="100"> </a>',  # 只包含空格
        '<a id="101">\t\n\r</a>',  # 只包含空白字符
        '<a id="102">  测试  </a>',  # 首尾有空格
    ]
    agent_parameters = MockAgentParameters(clickable_elements)

    # 测试空格内容 - 当element_tag为空时，应该直接返回原始参数
    result = await convert_element_id({"element_tag": ""}, agent_parameters)
    assert result == {"element_tag": ""}

    # 测试只包含空格的内容
    result = await convert_element_id({"element_tag": " "}, agent_parameters)
    assert result == {"element_tag": " ", "element_id": ""}

    # 测试首尾空格
    result = await convert_element_id({"element_tag": "测试"}, agent_parameters)
    assert result == {"element_tag": "测试", "element_id": "102"}


if __name__ == "__main__":
    import asyncio

    async def run_tests():
        # 运行所有测试用例
        await test_convert_element_id_normal_case()
        await test_convert_element_id_with_empty_input()
        await test_convert_element_id_with_multiline_content()
        await test_convert_element_id_with_special_cases()
        print("所有测试用例执行成功！")

    # 运行测试
    asyncio.run(run_tests())
