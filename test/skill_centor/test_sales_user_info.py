import unittest
from unittest.mock import Mock, patch, AsyncMock
import sys
import os
import asyncio
import time
import statistics
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))

from src.action.server_function import collect_user_info_and_recommend
from src.action.model import FunctionResult, FunctionOutput, AudioRequest, NonStreamingContent


def timing_decorator(func_name: str):
    """计时装饰器"""
    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            start_time = time.perf_counter()
            result = await func(self, *args, **kwargs)
            end_time = time.perf_counter()

            execution_time = (end_time - start_time) * 1000  # 转换为毫秒

            # 记录执行时间
            if not hasattr(self, 'execution_times'):
                self.execution_times = {}
            if func_name not in self.execution_times:
                self.execution_times[func_name] = []

            self.execution_times[func_name].append(execution_time)
            print(f"\n⏱️  {func_name} 执行时间: {execution_time:.2f}ms")

            return result
        return wrapper
    return decorator


class MockMessage:
    def __init__(self, content="", role="user"):
        self.content = content
        self.role = role


class MockChatContext:
    def __init__(self, messages=None):
        self.messages = messages or []


class MockMemory:
    def __init__(self, messages=None, preferences=None):
        self._messages = messages or []
        self._preferences = preferences or {}

    def get_chat_context(self, max_chat_history=10):
        return MockChatContext(self._messages[:max_chat_history])

    def get_all_preferences(self):
        return self._preferences.copy()

    def add_preference(self, key, value):
        self._preferences[key] = value


class MockGeoLocation:
    def __init__(self):
        self.latitude = 31.2304
        self.longitude = 121.4737


class MockRobot:
    def __init__(self):
        self.device_id = "M03SCN2B19025008383B"
        self.enterprise_id = "orion.ovs.entprise.9945420568"
        self.map_id = "afcb5b7d2a564eceaf23d2ae7d860784"
        self.action_version = "v1.0.5"
        self.language = "zh_CN"
        self.timezone = "Asia/Shanghai"
        self.geo_location = MockGeoLocation()

    def is_multilingual(self):
        """Mock方法：返回是否支持多语言"""
        return False


class TestCollectUserInfoAndRecommend(unittest.TestCase):
    """测试收集用户信息并推荐功能"""

    def setUp(self):
        """测试初始化"""
        self.mock_robot = MockRobot()
        self.mock_memory = MockMemory()
        self.execution_times = {}

    def tearDown(self):
        """测试清理和性能统计"""
        if hasattr(self, 'execution_times') and self.execution_times:
            print("\n" + "="*60)
            print("📊 性能统计报告")
            print("="*60)

            for func_name, times in self.execution_times.items():
                if times:
                    avg_time = statistics.mean(times)
                    max_time = max(times)
                    min_time = min(times)
                    total_time = sum(times)

                    print(f"\n🔍 {func_name}:")
                    print(f"   • 执行次数: {len(times)}")
                    print(f"   • 平均耗时: {avg_time:.2f}ms")
                    print(f"   • 最大耗时: {max_time:.2f}ms")
                    print(f"   • 最小耗时: {min_time:.2f}ms")
                    print(f"   • 总耗时: {total_time:.2f}ms")

                    if len(times) > 1:
                        std_dev = statistics.stdev(times)
                        print(f"   • 标准差: {std_dev:.2f}ms")

            print("\n" + "="*60)

    async def _run_performance_test(self, test_func, test_name: str, iterations: int = 5):
        """运行性能测试"""
        print(f"\n🚀 开始性能测试: {test_name} (运行 {iterations} 次)")

        for i in range(iterations):
            print(f"\n第 {i+1}/{iterations} 次运行:")
            await test_func()

        # 计算统计信息
        if test_name in self.execution_times:
            times = self.execution_times[test_name]
            avg_time = statistics.mean(times)
            print(f"\n✅ {test_name} 平均耗时: {avg_time:.2f}ms")

    @timing_decorator("基本功能测试")
    @patch('src.action.server_function.get_promote_product_info')
    @patch('src.action.server_function.extract_user_info_from_conversation')
    @patch('src.action.server_function.LLMToolKit.get_text_conversation_records')
    @patch('src.action.server_function.put_run_step')
    async def async_test_basic_functionality(self, mock_put_run_step, mock_get_text_conversation,
                                           mock_extract_user_info, mock_get_product_info):
        """测试基本功能：用户信息收集和推荐"""

        # Mock异步函数返回值
        mock_put_run_step.return_value = None
        mock_get_text_conversation.return_value = ["用户说: 我想买台联想电脑，3000左右的"]
        mock_extract_user_info.return_value = {"预算": "3000左右", "品牌偏好": "联想"}
        mock_get_product_info.return_value = [
            {"name": "联想ThinkPad", "price": "2999", "description": "商务办公笔记本"}
        ]

        # 测试参数
        user_info_fields = ["预算", "品牌偏好"]
        kwargs = {
            "__robot": self.mock_robot,
            "__memory": self.mock_memory,
            "_USER_QUERY": "我想买台联想电脑，3000左右的"
        }

        # 调用函数 - 这里是我们要测量的核心部分
        function_start_time = time.perf_counter()
        result = await collect_user_info_and_recommend(
            user_info_fields=user_info_fields,
            **kwargs
        )
        function_end_time = time.perf_counter()

        # 记录核心函数执行时间
        core_function_time = (function_end_time - function_start_time) * 1000
        if 'collect_user_info_and_recommend' not in self.execution_times:
            self.execution_times['collect_user_info_and_recommend'] = []
        self.execution_times['collect_user_info_and_recommend'].append(core_function_time)

        # 验证结果
        self.assertIsInstance(result, FunctionResult)
        self.assertEqual(result.type, "function")

        # 验证返回的数据结构
        result_data = result.debug
        self.assertIn("user_profile", result_data)
        self.assertIn("products_count", result_data)

        # 验证
        print(result.content.audio_request)
        print("测试基本功能通过")

    @timing_decorator("内存集成测试")
    @patch('src.action.server_function.get_promote_product_info')
    @patch('src.action.server_function.extract_user_info_from_conversation')
    @patch('src.action.server_function.LLMToolKit.get_text_conversation_records')
    @patch('src.action.server_function.put_run_step')
    async def async_test_memory_integration(self, mock_put_run_step, mock_get_text_conversation,
                                          mock_extract_user_info, mock_get_product_info):
        """测试内存集成：从对话历史中提取用户信息"""

        # 设置带有历史数据的内存
        messages = [
            MockMessage("你好"),
            MockMessage("我需要联想品牌"),
        ]
        existing_preferences = {"预算": "5000以内"}
        memory_with_history = MockMemory(messages, existing_preferences)

        # Mock异步函数返回值
        mock_put_run_step.return_value = None
        mock_get_text_conversation.return_value = [
            "用户说: 你好",
            "用户说: 我需要联想品牌",
            "用户说: 给我推荐一下办公用的"
        ]
        mock_extract_user_info.return_value = {"使用场景": "办公", "品牌偏好": "联想"}
        mock_get_product_info.return_value = [
            {"name": "联想ThinkPad", "price": "4999", "description": "商务办公笔记本"}
        ]

        # 测试参数
        user_info_fields = ["使用场景", "品牌偏好"]
        kwargs = {
            "__robot": self.mock_robot,
            "__memory": memory_with_history,
            "_USER_QUERY": "给我推荐一下办公用的"
        }

        # 调用函数 - 测量核心函数耗时
        function_start_time = time.perf_counter()
        result = await collect_user_info_and_recommend(
            user_info_fields=user_info_fields,
            **kwargs
        )
        function_end_time = time.perf_counter()

        # 记录核心函数执行时间
        core_function_time = (function_end_time - function_start_time) * 1000
        if 'collect_user_info_and_recommend' not in self.execution_times:
            self.execution_times['collect_user_info_and_recommend'] = []
        self.execution_times['collect_user_info_and_recommend'].append(core_function_time)

        # 验证结果
        self.assertIsInstance(result, FunctionResult)

        # 验证用户画像合并（原有偏好 + 新提取的信息）
        result_data = result.debug
        user_profile = result_data["user_profile"]

        # 验证合并后的用户画像包含原有和新提取的信息
        self.assertIn("预算", user_profile)  # 原有信息
        self.assertIn("使用场景", user_profile)  # 新提取信息
        self.assertIn("品牌偏好", user_profile)  # 新提取信息

        # 验证内存中的偏好被更新
        self.assertEqual(memory_with_history.get_all_preferences()["使用场景"], "办公")
        self.assertEqual(memory_with_history.get_all_preferences()["品牌偏好"], "联想")
        print("测试内存集成通过")

    @timing_decorator("空提取测试")
    @patch('src.action.server_function.get_promote_product_info')
    @patch('src.action.server_function.extract_user_info_from_conversation')
    @patch('src.action.server_function.LLMToolKit.get_text_conversation_records')
    @patch('src.action.server_function.put_run_step')
    async def async_test_empty_extraction(self, mock_put_run_step, mock_get_text_conversation,
                                        mock_extract_user_info, mock_get_product_info):
        """测试没有提取到用户信息的情况"""

        # Mock异步函数返回值
        mock_put_run_step.return_value = None
        mock_get_text_conversation.return_value = ["用户说: 你好"]
        mock_extract_user_info.return_value = {}  # 没有提取到任何信息
        mock_get_product_info.return_value = []  # 没有产品信息

        # 测试参数
        user_info_fields = ["预算", "品牌偏好"]
        kwargs = {
            "__robot": self.mock_robot,
            "__memory": self.mock_memory,
            "_USER_QUERY": "你好"
        }

        # 调用函数 - 测量核心函数耗时
        function_start_time = time.perf_counter()
        result = await collect_user_info_and_recommend(
            user_info_fields=user_info_fields,
            **kwargs
        )
        function_end_time = time.perf_counter()

        # 记录核心函数执行时间
        core_function_time = (function_end_time - function_start_time) * 1000
        if 'collect_user_info_and_recommend' not in self.execution_times:
            self.execution_times['collect_user_info_and_recommend'] = []
        self.execution_times['collect_user_info_and_recommend'].append(core_function_time)

        # 验证结果
        result_data = result.debug
        print("测试空提取情况通过")

    def test_basic_functionality(self):
        """测试基本功能"""
        asyncio.run(self.async_test_basic_functionality())

    def test_memory_integration(self):
        """测试内存集成"""
        asyncio.run(self.async_test_memory_integration())

    def test_empty_extraction(self):
        """测试空提取情况"""
        asyncio.run(self.async_test_empty_extraction())

    def test_performance_basic_functionality(self):
        """性能测试：基本功能 - 多次运行"""
        async def run_test():
            await self._run_performance_test(
                self.async_test_basic_functionality,
                "基本功能测试",
                iterations=3
            )

        asyncio.run(run_test())

    def test_performance_memory_integration(self):
        """性能测试：内存集成 - 多次运行"""
        async def run_test():
            await self._run_performance_test(
                self.async_test_memory_integration,
                "内存集成测试",
                iterations=3
            )

        asyncio.run(run_test())

    def test_performance_all_scenarios(self):
        """性能测试：所有场景对比"""
        async def run_all_tests():
            print("\n🎯 开始综合性能测试")

            # 测试各个场景
            scenarios = [
                (self.async_test_basic_functionality, "基本功能"),
                (self.async_test_memory_integration, "内存集成"),
                (self.async_test_empty_extraction, "空提取"),
            ]

            for test_func, scenario_name in scenarios:
                print(f"\n📋 测试场景: {scenario_name}")
                for i in range(3):
                    await test_func()

            # 输出性能对比
            if 'collect_user_info_and_recommend' in self.execution_times:
                times = self.execution_times['collect_user_info_and_recommend']
                print(f"\n🏆 collect_user_info_and_recommend 总体性能:")
                print(f"   • 总执行次数: {len(times)}")
                print(f"   • 平均耗时: {statistics.mean(times):.2f}ms")
                print(f"   • 中位数: {statistics.median(times):.2f}ms")
                if len(times) > 1:
                    print(f"   • 标准差: {statistics.stdev(times):.2f}ms")

        asyncio.run(run_all_tests())


if __name__ == "__main__":
    unittest.main(verbosity=2)
