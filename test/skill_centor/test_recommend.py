import asyncio
import ssl
import urllib.parse
from loguru import logger
from src.action.post_processing import convert_recommend_url
from src.session_manager.robot import Robot
from src.settings import agent_setting
import pytest
import os

# 添加这些行来禁用 SSL 验证
os.environ["PYTHONHTTPSVERIFY"] = "0"
ssl._create_default_https_context = ssl._create_unverified_context


class MockAgentParameters:
    """模拟Agent参数类"""

    def __init__(self, robot):
        self.robot = robot
        self.llm_client = None  # 实际测试中需要mock LLM客户端
        # 添加一个自定义的 SSL 上下文
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE


async def run_test_scenario(scenario_num, description, action_parameters, agent_parameters):
    """运行单个测试场景"""
    logger.info(f"=== 测试场景 {scenario_num}: {description} ===")
    logger.info(f"输入参数: {action_parameters}")

    result = await convert_recommend_url(action_parameters, agent_parameters)

    logger.info(f"输出结果:")
    logger.info(f"  - place: {result.get('place', 'N/A')}")
    logger.info(f"  - target: {result.get('target', 'N/A')}")
    logger.info(f"  - url: {result.get('url', 'N/A')}")

    # 验证URL格式和参数
    if 'url' in result:
        parsed_url = urllib.parse.urlparse(result["url"])
        query_params = urllib.parse.parse_qs(parsed_url.query)
        logger.info(f"  - URL解析参数:")
        logger.info(f"    - keyword: {urllib.parse.unquote(query_params.get('keyword', ['N/A'])[0])}")
        logger.info(f"    - lng: {query_params.get('lng', ['N/A'])[0]}")
        logger.info(f"    - lat: {query_params.get('lat', ['N/A'])[0]}")

    logger.info(f"场景 {scenario_num} 测试完成\n")
    return result


@pytest.mark.asyncio
async def test_convert_recommend_url():
    """测试推荐URL转换功能 - 用户提供的测试场景"""
    logger.info("开始测试推荐URL转换功能 - 用户测试场景")

    # 初始化机器人
    robot = Robot()
    robot.device_id = "M03SCN2B19025008383B"
    robot.enterprise_id = "orion.ovs.entprise.9945420568"
    robot.map_id = "afcb5b7d2a564eceaf23d2ae7d860784"
    robot.language = "zh_CN"
    robot.timezone = "Asia/Shanghai"
    robot.geo_location.latitude = 31.2304
    robot.geo_location.longitude = 121.4737

    agent_parameters = MockAgentParameters(robot)

    # 测试场景列表
    test_scenarios = [
        {
            "num": 1,
            "description": "中文查询北京附近的餐厅",
            "parameters": {
                "query": "北京附近的餐厅",
                "place": "北京",
                "target": "餐厅"
            }
        },
        {
            "num": 2,
            "description": "中文查询上海的餐厅",
            "parameters": {
                "query": "上海的餐厅",
                "place": "上海",
                "target": "餐厅"
            }
        },
        {
            "num": 3,
            "description": "中文查询北京市的餐厅",
            "parameters": {
                "query": "北京的餐厅",
                "place": "北京市",
                "target": "餐厅"
            }
        },
        {
            "num": 4,
            "description": "中文查询香港九龙附近的餐厅",
            "parameters": {
                "query": "香港九龙附近的餐厅",
                "place": "香港九龙",
                "target": "餐厅"
            }
        },
        {
            "num": 5,
            "description": "中文查询附近酒店（place为-1）",
            "parameters": {
                "query": "酒店",
                "place": "-1",
                "target": "酒店"
            }
        },
        {
            "num": 6,
            "description": "中文查询附近美食（place为-1）",
            "parameters": {
                "query": "美食",
                "place": "-1",
                "target": "美食"
            }
        },
        {
            "num": 7,
            "description": "中文查询天安门附近酒店",
            "parameters": {
                "query": "天安门附近有什么酒店",
                "place": "天安门",
                "target": "酒店"
            }
        },
        {
            "num": 8,
            "description": "中文查询故宫附近酒店",
            "parameters": {
                "query": "故宫附近有什么酒店",
                "place": "故宫",
                "target": "酒店"
            }
        },
        {
            "num": 9,
            "description": "中文查询附近美食（place为-1）",
            "parameters": {
                "query": "附近有什么好吃的",
                "place": "-1",
                "target": "美食"
            }
        },
        {
            "num": 10,
            "description": "中文查询万东科技园附近景点",
            "parameters": {
                "query": "这个地方听说不错，但我想知道有没有什么特别的景点？",
                "place": "万东科技文化创意产业园",
                "target": "景点"
            }
        },
        {
            "num": 11,
            "description": "中文查询万东科技园附近美食",
            "parameters": {
                "query": "来到新地方总想尝尝地道的美食，有什么推荐？",
                "place": "万东科技文化创意产业园",
                "target": "美食"
            }
        },
        {
            "num": 12,
            "description": "中文查询天安门广场附近酒店",
            "parameters": {
                "query": "天安门广场附近的酒店",
                "place": "天安门广场",
                "target": "酒店"
            }
        },
        {
            "num": 13,
            "description": "中文查询上海的景点",
            "parameters": {
                "query": "上海的景点",
                "place": "上海",
                "target": "景点"
            }
        },
        {
            "num": 14,
            "description": "英文查询附近餐厅（place为-1）",
            "parameters": {
                "query": "What restaurants are nearby?",
                "place": "-1",
                "target": "restaurants"
            }
        },
        {
            "num": 15,
            "description": "英文查询故宫附近酒店",
            "parameters": {
                "query": "What hotels are near Forbidden City",
                "place": "Forbidden City",
                "target": "hotels"
            }
        },
        {
            "num": 16,
            "description": "粤语查询万东科技园附近美食",
            "parameters": {
                "query": "附近有咩美食",
                "place": "北京市朝阳区建国路万东科技文化创意产业园",
                "target": "美食"
            }
        },
        {
            "num": 17,
            "description": "繁体中文查询香港九龙附近餐厅",
            "parameters": {
                "query": "香港九龍附近嘅餐廳",
                "place": "香港九龍",
                "target": "餐廳"
            }
        },
        {
            "num": 18,
            "description": "中英文混合查询万东科技园附近food",
            "parameters": {
                "query": "附近的food",
                "place": "万东科技文化创意产业园",
                "target": "food"
            }
        },
        {
            "num": 19,
            "description": "中英文混合查询双桥附近hotel",
            "parameters": {
                "query": "双桥附近的hotel",
                "place": "双桥",
                "target": "hotel"
            }
        }
    ]

    # 执行所有测试场景
    results = []
    for scenario in test_scenarios:
        result = await run_test_scenario(
            scenario["num"],
            scenario["description"],
            scenario["parameters"],
            agent_parameters
        )
        results.append(result)

        # 基本断言验证
        assert "url" in result, f"场景{scenario['num']}: 结果中缺少url字段"
        assert "place" in result, f"场景{scenario['num']}: 结果中缺少place字段"
        assert "target" in result, f"场景{scenario['num']}: 结果中缺少target字段"

        # 验证place参数转换（-1应该转换为空字符串）
        expected_place = "" if scenario["parameters"]["place"] == "-1" else scenario["parameters"]["place"]

        # 验证target参数
        assert result["target"] == scenario["parameters"]["target"], f"场景{scenario['num']}: target参数不匹配"

        # 验证URL包含经纬度参数
        parsed_url = urllib.parse.urlparse(result["url"])
        query_params = urllib.parse.parse_qs(parsed_url.query)
        assert "lng" in query_params, f"场景{scenario['num']}: URL缺少经度参数"
        assert "lat" in query_params, f"场景{scenario['num']}: URL缺少纬度参数"
        assert float(query_params["lng"][0]) == 121.4737, f"场景{scenario['num']}: 经度参数错误"
        assert float(query_params["lat"][0]) == 31.2304, f"场景{scenario['num']}: 纬度参数错误"

    # 打印测试总结
    logger.info("=" * 60)
    logger.info("测试总结:")
    logger.info(f"总共执行了 {len(test_scenarios)} 个测试场景")
    logger.info("所有场景的URL生成结果:")

    for i, (scenario, result) in enumerate(zip(test_scenarios, results), 1):
        logger.info(f"场景{i}: {scenario['description']}")
        logger.info(f"  URL: {result.get('url', 'N/A')}")
        # 解码URL中的关键词以便阅读
        if 'url' in result:
            parsed_url = urllib.parse.urlparse(result["url"])
            query_params = urllib.parse.parse_qs(parsed_url.query)
            if 'keyword' in query_params:
                decoded_keyword = urllib.parse.unquote(query_params['keyword'][0])
                logger.info(f"  关键词: {decoded_keyword}")

    logger.info("=" * 60)
    logger.info("所有测试用例执行完成!")


async def main():
    """主测试函数"""
    # 设置区域版本和地图工具基础URL
    agent_setting.region_version = "domestic"
    # 设置一个测试用的基础URL（如果没有设置的话）
    if not hasattr(agent_setting, 'map_tool_base_url') or not agent_setting.map_tool_base_url:
        agent_setting.map_tool_base_url = "https://api.example.com/map"

    # 运行测试
    await test_convert_recommend_url()


if __name__ == "__main__":
    asyncio.run(main())