import asyncio
import ssl
from loguru import logger
from src.action.server_function import (
    convert_specific_route,
    generate_route_recommendation,
    generate_route_introduction,
)
from src.session_manager.robot import Robot
from src.settings import agent_setting
import pytest
import os

# 添加这些行来禁用 SSL 验证
os.environ["PYTHONHTTPSVERIFY"] = "0"
ssl._create_default_https_context = ssl._create_unverified_context


class MockMemory:
    """模拟Memory类"""

    def __init__(self):
        self.messages = []

    async def get_chat_context(self, max_chat_history=6):
        return type("obj", (object,), {"messages": self.messages})


class MockAgentParameters:
    """模拟Agent参数类"""

    def __init__(self, robot):
        self.robot = robot
        self.llm_client = None  # 实际测试中需要mock LLM客户端
        # 添加一个自定义的 SSL 上下文
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE


@pytest.mark.asyncio
async def test_convert_specific_route():
    """测试指定路线导览功能"""
    logger.info("开始测试指定路线导览功能")

    # 初始化机器人
    robot = Robot()
    # robot.device_id = "M03SCN2B19025008383B"
    # robot.enterprise_id = "orion.ovs.entprise.9945420568"
    # robot.map_id = "afcb5b7d2a564eceaf23d2ae7d860784"
    robot.device_id = "M03SCN1A14024430N038"
    robot.enterprise_id = "orion.ovs.entprise.4384083697"
    # robot.map_id = "6492c8da73664221a9a5e3b0fc4d7bbf"
    # robot.device_id = "M01BCNA010020142VB27"
    # robot.enterprise_id = "orion.ovs.entprise.0335036968"
    robot.language = "zh_CN"
    robot.timezone = "Asia/Shanghai"
    # robot.language = "en_US"
    # robot.timezone = "America/New_York"
    robot.geo_location.latitude = 31.2304
    robot.geo_location.longitude = 121.4737

    # 测试用例1: 使用路线名称选择
    logger.info("测试用例1: 使用路线名称选择")
    user_query = "带我参观猎户展厅"
    route_name = "猎户展厅"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"路线名称选择结果: {result}")

    # 测试用例2: 使用路线序号选择
    logger.info("测试用例2: 使用路线序号选择")
    user_query = "参观一下第1条路线"
    route_name = "第1条路线"
    # user_query = "Visit Route 3"
    # route_name = "Route 3"
    # user_query = "3번 경로를 방문하다"
    # route_name = "3번 경로"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"路线序号选择结果: {result}")

    # 测试用例3: 基本精准匹配
    logger.info("测试用例3: 基本精准匹配")
    user_query = "是的"
    route_name = "路线1:工厂"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"路线名称选择结果: {result}")

    # 测试用例4: 无效路线名称
    logger.info("测试用例4: 无效路线名称 路线不存在")
    user_query = "带我参观第五条路线"
    route_name = "第五条路线"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"无效路线名称结果: {result}")

    # 测试用例5: 无效路线名称
    logger.info("测试用例5: 无效路线名称 路线不存在")
    user_query = "带我参观第四条路线"
    route_name = "第四条路线"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"无效路线名称结果: {result}")

    # 测试用例6: 空路线名称
    logger.info("测试用例6: 空路线名称")
    user_query = "带我参观二号测试路线"
    route_name = ""
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"空路线名称结果: {result}")

    # 测试用例7: 使用路线名称选择
    logger.info("测试用例7: 使用路线顺序选择")
    user_query = "带我参观一下最后一条路线"
    route_name = "测试路线1"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"路线名称选择结果: {result}")

    # 海外版本测试用例1: 使用路线名称选择
    logger.info("海外测试用例1: 使用路线名称选择")
    user_query = "Take me on a factory tour route"
    route_name = "Factory"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"路线名称选择结果: {result}")

    # 海外版本测试用例2: 使用路线名称选择
    logger.info("海外测试用例2: 使用路线名称选择")
    user_query = "take me to meeting room"
    route_name = "meeting room"
    result = await convert_specific_route(user_query, route_name, __robot=robot)
    logger.info(f"路线名称选择结果: {result}")


@pytest.mark.asyncio
async def test_generate_route_recommendation():
    """测试路线推荐功能"""
    logger.info("开始测试路线推荐功能")

    # 初始化机器人
    robot = Robot()
    robot.device_id = "M03SCN1A14024430N038"
    robot.enterprise_id = "orion.ovs.entprise.4384083697"
    robot.language = "zh_CN"
    robot.timezone = "Asia/Shanghai"
    # robot.language = "en_US"
    # robot.timezone = "America/New_York"
    robot.geo_location.latitude = 31.2304
    robot.geo_location.longitude = 121.4737

    # 创建模拟的memory对象
    mock_memory = MockMemory()

    # 测试用例1: 正常用户查询
    logger.info("测试用例1: 正常用户查询")
    user_query = "我想参观办公区域路线"
    result = await generate_route_recommendation(user_query, __robot=robot)
    logger.info(f"正常用户查询结果: {result}")

    # 测试用例2: 空用户查询
    logger.info("测试用例2: 空用户查询")
    user_query = ""
    result = await generate_route_recommendation(user_query, __robot=robot)
    logger.info(f"空用户查询结果: {result}")

    # 测试用例3: 特殊需求查询
    logger.info("测试用例3: 特殊需求查询")
    user_query = "给我推荐一条参观路线。"
    result = await generate_route_recommendation(
        user_query, __robot=robot, __memory=mock_memory, _USER_QUERY=user_query
    )
    logger.info(f"特殊需求查询结果: {result}")

    # 海外测试用例1: 正常生成路线介绍
    logger.info("测试用例3: 特殊需求查询")
    user_query = "I'm on a tight schedule, please recommend one for me."
    result = await generate_route_recommendation(
        user_query, __robot=robot, __memory=mock_memory, _USER_QUERY=user_query
    )
    logger.info(f"特殊需求查询结果: {result}")


@pytest.mark.asyncio
async def test_generate_route_introduction():
    """测试生成路线介绍功能"""
    logger.info("开始测试生成路线介绍功能")

    # 初始化机器人
    robot = Robot()
    # robot.device_id = "M01BCNA010020142VB27"
    # robot.enterprise_id = "orion.ovs.entprise.0335036968"
    robot.device_id = "M03SCN1A14024430N038"
    robot.enterprise_id = "orion.ovs.entprise.4384083697"
    robot.language = "zh_CN"
    # robot.language = "en_US"
    # robot.language = "ar_SA"
    # robot.timezone = "Asia/Shanghai"
    # robot.timezone = "America/New_York"
    # robot.geo_location.latitude = 31.2304
    # robot.geo_location.longitude = 121.4737

    # 测试用例1: 正常生成路线介绍
    logger.info("测试用例1: 正常生成路线介绍")
    user_query = "带我参观"
    result = await generate_route_introduction(user_query, __robot=robot)
    logger.info(f"正常生成路线介绍结果: {result}")

    # 测试用例2: 模拟没有可用路线的情况
    logger.info("测试用例2: 模拟没有可用路线的情况")
    # 这里需要mock RouteService的get_all_routes方法返回空数据
    result = await generate_route_introduction(user_query, __robot=robot)
    logger.info(f"没有可用路线结果: {result}")

    # 测试用例3: 模拟异常情况
    logger.info("测试用例3: 模拟异常情况")
    # 这里需要mock RouteService抛出异常
    result = await generate_route_introduction(user_query, __robot=robot)
    logger.info(f"异常情况结果: {result}")

    # 海外测试用例1: 正常生成路线介绍
    logger.info("测试用例1: 正常生成路线介绍")
    user_query = "Show me around"
    result = await generate_route_introduction(user_query, __robot=robot)
    logger.info(f"正常生成路线介绍结果: {result}")

    # 测试海外阿拉伯语
    # 阿拉伯语测试用例1: 正常生成路线介绍
    logger.info("阿拉伯语测试用例1: 正常生成路线介绍")
    user_query = "أرني الطريق"  # 阿拉伯语："带我参观"的意思
    result = await generate_route_introduction(user_query, __robot=robot)
    logger.info(f"阿拉伯语正常生成路线介绍结果: {result}")


async def main():
    """主测试函数"""
    # 设置区域版本
    agent_setting.region_version = "oversea"

    # 运行测试
    await test_convert_specific_route()
    await test_generate_route_recommendation()
    await test_generate_route_introduction()


if __name__ == "__main__":
    asyncio.run(main())
