import asyncio
from src.action.server_function import calendar
from loguru import logger
from src.settings import agent_setting
from src.action.skill_center.calendars import get_calendar_data
from src.common.constant import Area
from src.utils.i18n import set_language


# 直接设置关键环境变量（这些是测试所需的最小环境变量集）
async def get_calendar_info():
    # user_question = "国庆节是几月几日"
    # user_question = "How many days until Christmas?"
    # user_question ="What month and day is today?"
    # user_question = "What's the next holiday?"
    # language = "ja_JP"
    # set_language(language)
    # user_question = "次の祝日は何ですか？"  # 日语
    # user_question = "다음 공휴일은 뭐야?"  # 韩语
    # user_question = "Quel est le prochain jour férié ?"  # 法语
    # language = "fr_FR"
    # set_language(language)
    # user_question = "When is the next holiday?"  # 英语
    language = "en_US"
    set_language(language)
    # user_question = "Wann ist der nächste Feiertag?"  # 德语
    # language = "de_DE"
    # set_language(language)
    # user_question = "¿Cuándo es el próximo día festivo?"  # 西班牙语
    # language = "es_ES"
    # set_language(language)
    # user_question = "Quando é o próximo feriado?"  # 葡萄牙语
    # language = "pt_PT"
    # set_language(language)
    # user_question = "Quando è la prossima festa?"  # 意大利语
    # language = "it_IT"
    # set_language(language)
    # user_question = "Wanneer is de volgende feestdag?"  # 荷兰语
    # language = "nl_NL"
    # set_language(language)
    # user_question = "När är nästa helgdag?"  # 瑞典语
    # language = "sv_SE"
    # set_language(language)
    # user_question = "Når er neste helligdag?"  # 挪威语
    # language = "nb_NO"
    # set_language(language)
    # user_question = "Hvornår er den næste helligdag?"  # 丹麦语
    # language = "da_DK"
    # set_language(language)
    # user_question = "Wann ist der nächste Feiertag?"  # 瑞士德语
    # language = "de_CH"
    # set_language(language)
    # user_question = "Wann ist der nächste Feiertag?"  # 奥地利德语
    # language = "de_AT"
    # set_language(language)
    # user_question = "Bir sonraki tatil ne zaman?"  # 土耳其语
    # language = "tr_TR"
    # set_language(language)
    # user_question = "متى العطلة القادمة؟"  # 阿拉伯语
    # language = "ar_AE"
    # set_language(language)
    # user_question = "When is the next holiday?"  # 新加坡英语
    # language = "en_SG"
    # set_language(language)
    # user_question = "วันหยุดถัดไปคือวันไหน?"  # 泰语
    # language = "th_TH"
    # set_language(language)
    # user_question = "Kailan ang susunod na holiday?"  # 菲律宾语
    # language = "tl_PH"
    # set_language(language)
    # user_question = "Kapan libur berikutnya?"  # 印尼语
    # language = "id_ID"
    # set_language(language)
    # user_question = "Bilakah cuti seterusnya?"  # 马来语
    # language = "ms_MY"
    # set_language(language)
    # user_question = "下一個假期是什麼時候？"  # 繁体中文(香港)
    # language = "ja_JP"
    # set_language(language)
    # user_question = "下一個假期是什麼時候？"  # 繁体中文(台湾)
    # language = "zh_TW"
    # set_language(language)
    # user_question = "下一个假期是什么时候？"  # 简体中文
    # language = "zh_CN"
    # set_language(language)
    # user_question = "When is the next holiday?"  # 澳大利亚英语
    # language = "en_AU"
    # set_language(language)
    # user_question = "When is the next holiday?"  # 加拿大英语
    # language = "en_CA"
    # set_language(language)
    # user_question = "When is the next holiday?"  # 新西兰英语
    # language = "en_NZ"
    # set_language(language)
    # user_question = "Когда следующий праздник?"  # 俄语
    # language = "ru_RU"
    # set_language(language)
    # user_question = "Quel est le prochain jour férié ?"  # 法语
    # language = "fr_FR"
    # set_language(language)

    from src.session_manager.robot import Robot

    # 直接修改 agent_setting 对象的属性

    # agent_setting.generate_text_model = "gpt-4o-2024-11-20"
    # agent_setting.generate_text_model_base_url = "http://proxy-ai.smartsales.vip/v1"
    # agent_setting.generate_text_model_api_key = "********************************************************************************************************************************************************************"
    # user_question = "明日の日の出時刻は何時ですか？"  # 日语
    user_question = "Open the calendar"
    # user_question = "京都の桜の季節はいつですか？"  # 日语
    root = Robot()
    language = "ar_AE"  # 设置为阿拉伯语
    set_language(language)
    agent_setting.region_version = "oversea"
    # root.language = "en_US"
    root.language = "ar_AE"  # 设置为阿拉伯语
    set_language(language)
    # root.language = "fr_FR"
    # root.language = "zh_GD"
    # root.language = "ko_KR"
    # root.timezone = "America/New_York"    #美国
    # root.timezone = "Europe/Madrid"   #西班牙
    # root.timezone = "Europe/Berlin"    #德国
    # root.timezone = "Asia/Shanghai"  # 中国
    root.geo_location.latitude, root.geo_location.longitude = (
        40.7128,
        -74.0060,
    )  # 纽约 美国
    # root.geo_location.latitude, root.geo_location.longitude = (
    #     35.682839,
    #     139.759455,
    # )  # 东京，日本
    # root.geo_location.latitude, root.geo_location.longitude = 48.856614, 2.352222  # Paris, France
    # root.geo_location.latitude, root.geo_location.longitude = 37.5665, 126.9780  # 首尔，韩国
    # root.geo_location.latitude, root.geo_location.longitude = 40.4168, -3.7038  # 马德里 西班牙
    # root.geo_location.latitude, root.geo_location.longitude = 52.52, 13.4050  # 柏林 德国
    # root.geo_location.latitude, root.geo_location.longitude = 31.2304, 121.4737  # 上海 中国
    # root.geo_location.latitude, root.geo_location.longitude = 51.5074, -0.1278  # London, UK
    # root.geo_location.latitude, root.geo_location.longitude = 55.7558, 37.6173  # Moscow, Russia
    # root.geo_location.latitude, root.geo_location.longitude = -35.2809, 149.1300  # Canberra, Australia
    # root.geo_location.latitude, root.geo_location.longitude = 45.4215, -75.6972  # Ottawa, Canada
    # root.geo_location.latitude, root.geo_location.longitude = -41.2865, 174.7762  # Wellington, New Zealand
    # root.geo_location.latitude, root.geo_location.longitude = 59.9139, 10.7522  # Oslo, Norway
    # root.geo_location.latitude, root.geo_location.longitude = 59.3293, 18.0686  # Stockholm, Sweden
    # root.geo_location.latitude, root.geo_location.longitude = 55.6761, 12.5683  # Copenhagen, Denmark
    # root.geo_location.latitude, root.geo_location.longitude = 52.3676, 4.9041  # Amsterdam, Netherlands
    # root.geo_location.latitude, root.geo_location.longitude = 50.8503, 4.3517  # Brussels, Belgium
    # root.geo_location.latitude, root.geo_location.longitude = 46.9480, 7.4474  # Bern, Switzerland
    # root.geo_location.latitude, root.geo_location.longitude = 48.2082, 16.3738  # Vienna, Austria
    # root.geo_location.latitude, root.geo_location.longitude = (
    #     41.9028,
    #     12.4964,
    # )  # Rome, Italy
    # root.geo_location.latitude, root.geo_location.longitude = 38.7223, -9.1393  # Lisbon, Portugal
    # root.geo_location.latitude, root.geo_location.longitude = 39.9334, 32.8597  # Ankara, Turkey
    # root.geo_location.latitude, root.geo_location.longitude = 25.2048, 55.2708  # Dubai, UAE
    # root.geo_location.latitude, root.geo_location.longitude = 1.3521, 103.8198  # Singapore
    # root.geo_location.latitude, root.geo_location.longitude = 13.7563, 100.5018  # Bangkok, Thailand
    # root.geo_location.latitude, root.geo_location.longitude = 14.5995, 120.9842  # Manila, Philippines
    # root.geo_location.latitude, root.geo_location.longitude = -6.2088, 106.8456  # Jakarta, Indonesia
    # root.geo_location.latitude, root.geo_location.longitude = 3.1390, 101.6869  # Kuala Lumpur, Malaysia
    # root.geo_location.latitude, root.geo_location.longitude = 22.3964, 114.1095  # Hong Kong
    # root.geo_location.latitude, root.geo_location.longitude = 25.0330, 121.5654  # Taipei, Taiwan
    # root.geo_location.latitude, root.geo_location.longitude = 39.9042, 116.4074  # Beijing, China
    # root.geo_location.latitude, root.geo_location.longitude = 22.5431, 114.0579  # Shenzhen, China
    # root.geo_location.latitude, root.geo_location.longitude = (
    #     23.1291,
    #     113.2644,
    # )  # Guangzhou, China
    # root.geo_location.latitude, root.geo_location.longitude = 30.2741, 120.1551  # Hangzhou, China
    # root.geo_location.latitude, root.geo_location.longitude = 31.2304, 121.4737  # Shanghai, China
    result = await calendar(user_question=user_question, __robot=root)
    logger.info(f"===result: {result}===")
    return result


async def get_calendar_data_info():
    # agent_setting.generate_text_model = "gpt-4o-2024-11-20"
    # agent_setting.generate_text_model_base_url = "http://proxy-ai.smartsales.vip/v1"
    # agent_setting.generate_text_model_api_key = "********************************************************************************************************************************************************************"
    # 示例使用
    # question = "今年的元旦是几月几日"
    # question = "今天是几月几日"
    # question="How many days until Christmas?"
    question = "2025年的今天"
    result = await get_calendar_data(
        user_question=question, env=Area.overseas, language="en_US"
    )

    # 打印或使用返回结果
    # print(result)
    logger.debug(f"===result:{result}===")
    return result


# 测试调国内日历的
async def get_domestic_calendar_info():
    # 示例问题
    # user_question = "What day is tomorrow?"
    user_question = "Whats the next holiday?"
    # user_question = "京都的樱花季是什么时候?"
    # user_question = "明天东京的日出时间是几点？"
    # user_question = "下一个节日是啥?"
    # user_question = "今天是几月几日"
    # user_question = "2025年的今天"
    # user_question = "今日係星期一嗎"
    # user_question = "今日係幾號"
    from src.session_manager.robot import Robot
    from src.common.constant import Area

    # 创建机器人实例并设置为国内环境
    root = Robot()
    agent_setting.region_version = "domestic"
    # language = "zh_GD"
    language = "en_US"
    set_language(language)
    root.language = "en_US"  # 设置为中文简体
    agent_setting.region_version = Area.domestic
    # root.region_version = Area.domestic  # 设置为国内环境
    # root.language = "zh_CN"  # 设置为中文
    # root.language = "en_US"
    # root.timezone = "Asia/Shanghai"  # 设置时区
    # root.multilingual = 1

    # 调用日历函数
    result = await calendar(user_question=user_question, __robot=root)
    logger.info(f"===国内日历结果: {result}===")
    return result


if __name__ == "__main__":
    import sys

    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # # 运行异步函数
    asyncio.run(get_calendar_info())
    # asyncio.run(get_calendar_data_info())
    #
    # # 测试调国内日历
    # asyncio.run(get_domestic_calendar_info())
