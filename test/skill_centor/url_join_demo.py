from urllib.parse import urljoin
from src.settings import agent_setting
from loguru import logger


if __name__ == "__main__":
    import sys

    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )
    # 不安全的方式
    search_user_url = (
        agent_setting.robot_openapi_host + agent_setting.corp_search_user_path
    )
    logger.info(f"===search_user_url:{search_user_url}")
    # 安全的方式
    search_user_url = urljoin(
        agent_setting.robot_openapi_host, agent_setting.corp_search_user_path
    )
    logger.info(f"===search_user_url:{search_user_url}===")
