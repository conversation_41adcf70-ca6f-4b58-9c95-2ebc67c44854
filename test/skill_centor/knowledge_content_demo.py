import asyncio
from src.action.server_function import _get_knowledge_content
from loguru import logger
from src.settings import agent_setting
from urllib.parse import urljoin


def compare_url_join():
    enterprise_id = "orion.ovs.entprise.1429922673"
    knowledge_content_url = (
        agent_setting.robot_openapi_host
        + agent_setting.knowledge_content_path
        + f"?chatmax_api=ctai_search_ctai_doc&ov_corp_id={enterprise_id}"
    )
    print(knowledge_content_url)
    knowledge_content_url_2 = (
        urljoin(agent_setting.robot_openapi_host, agent_setting.knowledge_content_path)
        + f"?chatmax_api=ctai_search_ctai_doc&ov_corp_id={enterprise_id}"
    )
    print(knowledge_content_url_2)


async def get_knowledge_content():
    result = await _get_knowledge_content(
        query="公司谁最能喝酒",
        knowledge_base_id="",
        enterprise_id="orion.ovs.entprise.1429922673",
    )
    logger.info(f"===result: {result}===")
    compare_url_join()
    return result


if __name__ == "__main__":
    import sys

    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # 运行异步函数
    asyncio.run(get_knowledge_content())
