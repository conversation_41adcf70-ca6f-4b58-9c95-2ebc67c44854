import asyncio
from src.action.server_function import weather, query_weather
from loguru import logger
from src.settings import agent_setting
from src.utils.i18n import set_language
from src.common.enums import AreaLevel  # Add this import


class MockMemory:
    """模拟Memory类"""

    def __init__(self):
        self.messages = []

    async def get_chat_context(self, max_chat_history=6):
        return type("obj", (object,), {"messages": self.messages})


async def get_weather_info():
    # 中文
    # user_question = "明天天气怎么样"
    # language = "zh_CN"
    # set_language(language)
    # city = "-1"

    # 繁体中文(台湾)
    # user_question = "今天天氣如何？"
    # language = "zh_TW"
    # set_language(language)
    # city = "台北"

    # 繁体中文(香港)
    # user_question = "今日天氣點呀？"
    # language = "zh_GD"
    # set_language(language)
    # city = "香港"

    # 英语
    user_question = "What's the weather like today?"
    language = "en_US"
    set_language(language)
    city = "New York"

    # 日语
    # user_question = "今日の天気はどうですか？"
    # user_question = "東京の天気予報"
    # language = "ja_JP"
    # set_language(language)
    # city = "東京"

    # 泰语
    # user_question = "วันนี้อากาศเป็นอย่างไร?"
    # language = "th_TH"
    # set_language(language)
    # city = "กรุงเทพ"

    # 德语
    # user_question = "Welche Temperatur wird an diesem Tag gewartet?"
    # user_question = "Ich plane....Außenaufnahmen am Heidelberger Kastel...Ich benötige die Sonneneinstellung....Temperaturschwankungen, Sonnenaufgangs- und Untergangszeiten für übermorgen"
    # user_question = "Wenn ich übermorgen direkt nach Düsseldorf fahre, muss ich dann einen Regenschirm mitnehmen?"
    # language = "de_DE"
    # set_language(language)
    # city = "Düsseldorf"

    # city = "Tokyo"
    # user_question = "“What's the weather tomorrow?"
    # language = "en_US"
    # set_language(language)
    # city = "USA"
    # user_question = "明天北京的天气怎么样？"
    # language = "zh_CN"
    # set_language(language)
    # city = "Berlin"

    # 西班牙语
    # user_question = "¿Qué tiempo hace hoy?"
    # language = "es_ES"
    # set_language(language)
    # city = "Madrid"

    # 韩语
    # user_question = "오늘 날씨가 어떻습니까?"
    # language = "ko_KR"
    # set_language(language)
    # city = "서울"

    # 丹麦语
    # user_question = "Hvordan er vejret i dag?"
    # language = "da_DK"
    # set_language(language)
    # city = "København"

    # 瑞典语
    # user_question = "Hur är vädret idag?"
    # language = "sv_SE"
    # set_language(language)
    # city = "Stockholm"

    # 芬兰语
    # user_question = "Millainen sää tänään on?"
    # language = "fi_FI"
    # set_language(language)
    # city = "Helsinki"

    # 挪威语
    # user_question = "Hvordan er været i dag?"
    # language = "nb_NO"
    # set_language(language)
    # city = "Oslo"

    # 法语
    # user_question = "Quel temps fait-il aujourd'hui?"
    # language = "fr_FR"
    # set_language(language)
    # city = "Paris"

    # 荷兰语
    # user_question = "Hoe is het weer vandaag?"
    # language = "nl_NL"
    # set_language(language)
    # city = "Amsterdam"

    # 俄语
    # user_question = "Какая сегодня погода?"
    # language = "ru_RU"
    # set_language(language)
    # city = "Москва"

    # 波兰语
    # user_question = "Jaka jest dzisiaj pogoda?"
    # language = "pl_PL"
    # set_language(language)
    # city = "Warszawa"

    # 葡萄牙语
    # user_question = "Como está o tempo hoje?"
    # language = "pt_PT"
    # set_language(language)
    # city = "Lisboa"

    # 意大利语
    # user_question = "Che tempo fa oggi?"
    # language = "it_IT"
    # set_language(language)
    # city = "Roma"

    # 罗马尼亚语
    # user_question = "Cum este vremea astăzi?"
    # language = "ro_RO"
    # set_language(language)
    # city = "București"

    # 马来语
    # user_question = "Bagaimana cuaca hari ini?"
    # language = "ms_MY"
    # set_language(language)
    # city = "Kuala Lumpur"

    # 越南语
    # user_question = "Thời tiết hôm nay thế nào?"
    # language = "vi_VN"
    # set_language(language)
    # city = "Hà Nội"

    # 印尼语
    # user_question = "Bagaimana cuaca hari ini?"
    # language = "id_ID"
    # set_language(language)
    # city = "Jakarta"

    # 菲律宾语
    # user_question = "Ano ang panahon ngayon?"
    # language = "fil_PH"
    # set_language(language)
    # city = "Manila"

    # 捷克语
    # user_question = "Jaké je dnes počasí?"
    # language = "cs_CZ"
    # set_language(language)
    # city = "Praha"

    # 希腊语
    # user_question = "Τι καιρό κάνει σήμερα;"
    # language = "el_GR"
    # set_language(language)
    # city = "Αθήνα"

    # 葡萄牙语(巴西)
    # user_question = "Como está o tempo hoje?"
    # language = "pt_BR"
    # set_language(language)
    # city = "São Paulo"

    # 匈牙利语
    # user_question = "Milyen idő van ma?"
    # language = "hu_HU"
    # set_language(language)
    # city = "Budapest"

    # 土耳其语
    # user_question = "Bugün hava nasıl?"
    # language = "tr_TR"
    # set_language(language)
    # city = "Ankara"

    # 斯洛伐克语
    # user_question = "Aké je dnes počasie?"
    # language = "sk_SK"
    # set_language(language)
    # city = "Bratislava"

    # 使用默认设置
    # user_question = "What's the weather like today?"
    # language = "en_US"
    # set_language(language)
    # city = "New York"

    from src.session_manager.robot import Robot

    root = Robot()
    agent_setting.region_version = "oversea"
    # agent_setting.region_version = "domestic"
    time = "未来10天"
    area_level = city
    # 设置对应城市的经纬度
    # 可以取消注释使用上面对应城市的坐标
    # root.geo_location.latitude, root.geo_location.longitude = (
    #     40.7128,
    #     -74.0060,
    # )  # New York, USA
    # root.geo_location.latitude, root.geo_location.longitude = (
    #     22.3193,
    #     114.1694,
    # )  # Hong Kong
    # root.geo_location.latitude, root.geo_location.longitude = (
    #     39.911416,
    #     116.5664,
    # )
    root.geo_location.latitude, root.geo_location.longitude = (
        48.14180703014265,
        11.579461097717285,
    )
    # root.region_version = "oversea"
    # agent_setting.region_version = "oversea"
    # agent_setting.region_version == "domestic"
    # root.geo_location.latitude, root.geo_location.longitude = 40.7128, -74.0060  # 纽约 美国
    result = await weather(
        user_question=user_question,
        city=city,
        area_level=area_level,
        time=time,
        __robot=root,
    )
    logger.info(f"===result: {result}===")
    return result


async def get_weather_test():
    """Test real-time weather for both domestic and overseas cities

    This test function verifies the weather query functionality for:
    1. Different languages and regions
    2. Different city types (domestic/overseas)
    3. Error cases and edge conditions
    """
    mock_memory = MockMemory()
    test_cases = [
        # Domestic cities (Chinese)
        {
            "user_question": "北京今天天气如何？",
            "language": "zh_CN",
            "city": "北京",
            "area_level": "city",
            "time": "realtime",
            "region": "domestic",
            "coordinates": (39.9042, 116.4074),
        },
        {
            "user_question": "澳门明天天气如何？",
            "language": "zh_GD",
            "city": "澳门",
            "area_level": "city",
            "time": "realtime",
            "region": "domestic",
            "coordinates": (22.1987, 113.5439),
        },
        # English cities
        {
            "user_question": "What's the weather like today?",
            "language": "en_US",
            "city": "New York",
            "area_level": "city",
            "time": "realtime",
            "region": "oversea",
            "coordinates": (40.7128, -74.0060),
        },
        # Arabic cities
        {
            "user_question": "كيف الطقس اليوم؟",
            "language": "ar_SA",
            "city": "الرياض",  # Riyadh
            "area_level": "city",
            "time": "realtime",
            "region": "oversea",
            "coordinates": (24.7136, 46.6753),
        },
        {
            "user_question": "ما هو الطقس في دبي؟",
            "language": "ar_SA",  # Changed from ar_AE to ar_SA
            "city": "دبي",  # Dubai
            "area_level": "city",
            "time": "realtime",
            "region": "oversea",
            "coordinates": (25.2048, 55.2708),
        },
        # # Japanese city
        {
            "user_question": "今日の天気はどうですか？",
            "language": "ja_JP",
            "city": "東京",
            "area_level": "city",
            "time": "realtime",
            "region": "oversea",
            "coordinates": (35.6762, 139.6503),
        },
        # # Edge cases
        {
            "user_question": "What's the weather like today?",
            "language": "en_US",
            "city": "",  # Empty city
            "area_level": "city",
            "time": "realtime",
            "region": "oversea",
            "coordinates": None,
        },
    ]

    from src.session_manager.robot import Robot

    for test_case in test_cases:
        try:
            # Setup test environment
            logger.info(f"===test_case: {test_case['user_question']}===")
            root = Robot()
            agent_setting.region_version = test_case["region"]
            set_language(test_case["language"])
            root.language = test_case["language"]

            # Set coordinates if provided
            if test_case["coordinates"]:
                root.geo_location.latitude, root.geo_location.longitude = test_case["coordinates"]

            logger.info(
                f"\n{'='*50}\nTesting weather for {test_case['city']} in {test_case['language']} "
                f"(region: {test_case['region']})\n{'='*50}"
            )

            # Execute test
            result = await query_weather(
                user_question=test_case["user_question"],
                city=test_case["city"],
                area_level=test_case["area_level"],
                time=test_case["time"],
                __robot=root,
                __memory=mock_memory,
                _USER_QUERY=test_case["user_question"],
                env=agent_setting.region_version,
            )

            # Verify result
            if result:
                logger.info(f"\nResult for {test_case['city']}:\n{result}\n{'='*50}")
            else:
                logger.warning(f"\nNo weather data returned for {test_case['city']}\n{'='*50}")

        except ValueError as ve:
            logger.error(f"Validation error for {test_case['city']}: {str(ve)}")
        except ConnectionError as ce:
            logger.error(f"Connection error for {test_case['city']}: {str(ce)}")
        except Exception as e:
            logger.error(f"Unexpected error for {test_case['city']}: {str(e)}")
            logger.exception("Detailed error information:")


if __name__ == "__main__":
    import sys

    logger.remove()  # 移除默认的处理器
    logger.add(
        sink=sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss:SSSSS}</green> |"
        "<level>{level:8}</level> |"
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{file}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> |"
        "<blue>context: {extra}</blue>",
        colorize=True,
    )

    # Run both test functions
    # asyncio.run(get_weather_info())
    asyncio.run(get_weather_test())
