"""
pytest test/session_manager/test_chat_context.py -v
"""

import pytest
from src.session_manager.chat_context import ChatMessage, ChatImage, ChatContext


def test_create_text_only_message():
    message = ChatMessage(content="Hello World", role="user")
    assert message.role == "user"
    assert message.content == "Hello World"


def test_create_empty_message():
    message = ChatMessage(role="system")
    assert message.role == "system"
    assert message.content is None


def test_create_message_with_images():
    images = [
        ChatImage(image="base64_string1", inference_width=512, inference_height=512),
        ChatImage(image="base64_string2", inference_width=256, inference_height=256),
    ]
    chat_ctx = ChatContext()
    chat_ctx.append(text="desc", images=images, role="user")

    assert chat_ctx.messages[0].role == "user"
    print(type(chat_ctx.messages[0].content))
    assert isinstance(chat_ctx.messages[0].content, list)
    assert len(chat_ctx.messages[0].content) == 3  # text + 2 images
    assert chat_ctx.messages[0].content[0] == "desc"
    assert isinstance(chat_ctx.messages[0].content[1], ChatImage)
    assert isinstance(chat_ctx.messages[0].content[2], ChatImage)
    assert chat_ctx.messages[0].content[1].image == "base64_string1"
    assert chat_ctx.messages[0].content[2].image == "base64_string2"


def test_create_message_invalid_role():
    with pytest.raises(ValueError):
        ChatMessage(text="Hello", role="invalid_role")


def test_create_message_with_action():
    from src.session_manager.chat_context import Action, Parameter, Result

    action = Action(
        name="test_action",
        display_name="Test Action",
        parameters=[Parameter(name="param1", desc="Test param", value="test")],
        result=[Result(name="result1", desc="Test result", value="success")],
    )

    message = ChatMessage(role="assistant", action=action)
    assert message.role == "assistant"
    assert message.action == action
    assert message.action.name == "test_action"
