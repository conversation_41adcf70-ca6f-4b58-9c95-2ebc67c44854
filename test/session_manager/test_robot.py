"""
pytest test/session_manager/test_robot.py -v
"""

import pytest
from src.session_manager.robot import Robot, InterfaceState, GeoLocation


def test_robot_update():
    # 初始化一个 Robot 实例
    robot = Robot(
        volume=50,
        brightness=70,
        device_id="test_device",
        interface_state=InterfaceState(app_id="test_app"),
        geo_location=GeoLocation(city="上海市"),
    )

    # 测试基本字段更新
    robot.update({"volume": 60, "device_id": "new_device"})
    assert robot.volume == 60
    assert robot.device_id == "new_device"
    assert robot.brightness == 70  # 未更新的字段保持不变
    assert robot.interface_state.app_id == "test_app"

    # 测试嵌套字段更新
    robot.update(
        {
            "interface_state": {"app_id": "new_app"},
            "geo_location": {"city": "北京市"},
        }
    )
    assert robot.interface_state.app_id == "new_app"
    assert robot.geo_location.city == "北京市"

    # 测试无变化的更新
    original_volume = robot.volume
    robot.update({"volume": original_volume})
    assert robot.volume == original_volume

    # 测试 face_id 更新
    robot.update({"face_id": "face_123"})
    assert robot.face_id == "face_123"

    # 测试默认值字段的更新
    robot_with_defaults = Robot()  # 创建一个使用所有默认值的实例
    assert robot_with_defaults.volume == -1
    assert robot_with_defaults.brightness == -1
    assert robot_with_defaults.current_speed == 1.0
    assert robot_with_defaults.battery_level == 100
    assert robot_with_defaults.device_id == "VIRTUAL_DEVICE"

    # 测试从默认值更新
    robot_with_defaults.update(
        {"volume": 75, "brightness": 80, "current_speed": 0.5, "battery_level": 90}
    )
    assert robot_with_defaults.volume == 75
    assert robot_with_defaults.brightness == 80
    assert robot_with_defaults.current_speed == 0.5
    assert robot_with_defaults.battery_level == 90

    # 测试复杂字段的默认值
    assert len(robot_with_defaults.indoor_location_history) == 0
    assert robot_with_defaults.geo_location.city == "北京市"
    assert robot_with_defaults.interface_state.package_name == "com.ainirobot.moduleapp"

    # 测试多个字段同时更新
    robot.update(
        {
            "volume": 30,
            "brightness": 40,
            "face_id": "face_456",
            "current_speed": 0.8,
            "target_speed": 1.2,
            "battery_level": 85,
        }
    )
    assert robot.volume == 30
    assert robot.brightness == 40
    assert robot.face_id == "face_456"
    assert robot.current_speed == 0.8
    assert robot.target_speed == 1.2
    assert robot.battery_level == 85

    # 测试带有完整 interface_info 的初始状态
    robot_with_interface = Robot(
        interface_state=InterfaceState(
            app_id="test_app",
            interface_info="<div>原始界面信息</div>",
            interface_type="custom",
            page_id="page1",
            clickable_elements=["button1", "button2"],
        )
    )

    # 测试部分字段更新（只更新部分字段，其他字段保持不变）
    robot_with_interface.update(
        {
            "interface_state": {
                "interface_info": "<div>新的界面信息</div>",
                "clickable_elements": ["button3"],
            }
        }
    )

    # 未提及的字段应该保持原值
    assert (
        robot_with_interface.interface_state.app_id == "test_app"
    )  # 未在更新中提及，保持原值
    assert (
        robot_with_interface.interface_state.interface_info == "<div>新的界面信息</div>"
    )
    assert (
        robot_with_interface.interface_state.interface_type == "custom"
    )  # 未在更新中提及，保持原值
    assert robot_with_interface.interface_state.page_id == "page1"
    assert robot_with_interface.interface_state.clickable_elements == ["button3"]


if __name__ == "__main__":
    pytest.main(["-v", "test_robot.py"])
