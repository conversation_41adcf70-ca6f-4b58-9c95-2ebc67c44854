import asyncio
import pytest

from src.utils.llm import LLMManager


class DummyResult:
    def __init__(self, content):
        self.content = content


@pytest.mark.asyncio
async def test_handle_parallel_tasks_first_has_content():
    async def fast():
        await asyncio.sleep(0.1)
        return DummyResult("hello")

    async def slow():
        await asyncio.sleep(0.5)
        return DummyResult("world")

    tasks = [asyncio.create_task(fast()), asyncio.create_task(slow())]
    result = await LLMManager._handle_parallel_tasks(tasks)
    assert result.content == "hello"


@pytest.mark.asyncio
async def test_handle_parallel_tasks_first_empty_second_has_content():
    async def fast():
        await asyncio.sleep(0.1)
        return DummyResult("")

    async def slow():
        await asyncio.sleep(0.2)
        return DummyResult("world")

    tasks = [asyncio.create_task(fast()), asyncio.create_task(slow())]
    result = await LLMManager._handle_parallel_tasks(tasks)
    assert result.content == "world"


@pytest.mark.asyncio
async def test_handle_parallel_tasks_all_empty():
    async def fast():
        await asyncio.sleep(0.1)
        return DummyResult("")

    async def slow():
        await asyncio.sleep(0.2)
        return DummyResult("")

    tasks = [asyncio.create_task(fast()), asyncio.create_task(slow())]
    result = await LLMManager._handle_parallel_tasks(tasks)
    assert result.content == ""


@pytest.mark.asyncio
async def test_handle_parallel_tasks_cancelled():
    async def will_cancel():
        await asyncio.sleep(0.2)
        return DummyResult("should not reach here")

    async def canceller(tasks):
        await asyncio.sleep(0.05)
        for t in tasks:
            t.cancel()

    tasks = [asyncio.create_task(will_cancel())]
    # 启动一个任务在短时间后cancel掉主任务
    canceller_task = asyncio.create_task(canceller(tasks))
    try:
        await LLMManager._handle_parallel_tasks(tasks)
        assert False, "Should have raised CancelledError"
    except asyncio.CancelledError:
        pass  # 正常raise
    finally:
        canceller_task.cancel()
