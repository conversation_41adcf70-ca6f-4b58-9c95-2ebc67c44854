import json
import unittest
from unittest.mock import Mock, AsyncMock, patch

from livekit.rtc.chat import ChatMessage
from livekit.rtc.room import DataPacket, Room

from src.assistant.chat_manager import OrionChatManager


class TestOrionChatManager(unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的Room对象
        self.mock_room = Mock(spec=Room)
        self.mock_room.local_participant = Mock()

        # 模拟ChatManager的初始化
        with patch.object(OrionChatManager.__bases__[0], "__init__", return_value=None):
            # 创建OrionChatManager实例
            self.chat_manager = OrionChatManager(self.mock_room)
            self.chat_manager._room = self.mock_room  # Ensure _room is set for tests
            # 模拟父类的emit方法
            self.chat_manager.emit = Mock()

    async def test_send_normal_message(self):
        """测试发送正常大小的消息"""
        # 准备测试数据
        message = {
            "msg_type": "text",
            "content": {"text": "这是一条普通消息", "type": "response"},
            "diagnostic_info": "some diagnostic data",
        }

        # 模拟ChatMessage返回
        mock_chat_message = Mock(spec=ChatMessage)
        mock_chat_message.message = json.dumps(message, ensure_ascii=False)
        mock_chat_message.timestamp = 1234567890
        mock_chat_message.id = "test_msg_id"

        # 模拟父类send_message方法
        with patch.object(
            self.chat_manager.__class__.__bases__[0],
            "send_message",
            new_callable=AsyncMock,
        ) as mock_parent_send:
            mock_parent_send.return_value = mock_chat_message

            # 执行测试
            result = await self.chat_manager.send_message(message.copy())

            # 验证结果
            self.assertIsNotNone(result)
            self.assertEqual(result.id, "test_msg_id")

            # 验证调用参数
            mock_parent_send.assert_called_once()
            call_args = mock_parent_send.call_args[0][0]
            sent_message = json.loads(call_args)

            # 验证消息内容
            self.assertEqual(sent_message["msg_type"], "text")
            self.assertEqual(
                sent_message["content"],
                {"text": "这是一条普通消息", "type": "response"},
            )
            self.assertEqual(sent_message["total"], 1)
            self.assertEqual(sent_message["idx"], 1)
            # diagnostic_info应该被移除
            self.assertNotIn("diagnostic_info", sent_message)

    async def test_send_large_message_over_14kb(self):
        """测试发送超过14KB的消息（分块发送）"""
        # 创建一个超过14KB的消息
        large_text = "这是一个很长的内容 " * 1000  # 大约20KB
        large_content = {"text": large_text, "type": "response", "data": ["item"] * 100}
        message = {"msg_type": "text", "content": large_content}

        # 模拟父类send_message方法
        with patch.object(
            self.chat_manager.__class__.__bases__[0],
            "send_message",
            new_callable=AsyncMock,
        ) as mock_parent_send:
            mock_parent_send.return_value = None

            # 执行测试
            result = await self.chat_manager.send_message(message.copy())

            # 验证结果 - 大消息应该返回None（因为是分块发送）
            self.assertIsNone(result)

            # 验证分块发送被调用多次
            self.assertGreater(mock_parent_send.call_count, 1)

            # 验证每个分块的内容
            call_args_list = mock_parent_send.call_args_list
            total_chunks = len(call_args_list)

            for i, call_args in enumerate(call_args_list):
                sent_message_str = call_args[0][0]
                sent_message = json.loads(sent_message_str)

                # 验证分块属性
                self.assertEqual(sent_message["total"], total_chunks)
                self.assertEqual(sent_message["idx"], i + 1)
                self.assertEqual(sent_message["msg_type"], "text")
                self.assertIsInstance(sent_message["content"], str)

                # 验证第一个和最后一个分块不为空
                if i == 0 or i == total_chunks - 1:
                    self.assertGreater(len(sent_message["content"]), 0)

    def test_receive_normal_message(self):
        """测试接收正常消息"""
        # 准备测试数据
        message_data = {
            "id": "normal_msg_id",
            "message": json.dumps(
                {
                    "msg_type": "text",
                    "content": {"text": "这是一条接收到的消息", "type": "response"},
                },
                ensure_ascii=False,
            ),
            "total": 1,
            "idx": 1,
        }

        # 创建模拟的DataPacket
        mock_data_packet = Mock(spec=DataPacket)
        mock_data_packet.topic = "lk-chat-topic"
        mock_data_packet.data = json.dumps(message_data, ensure_ascii=False)
        mock_data_packet.participant = Mock()

        # 模拟事件发射
        events_received = []

        def mock_emit(event_name, message):
            events_received.append((event_name, message))

        self.chat_manager.emit = mock_emit

        # 执行测试
        self.chat_manager._on_data_received(mock_data_packet)

        # 验证结果
        self.assertEqual(len(events_received), 1)
        event_name, received_message = events_received[0]

        self.assertEqual(event_name, "message_received")
        # 不验证ChatMessage类型，只验证事件被触发

    def test_receive_chunked_message(self):
        """测试接收分块消息"""
        # 准备分块消息数据
        message_id = "chunked_msg_id"
        original_content = {"text": "这是一条被分块的长消息", "type": "response"}
        content_str = json.dumps(original_content, ensure_ascii=False)

        # 模拟原始代码的分块方式：按1000字符切分
        chunks = [
            content_str[i : i + 20]  # 使用较小的块以便测试
            for i in range(0, len(content_str), 20)
        ]

        # 创建分块消息
        chunk_messages = []
        for i, chunk in enumerate(chunks):
            chunk_data = {
                "id": message_id,
                "message": json.dumps(
                    {
                        "content": chunk,
                        "msg_type": "text",
                        "total": len(chunks),
                        "idx": i + 1,
                    },
                    ensure_ascii=False,
                ),
            }
            chunk_messages.append(chunk_data)

        # 模拟事件发射
        events_received = []

        def mock_emit(event_name, message):
            events_received.append((event_name, message))

        self.chat_manager.emit = mock_emit

        # 发送前两个分块，不应该触发消息接收
        for i in range(len(chunks) - 1):
            mock_data_packet = Mock(spec=DataPacket)
            mock_data_packet.topic = "lk-chat-topic"
            mock_data_packet.data = json.dumps(chunk_messages[i], ensure_ascii=False)
            mock_data_packet.participant = Mock()

            self.chat_manager._on_data_received(mock_data_packet)

        # 此时不应该有消息被发射
        self.assertEqual(len(events_received), 0)

        # 验证消息缓存中有部分消息
        self.assertIn(message_id, self.chat_manager.message_cache)

        # 发送最后一个分块，应该触发完整消息接收
        mock_data_packet = Mock(spec=DataPacket)
        mock_data_packet.topic = "lk-chat-topic"
        mock_data_packet.data = json.dumps(chunk_messages[-1], ensure_ascii=False)
        mock_data_packet.participant = Mock()

        self.chat_manager._on_data_received(mock_data_packet)

        # 验证完整消息被接收
        self.assertEqual(len(events_received), 1)
        event_name, received_message = events_received[0]

        self.assertEqual(event_name, "message_received")
        # 不能验证ChatMessage类型，因为from_jsondict可能有依赖，但可以验证事件被触发

        # 验证消息缓存被清理
        self.assertNotIn(message_id, self.chat_manager.message_cache)


if __name__ == "__main__":
    unittest.main()
