# Base image with Python and dependencies
# syntax=docker/dockerfile:1
FROM --platform=linux/amd64 reg.ainirobot.com/speech/easy-nlp/python:3.12-slim.amd

# Prevents Python from writing pyc files.
ENV PYTHONDONTWRITEBYTECODE=1

# Keeps Python from buffering stdout and stderr
ENV PYTHONUNBUFFERED=1

# Install gcc and other build dependencies.
RUN apt-get update && \
    apt-get install -y \
    gcc \
    python3-dev \
    iputils-ping \
    vim \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /easynlp

COPY requirements.txt .
RUN python -m pip install -r requirements.txt