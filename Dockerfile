# Application image built on top of the base image
FROM reg.ainirobot.com/speech/easy-nlp:release-eks-awsnx-easynlp-base-image-20250711

WORKDIR /easynlp

COPY src /easynlp/src

# Copy reload_few_shot.py from scripts directory
COPY scripts/reload_few_shot.py /easynlp/
COPY scripts/reload_intervene_action.py /easynlp/

# Test cases
COPY scripts/benchmark_plan_prompt /easynlp/benchmark_plan_prompt

# ensure that any dependent models are downloaded at build-time
ENV PYTHONPATH=/easynlp

RUN python src/server.py download-files
RUN pybabel compile -d src/locale

# Run the application.
ENTRYPOINT ["python", "src/server.py"]
CMD ["start"]
