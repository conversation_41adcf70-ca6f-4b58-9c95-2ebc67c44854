apiVersion: v1
kind: ConfigMap
metadata:
  name: easy-nlp-config-env
  namespace: agent
data:
  ENV: 'oversea_global'

  LIVEKIT_URL: 'wss://speech-rtc-global.orionstar.com'
  LIVEKIT_API_KEY: 'orionstar_ec2_oversea_global'
  LIVEKIT_API_SECRET: 'cBZD1xALbi9We8mpXh2dlsNyKurFt7GP'

  OPENAI_BASE_URL: 'https://api.openai.com/v1'
  OPENAI_API_KEY: '********************************************************************************************************************************************************************'

  TTS_BASE_URL: 'https://speech-global.orionstar.com'
  TTS_TOKEN: 'eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Im9yaW9uLm92cy5jbGllbnQuMTUxNDI1OTUxMjQ3MSIsIm9wZW5JZCI6IjNhMWY3OWMzMzlkZmI5MWNjMmQ3OTMwODcxMjljZDY1Iiwic2NvcGUiOiJvdnM6YXBwIiwiaXNzIjoib3Jpb24iLCJleHAiOjE2NDUwOTUxNzMsInR5cGUiOiJhY2Nlc3NfdG9rZW4iLCJpYXQiOjE2NDUwODc5NzMsInZlcnNpb24iOiIxLjAifQ.1wGORkN5Ik2SC2pi1F_yRaHlFYQ-6UqiBxpOiUItZ2k'

  DIAGNOSTIC_HOST: 'https://speech-global.orionstar.com'
  DIAGNOSTIC_SECRET_KEY: '6755e0770da042d88e2a8f85c21ae4ba'

  ROBOT_OPENAPI_HOST: "https://global-openapi.orionstar.com"
  ROBOT_OPENAPI_KEY: "34b44257d6a129cbf79c14f4d420f722"
  REGION_VERSION: 'oversea'
  ROBOT_SUPPORT_MAP_POINTS_URL: 'https://speech-global.orionstar.com/speech-manager/v1/grpc/resource/dict'


  QDRANT_HOST: "*************"
  REDIS_HOST: "agentos.mnoajz.ng.0001.euc1.cache.amazonaws.com"

  PLAN_MODEL_BASE_URL: 'https://api.openai.com/v1'
  PLAN_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  PLAN_MODEL: 'gpt-4o-2024-11-20'

  SUMMARY_MODEL_BASE_URL: 'https://api.openai.com/v1'
  SUMMARY_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  SUMMARY_MODEL: 'gpt-4o-2024-11-20'

  GENERATE_TEXT_MODEL_BASE_URL: 'https://api.openai.com/v1'
  GENERATE_TEXT_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  GENERATE_TEXT_MODEL: 'gemini-2.5-flash-lite-preview-06-17'

  EMBEDDING_MODEL_BASE_URL: 'https://api.openai.com/v1'
  EMBEDDING_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  EMBEDDING_MODEL: 'text-embedding-3-small'
  EMBEDDING_DIM: '1024'
  MAX_BATCH_SIZE: '32'
  FEW_SHOT_SCORE_THRESHOLD: '0.3'

  TURBO_PLAN_MODEL_BASE_URL: 'https://api.openai.com/v1'
  TURBO_PLAN_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  TURBO_PLAN_MODEL: 'gpt-4o-2024-11-20'
  TURBO_CHAT_MODEL: 'gpt-4o-2024-11-20'
  TURBO_GENERATE_TEXT_MODEL: 'gemini-2.5-flash-lite-preview-06-17'

  FULL_POWER_PLAN_MODEL_BASE_URL: 'https://api.openai.com/v1'
  FULL_POWER_PLAN_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  FULL_POWER_PLAN_MODEL: 'gpt-4o-2024-11-20'
  FULL_POWER_CHAT_MODEL: 'gpt-4o-2024-11-20'
  FULL_POWER_GENERATE_TEXT_MODEL: 'gemini-2.5-flash-lite-preview-06-17'

  ASR_WS_URL: 'wss://speech-global.orionstar.com/ws/streaming-asr'

  VISION_MODEL: 'gpt-4o-2024-11-20'
  VISION_BASE_URL: 'https://api.openai.com/v1'
  VISION_API_KEY: '********************************************************************************************************************************************************************'

  SPEECH_BRIDGE_SSE_URL: 'https://speech-global.orionstar.com/speech-bridge/events'
  SPEECH_BRIDGE_ENTERPRISE_VALIDATOR_URL: 'https://speech-global.orionstar.com/speech-bridge/assistant/status'

  SPEECH_WAKEUP_RESULT_REPORT_URL: 'https://speech-global.orionstar.com/speech-ai-robot/speech/oversea_asr_vad'

  INTERVENE_EMBEDDING_ENABLE_REDIS: 'true'
  AGENT_CORE_BASE_URL: 'http://agent-core:8000/'

  GUEST_TEXT_MODEL_BASE_URL: 'https://api.openai.com/v1'
  GUEST_TEXT_MODEL_API_KEY: '********************************************************************************************************************************************************************'
  GUEST_TEXT_MODEL: 'gemini-2.5-flash-lite-preview-06-17'

  ENABLE_SINGLE_ACTION_FEWSHOT: 'true'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_API_KEY: '********************************************************************************************************************************************************************'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL_BASE_URL: 'https://api.openai.com/v1'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL: 'text-embedding-3-small'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_DIM: '1024'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_BATCH_SIZE: '16'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_ENABLE_REDIS: 'true'
  SINGLE_ACTION_FEWSHOT_QDRANT_HOST: '*************'
  SINGLE_ACTION_FEWSHOT_QDRANT_INSERT_BATCH_SIZE: '16'

  ASSISTANT_REDIS_HOST: 'redis-nlp-robot.mnoajz.ng.0001.euc1.cache.amazonaws.com'
  ASSISTANT_REDIS_PORT: '6379'
  ASSISTANT_REDIS_DB: '14'
