apiVersion: v1
kind: ConfigMap
metadata:
  name: easy-nlp-config-env
  namespace: agent
data:
  ENV: 'test'

  LIVEKIT_URL: 'wss://speech-rtc-dev.orionstar.com'
  LIVEKIT_API_KEY: 'orionstar_test'
  LIVEKIT_API_SECRET: 'IPa4ltevBiNF9u1wksEUGD6C3S0zAXyr'

  OPENAI_BASE_URL: 'http://proxy-ai.smartsales.vip/v1'
  OPENAI_API_KEY: '***************************************************'

  TTS_BASE_URL: 'https://speech-test.ainirobot.com'
  TTS_TOKEN: 'eyJhbGciOiJIUzI1NiJ9.eyJjbGllbnRJZCI6Im9yaW9uLm92cy5jbGllbnQuMTUxNDI1OTUxMjQ3MSIsIm9wZW5JZCI6IjNhMWY3OWMzMzlkZmI5MWNjMmQ3OTMwODcxMjljZDY1Iiwic2NvcGUiOiJvdnM6YXBwIiwiaXNzIjoib3Jpb24iLCJleHAiOjE2NDUwOTUxNzMsInR5cGUiOiJhY2Nlc3NfdG9rZW4iLCJpYXQiOjE2NDUwODc5NzMsInZlcnNpb24iOiIxLjAifQ.1wGORkN5Ik2SC2pi1F_yRaHlFYQ-6UqiBxpOiUItZ2k'

  DIAGNOSTIC_HOST: 'https://speech-test.orionstar.com'
  DIAGNOSTIC_SECRET_KEY: 'd6487560941d4d2c8d58975d9e47c15c'

  ROBOT_OPENAPI_HOST: 'https://test-openapi.orionstar.com'
  ROBOT_OPENAPI_KEY: '7ef1f8909e3da64c67f3e7e839070b6b'
  ROBOT_SUPPORT_MAP_POINTS_URL: 'http://resource-online-test.ainirobot.com/open/v1/resource/dict'

  PLAN_MODEL_BASE_URL: 'https://prem.dashscope.aliyuncs.com/compatible-mode/v1'
  PLAN_MODEL_API_KEY: 'sk-2f99e60920ee4e22bde8fda877567b99'
  PLAN_MODEL: 'qwen-max'

  SUMMARY_MODEL_BASE_URL: 'https://prem.dashscope.aliyuncs.com/compatible-mode/v1'
  SUMMARY_MODEL_API_KEY: 'sk-2f99e60920ee4e22bde8fda877567b99'
  SUMMARY_MODEL: 'qwen-max'

  GENERATE_TEXT_MODEL_BASE_URL: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
  GENERATE_TEXT_MODEL_API_KEY: 'sk-2f99e60920ee4e22bde8fda877567b99'
  GENERATE_TEXT_MODEL: 'qwen-turbo'

  EMBEDDING_MODEL_BASE_URL: 'http://*************:8080/embed'
  EMBEDDING_MODEL_API_KEY: '***************************************************'
  EMBEDDING_MODEL: 'bge'
  EMBEDDING_DIM: '1024'
  MAX_BATCH_SIZE: '32'
  FEW_SHOT_SCORE_THRESHOLD: '0.45'

  TURBO_PLAN_MODEL_BASE_URL: 'https://prem.dashscope.aliyuncs.com/compatible-mode/v1'
  TURBO_PLAN_MODEL_API_KEY: 'sk-2f99e60920ee4e22bde8fda877567b99'
  TURBO_PLAN_MODEL: 'qwen-max'
  TURBO_CHAT_MODEL: 'qwen-max'

  FULL_POWER_PLAN_MODEL_BASE_URL: 'http://proxy-ai.smartsales.vip/v1'
  FULL_POWER_PLAN_MODEL_API_KEY: '***************************************************'
  FULL_POWER_PLAN_MODEL: 'gpt-4o-2024-11-20'
  FULL_POWER_CHAT_MODEL: 'gpt-4o-2024-11-20'

  INTERVENE_QA_PAIR_URL: 'http://resource-online-test.ainirobot.com/open/v1/resource/qa_pair'
  INTERVENE_NUM_RETRIES_FOR_QA_PAIR: '3'
  INTERVENE_POLISH_ANSWER_MODEL_BASE_URL: 'https://prem.dashscope.aliyuncs.com/compatible-mode/v1'
  INTERVENE_POLISH_ANSWER_MODEL_API_KEY: 'sk-2f99e60920ee4e22bde8fda877567b99'
  INTERVENE_POLISH_ANSWER_MODEL_NAME: 'qwen-max'
  INTERVENE_POLISH_ANSWER_MODEL_MAX_COMPLETION_TOKENS: '512'
  INTERVENE_POLISH_ANSWER_MODEL_TEMPERATURE: '0.0'
  INTERVENE_POLISH_ANSWER_MODEL_REPETITION_PENALTY: '1.0'
  INTERVENE_NUM_RETRIES_FOR_POLISH_ANSWER: '1'
  INTERVENE_EMBEDDING_ENABLE_REDIS: 'false'

  ENABLE_SINGLE_ACTION_FEWSHOT: 'true'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_API_KEY: '***************************************************'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL_BASE_URL: 'http://*************:8080/embed'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_MODEL: 'bge'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_DIM: '1024'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_BATCH_SIZE: '16'
  SINGLE_ACTION_FEWSHOT_EMBEDDING_ENABLE_REDIS: 'false'
  SINGLE_ACTION_FEWSHOT_QDRANT_HOST: '*************'
  SINGLE_ACTION_FEWSHOT_QDRANT_INSERT_BATCH_SIZE: '16'

  AGENT_CORE_BASE_URL: 'http://agent-core-test:8000/'

  GUEST_TEXT_MODEL_BASE_URL: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
  GUEST_TEXT_MODEL_API_KEY: 'sk-2f99e60920ee4e22bde8fda877567b99'
  GUEST_TEXT_MODEL: 'qwen-turbo'

  MAP_TOOL_BASE_URL: 'https://test-agentpoi.orionstar.com/amap/'
