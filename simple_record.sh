#!/bin/bash

# 简单的 Android 屏幕录制脚本
# 先测试基本功能

echo "=== 简单 Android 屏幕录制测试 ==="

# 检查设备连接
echo "检查设备连接..."
if ! adb devices | grep -q "device"; then
    echo "❌ 没有检测到设备"
    exit 1
fi

echo "✓ 设备已连接"

# 生成文件名
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
REMOTE_FILE="/sdcard/test_record_${TIMESTAMP}.mp4"
LOCAL_FILE="./test_record_${TIMESTAMP}.mp4"

echo "开始录制30秒测试视频..."
echo "远程文件: $REMOTE_FILE"
echo "本地文件: $LOCAL_FILE"

# 使用最简单的命令录制30秒
adb shell screenrecord --time-limit 30 $REMOTE_FILE

echo "录制完成，检查文件..."

# 检查文件是否存在
if adb shell "[ -f $REMOTE_FILE ] && echo 'exists'" | grep -q "exists"; then
    echo "✓ 录制文件已生成"
    
    # 拉取文件
    echo "正在拉取文件..."
    if adb pull $REMOTE_FILE $LOCAL_FILE; then
        echo "✓ 文件拉取成功: $LOCAL_FILE"
        
        # 显示文件大小
        if [ -f "$LOCAL_FILE" ]; then
            FILE_SIZE=$(ls -lh "$LOCAL_FILE" | awk '{print $5}')
            echo "📊 文件大小: $FILE_SIZE"
        fi
        
        # 清理设备文件
        adb shell rm $REMOTE_FILE
        echo "✓ 设备文件已清理"
    else
        echo "❌ 文件拉取失败"
    fi
else
    echo "❌ 录制文件未生成"
fi

echo "测试完成！"
