#!/bin/bash

# Android 屏幕录制脚本 - 支持中断保存
# 按 Ctrl+C 会保存当前录制

echo "=== Android 屏幕录制（支持中断保存）==="

# 检查设备连接
if ! adb devices | grep -q "device"; then
    echo "❌ 没有检测到设备"
    exit 1
fi

echo "✓ 设备已连接"

# 生成文件名
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
REMOTE_FILE="/sdcard/record_${TIMESTAMP}.mp4"
LOCAL_FILE="./record_${TIMESTAMP}.mp4"

# 中断处理函数
cleanup() {
    echo ""
    echo "🛑 检测到中断，正在保存录制..."
    
    # 给录制一点时间完成当前帧
    sleep 2
    
    # 检查文件是否存在并拉取
    if adb shell "[ -f $REMOTE_FILE ] && echo 'exists'" | grep -q "exists"; then
        echo "正在拉取录制文件..."
        if adb pull "$REMOTE_FILE" "$LOCAL_FILE"; then
            echo "✓ 录制已保存: $LOCAL_FILE"
            
            # 显示文件大小
            if [ -f "$LOCAL_FILE" ]; then
                FILE_SIZE=$(ls -lh "$LOCAL_FILE" | awk '{print $5}')
                echo "📊 文件大小: $FILE_SIZE"
            fi
        else
            echo "❌ 文件拉取失败"
        fi
        
        # 清理设备文件
        adb shell rm "$REMOTE_FILE" 2>/dev/null
    else
        echo "❌ 录制文件不存在"
    fi
    
    echo "录制结束"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

echo "开始录制屏幕..."
echo "文件将保存为: $LOCAL_FILE"
echo "按 Ctrl+C 停止录制并保存文件"
echo ""

# 开始录制（最长3分钟，或直到用户中断）
adb shell screenrecord --time-limit 180 "$REMOTE_FILE"

# 如果正常结束（没有中断）
echo "录制完成（达到时间限制）"

# 拉取文件
if adb shell "[ -f $REMOTE_FILE ] && echo 'exists'" | grep -q "exists"; then
    echo "正在拉取录制文件..."
    if adb pull "$REMOTE_FILE" "$LOCAL_FILE"; then
        echo "✓ 录制已保存: $LOCAL_FILE"
        
        # 显示文件大小
        if [ -f "$LOCAL_FILE" ]; then
            FILE_SIZE=$(ls -lh "$LOCAL_FILE" | awk '{print $5}')
            echo "📊 文件大小: $FILE_SIZE"
        fi
    else
        echo "❌ 文件拉取失败"
    fi
    
    # 清理设备文件
    adb shell rm "$REMOTE_FILE" 2>/dev/null
else
    echo "❌ 录制文件不存在"
fi

echo "录制任务完成！"
